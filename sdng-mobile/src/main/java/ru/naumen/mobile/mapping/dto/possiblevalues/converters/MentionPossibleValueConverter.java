package ru.naumen.mobile.mapping.dto.possiblevalues.converters;

import static ru.naumen.core.shared.Constants.Employee.EMAIL;
import static ru.naumen.core.shared.Constants.Employee.LOGIN;
import static ru.naumen.mobile.Constants.SystemAttributes.MENTION;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter;
import ru.naumen.mobile.mapping.dto.attrvalue.converters.AbstractAttributeValueConverter;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValuesDtObject;
import ru.naumen.sec.server.autorize.AuthorizationService;

/**
 * Конвертер для преобразования возможных упоминаний
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
@Lazy
@Component
@ValueConverter(attributes = MENTION)
public class MentionPossibleValueConverter
        extends AbstractAttributeValueConverter<List<ICoreBO>, ListPossibleValuesDtObject>
{
    private final AccessorHelper accessorHelper;
    private final AuthorizationService authorizationService;

    @Inject
    public MentionPossibleValueConverter(final AccessorHelper accessorHelper,
            final AuthorizationService authorizationService)
    {
        this.accessorHelper = accessorHelper;
        this.authorizationService = authorizationService;
    }

    @Override
    protected ListPossibleValuesDtObject convert(final List<ICoreBO> value, final AttrMapContext context)
    {
        final List<ListPossibleValueDtObject> dtos = value.stream()
                .map(this::buildListElement)
                .toList();
        return new ListPossibleValuesDtObject(dtos);
    }

    /**
     * Формирует название для объекта.
     *
     * @param element значение атрибута
     */
    protected ListPossibleValueDtObject buildListElement(final ICoreBO element)
    {
        final String uuid = element.getUUID();
        final String title = element.getTitle();
        if (Employee.FQN.isSameClass(element.getMetaClass()))
        {
            final String subtitle = getSubtitleFromAttributes(element, LOGIN, EMAIL);
            return new ListPossibleValueDtObject(uuid, title, subtitle);
        }
        return new ListPossibleValueDtObject(uuid, title);
    }

    private String getSubtitleFromAttributes(final IUUIDIdentifiable object, final String... attributeCodes)
    {
        return Arrays.stream(attributeCodes)
                .filter(attributeCode -> StringUtilities.isNotEmpty(attributeCode)
                                         && authorizationService.hasAttrPermission((IHasMetaInfo)object, attributeCode,
                        false))
                .map(attributeCode -> (String)accessorHelper.getAttributeValue(object, attributeCode))
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" | "));
    }
}
