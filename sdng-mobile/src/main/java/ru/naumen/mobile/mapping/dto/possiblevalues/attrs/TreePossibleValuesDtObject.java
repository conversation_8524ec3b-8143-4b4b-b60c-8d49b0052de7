package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import java.util.Collection;
import java.util.Map;

import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.serializers.TreePossibleValuesDtObjectJsonSerializer;

/**
 * DTO для передачи дерева возможных значений атрибута на сторону мобильного клиента
 *
 * @param values DTO элемента дерева возможных значений
 * @param hasMore логический признак того, что уровень дерева возвращён не полностью
 * @param positions возвращает сдвиг относительно начала списка для каждого из классов для текущего родителя
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@MobileCustomTypeAdapter(TreePossibleValuesDtObjectJsonSerializer.class)
public record TreePossibleValuesDtObject(
        Collection<AttrValueDtObject> values, boolean hasMore,
        Map<String, Integer> positions) implements AttrValueDtObject
{
    public TreePossibleValuesDtObject(
            final Collection<AttrValueDtObject> values,
            final boolean hasMore,
            final Map<String, Integer> positions)
    {
        this.hasMore = hasMore;
        this.values = values.stream()
                .filter(AttrValueDtObject::isNotEmpty)
                .toList();
        this.positions = positions;
    }

    @Override
    public boolean isEmpty()
    {
        return values.isEmpty();
    }
}
