package ru.naumen.mobile.mapping.dto.form.dtos;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.UUIDAttributeType;
import ru.naumen.mobile.Constants.SystemAttributes;
import ru.naumen.mobile.mapping.dto.action.AbstractMobileActionDtObject;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;

/**
 * DTO для передачи атрибута на форме на сторону мобильного клиента.
 * Используется для передачи атрибутов в ходе перевычисления значений на формах.
 *
 * <AUTHOR>
 * @since 01.07.2019
 */
public class BaseFormAttributeDtObject
{
    // служебные типы атрибутов, которые для мобильного клиента являются Ссылкой на БО
    private static final Set<String> OBJECT_ATTRIBUTE_TYPES = Set.of(UUIDAttributeType.CODE,
            SystemAttributes.CLIENT_NAMES);

    /** Код атрибута */
    private final String code;
    /** Код типа атрибута */
    private final String type;
    /** Представление для редактирования атрибута */
    private final FormAttributeEditPresentationDtObject editPresentation;
    /** Список доступных на форме быстрых действий смены ответственного */
    private List<AbstractMobileActionDtObject> actions = Collections.emptyList();
    /** Текущее значение атрибута */
    private AttrValueDtObject value;
    /** Признак обязательности атрибута */
    private final boolean required;

    public BaseFormAttributeDtObject(
            final String code,
            final String type,
            final @Nullable AttrValueDtObject value,
            final boolean isRequired,
            final boolean onlyVisible,
            final boolean isHidden)
    {
        this.code = code;
        this.type = (!OBJECT_ATTRIBUTE_TYPES.contains(type)) ? type : ObjectAttributeType.CODE;
        this.value = value;
        this.required = isRequired;

        this.editPresentation = new FormAttributeEditPresentationDtObject(onlyVisible, isHidden);
    }

    public String getCode()
    {
        return code;
    }

    public String getType()
    {
        return type;
    }

    public FormAttributeEditPresentationDtObject getEditPresentation()
    {
        return editPresentation;
    }

    public List<AbstractMobileActionDtObject> getActions()
    {
        return actions;
    }

    @Nullable
    public AttrValueDtObject getValue()
    {
        return value;
    }

    public boolean isRequired()
    {
        return required;
    }

    public void setActions(List<AbstractMobileActionDtObject> actions)
    {
        this.actions = actions;
    }

    public void setValue(AttrValueDtObject value)
    {
        this.value = value;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj instanceof BaseFormAttributeDtObject other)
        {
            return code.equals(other.code);
        }
        return false;
    }

    @Override
    public int hashCode()
    {
        return code.hashCode();
    }

    @Override
    public String toString()
    {
        return "BaseFormAttributeDtObject[code=" + code + ']';
    }
}
