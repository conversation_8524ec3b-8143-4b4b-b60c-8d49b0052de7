package ru.naumen.mobile.mapping.dto.action.result;

import java.util.Map;

import ru.naumen.mobile.logging.MobileLogPrepareUtils;

/**
 * Результат выполнения пользовательского действия, перенаправляющий на форму создания объекта.
 * Аналог - {@link ru.naumen.core.shared.userevents.GoToInMobileInterfaceActionResult}. Используется до 15 версии API
 * (не включительно)
 *
 * <AUTHOR>
 * @since 29.05.2024
 */
@SuppressWarnings("java:S101")
public class MobileActionOpenCreateObjectFormV14_Result implements MobileActionResult
{
    private final Map<String, Object> attributes;
    private final String contentCode;

    /**
     * @param contentCode код контента формы добавления
     * @param attributes заполненные атрибуты
     */
    public MobileActionOpenCreateObjectFormV14_Result(String contentCode, Map<String, Object> attributes)
    {
        this.attributes = attributes;
        this.contentCode = contentCode;
    }

    @Override
    public MobileUserActionResultType getType()
    {
        return MobileUserActionResultType.CREATE_OBJECT_FORM;
    }

    @Override
    public String toString()
    {
        return "MobileActionOpenCreateObjectFormV14_Result[attributes="
               + MobileLogPrepareUtils.replaceBase64ImagesInValues(attributes)
               + ", contentCode='" + contentCode + "']";
    }
}
