package ru.naumen.mobile.mapping.dto.tx.lists.actions;

import java.util.List;

import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;

/**
 * Представление для передачи в МК {@link ContentListAction действия со списком}, которое не имеет специальных полей
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
public class ListActionDtObject
{
    private String type;
    private String code;
    private String title;
    private List<String> requiredFields;

    public void setType(String type)
    {
        this.type = type;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setTitle(String title)
    {
        this.title = title;
    }

    public void setRequiredFields(List<String> requiredFields)
    {
        this.requiredFields = requiredFields;
    }

    public String getType()
    {
        return type;
    }

    public String getCode()
    {
        return code;
    }

    public String getTitle()
    {
        return title;
    }

    public List<String> getRequiredFields()
    {
        return requiredFields;
    }
}
