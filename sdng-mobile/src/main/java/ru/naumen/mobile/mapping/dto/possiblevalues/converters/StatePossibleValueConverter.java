package ru.naumen.mobile.mapping.dto.possiblevalues.converters;

import static ru.naumen.metainfo.shared.Constants.StateAttributeType;

import java.util.Collections;
import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.ISimpleBO;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter;
import ru.naumen.mobile.mapping.dto.attrvalue.converters.AbstractAttributeValueConverter;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleStateDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.PossibleStatesDtObject;
import ru.naumen.mobile.services.forms.MobileStateFormsTransitionsService;
import ru.naumen.mobile.services.objects.MobileObjectsRetriever;
import ru.naumen.mobile.services.possiblevalues.providers.StatePossibleValuesContainer;

/**
 * Конвертер для преобразования возможных статусов
 *
 * <AUTHOR>
 * @since 11.10.2016
 */
@Lazy
@Component
@ValueConverter(attributes = StateAttributeType.CODE)
public class StatePossibleValueConverter
        extends AbstractAttributeValueConverter<StatePossibleValuesContainer, PossibleStatesDtObject>
{
    private final MobileStateFormsTransitionsService stateFormsTransitionsService;
    private final MobileObjectsRetriever objectsRetriever;

    @Inject
    public StatePossibleValueConverter(final MobileStateFormsTransitionsService stateFormsTransitionsService,
            final MobileObjectsRetriever objectsRetriever)
    {
        this.stateFormsTransitionsService = stateFormsTransitionsService;
        this.objectsRetriever = objectsRetriever;
    }

    @Override
    protected PossibleStatesDtObject convert(StatePossibleValuesContainer value, final AttrMapContext context)
    {
        final State currentState = value.getCurrentState();
        final ListPossibleStateDtObject currentStateDto =
                new ListPossibleStateDtObject(currentState.getCode(), currentState.getTitle(), false);

        final List<State> states = value.getStates();
        if (states.isEmpty())
        {
            return new PossibleStatesDtObject(currentStateDto, Collections.emptyList());
        }

        final MetaClass metaClass = value.getMetaClass();
        final ISimpleBO object = value.getCardObject();
        final DtObject dtObject = objectsRetriever.getDtObject(object.getUUID());
        final List<ListPossibleStateDtObject> statesDto = states.stream()
                .map(state -> buildStateDto(currentState, state, metaClass, dtObject))
                .toList();
        return new PossibleStatesDtObject(currentStateDto, statesDto);
    }

    private ListPossibleStateDtObject buildStateDto(final State currentState, final State newState,
            final MetaClass metaClass, final DtObject object)
    {
        String newStateCode = newState.getCode();
        boolean hasAttributesOnForm = stateFormsTransitionsService.hasApplicableAttributes(
                metaClass, object, currentState.getCode(), newStateCode);
        return new ListPossibleStateDtObject(newStateCode, newState.getTitle(), hasAttributesOnForm);
    }
}
