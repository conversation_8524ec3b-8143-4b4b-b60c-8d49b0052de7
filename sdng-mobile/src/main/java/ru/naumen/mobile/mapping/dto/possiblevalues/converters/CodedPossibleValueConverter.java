package ru.naumen.mobile.mapping.dto.possiblevalues.converters;

import static ru.naumen.mobile.VersionConstants.Version.*;

import java.util.List;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.form.possiblevalues.values.CodedPossibleValue;
import ru.naumen.metainfo.shared.Constants.CatalogAnyItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogAnyItemsSetAttributeType;
import ru.naumen.metainfo.shared.Constants.LicenseAttributeType;
import ru.naumen.metainfo.shared.Constants.SecGroupsAttributeType;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverters;
import ru.naumen.mobile.mapping.dto.attrvalue.converters.AbstractAttributeValueConverter;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValuesDtObject;

/**
 * Конвертер для преобразования возможных значений, представленных кодом и названием.
 * Предназначен для конвертации возможных значений для атрибутов типов:
 * <ul>
 *     <li>Группы пользователей</li>
 *     <li>Лицензия</li>
 *     <li>Элемент произвольного справочника</li>
 *     <li>Набор элементов произвольного справочника</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
@Lazy
@Component
@ValueConverters({
        @ValueConverter(attributes = { SecGroupsAttributeType.CODE, LicenseAttributeType.CODE }),
        @ValueConverter(
                attributes = { CatalogAnyItemAttributeType.CODE, CatalogAnyItemsSetAttributeType.CODE },
                versions = { REST_V12, REST_V13, REST_V13_1, REST_V13_2, REST_V14, REST_V15 }
        )
})
public class CodedPossibleValueConverter
        extends AbstractAttributeValueConverter<List<CodedPossibleValue>, ListPossibleValuesDtObject>
{
    @Override
    protected ListPossibleValuesDtObject convert(final List<CodedPossibleValue> value,
            final AttrMapContext context)
    {
        List<ListPossibleValueDtObject> dtos = value.stream()
                .map(CodedPossibleValueConverter::buildElement)
                .toList();
        return new ListPossibleValuesDtObject(dtos);
    }

    private static ListPossibleValueDtObject buildElement(CodedPossibleValue element)
    {
        return new ListPossibleValueDtObject(element.getCode(), element.getTitle());
    }
}
