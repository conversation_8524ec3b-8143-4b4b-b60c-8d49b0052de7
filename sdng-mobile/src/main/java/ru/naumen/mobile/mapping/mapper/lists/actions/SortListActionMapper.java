package ru.naumen.mobile.mapping.mapper.lists.actions;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.mobile.mapping.dto.tx.lists.actions.ListActionDtObject;
import ru.naumen.mobile.mapping.dto.tx.lists.actions.SortListActionDtObject;
import ru.naumen.mobile.mapping.dto.tx.lists.actions.SortableAttributeDtObject;
import ru.naumen.mobile.services.lists.containers.actions.ListAction;
import ru.naumen.mobile.services.lists.containers.actions.SortListAction;

/**
 * Класс, выполняющий преобразование {@link SortListAction списочных действий типа "Сортировка"} в формат МК.
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
@Component
public class SortListActionMapper implements ListActionMapper
{
    @Override
    public Class<? extends ListAction> getSupportedAction()
    {
        return SortListAction.class;
    }

    @Override
    public ListActionDtObject map(ListAction action)
    {
        SortListActionDtObject dto = new SortListActionDtObject();
        ListActionMapper.fillCommonFields(action, dto);

        SortListAction sortListAction = (SortListAction)action;
        List<SortableAttributeDtObject> sortableAttributes = sortListAction.getAttributes().stream()
                .map(attribute -> new SortableAttributeDtObject(attribute.getFqn().toString(), attribute.getTitle()))
                .toList();
        dto.setAttributes(sortableAttributes);

        return dto;
    }
}
