package ru.naumen.mobile.mapping.dto.possiblevalues.converters;

import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.form.possiblevalues.LegacyAgreementServicePossibleValuesTreeResult;
import ru.naumen.core.server.form.possiblevalues.values.AggregatedPossibleValue;
import ru.naumen.core.server.form.possiblevalues.values.TreePossibleValue;
import ru.naumen.metainfo.shared.Constants.AggregateAttributeType;
import ru.naumen.metainfo.shared.Constants.AgreementServiceAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.mobile.mapping.AttrMapContext;
import ru.naumen.mobile.mapping.dto.attrvalue.ValueConverter;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreeAggregatedObjectValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreeAggregatedPossibleValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreePossibleValuesDtObject;

/**
 * Конвертер для преобразования возможных значений агрегируемых атрибутов.
 * Предназначен для конвертации возможных значений для атрибутов типов:
 * <ul>
 *     <li>Агрегирующий</li>
 *     <li>Ответственный</li>
 *     <li>Соглашение/услуга</li>
 * </ul>
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
@Lazy
@Component
@ValueConverter(attributes = {
        AggregateAttributeType.CODE, ResponsibleAttributeType.CODE, AgreementServiceAttributeType.CODE
})
public class AggregatedPossibleValueConverter extends AbstractPossibleValuesTreeConverter<AggregatedPossibleValue>
{
    @Override
    protected AttrValueDtObject convert(Object value, AttrMapContext context)
    {
        if (value instanceof LegacyAgreementServicePossibleValuesTreeResult result)
        {
            final List<AttrValueDtObject> dtos = buildHierarchy(result.getHierarchy(), result.getHierarchyHead());
            return new TreePossibleValuesDtObject(dtos, false, Map.of());
        }
        return super.convert(value, context);
    }

    @Override
    protected AttrValueDtObject buildTreeElement(
            TreePossibleValue<AggregatedPossibleValue> treeElement,
            List<AttrValueDtObject> children)
    {
        final AggregatedPossibleValue element = treeElement.getElement();
        final String title = element.getTitle();
        final boolean isLeaf = treeElement.isLeaf();
        final boolean isSelectable = treeElement.isSelectable();

        List<TreeAggregatedObjectValueDtObject> values = element.getObjects().stream()
                .map(object -> new TreeAggregatedObjectValueDtObject(object.getUUID(), object.getTitle()))
                .toList();

        return new TreeAggregatedPossibleValueDtObject(
                element.getIdentifier(), values, title, isLeaf, isSelectable, children);
    }
}
