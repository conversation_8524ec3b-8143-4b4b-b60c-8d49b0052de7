package ru.naumen.mobile.mapping.dto.tx.lists.actions;

import java.util.Map;

import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;
import ru.naumen.mobile.mapping.dto.form.MobileFormExecutionContext;

/**
 * Представление для передачи в МК {@link ContentListAction действия со списком} типа
 * {@link ContentListActionType#ADD_OBJECT "Добавить объект"}
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
public class AddObjectListActionDtObject extends ListActionDtObject
{
    private String formCode;
    private MobileFormExecutionContext context;
    private Map<String, Object> attributes;

    public void setFormCode(String formCode)
    {
        this.formCode = formCode;
    }

    public void setContext(MobileFormExecutionContext context)
    {
        this.context = context;
    }

    public void setAttributes(Map<String, Object> attributes)
    {
        this.attributes = attributes;
    }

    public String getFormCode()
    {
        return formCode;
    }

    public MobileFormExecutionContext getContext()
    {
        return context;
    }

    public Map<String, Object> getAttributes()
    {
        return attributes;
    }
}
