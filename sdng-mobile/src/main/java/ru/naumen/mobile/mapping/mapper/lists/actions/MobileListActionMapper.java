package ru.naumen.mobile.mapping.mapper.lists.actions;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.mobile.mapping.dto.tx.lists.actions.ListActionDtObject;
import ru.naumen.mobile.services.lists.containers.actions.ListAction;

/**
 * Класс, предоставляющий возможность конвертирования {@link ContentListAction действия со списком} в
 * {@link ListActionDtObject представление, подходящее для передачи в МК}
 *
 * <AUTHOR>
 * @since 05.07.2024
 */
@Component
public class MobileListActionMapper
{
    private final Map<Class<? extends ListAction>, ListActionMapper> mappers;

    @Inject
    public MobileListActionMapper(List<ListActionMapper> mappers)
    {
        this.mappers = mappers.stream().collect(toMap(ListActionMapper::getSupportedAction, identity()));
    }

    public ListActionDtObject mapAction(ListAction action)
    {
        return getActionMapper(action).map(action);
    }

    private ListActionMapper getActionMapper(ListAction action)
    {
        Class<? extends ListAction> actionType = action.getClass();
        ListActionMapper mapper = mappers.get(actionType);
        if (mapper == null)
        {
            throw new IllegalArgumentException("Unregistered mapper for action type: " + actionType);
        }
        return mapper;
    }
}
