package ru.naumen.mobile.mapping.dto.possiblevalues.serializers;

import static ru.naumen.mobile.VersionConstants.Version.FROM_V15;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.OLD_STATE;

import java.lang.reflect.Type;
import java.util.List;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonSerializationContext;

import ru.naumen.commons.shared.FxException;
import ru.naumen.mobile.context.MobileVersionContextService;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.PossibleStatesDtObject;

/**
 * JSON сериализатор для преобразования {@link PossibleStatesDtObject возможных значений атрибута "Статус"}
 * в формат ожидаемый на стороне мобильного клиента
 *
 * <AUTHOR>
 * @since 29.04.2022
 */
public class PossibleStatesDtObjectJsonSerializer extends PossibleValuesDtObjectJsonSerializer<PossibleStatesDtObject>
{
    @Override
    public JsonElement serialize(PossibleStatesDtObject src, Type typeOfSrc, JsonSerializationContext context)
    {
        if (FROM_V15.contains(MobileVersionContextService.getInvokedVersion()))
        {
            throw new FxException("Not supported");
        }
        final JsonObject object = (JsonObject)super.serialize(src, typeOfSrc, context);
        object.add(OLD_STATE, context.serialize(List.of(src.getCurrentState())));
        return object;
    }
}
