package ru.naumen.mobile.mapping.dto.possiblevalues.serializers;

import static ru.naumen.mobile.VersionConstants.Version.FROM_V15;
import static ru.naumen.mobile.mapping.MappingConstants.Common.UUID;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.CHILDREN;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.IS_LEAF;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.IS_SELECTABLE;
import static ru.naumen.mobile.mapping.MappingConstants.PossibleValues.TITLE;

import java.lang.reflect.Type;
import java.util.List;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.mobile.context.MobileVersionContextService;
import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;
import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.TreePossibleValueDtObject;

/**
 * JSON сериализатор для преобразования {@link TreePossibleValueDtObject элемента дерева возможных значений атрибута}
 * в формат ожидаемый на стороне мобильного клиента
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class TreePossibleValueDtObjectJsonSerializer
        implements JsonSerializer<TreePossibleValueDtObject>
{
    @Override
    public JsonElement serialize(TreePossibleValueDtObject src, Type typeOfSrc,
            JsonSerializationContext context)
    {
        final JsonObject object = new JsonObject();
        object.add(UUID, new JsonPrimitive(src.uuid()));
        // аналогично TreeAggregatedPossibleValueDtObjectJsonSerializer
        object.add(TITLE, new JsonPrimitive(src.title()));
        object.add(IS_LEAF, new JsonPrimitive(src.isLeaf()));
        object.add(IS_SELECTABLE, new JsonPrimitive(src.isSelectable()));
        // всегда возвращаем пустой список дочерних элементов до 15 версии, начиная с неё - только когда список не пуст
        // (необходимо для консистентности API)
        List<AttrValueDtObject> children = src.children();
        if (!FROM_V15.contains(MobileVersionContextService.getInvokedVersion()) || CollectionUtils.isNotEmpty(children))
        {
            object.add(CHILDREN, context.serialize(children));
        }

        return object;
    }
}
