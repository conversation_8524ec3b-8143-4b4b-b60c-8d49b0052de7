package ru.naumen.mobile.mapping.dto.possiblevalues.serializers;

import java.lang.reflect.Type;

import com.google.gson.JsonElement;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;

import ru.naumen.mobile.mapping.dto.possiblevalues.attrs.ListPossibleValuesDtObject;

/**
 * JSON сериализатор для преобразования {@link ListPossibleValuesDtObject списка возможных значений атрибута} в
 * формат ожидаемый на стороне мобильного клиента
 *
 * <AUTHOR>
 * @since 04.04.2024
 */
public class ListPossibleValuesDtObjectJsonSerializer implements JsonSerializer<ListPossibleValuesDtObject>
{
    @Override
    public JsonElement serialize(ListPossibleValuesDtObject src, Type typeOfSrc, JsonSerializationContext context)
    {
        return context.serialize(src.values());
    }
}
