package ru.naumen.mobile.mapping.dto.form;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonCreator;

import jakarta.annotation.Nullable;

/**
 * Перечисление типов форм
 *
 * <AUTHOR>
 * @since 01.07.2019
 */
public enum MobileFormType
{
    /**
     * Форма добавления объекта
     */
    CREATE_OBJECT("create-object"),
    /**
     * Форма редактирования объекта
     */
    EDIT_OBJECT("editObject"),
    /**
     * Форма смены статуса
     */
    CHANGE_STATE("changeState"),
    /**
     * Форма смены типа
     */
    CHANGE_CASE("changeCase"),
    /**
     * Форма смены привязки
     */
    CHANGE_ASSOCIATION("changeAssociation"),
    /**
     * Форма смены ответственного
     */
    CHANGE_RESPONSIBLE("changeResponsible"),
    /**
     * Форма для заполнения параметров пользовательского ДПС
     */
    PARAMETRIZED_USER_ACTION("parametrized-user-action");

    private static final Map<String, MobileFormType> TYPES = Arrays.stream(values())
            .collect(Collectors.toMap(MobileFormType::getType, Function.identity()));

    @JsonCreator
    public static MobileFormType of(final @Nullable String value)
    {
        if (value == null)
        {
            return null;
        }

        final MobileFormType formType = TYPES.get(value);
        if (formType != null)
        {
            return formType;
        }
        throw new IllegalArgumentException("Incorrect form type - " + value);
    }

    /**
     * Тип формы
     */
    private final String type;

    MobileFormType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    @Override
    public String toString()
    {
        return type;
    }
}
