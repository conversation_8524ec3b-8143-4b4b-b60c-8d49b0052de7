package ru.naumen.mobile.mapping.dto.possiblevalues.attrs;

import java.util.Collection;

import ru.naumen.mobile.mapping.dto.attrvalue.attrs.AttrValueDtObject;

/**
 * DTO для передачи для результатов поиска по дереву возможных значений атрибута на сторону мобильного клиента
 *
 * @param values DTO элемента дерева возможных значений
 * @param hasMore логический признак того, что уровень дерева возвращён не полностью
 * @param foundAmount количество найденных возможных значений
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public record TreeSearchPossibleValuesDtObject(
        Collection<AttrValueDtObject> values, boolean hasMore, int foundAmount) implements AttrValueDtObject
{
    public TreeSearchPossibleValuesDtObject(Collection<AttrValueDtObject> values, boolean hasMore, int foundAmount)
    {
        this.hasMore = hasMore;
        this.values = values.stream()
                .filter(AttrValueDtObject::isNotEmpty)
                .toList();
        this.foundAmount = foundAmount;
    }

    @Override
    public boolean isEmpty()
    {
        return values.isEmpty();
    }
}
