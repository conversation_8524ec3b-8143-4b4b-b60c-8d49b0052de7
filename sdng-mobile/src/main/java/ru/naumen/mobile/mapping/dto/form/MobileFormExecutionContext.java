package ru.naumen.mobile.mapping.dto.form;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.mobile.mapping.MobileCustomTypeAdapter;
import ru.naumen.mobile.mapping.dto.serializers.MobileFormExecutionContextSerializer;

/**
 * DTO для получения и передачи контекста работы с формой
 *
 * <AUTHOR>
 * @since 21.05.2024
 */
@MobileCustomTypeAdapter(MobileFormExecutionContextSerializer.class)
public class MobileFormExecutionContext
{
    /** Код карточки */
    private String cardCode;
    /** Идентификатор объекта */
    private String objectUuid;
    /** Код контента */
    private String contentCode;
    /** Доступные типы на форме добавления */
    private List<ClassFqn> possibleCases;

    public MobileFormExecutionContext setCardCode(@Nullable String cardCode)
    {
        this.cardCode = cardCode;
        return this;
    }

    public MobileFormExecutionContext setObjectUuid(@Nullable String objectUuid)
    {
        this.objectUuid = objectUuid;
        return this;
    }

    public MobileFormExecutionContext setContentCode(@Nullable String contentCode)
    {
        this.contentCode = contentCode;
        return this;
    }

    public MobileFormExecutionContext setPossibleCases(@Nullable List<ClassFqn> possibleCases)
    {
        this.possibleCases = possibleCases;
        return this;
    }

    public String getCardCode()
    {
        return cardCode;
    }

    public String getObjectUuid()
    {
        return objectUuid;
    }

    @Nullable
    public String getContentCode()
    {
        return contentCode;
    }

    public List<ClassFqn> getPossibleCases()
    {
        return possibleCases == null ? List.of() : possibleCases;
    }

    @Override
    public String toString()
    {
        return "MobileFormExecutionContext[cardCode=" + cardCode + ", objectUuid=" + objectUuid
               + ", contentCode=" + contentCode + ", possibleCases=" + possibleCases + ']';
    }
}
