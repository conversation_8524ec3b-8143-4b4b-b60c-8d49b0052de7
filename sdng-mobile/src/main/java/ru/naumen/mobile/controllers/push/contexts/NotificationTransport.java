package ru.naumen.mobile.controllers.push.contexts;

import com.fasterxml.jackson.annotation.JsonCreator;

import jakarta.annotation.Nullable;

/**
 * Информация о том, каким образом требуется доставлять уведомления на устройство клиента. Значения данного enum
 * используются при хранении в базе по названию значения.
 *
 * <AUTHOR>
 * @since 02.11.2020
 */
public enum NotificationTransport
{
    /**
     * Data-пуши обрабатываются мобильным клиентом и могут использоваться для отправки push-уведомлений, которые не
     * должны отображаться пользователю (например, для запроса геопозиции)
     */
    DATA_PUSH,

    /**
     * Стандартные push-уведомления, выводом которых занимается магазин приложений соответствующего провайдера
     * (например, Google Play для FCM). Отображаются всегда, мобильным клиентом не обрабатываются.
     */
    NOTIFICATION_PUSH;

    /**
     * Преобразовать строку в элемент перечисления
     */
    @JsonCreator
    public static NotificationTransport of(@Nullable String transport)
    {
        return transport == null ? NOTIFICATION_PUSH : valueOf(transport);
    }
}
