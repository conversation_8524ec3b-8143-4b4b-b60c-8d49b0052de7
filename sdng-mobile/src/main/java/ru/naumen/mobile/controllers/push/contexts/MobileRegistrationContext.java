package ru.naumen.mobile.controllers.push.contexts;

import jakarta.annotation.Nullable;

/**
 * Тело запроса с FCM-токеном устройства, который необходим для отправки пуш-уведомлений на устройства.
 *
 * <AUTHOR>
 * @since Nov 17, 2017
 */
public final class MobileRegistrationContext
{
    /**
     * FCM-токен устройства
     */
    private String registrationId;
    /**
     * Способ доставки уведомления до устройства клиента
     */
    private String notificationTransport;

    @Nullable
    public String getRegistrationId()
    {
        return registrationId;
    }

    public NotificationTransport getNotificationTransport()
    {
        return NotificationTransport.of(notificationTransport);
    }

    @Override
    public String toString()
    {
        return "MobileRegistrationContext{" +
                "registrationId='" + registrationId + '\'' +
                ", notificationTransport='" + notificationTransport + '\'' +
                '}';
    }
}