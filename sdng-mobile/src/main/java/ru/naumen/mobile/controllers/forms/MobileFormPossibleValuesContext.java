package ru.naumen.mobile.controllers.forms;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.SequencedMap;

import com.fasterxml.jackson.annotation.JsonAlias;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.mobile.logging.MobileLogPrepareUtils;
import ru.naumen.mobile.mapping.dto.form.MobileFormExecutionContext;
import ru.naumen.mobile.mapping.dto.form.MobileFormType;
import ru.naumen.mobile.services.forms.MobileFormValuesContext;

/**
 * Контекст для получения возможных значений атрибута через мобильное API
 *
 * <AUTHOR>
 * @since 19.04.2017
 */
public final class MobileFormPossibleValuesContext implements MobileFormValuesContext
{
    /** Уникальный идентификатор объекта, для которого запрошены возможные значения атрибута */
    private String uuid;
    /** Код атрибута */
    @JsonAlias("code")
    private String attributeCode;
    /** Сдвиг относительно начала списка при получении объектов */
    @JsonAlias("firstResult")
    private Integer offset;
    /**
     * Сдвиги относительно начала списка для каждого из классов
     * (для пагинации агрегирующих атрибутов и представлений с папками)
     **/
    private SequencedMap<String, Integer> positions;
    /** Строка поиска */
    private String searchString;
    /** Уникальный идентификатор родительского объекта (для древовидных атрибутов) */
    private List<String> parent;
    /** Атрибуты объекта */
    private Map<String, Object> attributes;
    /** Доступные типы для выбора на форме */
    private MobileFormExecutionContext context;
    /** Код запрашиваемой формы */
    @JsonAlias("contentCode")
    private String formCode;
    /** Тип запрашиваемой формы */
    private String formType;

    @Override
    public Map<String, Object> getAttributes()
    {
        if (attributes == null)
        {
            attributes = Collections.emptyMap();
        }
        return attributes;
    }

    @Override
    public MobileFormExecutionContext getExecutionContext()
    {
        if (context == null)
        {
            context = new MobileFormExecutionContext();
        }
        return context;
    }

    public String getAttributeCode()
    {
        return attributeCode;
    }

    @Nullable
    @Override
    public String getFormCode()
    {
        return formCode;
    }

    public int getOffset()
    {
        return Objects.requireNonNullElse(offset, 0);
    }

    @Nullable
    public List<String> getParent()
    {
        return parent;
    }

    @Nullable
    public SequencedMap<String, Integer> getPositions()
    {
        return positions;
    }

    @Nullable
    public String getSearchString()
    {
        return !StringUtilities.isEmptyTrim(searchString) ? searchString : null;
    }

    @Nullable
    @Override
    public String getUuid()
    {
        return uuid;
    }

    @Nullable
    @Override
    public MobileFormType getFormType()
    {
        return MobileFormType.of(formType);
    }

    public void setAttributes(@Nullable Map<String, Object> attributes)
    {
        this.attributes = attributes;
    }

    public void setContext(@Nullable MobileFormExecutionContext context)
    {
        this.context = context;
    }

    public void setAttributeCode(String attributeCode)
    {
        this.attributeCode = attributeCode;
    }

    public void setFormCode(@Nullable String formType)
    {
        this.formCode = formType;
    }

    public void setOffset(@Nullable Integer offset)
    {
        this.offset = offset;
    }

    public void setParent(@Nullable List<String> parent)
    {
        this.parent = parent;
    }

    public void setPositions(@Nullable SequencedMap<String, Integer> positions)
    {
        this.positions = positions;
    }

    public void setSearchString(@Nullable String searchString)
    {
        this.searchString = searchString;
    }

    public void setUuid(@Nullable String uuid)
    {
        this.uuid = uuid;
    }

    public void setFormType(@Nullable String formType)
    {
        this.formType = formType;
    }

    @Override
    public String toString()
    {
        String contextPart = context != null ? context.toString() : null;
        return "MobilePossibleValuesContext [uuid=" + uuid + ", attributeCode=" + attributeCode + ", offset=" + offset
               + ", positions=" + positions + ", searchString=" + searchString + ", parent=" + parent
               + ", attributes=" + MobileLogPrepareUtils.replaceBase64ImagesInValues(attributes)
               + ", context=" + contextPart + ", formCode=" + formCode + ", formType=" + formType + "]";
    }
}
