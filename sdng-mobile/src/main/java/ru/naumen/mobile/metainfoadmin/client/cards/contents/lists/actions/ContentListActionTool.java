package ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.IHasI18nTitle;
import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.metainfo.shared.ui.Tool;

/**
 * GWT аналог {@link ContentListAction действия в контенте}.
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
@SuppressWarnings("java:S2160") // Нет смысла переопределять equals, так как он смотрит на uuid
public class ContentListActionTool extends Tool implements HasCode, IHasI18nTitle
{
    private List<LocalizedString> title;
    private ContentListActionType actionType;
    private String actionContentCode;
    private boolean isGeoRequired;

    private List<String> tags = Collections.emptyList();

    public ContentListActionTool()
    {
        this(Collections.emptyList());
    }

    public ContentListActionTool(ContentListAction contentListAction)
    {
        this(contentListAction.getTitle());
        this.actionType = contentListAction.getActionType();
        this.actionContentCode = contentListAction.getActionContentCode();
        this.isGeoRequired = contentListAction.isGeoRequired();
        setUuid(contentListAction.getUuid());
    }

    public ContentListActionTool(List<LocalizedString> title)
    {
        this.title = new ArrayList<>(title);
    }

    @Override
    public List<LocalizedString> getTitle()
    {
        return title;
    }

    @Nullable
    public ContentListActionType getActionType()
    {
        return actionType;
    }

    @Nullable
    public String getActionContentCode()
    {
        return actionContentCode;
    }

    public boolean isGeoRequired()
    {
        return isGeoRequired;
    }

    @Override
    public List<String> getTags()
    {
        return tags;
    }

    public void setActionType(@Nullable ContentListActionType actionType)
    {
        this.actionType = actionType;
    }

    public void setActionContentCode(@Nullable String actionContentCode)
    {
        this.actionContentCode = actionContentCode;
    }

    public void setGeoRequired(boolean geoRequired)
    {
        isGeoRequired = geoRequired;
    }

    public void setTags(List<String> tags)
    {
        this.tags = tags;
    }

    public ContentListAction toContentListAction()
    {
        ContentListAction contentListAction = new ContentListAction();
        contentListAction.setUuid(getUuid());
        contentListAction.setTitle(getTitle());
        contentListAction.setActionType(getActionType());
        contentListAction.setActionContentCode(getActionContentCode());
        contentListAction.setGeoRequired(isGeoRequired());
        return contentListAction;
    }

    @Override
    public String getCode()
    {
        return getUuid();
    }
}
