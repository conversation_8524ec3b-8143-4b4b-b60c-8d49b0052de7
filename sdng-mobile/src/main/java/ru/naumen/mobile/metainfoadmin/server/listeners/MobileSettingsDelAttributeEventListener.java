package ru.naumen.mobile.metainfoadmin.server.listeners;

import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER;

import java.util.Iterator;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.BeforeAttributeDeleteEvent;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileRelObjectsListContent;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;

/**
 * Проверяет не используется ли удаляемый атрибут в настройках мобильного приложения.
 *
 * <AUTHOR>
 * @since 26 мая 2015 г.
 */
@Component
public class MobileSettingsDelAttributeEventListener
        extends AbstractDeleteEventListener<BeforeAttributeDeleteEvent, Attribute>
{
    @Inject
    private SortAndFiltrationDelListentersHelper helper;

    @Inject
    private MetainfoUtils utils;

    @Override
    protected String getMessage(BeforeAttributeDeleteEvent event)
    {
        return messages.getMessage("mobile.haveSettingsForAttr");
    }

    @Override
    protected boolean handleAddForm(AddForm form, Attribute attr, boolean hasMobileModule)
    {
        boolean hasAttribute = hasAttribute(form, attr, hasMobileModule);
        hasAttribute |= attr.getFqn().toString()
                .equals(form.getVoiceDecryptionAttribute());
        return saveIfHasAttribute(form, hasMobileModule, hasAttribute);
    }

    @Override
    protected boolean handleCard(ObjectCard card, Attribute attr, boolean hasMobileModule, List<String> cardErrors)
    {
        boolean result = false;
        List<String> contentsWithErrors = new ArrayList<>();
        for (AbstractMobileContent content : card.getAllContents())
        {
            if (content instanceof MobileListContentBase
                && handleContentList((MobileListContentBase)content, attr, hasMobileModule))
            {
                contentsWithErrors.add(utils.getLocalizedValue(content.getCaption()) + " (" + content.getCode() + ")");
                result = true;
            }
        }

        if (!hasMobileModule && result)
        {
            mobileSettingsService.saveContent(card);
        }

        if (!contentsWithErrors.isEmpty())
        {
            cardErrors.add(messages.getMessage("mobile.contentDetails",
                    ru.naumen.commons.shared.utils.StringUtilities.join(contentsWithErrors), card.getCode()));
        }

        // Если ошибки есть - они записаны в contentDetails
        return false;
    }

    @Override
    protected boolean handleEditForm(EditForm form, Attribute attr, boolean hasMobileModule)
    {
        boolean hasAttribute = hasAttribute(form, attr, hasMobileModule);
        return saveIfHasAttribute(form, hasMobileModule, hasAttribute);
    }

    @Override
    protected boolean handleList(MobileList list, Attribute attr, boolean hasMobileModule)
    {
        boolean hasAttribute = false;
        if (list instanceof MobileRelObjectsList)
        {
            List<AttrReference> attrChain = ((MobileRelObjectsList)list).getAttrsChain();
            hasAttribute = handleContentAttrChain(attr, hasMobileModule, hasAttribute, attrChain);
        }

        hasAttribute |= helper.findAttrInSortOfFiltrationSettings(attr, list);

        hasAttribute |= hasAttribute(list, attr, hasMobileModule);

        return saveIfHasAttribute(list, hasMobileModule, hasAttribute);
    }

    private boolean handleContentAttrChain(Attribute attr, boolean hasMobileModule, boolean hasAttribute,
            List<AttrReference> attrChain)
    {
        for (AttrReference attrRef : attrChain)
        {
            if (attr.getMetaClass().getFqn().isSameClass(attrRef.getClassFqn())
                && ObjectUtils.equals(attr.getCode(), attrRef.getAttrCode()))
            {
                if (!hasMobileModule)
                {
                    attrChain.clear();
                }
                hasAttribute = true;
                break;
            }
        }
        return hasAttribute;
    }

    private boolean handleContentList(MobileListContentBase list, Attribute attr, boolean hasMobileModule)
    {
        boolean hasAttribute = false;
        if (list instanceof MobileRelObjectsListContent)
        {
            MobileRelObjectsListContent relObjList = (MobileRelObjectsListContent)list;
            List<AttrReference> attrChain = relObjList.getAttrsChain();
            hasAttribute = handleContentAttrChain(attr, hasMobileModule, hasAttribute, attrChain);

            if (relObjList.getNestedAttrLinkFqn() != null
                && attr.getCode().equals(relObjList.getNestedAttrLinkFqn().getCode())
                && attr.getMetaClass().getFqn().isSameClass(relObjList.getNestedAttrLinkFqn().getClassFqn()))
            {
                return true;
            }

            if (!ObjectUtils.isEmpty(relObjList.getNestedHierarchyAttrFqn())
                && !ru.naumen.metainfo.shared.Constants.CURRENT_OBJECT
                    .equals(relObjList.getNestedHierarchyAttrFqn()))
            {
                AttributeFqn hierarchyAttrFqn = AttributeFqn.parse(relObjList.getNestedHierarchyAttrFqn());
                if (attr.getCode().equals(hierarchyAttrFqn.getCode())
                    && attr.getMetaClass().getFqn().isSameClass(relObjList.getNestedAttrLinkFqn().getClassFqn()))
                {
                    return true;
                }
            }
        }

        return hasAttribute;
    }

    private static boolean hasAttribute(CommonMobileView content, Attribute attr, boolean hasMobileModule)
    {
        boolean found = false;

        ClassFqn fqn = content.getFqnOfClass();
        MetaClass attrMetaclass = attr.getMetaClass();
        if (null != fqn && (fqn.equals(attrMetaclass.getFqn()) || attrMetaclass.isChildForClassFqn(fqn)))
        {
            List<MobileAttribute> attrs = content.getAttributes();
            Iterator<MobileAttribute> i = attrs.iterator();
            while (i.hasNext())
            {
                if (ObjectUtils.equals(i.next().getAttributeFqn(), attr.getFqn()))
                {
                    if (!hasMobileModule)
                    {
                        i.remove();
                    }
                    found = true;
                }
            }
        }
        return found;
    }

    private boolean saveIfHasAttribute(CommonMobileView content, boolean hasMobileModule, boolean hasAttribute)
    {
        if (hasAttribute && !hasMobileModule)
        {
            mobileSettingsService.saveContent(content);
        }
        return hasAttribute;
    }

    @Override
    protected boolean handleNavigationElement(MobileEmbeddedApplicationMenuItemValue menuItem, Attribute checkedAttr,
            boolean hasMobileModule)
    {
        final List<AttrReference> references = menuItem.getLinkObjectAttrs();
        if (references == null)
        {
            return false;
        }
        boolean hasAttribute = references.stream()
                .anyMatch(attrReference -> isReferenceForCheckedAttr(attrReference, checkedAttr));

        if (hasAttribute && !hasMobileModule)
        {
            //для параметра "Объект, с которым работает приложение" элемента menuItem, устанавливаем занчение -
            //"Текущий пользователь", так как только с таким значением можно не назначать атрибут связи
            menuItem.setLinkObject(CURRENT_USER);
            menuItem.setLinkObjectCase(null);
            menuItem.setLinkObjectAttrs(null);

            return !mobileSettingsService.saveNavigationElement(menuItem);
        }
        return hasAttribute;
    }

    /**
     * Сравнивает атрибут элемента навигационного меню с атрибутом переданным для проверки
     * @param attrReference атрибут элемента навигационного меню МК
     * @param checkedAttr проверяемый атрибут
     * @return true, если атрибут используется в элементе навигационного меню МК
     */
    private static boolean isReferenceForCheckedAttr(AttrReference attrReference, Attribute checkedAttr)
    {
        final String attrFqn = checkedAttr.getFqn().toString();
        final MetaClass metaclass = checkedAttr.getMetaClass();
        if (attrFqn.equals(attrReference.toFqnString()) || metaclass.isParentForClassFqn(attrReference.getClassFqn()))
        {
            return attrReference.getAttrCode().equals(checkedAttr.getCode());
        }
        return false;
    }
}
