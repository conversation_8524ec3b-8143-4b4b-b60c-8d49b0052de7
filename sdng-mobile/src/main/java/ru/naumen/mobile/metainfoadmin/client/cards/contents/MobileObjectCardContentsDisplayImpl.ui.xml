<?xml version="1.0" encoding="UTF-8"?>
<!-- <AUTHOR> -->
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder"
			 xmlns:g="urn:import:com.google.gwt.user.client.ui"
			 xmlns:toolbar="urn:import:ru.naumen.core.client.content.toolbar.display.buttons">
	<ui:with field="res" type="ru.naumen.admin.client.widgets.AdminWidgetResources" />
	<ui:with field="msg" type="ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages"/>
	<g:FlowPanel ui:field="cardPanel" styleName="{res.mobileContent.mobileObjectCard}">
		<toolbar:ButtonToolBarDisplayImpl ui:field="toolBar"/>
		<g:FlowPanel ui:field="outer" styleName="{res.metainfoAdmin.uiDisplayContent} {res.mobileContent.mobileContent}">
			<g:FlowPanel ui:field="inner">
				<g:FlowPanel ui:field="inlineForm">
					<g:FlowPanel ui:field="body" styleName="{res.mobileContent.mobileContentBody}">
						<g:FlowPanel ui:field="outerPanel" styleName="{res.mobileContent.mobileCardHeader}">
							<g:HTML ui:field="cardTitle" styleName="{res.mobileContent.gMh1}"/>
						</g:FlowPanel>
						<g:FlowPanel ui:field="contentsContainer" styleName="{res.mobileContent.contentsContainer}"/>
						<g:FlowPanel ui:field="toolContainer" />
					</g:FlowPanel>
					<g:FlowPanel ui:field="commandPanel" styleName="{res.mobileContent.actionBar}">
					</g:FlowPanel>
				</g:FlowPanel>
			</g:FlowPanel>
		</g:FlowPanel>
	</g:FlowPanel>
</ui:UiBinder>