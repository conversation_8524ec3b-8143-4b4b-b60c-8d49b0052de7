package ru.naumen.mobile.metainfoadmin.server.cards;

import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.tags.usage.listeners.mobile.contents.events.BeforeEditMobileContentEvent;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfo.MobileVisibilityConditionService;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.ChangeMobileContentResponse;
import ru.naumen.mobile.metainfoadmin.shared.EditObjectCardMobileContentAction;
import ru.naumen.mobile.services.lists.MobileListActionsService;

/**
 * Реализация действия добавления контента к представлению МК
 *
 * <AUTHOR>
 * @since 25 февр. 2019 г.
 */
@Component
public class EditObjectCardMobileContentActionHandler extends
        TransactionalActionHandler<EditObjectCardMobileContentAction, ChangeMobileContentResponse>
{
    private final MobileSettingsService mobileSettingsService;
    private final EditMobilePropertiesListHelper mobilePropertiesListHelper;
    private final MobileSettingsLogService mobileSettingsLogService;
    private final EditMobileEmbeddedApplicationContentHelper mobileEmbeddedApplicationContentHelper;
    private final MobileVisibilityConditionService visibilityConditionService;
    private final ApplicationEventPublisher eventPublisher;
    private final MobileListActionsService listActionsService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public EditObjectCardMobileContentActionHandler(final MobileSettingsService mobileSettingsService,
            final MobileSettingsLogService mobileSettingsLogService,
            final EditMobilePropertiesListHelper mobilePropertiesListHelper,
            final EditMobileEmbeddedApplicationContentHelper mobileEmbeddedApplicationContentHelper,
            final MobileVisibilityConditionService visibilityConditionService,
            final ApplicationEventPublisher eventPublisher,
            final MobileListActionsService listActionsService,
            final AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobilePropertiesListHelper = mobilePropertiesListHelper;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.mobileEmbeddedApplicationContentHelper = mobileEmbeddedApplicationContentHelper;
        this.visibilityConditionService = visibilityConditionService;
        this.eventPublisher = eventPublisher;
        this.listActionsService = listActionsService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public ChangeMobileContentResponse executeInTransaction(EditObjectCardMobileContentAction action,
            ExecutionContext context) throws DispatchException
    {
        AbstractMobileContent mobileContent = action.getMobileContent();
        ObjectCard objectCard = mobileSettingsService.getObjectCard(action.getViewUuid());
        MapProperties oldProperties = mobileSettingsLogService.getMobileContentLogInfo(objectCard);
        List<AbstractMobileContent> contents = objectCard.getContents();
        int index = contents.indexOf(mobileContent);
        AbstractMobileContent oldContent = contents.get(index);

        adminPermissionCheckService.checkPermission(oldContent, EDIT);
        eventPublisher.publishEvent(new BeforeEditMobileContentEvent(mobileContent, objectCard));

        if (mobileContent instanceof MobilePropertiesListContent propertiesList)
        {
            MobilePropertiesListContent oldPropertiesList = (MobilePropertiesListContent)oldContent;
            if (!Objects.equals(oldPropertiesList.getAttributeGroup(), propertiesList.getAttributeGroup()))
            {
                mobilePropertiesListHelper.fillMobileAttributes(objectCard, propertiesList);
            }
        }
        if (mobileContent instanceof MobileEmbeddedApplicationContent embeddedApplicationContent)
        {
            mobileEmbeddedApplicationContentHelper.convertParametersValuesTosSerializable(embeddedApplicationContent);
        }
        if (mobileContent instanceof MobileListContentBase listContent)
        {
            listActionsService.resetNotCoveredAddForms(listContent);
        }

        contents.set(index, mobileContent);

        if (mobileContent instanceof MobileListsGroupContent listsGroup)
        {
            MobileListsGroupContent oldListsGroup = (MobileListsGroupContent)oldContent;

            List<AbstractMobileContent> oldContents = new ArrayList<>(oldListsGroup.getListContents());
            List<AbstractMobileContent> newContents = new ArrayList<>(listsGroup.getListContents());

            oldContents.removeAll(newContents);
            contents.addAll(oldContents);

            listsGroup.getListContents().forEach(contents::remove);
        }

        ObjectCard oldValues = mobileSettingsService.getObjectCard(action.getViewUuid());
        objectCard.setContents(contents);
        if (mobileSettingsService.saveContent(objectCard))
        {
            mobileSettingsLogService.logEditContent(oldValues, objectCard, mobileContent, oldProperties, "");
        }

        mobileContent = (AbstractMobileContent)mobileContent.clone();
        visibilityConditionService.setValueVisibilityCondition(mobileContent.getVisibilityCondition());
        visibilityConditionService.prepareConditionForClient(mobileContent.getVisibilityCondition());
        return new ChangeMobileContentResponse(mobileContent);
    }
}
