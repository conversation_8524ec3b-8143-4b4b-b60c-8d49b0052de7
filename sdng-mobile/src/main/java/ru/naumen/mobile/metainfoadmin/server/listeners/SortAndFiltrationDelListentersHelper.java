package ru.naumen.mobile.metainfoadmin.server.listeners;

import java.util.Iterator;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;

/**
 * Класс, служаший для поиска использования удаляемых атрибутов 
 * в настройках сортировки и фильтрации мобильных списков
 *
 * <AUTHOR>
 * @since 11 июня 2015 г.
 */
@Component
public class SortAndFiltrationDelListentersHelper
{
    private final ModulesService modulesService;
    private final MetainfoServiceBean metainfoService;

    @Inject
    public SortAndFiltrationDelListentersHelper(ModulesService modulesService,
            MetainfoServiceBean metainfoService)
    {
        this.modulesService = modulesService;
        this.metainfoService = metainfoService;
    }

    private boolean checkFilters(ListFilter filters, AttributeFqn declared)
    {
        boolean attributeFound = false;
        for (ListFilterAndElement andFilter : filters.getElements())
        {
            Iterator<ListFilterOrElement<?>> i = andFilter.getElements().iterator();
            while (i.hasNext())
            {
                ListFilterOrElement<?> orFilter = i.next();
                attributeFound |= findAttributeInFilter(declared, orFilter);

                if (attributeFound && !hasMobileModule())
                {
                    i.remove();
                }
            }
        }
        return attributeFound;
    }

    private boolean findAttributeInSortSettings(AttributeFqn declared, MobileList mobileList)
    {
        boolean hasMobileModule = hasMobileModule();

        boolean attributeFound = false;

        Iterator<AttributeOrder> i = mobileList.getOrders().iterator();
        while (i.hasNext())
        {
            AttributeOrder attributeOrder = i.next();
            AttributeFqn attrFqn = AttributeFqn.parse(attributeOrder.getCode());
            Attribute attr = getAttributeSafe(attrFqn);
            if (attr != null && declared.equals(attr.getFqn()))
            {
                attributeFound = true;
                if (!hasMobileModule)
                {
                    i.remove();
                }
            }
        }
        return attributeFound;
    }

    private boolean findAttributeInFilter(AttributeFqn declared, ListFilterOrElement<?> orFilter)
    {
        AttributeFqn attrFqn = AttributeFqn.parse(orFilter.getAttributeFqn());
        return declared.equals(attrFqn);
    }

    private boolean findAttributeInFiltrationSettings(AttributeFqn declared, MobileList mobileList)
    {
        if (mobileList.getListFilter().getElements().isEmpty())
        {
            return false;
        }
        return checkFilters(mobileList.getListFilter(), declared);
    }

    public boolean findAttrInSortOfFiltrationSettings(Attribute attribute, MobileList mobileList)
    {
        AttributeFqn declared = attribute.getFqn();
        return findAttributeInSortSettings(declared, mobileList)
               || findAttributeInFiltrationSettings(declared, mobileList);
    }

    private @Nullable Attribute getAttributeSafe(@Nonnull AttributeFqn attrFqn)
    {
        final ClassFqn metaClassFqn = attrFqn.getClassFqn();
        if (metainfoService.isMetaclassExists(metaClassFqn))
        {
            final MetaClassImpl metaClass = metainfoService.getMetaClass(metaClassFqn);
            if (metaClass.hasAttribute(attrFqn))
            {
                return metainfoService.getAttribute(attrFqn);
            }
        }
        return null;
    }

    private boolean hasMobileModule()
    {
        return modulesService.isInstalled(Modules.MOBILE_API);
    }
}
