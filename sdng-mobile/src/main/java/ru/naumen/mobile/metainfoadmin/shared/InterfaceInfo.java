package ru.naumen.mobile.metainfoadmin.shared;

import java.util.Collection;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import java.util.ArrayList;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.mobile.other.MobileLoginType;

/**
 * Поскольку в интерфейсе администратора нет синхронного доступа к метаинформации,
 * складывем всю информацию, необходимую для отображения определенных настроек, в
 * один объект и добавляем его в объекты переноса данных.
 *
 * <AUTHOR>
 * @since 17 апр. 2015 г.
 */
// TODO переименовать в более понятное MobileMetaInfoSync
public class InterfaceInfo implements IsSerializable
{
    private HashMap<ClassFqn, String> metaClassTitles = new HashMap<>();
    /* key: <MobileContentType, Uuid>, value: Set<Fqns>
     * (нужно, т.к. могут быть контенты с одинаковым uuid-ом, но разным типом)
     */
    private HashMap<Pair<String, String>, Set<ClassFqn>> mobileContentMetaClases = new HashMap<>();
    private HashMap<ClassFqn, Boolean> metaClassHasWorkflow = new HashMap<>();
    private HashMap<AttributeFqn, Attribute> attributes = new HashMap<>();
    private HashMap<String, String> profileTitles = new HashMap<>();
    private HashMap<String, String> tagTitles = new HashMap<>();
    private HashMap<String, Boolean> tagEnabled = new HashMap<>();
    private HashMap<String, String> stateTitles = new HashMap<>();
    private HashSet<AttributeFqn> disabledAttributes = new HashSet<>();
    private HashMap<ClassFqn, List<String>> attributeGroups = new HashMap<>();
    private HashMap<ClassFqn, List<String>> attributeCodes = new HashMap<>();
    private HashMap<String, EmbeddedApplication> embeddedApplications = new HashMap<>();
    private EnumMap<MobileLoginType, String> loginTypeTitles = new EnumMap<>(MobileLoginType.class);
    private HashMap<String, List<String>> transitionCodes = new HashMap<>();
    private HashSet<String> mobileVersions = new HashSet<>();
    private boolean silentMode = false;

    public void addAttribute(Attribute attribute)
    {
        attributes.put(attribute.getFqn(), attribute);
    }

    public HashMap<ClassFqn, List<String>> getAttributeCodes()
    {
        return attributeCodes;
    }

    public HashMap<ClassFqn, List<String>> getAttributeGroups()
    {
        return attributeGroups;
    }

    public String getAttributeTitle(AttributeFqn attributeFqn)
    {
        return attributes.get(attributeFqn).getTitle();
    }

    public HashMap<String, Attribute> getAttributes()
    {
        final HashMap<String, Attribute> attributesMap = new HashMap<>();
        for (Map.Entry<AttributeFqn, Attribute> entry : attributes.entrySet())
        {
            attributesMap.put(entry.getKey().toString(), entry.getValue());
        }
        return attributesMap;
    }

    public HashMap<String, Attribute> getCodeToAttribute()
    {
        HashMap<String, Attribute> attrMap = new HashMap<>();

        for (Map.Entry<AttributeFqn, Attribute> e : attributes.entrySet())
        {
            attrMap.put(e.getKey().getCode(), e.getValue());
        }
        return attrMap;
    }

    public Set<String> getDisabledAttributeFqns()
    {
        return disabledAttributes.stream().map(AttributeFqn::toString).collect(Collectors.toSet());
    }

    public HashMap<String, EmbeddedApplication> getEmbeddedApplications()
    {
        return embeddedApplications;
    }

    public String getMetaClassTitle(ClassFqn fqn)
    {
        return metaClassTitles.get(fqn);
    }

    public HashMap<Pair<String, String>, Set<ClassFqn>> getMobileContentMetaClases()
    {
        return mobileContentMetaClases;
    }

    public String getProfileTitle(String code)
    {
        return profileTitles.get(code);
    }

    public HashMap<String, String> getStateTitles()
    {
        return stateTitles;
    }

    public String getTagTitle(String code)
    {
        return tagTitles.get(code);
    }

    public Map<MobileLoginType, String> getLoginTypeTitles()
    {
        return loginTypeTitles;
    }

    public HashMap<String, List<String>> getTransitionCodes()
    {
        return transitionCodes;
    }

    public boolean hasAttribute(Attribute attribute)
    {
        return attributes.containsKey(new AttributeFqn(attribute.getDeclaredMetaClass(), attribute.getCode()));
    }

    public boolean isAttributeEnabled(AttributeFqn attributeFqn)
    {
        return !disabledAttributes.contains(attributeFqn);
    }

    public boolean isContentUseMetaClass(String contentType, String contentUuid, ClassFqn fqn)
    {
        Pair<String, String> key = new Pair<String, String>(contentType, contentUuid);
        if (mobileContentMetaClases.containsKey(key))
        {
            return mobileContentMetaClases.get(key).contains(fqn);
        }
        return false;
    }

    public boolean isEditableAttribute(AttributeFqn attributeFqn)
    {
        if (!attributes.containsKey(attributeFqn))
        {
            return false;
        }
        return attributes.get(attributeFqn).isEditable();
    }

    public boolean isExistAttribute(AttributeFqn attributeFqn)
    {
        return attributes.containsKey(attributeFqn);
    }

    public boolean isExistMetaclass(ClassFqn metaclass)
    {
        return metaClassTitles.containsKey(metaclass);
    }

    public boolean isExistProfile(String code)
    {
        return profileTitles.containsKey(code);
    }

    public boolean isExistTag(String code)
    {
        return tagTitles.containsKey(code);
    }

    public boolean isMetaClassHasWorkflow(ClassFqn fqn)
    {
        return metaClassHasWorkflow.get(fqn) != null && metaClassHasWorkflow.get(fqn);
    }

    public boolean isSilentMode()
    {
        return silentMode;
    }

    public boolean isTagEnabled(String code)
    {
        return tagEnabled.containsKey(code) && tagEnabled.get(code);
    }

    /**
     * Проверяет, является ли хотя бы одна метка включенной.
     * Если меток нет, возвращает true
     *
     * @param tags коллекция с кодами меток
     * @return true - если коллекция пуста, либо хотя бы одна из меток включена, false - если нет
     */
    public boolean isAnyTagEnabled(final Collection<String> tags)
    {
        return tags.isEmpty()
               || tags.stream().anyMatch(this::isTagEnabled);
    }

    public void setAttributeEnabled(AttributeFqn attributeFqn, boolean enabled)
    {
        if (enabled)
        {
            disabledAttributes.remove(attributeFqn);
        }
        else
        {
            disabledAttributes.add(attributeFqn);
        }
    }

    public void setAttributeGroups(HashMap<ClassFqn, List<String>> attributeGroups)
    {
        this.attributeGroups = attributeGroups;
    }

    public void setEmbeddedApplications(
            HashMap<String, EmbeddedApplication> embeddedApplications)
    {
        this.embeddedApplications = embeddedApplications;
    }

    public void setIsHasMetaClassWorkflow(ClassFqn fqn, boolean isHasWorkflow)
    {
        metaClassHasWorkflow.put(fqn, isHasWorkflow);
    }

    public void setMetaClassTitle(ClassFqn fqn, String title)
    {
        metaClassTitles.put(fqn, title);
    }

    public void setMobileContentMetaClases(Pair<String, String> key, Set<ClassFqn> fqns)
    {
        mobileContentMetaClases.put(key, fqns);
    }

    public void setProfileTitle(String code, String title)
    {
        profileTitles.put(code, title);
    }

    public void setSilentMode(boolean silentMode)
    {
        this.silentMode = silentMode;
    }

    public void setTagEnabled(String code, boolean enabled)
    {
        tagEnabled.put(code, enabled);
    }

    public void setTagTitle(String code, String title)
    {
        tagTitles.put(code, title);
    }

    public void setLoginTypeTitle(MobileLoginType type, String title)
    {
        this.loginTypeTitles.put(type, title);
    }

    public void addTransitionCodes(String cardUuid, String fromCode, String toCode)
    {
        String transitionCode = fromCode + "-" + toCode;
        if (transitionCodes.containsKey(cardUuid))
        {
            List<String> listTransitionsForCard = transitionCodes.get(cardUuid);
            if (!listTransitionsForCard.contains(transitionCode))
            {
                listTransitionsForCard.add(transitionCode);
            }
        }
        else
        {
            List<String> newListTransitionsForCard = new ArrayList<>();
            newListTransitionsForCard.add(transitionCode);
            transitionCodes.put(cardUuid, newListTransitionsForCard);
        }
    }

    /**
     * Устанавливает список поддерживаемых версий мобильного API
     */
    public void setMobileVersions(Set<String> supportedMobileVersions)
    {
        mobileVersions = new HashSet<>(supportedMobileVersions);
    }

    /**
     * Возвращает true, если версия есть в списке поддерживаемых версий мобильного API, false - в противном случае
     *
     * @param version версия мобильного API, которая проверяется в списке поддерживаемых версий
     */
    public boolean isMobileVersionSupported(String version)
    {
        return mobileVersions.contains(version);
    }
}
