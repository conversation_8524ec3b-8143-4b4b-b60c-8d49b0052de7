package ru.naumen.mobile.metainfoadmin.client.cards.contents.propertieslist;

import static ru.naumen.core.shared.ui.toolbar.ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR;
import static ru.naumen.metainfo.shared.Constants.MOBILE_ACTION_LIST.Area.BOTTOM_ACTION_BLOCK;
import static ru.naumen.metainfo.shared.Constants.MOBILE_ACTION_LIST.Area.TOP_ACTION_BLOCK;
import static ru.naumen.metainfo.shared.Constants.MOBILE_ACTION_LIST.LINE_BREAK;
import static ru.naumen.metainfo.shared.Constants.SYSTEM_METACLASSES;
import static ru.naumen.metainfo.shared.Constants.UI.WINDOW_KEY;
import static ru.naumen.metainfo.shared.mobile.MobileContentUtil.getCases;
import static ru.naumen.metainfo.shared.ui.Constants.FIRE_USER_EVENT;
import static ru.naumen.metainfo.shared.ui.Tool.AppliedToType.CURRENT_OBJECT;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.Sets;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.IsWidget;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.attr.presentation.PresentationFactoryView;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.ToolPanelContentPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinjector.PropertyCreator;
import ru.naumen.core.shared.userevents.GetActionsForUserEventToolAction;
import ru.naumen.core.shared.userevents.GetActionsForUserEventToolResponse;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.MOBILE_ACTION_LIST.Area;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.PresentationType;
import ru.naumen.metainfo.shared.mobile.actions.ContentActionsRow;
import ru.naumen.metainfo.shared.mobile.actions.ContentObjectActions;
import ru.naumen.metainfo.shared.mobile.actions.ObjectActionHelper;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.ui.MobileUserEventTool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.common.content.AdminContentFactory;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.ExampleWidgetFactories;
import ru.naumen.metainfoadmin.client.dynadmin.presentation.examplewidget.ExampleWidgetFactory;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentPresenter;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;
import ru.naumen.mobile.metainfoadmin.client.cards.events.DeleteMobileContentEvent;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileSettingsCommandCode;
import ru.naumen.mobile.metainfoadmin.client.helper.MobileContentCardsValidator;

/**
 * Презентер контента "Параметры объекта" на карточке объекта мобильного приложения
 *
 * <AUTHOR>
 * @since 21 февр. 2019 г.
 */
public class MobilePropertiesListPresenter extends
        AbstractFlowContentPresenter<MobilePropertiesListContent, MobileObjectCardContext,
                MobilePropertiesListDisplayImpl>
{
    public static final String ATTRIBUTES_MAP = "ATTRIBUTES_MAP";
    private static final String GWT_ATTR_DEBUG_CAPTION = "title";
    private static final String GWT_ATTR_DEBUG_VALUE = "value";
    private static final String DEBUG_ID_PREFIX = "mobilePropertiesList";
    private final MetainfoUtils metainfoUtils;
    private Map<AttributeFqn, Attribute> attributesMap = new HashMap<>();
    private final List<PresenterRegistration> registerChildPresenters = new ArrayList<>();
    private final List<MobileAttribute> registeredAttributes = new ArrayList<>();
    private final ExampleWidgetFactories exampleWidgetFactories;
    private final String leftColumnStyle;
    private final String rightColumnStyle;
    private final String rowStyle;
    private final String rowWrapperWideStyle;
    private final PresentationFactories prsFactories;
    private final PropertyCreator propertyCreator;
    private AsyncCallback<MobilePropertiesListContent> refreshCallback; //NOSONAR
    private AsyncCallback<MobilePropertiesListContent> deleteContentCallback;
    private final AdminMetainfoServiceAsync metainfoService;
    private final MobileSettingsMessages mobileSettingsMessages;
    private final MobileContentCardsValidator contentCardsValidator;
    private final DispatchAsync dispatch;
    private final ObjectActionHelper actionHelper;
    protected final AdminContentFactory contentFactory;

    /** Индекс расположения строк с настроенными действиями в контенте на карточке объекта в Мобильном приложении */
    private int toolPanelBindIndex;

    @Inject
    @SuppressWarnings("java:S107")
    protected MobilePropertiesListPresenter(
            final MobilePropertiesListDisplayImpl display,
            final EventBus eventBus,
            final MetainfoUtils metainfoUtils,
            final PresentationFactories prsFactories,
            final PropertyCreator propertyCreator,
            final ExampleWidgetFactories exampleWidgetFactories,
            final AdminMetainfoServiceAsync metainfoService,
            final MobileSettingsMessages mobileSettingsMessages,
            final AdminWidgetResources widgetResources,
            final MobileSettingsService mobileSettingsService,
            final MobileContentCardsValidator contentCardsValidator,
            final DispatchAsync dispatch,
            final ObjectActionHelper actionHelper,
            final AdminContentFactory contentFactory)
    {
        super(display, eventBus, DEBUG_ID_PREFIX, widgetResources);
        this.metainfoUtils = metainfoUtils;
        leftColumnStyle = widgetResources.zapros().attrTitle();
        rightColumnStyle = widgetResources.zapros().attrValue();
        rowWrapperWideStyle = widgetResources.zapros().attrWide();
        rowStyle = widgetResources.propertyDescription().descriptionOnCard();
        this.prsFactories = prsFactories;
        this.propertyCreator = propertyCreator;
        this.exampleWidgetFactories = exampleWidgetFactories;
        this.metainfoService = metainfoService;
        this.mobileSettingsMessages = mobileSettingsMessages;
        this.dispatch = dispatch;
        this.actionHelper = actionHelper;
        this.contentFactory = contentFactory;
        this.mobileSettingsService = mobileSettingsService;
        this.contentCardsValidator = contentCardsValidator;
    }

    @Override
    public void init(MobilePropertiesListContent content, MobileObjectCardContext context)
    {
        super.init(content, context);
        DebugIdBuilder.ensureDebugId((Display)getDisplay(), DEBUG_ID_PREFIX, content.getCode());
        attributesMap = context.getContextProperty(ATTRIBUTES_MAP);
        opened = getOpenedDefaultValue();
        refreshCallback = new BasicCallback<MobilePropertiesListContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobilePropertiesListContent content)
            {
                MobilePropertiesListPresenter.this.content = content;
                refreshInterfaceInfo();
            }
        };
        deleteContentCallback = new BasicCallback<MobilePropertiesListContent>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MobilePropertiesListContent propertiesList)
            {
                context.getEventBus().fireEvent(new DeleteMobileContentEvent(propertiesList));
                refreshInterfaceInfo();
            }
        };
    }

    @Override
    public void refreshDisplay()
    {
        unbindContent();
        bindContent();
        updateDisplay();
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void addCommonCommand(List<BaseCommand<MobilePropertiesListContent, ?>> commands)
    {
        MobileCommandParam<MobileObjectCardContext, MobilePropertiesListContent, MobilePropertiesListContent> param =
                new MobileCommandParam<>(
                        context, content, refreshCallback);
        MobileCommandParam<MobileObjectCardContext, MobilePropertiesListContent, MobilePropertiesListContent> deleteParam = new MobileCommandParam<>(
                context, content, deleteContentCallback);

        commands.add((BaseCommand<MobilePropertiesListContent, MobilePropertiesListContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_EDIT, param));

        if (content.getAttributeGroup() != null)
        {
            commands.add((BaseCommand<MobilePropertiesListContent, MobilePropertiesListContent>)commandFactory
                    .create(MobileSettingsCommandCode.MOBILE_PROPERTIES_LIST_ATTRIBUTES_EDIT, param));
        }

        ClassFqn classFqn = context.getView().getCases().isEmpty()
                ? context.getView().getFqnOfClass()
                : context.getView().getCases().get(0);
        if (!SYSTEM_METACLASSES.contains(classFqn))
        {
            commands.add((BaseCommand<MobilePropertiesListContent, MobilePropertiesListContent>)commandFactory
                    .create(MobileSettingsCommandCode.MOBILE_EDIT_TOOL_PANEL, param));
        }

        commands.add((BaseCommand<MobilePropertiesListContent, MobilePropertiesListContent>)commandFactory
                .create(MobileSettingsCommandCode.MOBILE_CONTENT_DELETE, deleteParam));
    }

    @Override
    protected CommandParam<MobilePropertiesListContent, MobileViewBase> getEditViewParam()
    {
        return new MobileCommandParam<>(context, content, editViewCallback);
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        super.loadData(readyState);
        HashSet<ClassFqn> classFqns = context.getView().getCases().isEmpty()
                ? Sets.newHashSet(context.getView().getFqnOfClass())
                : Sets.newHashSet(context.getView().getCases());
        if (!SYSTEM_METACLASSES.contains(classFqns.iterator().next()))
        {
            dispatch.execute(
                    new GetActionsForUserEventToolAction(classFqns)
                            .setFormInformation(MOBILE_OBJECT_ACTIONS_BAR, CURRENT_OBJECT, WINDOW_KEY)
                            .anyMatch(true),
                    new BasicCallback<GetActionsForUserEventToolResponse>(readyState)
                    {
                        @Override
                        public void handleSuccess(GetActionsForUserEventToolResponse response)
                        {
                            context.setAvailableUserEvents(response.getEvents());
                            context.setAvailableTools(response.getAvailableForObjectTools());
                        }
                    });
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        bindContent();
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        unbindContent();
    }

    private void bindAttributeLongValue(MobileAttribute attr)
    {
        Attribute attribute = attributesMap.get(attr.getAttributeFqn());
        ExampleWidgetFactory<Object, IsWidget> factory = exampleWidgetFactories.getFactory(attribute
                .getViewPresentation().getCode());
        IsWidget exampleWidget = factory.createExampleWidget(new PresentationContext(attribute));
        DebugIdBuilder.ensureDebugId(exampleWidget.asWidget(), GWT_ATTR_DEBUG_VALUE, attr.getCode());
        VerticalPanel vp = new VerticalPanel();
        vp.setWidth("100%");
        vp.add(exampleWidget);
        exampleWidget.asWidget().addStyleName(widgetResources.mobileContent().valueWoTitle());
        insertRowWide(attr, vp);
    }

    private void bindAttributeUnderTitle(MobileAttribute attr)
    {
        Attribute attribute = attributesMap.get(attr.getAttributeFqn());
        PresentationContext presentationContext = new PresentationContext(attribute);
        Property<Object> property = createProperty(attribute, presentationContext);
        ExampleWidgetFactory<Object, IsWidget> factory = exampleWidgetFactories.getFactory(attribute
                .getViewPresentation().getCode());
        VerticalPanel vp = new VerticalPanel();
        vp.setWidth("100%");
        IsWidget captionWidget = property.getCaptionWidget();
        IsWidget exampleWidget = factory.createExampleWidget(presentationContext);
        DebugIdBuilder.ensureDebugId(captionWidget.asWidget(), GWT_ATTR_DEBUG_CAPTION, attr.getCode());
        DebugIdBuilder.ensureDebugId(exampleWidget.asWidget(), GWT_ATTR_DEBUG_VALUE, attr.getCode());
        vp.add(captionWidget);
        vp.add(exampleWidget);
        insertRowWide(attr, vp);
    }

    private void bindAttributeWithTitle(MobileAttribute attr)
    {
        Attribute attribute = attributesMap.get(attr.getAttributeFqn());
        PresentationContext presentationContext = new PresentationContext(attribute);
        Property<Object> property = createProperty(attribute, presentationContext);
        ExampleWidgetFactory<Object, IsWidget> factory = exampleWidgetFactories.getFactory(attribute
                .getViewPresentation().getCode());
        NauGrid grid = getDisplay().getGrid();
        int index = grid.insertRow(registeredAttributes.size());
        registeredAttributes.add(attr);
        IsWidget captionWidget = property.getCaptionWidget();
        IsWidget exampleWidget = factory.createExampleWidget(new PresentationContext(attribute));
        DebugIdBuilder.ensureDebugId(captionWidget.asWidget(), GWT_ATTR_DEBUG_CAPTION, attr.getCode());
        DebugIdBuilder.ensureDebugId(exampleWidget.asWidget(), GWT_ATTR_DEBUG_VALUE, attr.getCode());
        grid.setWidget(index, 0, captionWidget);
        grid.setWidget(index, 1, exampleWidget);
        grid.getCellFormatter().addStyleName(index, 0, leftColumnStyle);
        grid.getCellFormatter().addStyleName(index, 1, rightColumnStyle);
        grid.getRowFormatter().addStyleName(index, rowStyle);
    }

    private void bindAttributes()
    {
        registeredAttributes.clear();
        getDisplay().getGrid().resize(0, 2);
        if (content.getAttributeGroup() == null)
        {
            return;
        }
        content.getAttributes().forEach(this::bindAttribute);
    }

    private void bindAttribute(final MobileAttribute attribute)
    {
        if (!attributesMap.containsKey(attribute.getAttributeFqn()))
        {
            return;
        }
        final PresentationType type = attribute.getPresentation();
        if (PresentationType.UNDER_TITLE.equals(type))
        {
            bindAttributeUnderTitle(attribute);
            return;
        }
        if (PresentationType.LONG_VALUE.equals(type))
        {
            bindAttributeLongValue(attribute);
            return;
        }
        bindAttributeWithTitle(attribute);
    }

    private void bindContent()
    {
        bindTitle();

        final ContentObjectActions actions = content.getContentObjectActions();
        bindButtons(TOP_ACTION_BLOCK, actions.getTopActions());
        bindAttributes();
        bindButtons(BOTTOM_ACTION_BLOCK, actions.getBottomActions());
        validateContent();
    }

    private void bindTitle()
    {
        display.setCollapsible(content.isAllowCollapse());
        display.setCaption(metainfoUtils.getLocalizedValue(content.getCaption()));
        display.setOpened(opened);
        display.addCaptionStyle(widgetResources.mobileContent().contentTitle());

        metainfoService.getCommonParent(getCases(context.getView()), new BasicCallback<MetaClass>(getDisplay())
        {
            @Override
            protected void handleSuccess(final MetaClass commonClass)
            {
                final AttributeGroup attributeGroup = commonClass.getAttributeGroup(content.getAttributeGroup());
                final String attrTitle = attributeGroup == null ? "" : attributeGroup.getTitle();

                final StringBuilder infoText = new StringBuilder();
                infoText.append(mobileSettingsMessages.mobilePropertiesListInfo(attrTitle));
                if (!content.getVisibilityCondition().getElements().isEmpty())
                {
                    infoText.append(' ').append(messages.displayConditionsSet());
                }
                display.setInfo(infoText.toString());
            }
        });
    }

    private void bindButtons(final Area area, final List<ContentActionsRow> actionsRows)
    {
        //индекс сдвига элементов презентера для добавления панелей с действиями в верхний блок
        toolPanelBindIndex = 0;

        final List<MobileUserEventTool> row = new ArrayList<>();
        for (final MobileUserEventTool tool : actionHelper.transformToClientTools(TOP_ACTION_BLOCK, actionsRows))
        {
            if (!LINE_BREAK.equals(tool.getCode()))
            {
                enableButton(tool);
                row.add(tool);
                continue;
            }
            bindToolPanelPresenter(area, row);
            row.clear();
        }
        // добавляем последнюю строку, так как выходи из цикла после "continue"
        if (!row.isEmpty())
        {
            bindToolPanelPresenter(area, row);
            row.clear();
        }
    }

    private void bindToolPanelPresenter(final Area area, final List<MobileUserEventTool> tools)
    {
        //формируем строку для действий
        ToolBar toolBar = new ToolBar();
        tools.forEach(toolBar::addTool);
        // Добавляем строку с действиями в панель
        ToolPanel panel = new ToolPanel(content);
        panel.addToolBar(toolBar);

        final ToolPanelContentPresenter<UIContext> toolPanelPresenter = contentFactory.build(panel, getContext());
        toolPanelPresenter.ensureDebugId(area.getCode());
        toolPanelPresenter.getDisplay()
                .addStyleName(AdminWidgetResources.INSTANCE.mobileContent().mobileActionButtons());
        registerChildPresenters.add(registerChildPresenter(toolPanelPresenter));

        if (TOP_ACTION_BLOCK.equals(area))
        {
            // Добавляем презентер с действиями до указанного индекса
            getDisplay().getContainer().insert(toolPanelPresenter.getDisplay(), toolPanelBindIndex++);
            return;
        }
        // Добавляем презентер на последнюю позицию
        getDisplay().getContainer().add(toolPanelPresenter.getDisplay());
    }

    private void enableButton(final MobileUserEventTool eventTool)
    {
        if (eventTool.getArea() == null || !FIRE_USER_EVENT.equals(eventTool.getCode()))
        {
            eventTool.setEnabled(true);
            return;
        }
        final String eventUuid = eventTool.getEventUuid();
        if (null == eventUuid)
        {
            eventTool.setEnabled(false);
            return;
        }
        final boolean isEnabled = context.getAvailableUserEvents().stream()
                .anyMatch(event -> eventUuid.equals(event.getUuid()) && event.isEnabled());
        eventTool.setEnabled(isEnabled);
    }

    private Property<Object> createProperty(Attribute attribute, PresentationContext presentationContext)
    {
        PresentationFactoryView<?> prsViewFactory = prsFactories.getViewPresentationFactory(attribute
                .getViewPresentation().getCode());
        Property<Object> property = propertyCreator.create(attribute.getTitle(),
                prsViewFactory.createWidget(presentationContext));
        Set<String> disabledAttributes = context.getInterfaceInfo().getDisabledAttributeFqns();
        if (disabledAttributes.contains(attribute.getFqn().toString()))
        {
            property.getCaptionWidget().asWidget().addStyleName(widgetResources.all().warningItem());
        }
        return property;
    }

    private void insertRowWide(MobileAttribute attr, Widget propertyWidget)
    {
        NauGrid grid = getDisplay().getGrid();
        int index = grid.insertRow(registeredAttributes.size());
        registeredAttributes.add(attr);
        grid.getNauCellFormatter().setColSpan(index, 0, 2);
        grid.setWidget(index, 0, propertyWidget);
        grid.getCellFormatter().addStyleName(index, 0, rowWrapperWideStyle);
    }

    private void unbindContent()
    {
        registerChildPresenters.forEach(pr -> pr.getPresenter().getDisplay().destroy());
        registerChildPresenters.forEach(PresenterRegistration::unregister);
        registerChildPresenters.clear();
        registeredAttributes.clear();
        getDisplay().getPopup().clear();
    }

    @Override
    protected void validateContent()
    {
        super.validateContent();
        boolean hasMissingAttributes = getContent().getAttributes().stream()
                .anyMatch(attr -> !attributesMap.containsKey(attr.getAttributeFqn()));
        boolean isValid = contentCardsValidator.validate(content, context);
        display.setStyleName(widgetResources.mobileContent().hasError(), hasMissingAttributes || !isValid);
    }
}
