package ru.naumen.mobile.metainfoadmin.server.othersettings;

import static ru.naumen.mobile.Constants.BARCODE_SCANNER_ALLOWED_ATTR_TYPES;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;

import com.google.common.base.Predicates;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.dispatch.GetAttributesHelper;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.HasHidden;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.GetBarcodeScannerAttributesTreeAction;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.GetBarcodeScannerAttributesTreeResponse;

/**
 * <AUTHOR>
 * @since Aug 9, 2018
 */
@Component
public class GetBarcodeScannerAttributesTreeActionHandler extends
        TransactionalReadActionHandler<GetBarcodeScannerAttributesTreeAction, GetBarcodeScannerAttributesTreeResponse>
{
    @Inject
    private GetAttributesHelper attrHelper;
    @Inject
    private MetainfoServiceBean metainfo;
    @Inject
    private BarcodeScannerSettingsDtoService barcodeScannerService;

    @Override
    public GetBarcodeScannerAttributesTreeResponse executeInTransaction(GetBarcodeScannerAttributesTreeAction action,
            ExecutionContext context) throws DispatchException
    {
        List<MetaClass> baseClasses = Stream.concat(
                        getChildrenMetaClasses(Constants.AbstractBO.FQN)
                                .filter(Predicates.not(HasHidden.IS_HIDDEN)),
                        getChildrenMetaClasses(Constants.AbstractUserEntity.FQN))
                .sorted(CommonUtils.ITITLED_COMPARATOR)
                .toList();

        ArrayList<SimpleTreeDtObject> allTreeElements = new ArrayList<>();
        for (MetaClass metaClass : baseClasses)
        {
            SimpleTreeDtObject parent = barcodeScannerService.createMetaclassDto(metaClass);
            allTreeElements.add(parent);
            allTreeElements.addAll(getMetaClassAttrs(metaClass, parent));
        }

        return new GetBarcodeScannerAttributesTreeResponse(allTreeElements);
    }

    private Stream<MetaClass> getChildrenMetaClasses(ClassFqn parent)
    {
        return metainfo.getMetaClass(parent).getChildren().stream().map(metainfo::getMetaClass);
    }

    private List<SimpleTreeDtObject> getMetaClassAttrs(MetaClass metaClass, SimpleTreeDtObject parent)
    {
        return attrHelper.getAttributes(metaClass.getFqn(), Collections.emptyList())
                .stream()
                .filter(attr -> BARCODE_SCANNER_ALLOWED_ATTR_TYPES.contains(attr.getType().getCode()))
                .map(attr -> barcodeScannerService.createAttributeDto(metaClass, attr, parent))
                .toList();
    }
}
