package ru.naumen.mobile.metainfoadmin.shared.lists;

import java.util.ArrayList;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.mobile.metainfoadmin.shared.GetMobileContentDtoResponse;

/**
 * Действие редактирования списка объектов в мобильном приложении
 *
 * <AUTHOR>
 * @since 13 мая 2015 г.
 */
@AdminAction
public class EditMobileListAction extends AbstractMobileListAction<GetMobileContentDtoResponse>
{
    private String uuid;

    public EditMobileListAction()
    {
    }

    public EditMobileListAction(String uuid, String title, ClassFqn clazz, ArrayList<ClassFqn> cases,
            ArrayList<String> profiles, ArrayList<String> tags, ArrayList<AttrReference> attrChain,
            boolean allowPerformActions, String settingsSet)
    {
        super(title, clazz, cases, profiles, tags, attrChain, allowPerformActions, settingsSet);
        this.uuid = uuid;
    }

    public String getUuid()
    {
        return uuid;
    }
}
