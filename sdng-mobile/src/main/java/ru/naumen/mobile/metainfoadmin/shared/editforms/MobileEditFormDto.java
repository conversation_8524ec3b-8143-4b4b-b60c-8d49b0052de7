package ru.naumen.mobile.metainfoadmin.shared.editforms;

import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.mobile.metainfoadmin.shared.InterfaceInfo;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentDto;

/**
 * Объект переноса данных для формы редактирования объекта в мобильном приложении.
 * Помимо самой карточки содержит объект с информацией для отображения
 * ее в интерфейсе технолога.
 *
 * <AUTHOR>
 * @since 09.11.2016
 */
public class MobileEditFormDto extends MobileContentDto
{
    private static final long serialVersionUID = 6631533126595887184L;

    public MobileEditFormDto()
    {
    }

    public MobileEditFormDto(EditForm editForm, InterfaceInfo interfaceInfo)
    {
        super(editForm, interfaceInfo);
    }
}
