package ru.naumen.mobile.metainfoadmin.server.listeners;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.MobileContentUtil;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileRelObjectsListContent;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;

/**
 * Проверяет не используется ли метакласс в настройках мобильного приложения
 *
 * <AUTHOR>
 * @since 26 мая 2015 г.
 */
@Component
public class MobileSettingsDelMetaClassEventListener
        extends AbstractDeleteEventListener<BeforeMetaClassDeleteEvent, MetaClass>
{
    @Inject
    private SortAndFiltrationDelListentersHelper helper;
    @Inject
    private MetainfoUtils utils;

    @Override
    protected String getMessage(BeforeMetaClassDeleteEvent event)
    {
        MetaClass metaClass = (MetaClass)event.getSource();
        if (metaClass.getFqn().isClass())
        {
            return messages.getMessage("mobile.haveSettingsForClass");
        }
        return messages.getMessage("mobile.haveSettingsForCase");
    }

    @Override
    protected boolean handleAddForm(AddForm form, MetaClass metaClass, boolean hasMobileModule)
    {
        return handleDelMetaClassForMobileContent(form, metaClass, hasMobileModule);
    }

    @Override
    protected boolean handleCard(ObjectCard card, MetaClass metaClass, boolean hasMobileModule,
            List<String> cardErrors)
    {
        ClassFqn fqn = metaClass.getFqn();
        if (ObjectUtils.equals(card.getFqnOfClass(), fqn))
        {
            if (!hasMobileModule)
            {
                mobileSettingsService.deleteContent(card.getUuid());
            }
            return true;
        }

        boolean usesMetaClass = false;

        List<ClassFqn> cases = card.getCases();
        if (!CollectionUtils.isEmpty(cases) && cases.contains(fqn))
        {
            if (!hasMobileModule)
            {
                cases.remove(fqn);
                if (cases.isEmpty())
                {
                    //Если типов больше не осталось, для того чтобы
                    //настройки оставались валидными, вместо типов выставляем класс
                    card.setClazz(fqn.fqnOfClass());
                    card.setTransitions(new ArrayList<>());
                }
            }
            usesMetaClass = true;
        }

        List<String> contentsWithErrors = new ArrayList<>();
        for (AbstractMobileContent content : card.getAllContents())
        {
            if (content instanceof MobileListContentBase
                && handleContentList((MobileListContentBase)content, metaClass, hasMobileModule, card))
            {
                contentsWithErrors.add(utils.getLocalizedValue(content.getCaption()) + " (" + content.getCode() + ")");
            }
        }

        usesMetaClass |= findAttributes(card, metaClass, hasMobileModule);
        findAttributesInContents(card, metaClass, hasMobileModule, contentsWithErrors);

        if ((usesMetaClass || !contentsWithErrors.isEmpty()) && !hasMobileModule)
        {
            mobileSettingsService.saveContent(card);
        }

        if (!contentsWithErrors.isEmpty())
        {
            cardErrors.add(messages.getMessage("mobile.contentDetails",
                    ru.naumen.commons.shared.utils.StringUtilities.join(contentsWithErrors), card.getCode()));
        }

        return usesMetaClass;
    }

    @Override
    protected boolean handleEditForm(EditForm form, MetaClass metaClass, boolean hasMobileModule)
    {
        return handleDelMetaClassForMobileContent(form, metaClass, hasMobileModule);
    }

    @Override
    protected boolean handleList(MobileList list, MetaClass metaClass, boolean hasMobileModule)
    {
        ClassFqn fqn = metaClass.getFqn();
        if (ObjectUtils.equals(list.getFqnOfClass(), fqn))
        {
            if (!hasMobileModule)
            {
                mobileSettingsService.deleteContent(list.getUuid());
            }
            return true;
        }

        boolean usesMetaClass = false;
        List<ClassFqn> cases = list.getCases();
        if (!CollectionUtils.isEmpty(cases) && cases.contains(fqn))
        {
            if (!hasMobileModule)
            {
                cases.remove(fqn);
                if (cases.isEmpty())
                {
                    //Если типов больше не осталось, для того чтобы
                    //настройки оставались валидными, вместо типов выставляем класс
                    list.setClazz(fqn.fqnOfClass());
                }
            }
            usesMetaClass = true;
        }

        usesMetaClass |= findAttributes(list, metaClass, hasMobileModule);

        Set<AttributeFqn> declaredAttrFqns = getDeclaredAttrsFqns(metaClass);

        //Проверяем что удаляемые атрибуты не используются как атрибут связи
        if (list instanceof MobileRelObjectsList)
        {
            List<AttrReference> attrChain = ((MobileRelObjectsList)list).getAttrsChain();
            if (!CollectionUtils.isEmpty(attrChain))
            {
                for (AttrReference attrRef : attrChain)
                {
                    AttributeFqn attributeFqn = AttributeFqn.create(attrRef.getClassFqn(), attrRef.getAttrCode());
                    if (declaredAttrFqns.contains(attributeFqn))
                    {
                        if (!hasMobileModule) //NOPMD
                        {
                            list = (MobileList)list.clone();
                        }
                        usesMetaClass = true;
                    }
                }
            }
        }

        //Ищем использование удаляемых атрибутов в настройках сортировки и фильтрации списка
        for (AttributeFqn attrFqn : declaredAttrFqns)
        {
            if (usesMetaClass)
            {
                break;
            }
            Attribute attribute = metaClass.getAttribute(attrFqn.getCode());
            usesMetaClass |= helper.findAttrInSortOfFiltrationSettings(attribute, list);
        }

        if (usesMetaClass && !hasMobileModule)
        {
            mobileSettingsService.saveContent(list);
        }
        return usesMetaClass;
    }

    private boolean findAttributes(AbstractMobileView content, MetaClass metaClass, boolean hasMobileModule)
    {
        if (!metaClass.getFqn().isSameClass(content.getFqnOfClass()))
        {
            return false;
        }
        Collection<MobileAttribute> attributes = MobileContentUtil.getAllAttributes(content);
        if (CollectionUtils.isEmpty(attributes) && !(content instanceof CommonMobileView))
        {
            return false;
        }
        final Set<AttributeFqn> declaredAttrFqns = getDeclaredAttrsFqns(metaClass);
        if (CollectionUtils.isEmpty(declaredAttrFqns))
        {
            return false;
        }
        Collection<MobileAttribute> toDelete = attributes.stream()
                .filter(mobileAttribute -> declaredAttrFqns.contains(mobileAttribute.getAttributeFqn()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(toDelete))
        {
            if (!hasMobileModule)
            {
                attributes.removeAll(toDelete);
            }
            return true;
        }

        return false;
    }

    private void findAttributesInContents(AbstractMobileView content, MetaClass metaClass, boolean hasMobileModule,
            List<String> contentsWithErrors)
    {
        if (!metaClass.getFqn().isSameClass(content.getFqnOfClass()))
        {
            return;
        }

        final Set<AttributeFqn> declaredAttrFqns = getDeclaredAttrsFqns(metaClass);
        if (CollectionUtils.isEmpty(declaredAttrFqns) || !(content instanceof MobileViewBase))
        {
            return;
        }

        for (AbstractMobileContent flowContent : ((MobileViewBase)content).getContents())
        {
            if (flowContent instanceof MobilePropertiesListContent)
            {
                Collection<MobileAttribute> attributes = ((MobilePropertiesListContent)flowContent).getAttributes();
                Collection<MobileAttribute> toDelete = attributes.stream()
                        .filter(mobileAttribute -> declaredAttrFqns.contains(mobileAttribute.getAttributeFqn()))
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(toDelete))
                {
                    if (!hasMobileModule)
                    {
                        attributes.removeAll(toDelete);
                    }
                    contentsWithErrors
                            .add(utils.getLocalizedValue(flowContent.getCaption()) + " (" + flowContent.getCode()
                                 + ")");
                }
            }
        }
    }

    /**
     * Метод возврящает коды всех атрибутов которые были объявлены в метаклассе и
     * будут удалены вместе с ним.
     * @param metaClass
     * @return
     */
    private Set<AttributeFqn> getDeclaredAttrsFqns(final MetaClass metaClass)
    {
        return metaClass.getAttributeFqns().stream()
                .filter(attrFqn -> ObjectUtils.equals(attrFqn.getClassFqn(), metaClass.getFqn()))
                .collect(Collectors.toSet());
    }

    private boolean handleContentList(MobileListContentBase list, MetaClass metaClass, boolean hasMobileModule,
            ObjectCard card)
    {
        ClassFqn fqn = metaClass.getFqn();
        if (ObjectUtils.equals(list.getFqnOfClass(), fqn))
        {
            if (!hasMobileModule)
            {
                card.removeChild(list);
            }
            return true;
        }

        boolean usesMetaClass = false;
        List<ClassFqn> cases = list.getCases();
        if (!CollectionUtils.isEmpty(cases) && cases.contains(fqn))
        {
            if (!hasMobileModule)
            {
                cases.remove(fqn);
                if (cases.isEmpty())
                {
                    //Если типов больше не осталось, для того чтобы
                    //настройки оставались валидными, вместо типов выставляем класс
                    list.setClazz(fqn.fqnOfClass());
                }
            }
            usesMetaClass = true;
        }

        Set<AttributeFqn> declaredAttrFqns = getDeclaredAttrsFqns(metaClass);

        //Проверяем что удаляемые атрибуты не используются как атрибут связи
        if (list instanceof MobileRelObjectsListContent)
        {
            List<AttrReference> attrChain = ((MobileRelObjectsListContent)list).getAttrsChain();
            if (!CollectionUtils.isEmpty(attrChain))
            {
                for (AttrReference attrRef : attrChain)
                {
                    AttributeFqn attributeFqn = AttributeFqn.create(attrRef.getClassFqn(), attrRef.getAttrCode());
                    if (declaredAttrFqns.contains(attributeFqn))
                    {
                        if (!hasMobileModule) //NOPMD
                        {
                            list = (MobileListContentBase)list.clone();
                        }
                        usesMetaClass = true;
                    }
                }
            }
        }

        return usesMetaClass;
    }

    /**
     * Валидация возмложности удаления метакласса
     * @param content проверяемый контент МК
     * @param metaClass удаляемый метакласс
     * @param hasMobileModule наличие модуля
     * @return
     */
    private boolean handleDelMetaClassForMobileContent(CommonMobileView content, MetaClass metaClass,
            boolean hasMobileModule)
    {
        ClassFqn fqn = metaClass.getFqn();
        if (ObjectUtils.equals(content.getFqnOfClass(), fqn))
        {
            if (!hasMobileModule)
            {
                mobileSettingsService.deleteContent(content.getUuid());
            }
            return true;
        }

        boolean usesMetaClass = false;

        List<ClassFqn> cases = content.getCases();
        if (!CollectionUtils.isEmpty(cases) && cases.contains(fqn))
        {
            if (!hasMobileModule)
            {
                cases.remove(fqn);
                if (cases.isEmpty())
                {
                    content.setClazz(fqn.fqnOfClass());
                }
            }
            usesMetaClass = true;
        }

        usesMetaClass |= findAttributes(content, metaClass, hasMobileModule);

        if (usesMetaClass && !hasMobileModule)
        {
            mobileSettingsService.saveContent(content);
        }
        return usesMetaClass;
    }

    @Override
    protected boolean handleNavigationElement(MobileEmbeddedApplicationMenuItemValue element, MetaClass metaclass,
            boolean hasMobileModule)
    {
        return metaclass.getFqn().toString().equals(element.getLinkObjectCase());
    }
}
