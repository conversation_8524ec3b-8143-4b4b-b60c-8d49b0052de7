package ru.naumen.mobile.metainfoadmin.server.listeners;

import static ru.naumen.metainfo.server.mobile.MobileNavigationMenuItemUtils.getNavigationEmbeddedApplicationItems;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;

import java.util.ArrayList;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.IHasI18nTitle.HasI18nTitleComparator;
import ru.naumen.metainfo.server.AbstractCancelMessage;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.MobileContentDescriptionBuilder;

/**
 * Абстрактная реализация listenter'а события удаления объекта метаинформации.
 * Служит для поддержания ссылочной целостности.
 * Предоставляет для переопределения методы обработки карточек и списков объектов
 * мобильного приложения.
 *
 * <AUTHOR>
 * @since 26 мая 2015 г.
 * @param <T> - тип события
 * @param <O> - тип удаляемого объекта
 */
public abstract class AbstractDeleteEventListener<T extends AbstractCancelMessage, O> implements ApplicationListener<T>
{
    @Inject
    protected MobileSettingsService mobileSettingsService;
    @Inject
    protected MessageFacade messages;
    @Inject
    private ModulesService modulesService;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private MobileContentDescriptionBuilder mobileContentDescriptionBuilder;

    @Override
    @SuppressWarnings("unchecked")
    public void onApplicationEvent(T event)
    {
        final boolean hasMobileModule = modulesService.isInstalled(Modules.MOBILE_API);

        final O object = (O)event.getSource();
        MobileSettings settings = mobileSettingsService.getSettingsOrEmpty();

        //Используем CopyOnWriteArrayList для того чтобы избежать ConcurrentModificationException
        //@formatter:off
        List<? extends CommonMobileView> lists = settings.getLists().stream()
                .filter(input -> handleList(input, object, hasMobileModule))
                .sorted(new HasI18nTitleComparator(metainfoUtils))
                .toList();
        
        List<String> cardErrors = new ArrayList<>();
        List<? extends AbstractMobileView> cards = settings.getObjectCards().stream()
                .filter(card -> handleCard(card, object, hasMobileModule, cardErrors))
                .toList();
        String cardErrorsDetails = "";
        if(!cardErrors.isEmpty())
        {
            cardErrorsDetails = "\n" + messages.getMessage("mobile.deleteContents.contentCards",
                    StringUtilities.join(cardErrors, "; "));
        }

        List<? extends CommonMobileView> editForms = settings.getEditForms().stream()
                .filter(form -> handleEditForm(form, object, hasMobileModule))
                .toList();

        List<? extends CommonMobileView> addForms = settings.getAllAddForms().stream()
                .filter(form -> handleAddForm(form, object, hasMobileModule))
                .sorted(new HasI18nTitleComparator(metainfoUtils))
                .toList();

        List<MobileMenuItemValue> navigationElements =
                getNavigationEmbeddedApplicationItems(settings.getNavigationSettings()).stream()
                        .filter(element -> handleNavigationElement(element, object, hasMobileModule))
                        .collect(Collectors.toList());
        //@formatter:on

        if (!hasMobileModule || !isCancelling()
            || cards.isEmpty() && lists.isEmpty() && editForms.isEmpty() && addForms.isEmpty()
               && cardErrorsDetails.isEmpty() && navigationElements.isEmpty())
        {
            return;
        }

        event.cancel();
        //@formatter:off
        String message = getMessage(event)
                + getMobileContentsDeleteMessage(lists, "mobile.deleteContents.lists")
                + getMobileContentsDeleteMessage(cards, "mobile.deleteContents.cards")
                + cardErrorsDetails 
                + getMobileContentsDeleteMessage(editForms, "mobile.deleteContents.editForms")
                + getMobileContentsDeleteMessage(addForms, "mobile.deleteContents.addForms")
                + getMobileNavigationMenuItemUsageMessage(navigationElements);
        //@formatter:on

        event.addMessage(message);
    }

    private String getMobileContentsDeleteMessage(List<? extends AbstractMobileView> contents, String titleResId)
    {
        List<String> descriptions = contents.stream()
                .map(mobileContentDescriptionBuilder::buildMobileContentDescription)
                .toList();
        return descriptions.isEmpty() ?
                "" :
                "\n" + messages.getMessage(titleResId, StringUtilities.join(descriptions, ", "));
    }

    /**
     * Подготавливает текстовое сообщение о местах использования ВП в навигационном меню МК, возвращаемое клиенту
     * @param mobileNavigationUsagePlaces места использования ВП в навигационном меню МК
     * @return текстовое сообщение
     */
    private String getMobileNavigationMenuItemUsageMessage(
            final List<? extends MobileMenuItemValue> mobileNavigationUsagePlaces)
    {
        if (mobileNavigationUsagePlaces.isEmpty())
        {
            return "";
        }
        final String usagePlaces = mobileNavigationUsagePlaces.stream()
                .map(place -> String.format("'%s'(%s)", metainfoUtils.getLocalizedValue(place.getTitle()),
                        place.getCode()))
                .collect(Collectors.joining(", "));

        return "\n" + messages.getMessage("metainfoValidation.applicationUsedInNavigationMenuMKReason", usagePlaces);
    }

    /**
     * Обработать элемент навигационного меню
     * @param element - элемент навигационного меню
     * @param metaclass - удаляемый объект
     * @param hasMobileModule - true если есть лицензия на мобильное приложение
     * @return true если в элементе навигационного меню используется удаляемый объект
     */
    protected boolean handleNavigationElement(final MobileEmbeddedApplicationMenuItemValue element, final O metaclass,
            final boolean hasMobileModule)
    {
        return false;
    }

    /**
     * Получить сообщение об ошибке
     */
    abstract protected String getMessage(T event);

    /**
     * Обработать форму добавления объекта
     *
     * @param form - форма добавления
     * @param object - удаляемый объект
     * @param hasMobileModule - true если есть лицензия на мобильное приложение
     * @return true если в настройках карточки используется удаляемый объект
     */
    protected boolean handleAddForm(AddForm form, O object, boolean hasMobileModule)
    {
        return false;
    }

    /**
     * Обработать карточку объекта
     *
     * @param card - карточка
     * @param object - удаляемый объект
     * @param hasMobileModule - true если есть лицензия на мобильное приложение
     * @return true если в настройках карточки используется удаляемый объект
     */
    protected boolean handleCard(ObjectCard card, O object, boolean hasMobileModule, List<String> contentDetails)
    {
        return false;
    }

    /**
     * Обработать форму редактирования объекта
     *
     * @param form - форма редактирования
     * @param object - удаляемый объект
     * @param hasMobileModule - true если есть лицензия на мобильное приложение
     * @return true если в настройках карточки используется удаляемый объект
     */
    protected boolean handleEditForm(EditForm form, O object, boolean hasMobileModule)
    {
        return false;
    }

    /**
     * Обработать список объектов
     *
     * @param list - список объектов
     * @param object - удаляемый объект
     * @param hasMobileModule - true если есть лицензия на мобильное приложение
     * @return true если в настройках списка используется удаляемый объект
     */
    protected boolean handleList(MobileList list, O object, boolean hasMobileModule)
    {
        return false;
    }

    /**
     * Метод возвращает true в случае если при обнаружении использования
     * удаляемого объекта в настройках мобильного приложения процесс удаления
     * должен быть пререван
     */
    protected boolean isCancelling()
    {
        return true;
    }
}
