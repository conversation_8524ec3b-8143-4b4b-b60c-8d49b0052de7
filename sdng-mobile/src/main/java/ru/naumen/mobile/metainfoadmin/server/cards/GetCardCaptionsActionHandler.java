package ru.naumen.mobile.metainfoadmin.server.cards;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.snapshot.SnapshotContext;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Association;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.PushMobile;
import ru.naumen.core.shared.Constants.ServiceCall;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.mobile.metainfoadmin.shared.GetCardCaptionsResponse;
import ru.naumen.mobile.metainfoadmin.shared.cards.GetCardCaptionsAction;

@Component
public class GetCardCaptionsActionHandler
        extends TransactionalActionHandler<GetCardCaptionsAction, GetCardCaptionsResponse>
{
    private final static ImmutableSet<String> SYSTEM_COMPUTABLE_STRING_ATTR = ImmutableSet.of(
            //@formatter:off
            Employee.PHONES_INDEX, 
            Employee.PASSWORD,
            AbstractBO.UUID, 
            ServiceCall.WF_PROFILE_CODE, 
            Association.CLIENT_LINK_NAME,
            PushMobile.LINK,
            PushMobile.EVENT_ACTION_UUID,
            PushMobile.TO);
            //@formatter:on

    public static boolean applyFilter(Attribute attribute)
    {
        String code = attribute.getCode();
        return !SYSTEM_COMPUTABLE_STRING_ATTR.contains(code) && !attribute.isComputable()
               && attribute.getType().getCode().equals(StringAttributeType.CODE);
    }

    @Inject
    private MetainfoService metainfoService;
    @Inject
    private MetainfoUtils metainfoUtils;
    @Inject
    private SnapshotService snapshotService;

    @Override
    public GetCardCaptionsResponse executeInTransaction(GetCardCaptionsAction action, ExecutionContext context)
            throws DispatchException
    {
        ClassFqn fqnOfClass = action.getClazz();
        Collection<ClassFqn> cases = action.getCases();
        GetCardCaptionsResponse result = new GetCardCaptionsResponse();
        if (fqnOfClass == null)
        {
            return result;
        }
        List<ClassFqn> metaclasses = Lists.newArrayList(cases);
        MetaClass clazz = metainfoService.getMetaClass(fqnOfClass);
        List<MetaClass> casesList = metainfoService.getMetaClasses(metaclasses);

        Set<Attribute> attributes = Sets.newHashSet(clazz.getAttributes().stream()
                .filter(GetCardCaptionsActionHandler::applyFilter).collect(Collectors.toSet()));
        Set<String> classAttrsCodes = attributes.stream().map(Attribute::getCode)
                .collect(Collectors.toSet());
        Set<Attribute> caseAttrIntersection = new HashSet<>();
        for (MetaClass metaClass : casesList)
        {
            Set<Attribute> caseAttributes = Sets.newHashSet(metaClass.getAttributes().stream()
                    .filter(GetCardCaptionsActionHandler::applyFilter)
                    .collect(Collectors.toSet())
            );
            caseAttributes = caseAttributes.stream()
                    .filter(attr -> !classAttrsCodes.contains(attr.getCode()))
                    .collect(Collectors.toSet());
            caseAttrIntersection.addAll(caseAttributes);
        }
        attributes.addAll(caseAttrIntersection);
        metainfoUtils.sort(Lists.newArrayList(attributes));
        SnapshotContext ctx = new SnapshotContext();
        result.setCaptionAttributes(
                snapshotService.processCollection(Lists.newArrayList(attributes), Attribute.class, ctx));
        MetaClass metaClass = metainfoService.getMetaClass(fqnOfClass);
        if (PushMobile.FQN.equals(metaClass.getFqn()))
        {
            result.setTitleAttrName(metaClass.getAttribute(Constants.PushMobile.TITLE_SYS).getTitle());
        }
        else
        {
            result.setTitleAttrName(metaClass.getAttribute(AbstractBO.TITLE).getTitle());
        }
        return result;
    }

}
