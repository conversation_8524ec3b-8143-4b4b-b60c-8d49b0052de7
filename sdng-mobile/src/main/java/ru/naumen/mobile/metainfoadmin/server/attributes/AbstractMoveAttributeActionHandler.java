package ru.naumen.mobile.metainfoadmin.server.attributes;

import java.util.List;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.attributes.AbstractMoveMobileAttributeAction;

/**
 *
 * <AUTHOR>
 * @since 22 мая 2015 г.
 * @param <T>
 */
public abstract class AbstractMoveAttributeActionHandler<T extends AbstractMoveMobileAttributeAction> extends
        TransactionalActionHandler<T, EmptyResult>
{
    @Inject
    protected MobileSettingsService mobileSettingsService;

    @Override
    public EmptyResult executeInTransaction(T action, ExecutionContext context) throws DispatchException
    {
        AbstractMobileView content = getContent(action);
        int indx = indexOf(action.getAttrUuid(), (CommonMobileView)content);
        List<MobileAttribute> attributes = ((CommonMobileView)content).getAttributes();
        attributes.add(indx + action.getDirection(), attributes.remove(indx));
        saveContent((CommonMobileView)content);
        return new EmptyResult();
    }

    abstract protected AbstractMobileView getContent(T action);

    abstract protected void saveContent(CommonMobileView content);

    private int indexOf(String attrUuid, CommonMobileView content)
    {
        List<MobileAttribute> attrbutes = content.getAttributes();
        if (CollectionUtils.isEmpty(attrbutes))
        {
            return -1;
        }

        return attrbutes.indexOf(MobileAttribute.withUuid(attrUuid));
    }
}
