package ru.naumen.mobile.metainfoadmin.shared.lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.sec.shared.actions.AdminAction;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.mobile.metainfoadmin.shared.ContentUuidResponse;

/**
 * Действие добавления списка объектов в мобильное приложение
 *
 * <AUTHOR>
 * @since 24 апр. 2015 г.
 */
@AdminAction
public class AddMobileListAction extends AbstractMobileListAction<ContentUuidResponse>
{
    private String code;

    private ListFilter listFilter;

    private ListSort listSort;

    private List<MobileAttribute> listAttributes;

    private String originalListTitle;

    public AddMobileListAction()
    {
    }

    @SuppressWarnings("java:S107")
    public AddMobileListAction(String code, String title, ClassFqn clazz, List<ClassFqn> cases,
            List<String> profiles, List<String> tags, List<AttrReference> attrChain,
            boolean allowActionsWithObjects, String settingsSet) //NOSONAR
    {
        super(title, clazz, cases, profiles, tags, attrChain, allowActionsWithObjects, settingsSet);
        this.code = code;
        listAttributes = Collections.emptyList();
    }

    public AddMobileListAction(String code, String title, MobileList content, List<AttrReference> attrChain,
            String originalListTitle, boolean allowActionsWithObjects, String settingsSet)
    {
        this(code, title, content.getFqnOfClass(), new ArrayList<>(content.getCases()), new ArrayList<>(content
                .getProfiles()), new ArrayList<>(content.getTags()), attrChain, allowActionsWithObjects, settingsSet);
        this.listFilter = content.getListFilter();
        this.listSort = content.getSortForClient();
        this.listAttributes = content.getAttributes();
        this.originalListTitle = originalListTitle;
    }

    public String getCode()
    {
        return code;
    }

    public List<MobileAttribute> getListAttributes()
    {
        return listAttributes;
    }

    @Nullable
    public ListFilter getListFilter()
    {
        return listFilter;
    }

    @Nullable
    public ListSort getListSort()
    {
        return listSort;
    }

    @Nullable
    public String getOriginalListTitle()
    {
        return originalListTitle;
    }

    public void setOriginalListTitle(String originalListTitle)
    {
        this.originalListTitle = originalListTitle;
    }
}
