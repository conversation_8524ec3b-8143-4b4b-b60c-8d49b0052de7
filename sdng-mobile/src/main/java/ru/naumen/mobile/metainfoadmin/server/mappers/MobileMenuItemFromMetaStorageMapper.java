package ru.naumen.mobile.metainfoadmin.server.mappers;

import static ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType.embeddedApplication;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileAddFormLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileEmbeddedApplicationMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileListLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileReference;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileAddFormLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileListLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileReferenceValue;

/**
 * Преобразователь значения элемента навигационного меню при его получении из метаинформации.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class MobileMenuItemFromMetaStorageMapper extends AbstractMapper<MobileMenuItemValue, MobileMenuItem>
{
    public static final MappingService.ObjectCreator<MobileMenuItemValue, MobileMenuItem> MOBILE_MENU_ITEM_FROM_CREATOR =
            from -> embeddedApplication.equals(from.getType())
                    ? new MobileEmbeddedApplicationMenuItem()
                    : new MobileMenuItem();

    private static Object convertValueFromMetaStorage(MobileMenuItemType type, Object value)
    {
        switch (type)
        {
            case reference:
            {
                final MobileReferenceValue referenceValue = (MobileReferenceValue)value;
                return new MobileReference(referenceValue.getObjectCardCode(), referenceValue.getObjectCardCode());
            }
            case listLink:
                final MobileListLinkValue listLinkValue = (MobileListLinkValue)value;
                return new MobileListLink(listLinkValue.getListCardCode(), listLinkValue.getListCardCode());
            case addFormLink:
                final MobileAddFormLinkValue addFormLinkValue = (MobileAddFormLinkValue)value;
                return new MobileAddFormLink(addFormLinkValue.getAddFormCode(), addFormLinkValue.getAddFormCode());
            default:
                return value;
        }
    }

    private final MappingService mappingService;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public MobileMenuItemFromMetaStorageMapper(MappingService mappingService,
            AdminPermissionCheckService adminPermissionCheckService)
    {
        super(MobileMenuItemValue.class, MobileMenuItem.class);
        this.mappingService = mappingService;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public void transform(MobileMenuItemValue from, MobileMenuItem to, DtoProperties properties)
    {
        to.setCode(from.getCode());
        to.getTitle().clear();
        ObjectUtils.cloneCollection(from.getTitle(), to.getTitle());
        to.setType(from.getType());
        to.setUUID(from.getUUID());
        to.setValue(convertValueFromMetaStorage(from.getType(), from.getValue()));
        if (from.getChildren() != null)
        {
            to.getChildren().addAll(mappingService.transform(from.getChildren(), MOBILE_MENU_ITEM_FROM_CREATOR));
        }
        if (embeddedApplication.equals(from.getType()))
        {
            transform((MobileEmbeddedApplicationMenuItemValue)from, (MobileEmbeddedApplicationMenuItem)to);
        }
        to.setTags(from.getTags());
        to.setSettingsSet(from.getSettingsSet());
        to.setPermissions(adminPermissionCheckService.getPermissions(from));
    }

    private static void transform(MobileEmbeddedApplicationMenuItemValue from,
            MobileEmbeddedApplicationMenuItem to)
    {
        to.setContentCode(from.getContentCode());
        to.setEmbeddedApplicationCode(from.getEmbeddedApplicationCode());
        to.setLinkObject(from.getLinkObject());
        to.setLinkObjectCase(from.getLinkObjectCase());
        to.setLinkObjectAttrs(from.getLinkObjectAttrs());
        to.setProfiles(from.getProfiles());
        to.setSettingsSet(from.getSettingsSet());
    }
}
