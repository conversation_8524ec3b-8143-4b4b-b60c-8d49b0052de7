package ru.naumen.mobile.metainfoadmin.client.cards.contents.listgroup;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;

/**
 * Вспомогательный класс для редактирования контента типа "Агрегатор списков", инициированного действием
 * добавления/редактирования списочного контента с вкладыванием его в этот агрегатор.
 *
 * <AUTHOR>
 * @since 12.07.2021
 */
@Singleton
public class EditGroupContentWithListContentHelper
{
    private final MobileSettingsService mobileSettingsService;

    @Inject
    public EditGroupContentWithListContentHelper(MobileSettingsService mobileSettingsService)
    {
        this.mobileSettingsService = mobileSettingsService;
    }

    /** Вызывает действие редактирование группы списков при вкладывании списочного контента в эту группу во время
     * добавления или редактирования
     * @param groupContext - контекст добавления/редактирования
     * @param callback - колбэк, который должен быть вызван после успешного выполнения
     *                  операции редактирования (передаётся их вызывающего действия)
     */
    public void editGroupContent(MobileObjectCardContext groupContext, AsyncCallback<Void> callback)
    {
        String groupContentCode = groupContext.getGroupContentCode();
        AbstractMobileContent editedContent = groupContext.getContentIntoGroup();
        if (StringUtilities.isEmpty(groupContentCode) || !(editedContent instanceof MobileListContentBase))
        {
            callback.onSuccess(null);
        }
        groupContext.getView().getContents()
                .stream()
                .filter(cnt -> cnt instanceof MobileListsGroupContent)
                .filter(cnt -> cnt.getCode().equals(groupContentCode))
                .findAny()
                .ifPresent(groupContent ->
                {
                    ((MobileListsGroupContent)groupContent).getListContents().add(editedContent);
                    mobileSettingsService.editMobileContent(groupContext.getView().getUuid(), groupContent,
                            new BasicCallback<AbstractMobileContent>()
                            {
                                @Override
                                protected void handleSuccess(AbstractMobileContent mobileContent)
                                {
                                    callback.onSuccess(null);
                                }
                            });
                });
    }
}