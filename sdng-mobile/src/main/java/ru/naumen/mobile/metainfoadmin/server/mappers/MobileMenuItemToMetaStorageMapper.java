package ru.naumen.mobile.metainfoadmin.server.mappers;

import static ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType.embeddedApplication;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileAddFormLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileEmbeddedApplicationMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileListLink;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileReference;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileAddFormLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileListLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileReferenceValue;

/**
 * Преобразователь значения элемента навигационного меню при сохранении в метаинформацию.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class MobileMenuItemToMetaStorageMapper extends AbstractMapper<MobileMenuItem, MobileMenuItemValue>
{
    public static final MappingService.ObjectCreator<MobileMenuItem, MobileMenuItemValue> MOBILE_MENU_ITEM_TO_CREATOR =
            from -> embeddedApplication.equals(from.getType())
                    ? new MobileEmbeddedApplicationMenuItemValue()
                    : new MobileMenuItemValue();

    private static Object convertValueToMetaStorage(MobileMenuItemType type, Object value)
    {
        switch (type)
        {
            case reference:
                final MobileReference reference = (MobileReference)value;
                return new MobileReferenceValue(reference.getCode());
            case listLink:
                final MobileListLink listLink = (MobileListLink)value;
                return new MobileListLinkValue(listLink.getCode());
            case addFormLink:
                final MobileAddFormLink addFormLink = (MobileAddFormLink)value;
                return new MobileAddFormLinkValue(addFormLink.getCode());
            default:
                return value;
        }
    }

    @Inject
    private MappingService mappingService;

    public MobileMenuItemToMetaStorageMapper()
    {
        super(MobileMenuItem.class, MobileMenuItemValue.class);
    }

    @Override
    public void transform(MobileMenuItem from, MobileMenuItemValue to, DtoProperties properties)
    {
        to.setCode(from.getCode());
        to.setType(from.getType());
        to.setValue(convertValueToMetaStorage(from.getType(), from.getValue()));
        to.setChildren(mappingService.transform(from.getChildren(), MOBILE_MENU_ITEM_TO_CREATOR));
        to.getTitle().clear();
        ObjectUtils.cloneCollection(from.getTitle(), to.getTitle());
        if (embeddedApplication.equals(from.getType()))
        {
            transform((MobileEmbeddedApplicationMenuItem)from, (MobileEmbeddedApplicationMenuItemValue)to);
        }
        to.setTags(from.getTags());
        to.setSettingsSet(from.getSettingsSet());
    }

    private static void transform(MobileEmbeddedApplicationMenuItem from, MobileEmbeddedApplicationMenuItemValue to)
    {
        to.setContentCode(from.getContentCode());
        to.setEmbeddedApplicationCode(from.getEmbeddedApplicationCode());
        to.setLinkObject(from.getLinkObject());
        to.setLinkObjectCase(from.getLinkObjectCase());
        to.setLinkObjectAttrs(from.getLinkObjectAttrs());
        to.setProfiles(from.getProfiles());
        to.setSettingsSet(from.getSettingsSet());
    }
}
