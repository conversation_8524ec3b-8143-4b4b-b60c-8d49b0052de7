package ru.naumen.mobile.metainfoadmin.shared;

import java.io.Serializable;
import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;

/**
 * Объект переноса данных для контента мобильного приложения.
 *
 * <AUTHOR>
 * @since 22.02.2017
 */
public abstract class MobileContentDto implements IsSerializable, Serializable
{
    private static final long serialVersionUID = 0L;

    private InterfaceInfo interfaceInfo;
    private AbstractMobileView content;
    private List<PermissionType> permissions;

    public MobileContentDto()
    {
    }

    public MobileContentDto(AbstractMobileView content, InterfaceInfo interfaceInfo)
    {
        this.content = content;
        this.interfaceInfo = interfaceInfo;
    }

    public InterfaceInfo getInterfaceInfo()
    {
        return interfaceInfo;
    }

    public AbstractMobileView getMobileContent()
    {
        return content;
    }

    public List<PermissionType> getPermissions()
    {
        return permissions;
    }

    public void setPermissions(List<PermissionType> permissions)
    {
        this.permissions = permissions;
    }
}
