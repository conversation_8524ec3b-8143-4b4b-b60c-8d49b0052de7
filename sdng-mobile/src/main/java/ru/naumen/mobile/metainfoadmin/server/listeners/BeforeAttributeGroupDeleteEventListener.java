package ru.naumen.mobile.metainfoadmin.server.listeners;

import static ru.naumen.metainfo.shared.mobile.MobileContentUtil.getCases;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

import jakarta.inject.Inject;

import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import io.jsonwebtoken.lang.Collections;
import ru.naumen.common.shared.utils.MobilePropertyListUsageAttrGroup;
import ru.naumen.common.shared.utils.UsageAttrGroup;
import ru.naumen.core.server.common.attribute.group.AttributeGroupService;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.metainfo.server.BeforeAttributeGroupDeleteEvent;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.mobile.MobileViewBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;

/**
 * Обработчик события {@link BeforeAttributeGroupDeleteEvent} не позволяющий удалить группы атрибутов связанные с
 * контентами "Параметры объекта" на карточках мобильного приложения
 * <AUTHOR>
 * @since 21 мар. 2019 г.
 */
@Component
public class BeforeAttributeGroupDeleteEventListener
{

    private final MetainfoService metainfoService;
    private final AttributeGroupService attributeGroupService;
    private final MessageFacade messages;
    private final MetainfoUtils metainfoUtils;
    private final ModulesService modulesService;

    @Inject
    public BeforeAttributeGroupDeleteEventListener(MetainfoService metainfoService,
            AttributeGroupService attributeGroupService, MessageFacade messages, MetainfoUtils metainfoUtils,
            ModulesService modulesService)
    {
        this.metainfoService = metainfoService;
        this.attributeGroupService = attributeGroupService;
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
        this.modulesService = modulesService;
    }

    @EventListener
    public void onBeforeAttributeGroupDeleteEvent(BeforeAttributeGroupDeleteEvent event)
    {
        if (modulesService.isInstalled(Modules.MOBILE_API))
        {
            AttributeGroup attrGroup = (AttributeGroup)event.getSource();
            ClassFqn declaredMetaClass = attrGroup.getDeclaredMetaClass();
            Collection<ClassFqn> metaClassDescendantFqns = metainfoService.getMetaClassDescendants(declaredMetaClass,
                    true);
            List<UsageAttrGroup> attrGroupUsages = attributeGroupService.getAttrGroupUsages(attrGroup);
            attrGroupUsages.stream()
                    .filter(MobilePropertyListUsageAttrGroup.class::isInstance)
                    .map(MobilePropertyListUsageAttrGroup.class::cast)
                    .filter(getUsageFilter(attrGroup, metaClassDescendantFqns))
                    .forEach(existsUsage ->
                    {
                        MobilePropertiesListContent mobilePropertiesListContent =
                                existsUsage.getMobilePropertiesListContent();
                        MobileViewBase mobileView = existsUsage.getMobileView();
                        event.addMessage(messages.getMessage(
                                "BeforeAttributeGroupDeleteListener.errorUsedInMobilePropertiesList",
                                metainfoUtils.getLocalizedValue(mobilePropertiesListContent.getCaption()),
                                mobilePropertiesListContent.getCode(),
                                mobileView.getCode()));
                        event.cancel();
                    });
        }
    }

    private static Predicate<? super MobilePropertyListUsageAttrGroup> getUsageFilter(AttributeGroup attrGroup,
            Collection<ClassFqn> metaClassDescendantFqns)
    {
        return usage -> Objects.equals(attrGroup.getCode(), usage.getMobilePropertiesListContent().getAttributeGroup())
                        &&
                        Collections.containsAny(metaClassDescendantFqns, getCases(usage.getMobileView()));
    }

}
