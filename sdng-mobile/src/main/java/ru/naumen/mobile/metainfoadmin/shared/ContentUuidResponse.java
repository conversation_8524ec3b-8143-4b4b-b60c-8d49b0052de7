package ru.naumen.mobile.metainfoadmin.shared;

import net.customware.gwt.dispatch.shared.Result;

/**
 * Ответ, содержащий uuid мобильного контента.
 * Используется для того, чтобы при добавлении контента сразу переходить на его карточку
 *
 * <AUTHOR>
 * @since 02 июня 2015 г.
 */
public class ContentUuidResponse implements Result
{
    private String uuid;

    public ContentUuidResponse()
    {
    }

    public ContentUuidResponse(String uuid)
    {
        this.uuid = uuid;
    }

    public String getUuid()
    {
        return uuid;
    }
}
