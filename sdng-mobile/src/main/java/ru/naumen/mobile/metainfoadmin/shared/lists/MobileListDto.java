package ru.naumen.mobile.metainfoadmin.shared.lists;

import java.util.Collection;

import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.mobile.metainfoadmin.shared.InterfaceInfo;
import ru.naumen.mobile.metainfoadmin.shared.MobileContentDto;

/**
 * Объект переноса данных для списка объектов мобильного приложения.
 * Помимо самого списка содержит объект с информацией для отображения
 * его в интерфейсе технолога.
 *
 * <AUTHOR>
 * @since 24 апр. 2015 г.
 */
public class MobileListDto extends MobileContentDto
{
    private static final long serialVersionUID = -884641927316304942L;
    private Collection<MobileListUsagePlace> usagePlaces;

    public MobileListDto()
    {
    }

    public MobileListDto(MobileList mobileList, InterfaceInfo interfaceInfo)
    {
        super(mobileList, interfaceInfo);
    }

    public Collection<MobileListUsagePlace> getUsagePlaces()
    {
        return usagePlaces;
    }

    public void setUsagePlaces(Collection<MobileListUsagePlace> usagePlaces)
    {
        this.usagePlaces = usagePlaces;
    }
}
