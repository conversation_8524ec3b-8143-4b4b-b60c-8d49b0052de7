package ru.naumen.mobile.metainfoadmin.client.cards.contents.listgroup;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.content.tabbar.TabBarPopupResources;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentDisplay;
import ru.naumen.mobile.metainfoadmin.client.CommandSelectListCell;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;

/**
 * Дисплей отображения контента "Агрегатор списков" на карточке настройки мобильного приложения
 *
 * <AUTHOR>
 * @since 21.06.2021
 */
public class MobileListsGroupContentDisplayImpl extends
        AbstractFlowContentDisplay<MobileListsGroupContent>
{
    private final NauGrid grid;
    private final Label tableLabel = new Label();
    private final HTML infoText = new HTML(); // NOPMD safe html

    @Inject
    public MobileListsGroupContentDisplayImpl(
            FontIconFactory<String> fontIconFactory,
            Provider<CommandSelectListCell<MobileListsGroupContent>> commandSelectListCellProvider,
            AdminWidgetResources widgetResources,
            MobileSettingsMessages mobileSettingsMessages,
            TabBarPopupResources popupResources)
    {
        super(fontIconFactory, commandSelectListCellProvider, widgetResources, mobileSettingsMessages, popupResources);

        HorizontalPanel mainPanel = new HorizontalPanel();
        captionPanel.add(mainPanel);
        tableLabel.addStyleName(widgetResources.mobileContent().listsGroupTitle());
        mainPanel.add(tableLabel);
        setStyleName(widgetResources.mobileContent().mobileFlowContent());
        getPopup().addStyleName(widgetResources.mobileContent().mobileFlowContentPopup());

        grid = new NauGrid();
        grid.addStyleName(widgetResources.mobileContent().listsGroupList());
        grid.resize(1, 2);
        addWidget(grid);

        HorizontalPanel infoPanel = new HorizontalPanel();
        infoPanel.setStyleName(widgetResources.mobileContent().infoPanel());
        infoPanel.add(infoText);
        add(infoPanel);
    }

    public NauGrid getGrid()
    {
        return grid;
    }

    public void setInfo(String info)
    {
        infoText.setHTML(SafeHtmlUtils.fromString(info));
    }

    public void setLabel(String label)
    {
        tableLabel.setText(label);
    }
}