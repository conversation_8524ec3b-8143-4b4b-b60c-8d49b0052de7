package ru.naumen.mobile.metainfoadmin.server.listeners;

import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.tags.usage.listeners.mobile.view.events.BeforeEditMobileViewEvent;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.lists.MobileObjectList;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.cards.EditMobilePropertiesListHelper;

/**
 * <AUTHOR>
 * @since 4 мая 2019 г.
 *
 */
@Component
public class BeforeEditObjectCardEventListener implements ApplicationListener<BeforeEditMobileViewEvent>
{
    private final EditMobilePropertiesListHelper mobilePropertiesListHelper;
    private final MobileSettingsService mobileSettingsService;

    @Inject
    public BeforeEditObjectCardEventListener(EditMobilePropertiesListHelper mobilePropertiesListHelper,
            MobileSettingsService mobileSettingsService)
    {
        this.mobilePropertiesListHelper = mobilePropertiesListHelper;
        this.mobileSettingsService = mobileSettingsService;
    }

    @Override
    public void onApplicationEvent(BeforeEditMobileViewEvent event)
    {
        AbstractMobileView mobileView = event.getView();
        if (mobileView instanceof ObjectCard newView)
        {
            ObjectCard oldView = mobileSettingsService.getObjectCard(mobileView.getUuid());
            List<MobilePropertiesListContent> newContents = oldView.getContents().stream()
                    .filter(MobilePropertiesListContent.class::isInstance)
                    .map(MobilePropertiesListContent.class::cast)
                    .map(newContent -> processNewContent(newView, newContent))
                    .toList();
            mobilePropertiesListHelper.saveMobileViewContents(oldView, newContents);
        }

        if (mobileView instanceof MobileObjectList)
        {
            for (ObjectCard card : mobileSettingsService.getSettingsOrEmpty().getObjectCards())
            {
                boolean needToSave = false;
                for (AbstractMobileContent content : card.getAllContents())
                {
                    if (content instanceof MobileListContentBase mobileList
                        && mobileList.getLinkToMobileList() != null
                        && mobileList.getLinkToMobileList().equals(mobileView.getUuid())
                        && !mobileList.getFqnOfClass().equals(mobileView.getFqnOfClass()))
                    {
                        mobileList.setLinkToMobileList(null);
                        needToSave = true;
                    }
                }

                if (needToSave)
                {
                    mobileSettingsService.saveContent(card);
                }
            }
        }
    }

    private MobilePropertiesListContent processNewContent(ObjectCard newView, MobilePropertiesListContent newContent)
    {
        List<MetaClass> cardClasses = mobilePropertiesListHelper.getCardClasses(newView);
        List<Attribute> commonAttributes = mobilePropertiesListHelper.getAttributes(newContent, cardClasses);
        Map<AttributeFqn, MobileAttribute> mobileAttributesByFqn = EditMobilePropertiesListHelper
                .getMapMobileAttributesByFqn(newContent);
        List<MobileAttribute> mobileAttributesToSet = EditMobilePropertiesListHelper.getMobileAttributes(
                commonAttributes, mobileAttributesByFqn);
        newContent.setAttributes(mobileAttributesToSet);
        return newContent;
    }
}
