package ru.naumen.mobile.metainfoadmin.server.listeners;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.dispatch.sec.BeforeDeleteSecProfileEvent;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;

/**
 * Ищет использование удаляемого профиля в настройках мобильного приложения.
 * В случае если находит - удаляет без предупреждения
 *
 * <AUTHOR>
 */
@Component
public class MobileSettingsDelProfileEventListener extends
        AbstractDeleteEventListener<BeforeDeleteSecProfileEvent, Profile>
{
    @Override
    protected String getMessage(BeforeDeleteSecProfileEvent event)
    {
        return null;
    }

    @Override
    protected boolean handleAddForm(AddForm form, Profile profile, boolean hasMobileModule)
    {
        return handleContentAndSave(form, profile);
    }

    @Override
    protected boolean handleCard(ObjectCard card, Profile profile, boolean hasMobileModule, List<String> contentDetails)
    {
        boolean hasProfile = handleContent(card.getProfiles(), profile);
        for (AbstractMobileContent content : card.getAllContents())
        {
            if (handleContent(content.getProfiles(), profile))
            {
                hasProfile = true;
            }
        }
        if (hasProfile)
        {
            mobileSettingsService.saveContent(card);
        }
        return hasProfile;
    }

    @Override
    protected boolean handleEditForm(EditForm form, Profile profile, boolean hasMobileModule)
    {
        return handleContentAndSave(form, profile);
    }

    @Override
    protected boolean handleList(MobileList list, Profile profile, boolean hasMobileModule)
    {
        return handleContentAndSave(list, profile);
    }

    @Override
    protected boolean isCancelling()
    {
        return false;
    }

    private boolean handleContent(Collection<String> profiles, Profile profile)
    {
        if (CollectionUtils.isEmpty(profiles))
        {
            return false;
        }

        Iterator<String> i = profiles.iterator();
        while (i.hasNext())
        {
            if (ObjectUtils.equals(i.next(), profile.getCode()))
            {
                i.remove();
                return true;
            }
        }
        return false;
    }

    /**
     * Обработка удаления профиля
     * @param content
     * @param profile
     * @return
     */
    private boolean handleContentAndSave(AbstractMobileView content, Profile profile)
    {
        boolean hasProfile = handleContent(content.getProfiles(), profile);
        if (hasProfile)
        {
            mobileSettingsService.saveContent(content);
        }
        return hasProfile;
    }

    @Override
    protected boolean handleNavigationElement(MobileEmbeddedApplicationMenuItemValue element, Profile profile,
            boolean hasMobileModule)
    {
        final List<String> profiles = element.getProfiles();
        if (profiles != null && profiles.contains(profile.getCode()))
        {
            //удаляем профиль на прямую через ссылку на элемент навигационного меню
            profiles.remove(profile.getCode());
            return true;
        }
        return false;
    }
}
