package ru.naumen.mobile.metainfoadmin.server.cards;

import static ru.naumen.core.shared.permission.PermissionType.CREATE;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.tags.usage.listeners.mobile.contents.events.AfterAddMobileContentEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.mobile.ContentListActionHelper;
import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobileListsGroupContent;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.AddObjectCardMobileContentAction;
import ru.naumen.mobile.metainfoadmin.shared.ChangeMobileContentResponse;

/**
 * Реализация действия добавления контента к представлению карточки МК
 *
 * <AUTHOR>
 * @since 25 февр. 2019 г.
 */
@Component
public class AddObjectCardMobileContentActionHandler extends
        TransactionalActionHandler<AddObjectCardMobileContentAction, ChangeMobileContentResponse>
{

    private final MobileSettingsService mobileSettingsService;
    private final MobileSettingsLogService mobileSettingsLogService;
    private final EditMobilePropertiesListHelper mobilePropertiesListHelper;
    private final EditMobileEmbeddedApplicationContentHelper mobileEmbeddedApplicationContentHelper;
    private final EmbeddedApplicationService applicationService;
    private final MessageFacade messages;
    private final ApplicationEventPublisher eventPublisher;
    private final ContentListActionHelper contentListActionHelper;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public AddObjectCardMobileContentActionHandler(
            final MobileSettingsService mobileSettingsService,
            final MobileSettingsLogService mobileSettingsLogService,
            final EditMobilePropertiesListHelper mobilePropertiesListHelper,
            final EditMobileEmbeddedApplicationContentHelper mobileEmbeddedApplicationContentHelper,
            final EmbeddedApplicationService applicationService,
            final MessageFacade messages,
            final ApplicationEventPublisher eventPublisher,
            final ContentListActionHelper contentListActionHelper,
            final AdminPermissionCheckService adminPermissionCheckService)
    {
        this.mobileSettingsService = mobileSettingsService;
        this.mobileSettingsLogService = mobileSettingsLogService;
        this.mobilePropertiesListHelper = mobilePropertiesListHelper;
        this.mobileEmbeddedApplicationContentHelper = mobileEmbeddedApplicationContentHelper;
        this.applicationService = applicationService;
        this.messages = messages;
        this.eventPublisher = eventPublisher;
        this.contentListActionHelper = contentListActionHelper;
        this.adminPermissionCheckService = adminPermissionCheckService;
    }

    @Override
    public ChangeMobileContentResponse executeInTransaction(AddObjectCardMobileContentAction action,
            ExecutionContext context) throws DispatchException
    {
        final AbstractMobileContent mobileContent = action.getMobileContent();
        adminPermissionCheckService.checkPermission(mobileContent, CREATE);

        final ObjectCard objectCard = mobileSettingsService.getObjectCard(action.getViewUuid());
        final MapProperties oldProperties = mobileSettingsLogService.getMobileContentLogInfo(objectCard);
        if (mobileContent instanceof MobilePropertiesListContent propertiesListContent)
        {
            mobilePropertiesListHelper.fillMobileAttributes(objectCard, propertiesListContent);
        }
        if (mobileContent instanceof MobileEmbeddedApplicationContent embeddedApplicationContent)
        {
            final EmbeddedApplication embeddedApplication =
                    applicationService.getApplication(embeddedApplicationContent.getEmbeddedApplicationCode());
            if (embeddedApplication == null)
            {
                throw new FxException(messages.getLocalizedMessages("embeddedApplication.NotFound"));
            }
            mobileEmbeddedApplicationContentHelper.convertParametersValuesTosSerializable(embeddedApplicationContent);
        }
        if (mobileContent instanceof MobileListsGroupContent listsGroupContent)
        {
            final Set<String> contentsCodes = listsGroupContent.getListContents().stream()
                    .map(AbstractMobileContent::getCode)
                    .collect(Collectors.toSet());

            final List<AbstractMobileContent> nestedContents = objectCard.getContents().stream()
                    .filter(content -> contentsCodes.contains(content.getCode()))
                    .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

            objectCard.getContents().removeAll(nestedContents);
            listsGroupContent.setListContents(nestedContents);
        }
        if (mobileContent instanceof MobileListContentBase listContentBase)
        {
            List<ContentListAction> actions = listContentBase.getContentListActions().getActions();
            actions.addFirst(contentListActionHelper.createSortAction());
        }

        mobileContent.setParent(objectCard);
        objectCard.addContent(mobileContent);

        if (mobileSettingsService.saveContent(objectCard))
        {
            mobileSettingsLogService.logAddContent(objectCard, mobileContent, oldProperties, "");
        }

        eventPublisher.publishEvent(new AfterAddMobileContentEvent(mobileContent, objectCard));

        return new ChangeMobileContentResponse(mobileContent);
    }

}
