package ru.naumen.mobile.metainfoadmin.shared.othersettings;

import java.util.ArrayList;

import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;

/**
 * <AUTHOR>
 * @since Dec 07, 2018
 */
public class BarcodeScannerSettingsResponse implements Result
{
    private ArrayList<SimpleTreeDtObject> selectedElements;
    private ArrayList<String> profilesTitles;

    public BarcodeScannerSettingsResponse()
    {
        profilesTitles = new ArrayList<>();
    }

    public BarcodeScannerSettingsResponse(ArrayList<SimpleTreeDtObject> selectedElements,
            ArrayList<String> profilesTitles)
    {
        this.selectedElements = selectedElements;
        this.profilesTitles = profilesTitles;
    }

    public ArrayList<SimpleTreeDtObject> getSelectedElements()
    {
        return selectedElements;
    }

    public ArrayList<String> getProfilesTitles()
    {
        return profilesTitles;
    }
}
