package ru.naumen.mobile.metainfoadmin.client.cards.contents.embeddedapp;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.dom.client.Style.Cursor;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.dom.client.Style.VerticalAlign;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.content.tabbar.TabBarPopupResources;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentDisplay;
import ru.naumen.mobile.metainfoadmin.client.CommandSelectListCell;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;

/**
 * Дисплей контента "Встроенное приложение" на карточке мобильного объекта
 * <AUTHOR>
 * @since 02.10.2019
 */
public class MobileEmbeddedAppContentDisplayImpl extends
        AbstractFlowContentDisplay<MobileEmbeddedApplicationContent>
{

    private final HTML infoText = new HTML(); // NOPMD safe html
    private final HorizontalPanel windowApplicationTitle = new HorizontalPanel();
    private final Label windowLabel = new Label();
    private final NauGrid grid;

    @Inject
    public MobileEmbeddedAppContentDisplayImpl(FontIconFactory<String> fontIconFactory,
            Provider<CommandSelectListCell<MobileEmbeddedApplicationContent>> commandSelectListCellProvider,
            AdminWidgetResources widgetResources, MobileSettingsMessages mobileSettingsMessages,
            TabBarPopupResources popupResources)
    {
        super(fontIconFactory, commandSelectListCellProvider, widgetResources, mobileSettingsMessages, popupResources);

        grid = new NauGrid();
        grid.addStyleName(widgetResources.mobileContent().mobilePropertiesListAttributesTable());
        grid.resize(1, 2);
        addWidget(grid);

        setStyleName(widgetResources.mobileContent().mobileFlowContent());
        getPopup().addStyleName(widgetResources.mobileContent().mobileFlowContentPopup());

        addToCaptionPanel(windowApplicationTitle);
        FontIconDisplay<String> windowIcon = fontIconFactory.create(IconCodes.MOBILE_EMBEDDED_APP);
        windowApplicationTitle.add(windowIcon);
        windowLabel.getElement().getStyle().setMarginRight(11, Unit.PX);
        windowLabel.addStyleName(widgetResources.mobileContent().mobileEmbeddedApplicationLink());
        windowApplicationTitle.add(windowLabel);
        windowApplicationTitle.addStyleName(widgetResources.mobileContent().contentTitle());
        FontIconDisplay<String> linkIcon = fontIconFactory.create(IconCodes.MOBILE_TABLE_LINK);
        windowApplicationTitle.add(linkIcon);
        linkIcon.asWidget().getElement().getStyle().setCursor(Cursor.DEFAULT);
        linkIcon.getElement().getParentElement().getStyle().setVerticalAlign(VerticalAlign.BOTTOM);

        HorizontalPanel infoPanel = new HorizontalPanel();
        infoPanel.setStyleName(widgetResources.mobileContent().infoPanel());
        infoPanel.add(infoText);
        add(infoPanel);
    }

    public HorizontalPanel getWindowApplicationTitle()
    {
        return windowApplicationTitle;
    }

    public Label getWindowLabel()
    {
        return windowLabel;
    }

    public void setInfo(String info)
    {
        infoText.setHTML(SafeHtmlUtils.fromString(info));
    }

    NauGrid getGrid()
    {
        return grid;
    }
}
