package ru.naumen.mobile.metainfoadmin.server.listeners;

import static com.google.common.collect.Sets.intersection;
import static com.google.common.collect.Sets.newHashSet;
import static java.util.stream.Collectors.toSet;

import java.util.Set;

import jakarta.inject.Inject;
import jakarta.transaction.TransactionManager;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.TransactionSync;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.other.BarcodeScannerAttribute;
import ru.naumen.metainfo.shared.mobile.other.BarcodeScannerSettings;
import ru.naumen.mobile.metainfo.MobileSettingsService;

/**
 * При удалении метакласса удаляем все атрибуты из настроек сканера штрих-кодов
 * <AUTHOR>
 * @since Aug 29, 2018
 */
@Component
public class BarcodeScannerSettingsMetaClassDeleteListener implements ApplicationListener<BeforeMetaClassDeleteEvent>
{
    @Inject
    private MobileSettingsService mobileSettingsService;

    @Inject
    private TransactionManager txManager;

    @Override
    public void onApplicationEvent(BeforeMetaClassDeleteEvent event)
    {
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        BarcodeScannerSettings barcodeSettings = mobileSettings.getBarcodeScannerSettings();
        MetaClassImpl metaClass = (MetaClassImpl)event.getSource();
        //@formatter:off
        Set<BarcodeScannerAttribute> allAttrs = metaClass
                .getAttributes()
                .stream()
                .map(attr -> BarcodeScannerAttribute.create(metaClass.getFqn().getId(), attr.getFqn().toString()))
                .collect(toSet());
        //@formatter:on
        Set<BarcodeScannerAttribute> toDelete = newHashSet(intersection(barcodeSettings.getAvailableInAttrs(),
                allAttrs));
        if (!toDelete.isEmpty())
        {
            new TransactionSync(txManager)
            {
                @Override
                protected void onCommit()
                {
                    MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
                    BarcodeScannerSettings barcodeSettings = mobileSettings.getBarcodeScannerSettings();
                    barcodeSettings.getAvailableInAttrs().removeAll(toDelete);
                    mobileSettingsService.importSettings(mobileSettings);
                }
            }.execute();
        }
    }

}
