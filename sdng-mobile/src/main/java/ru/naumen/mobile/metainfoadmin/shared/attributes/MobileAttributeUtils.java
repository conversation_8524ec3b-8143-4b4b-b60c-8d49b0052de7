package ru.naumen.mobile.metainfoadmin.shared.attributes;

import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;

/**
 * Вспомогательные методы для {@link MobileAttribute}
 *
 * <AUTHOR>
 * @since May 5, 2017
 */
public class MobileAttributeUtils
{
    /**
     * Формирует код атрибута с учетом типа в котором атрибут объявлен.
     * Если атрибут объявлен в классе - %код_атрибута%.
     * Если атрибут объявлен в типе - %код_типа%@%код_атрибута%
     *
     * @param attributeFqn fqn атрибута
     * @return код атрибута с учетом типа в котором атрибут был определен
     */
    public static String getAttributeCodeWithDeclaredCaseId(AttributeFqn attributeFqn)
    {
        final ClassFqn classFqn = attributeFqn.getClassFqn();
        if (classFqn.isClass())
        {
            return attributeFqn.getCode();
        }
        //@formatter:off
         StringBuilder builder = new StringBuilder(classFqn.getCase())
                .append('@')
                .append(attributeFqn.getCode());
        //@formatter:on
        return builder.toString();
    }
}
