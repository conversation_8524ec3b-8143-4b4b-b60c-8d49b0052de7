package ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.RowStyles;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.view.client.AbstractDataProvider;
import com.google.gwt.view.client.HasData;
import com.google.inject.Provider;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.client.AdminMessages;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.toolbar.display.ToolBarDisplayMediator;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDController;
import ru.naumen.core.client.listeditor.dnd.ListEditorDnDControllerFactory;
import ru.naumen.core.client.listeditor.dnd.group.DataTableDnDGroupController;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.core.client.widgets.DefaultCellTable;
import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.client.widgets.columns.ToggleColumn;
import ru.naumen.core.shared.Constants.Tag;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ReadyState;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActions;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.addforms.MobileAddFormsHierarchy;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfoadmin.client.CatalogCellTableResources.CatalogCellTableStyle;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableResources;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.MoveToolEvent;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.MoveToolHandler;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.ShowToolFormEvent;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.ShowToolFormHandler;
import ru.naumen.metainfoadmin.client.tags.data.TagServiceAsync;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.client.cards.MobileObjectCardContext;
import ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions.event.MobileActionChangedEvent;
import ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions.event.MobileActionChangedEventHandler;
import ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions.forms.MobileContentListsActionAddFormPresenter;
import ru.naumen.mobile.metainfoadmin.client.cards.contents.lists.actions.forms.MobileContentListsActionEditFormPresenter;
import ru.naumen.mobile.metainfoadmin.client.commands.MobileCommandParam;
import ru.naumen.mobile.metainfoadmin.client.commands.cards.objectactions.DeleteMobileObjectActionCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.cards.objectactions.EditMobileObjectActionCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.cards.objectactions.MoveMobileObjectActionDownCommand;
import ru.naumen.mobile.metainfoadmin.client.commands.cards.objectactions.MoveMobileObjectActionUpCommand;
import ru.naumen.mobile.metainfoadmin.shared.MobileSettingsDto;
import ru.naumen.mobile.metainfoadmin.shared.addforms.GetMobileAddFormsHierarchiesResponse;
import ru.naumen.objectlist.client.ObjectListColumnBuilder;

/**
 * Презентер формы "Настройка действий" в контентах "Список связанных объектов" и "Список вложенных объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class MobileEditActionListPresenter extends BasicPresenter<TableWithArrowsDisplay<ContentListActionTool>>
        implements MobileActionChangedEventHandler, MoveToolHandler, ShowToolFormHandler
{
    /**
     * Функциональный интерфейс callback'a получения всех родительских форм добавления и их тегов
     *
     * <AUTHOR>
     * @since 08.05.2024
     */
    @FunctionalInterface
    private interface AddFormsHierarchiesAndTagsCallback
    {
        /**
         * Callback-метод для получения всех родительских форм добавления и их тегов
         */
        void callback(List<MobileAddFormsHierarchy> addFormsHierarchies, List<DtObject> tags);
    }

    /**
     * Контроллер Drag-and-drop механики на форме списка действий в контенте
     *
     * <AUTHOR>
     * @since 08.05.2024
     */
    private class MobileEditActionListDnDController extends DataTableDnDGroupController
    {
        public MobileEditActionListDnDController()
        {
            super(getDisplay().getTableContainer().getElement());
        }

        @Override
        public void move(int oldPosition, int newPosition, ReadyState readyState)
        {
            List<ContentListActionTool> actions = getDisplay().getTable().getVisibleItems();
            ContentListActionTool draggedTool = actions.get(oldPosition);
            ContentListActionTool draggedPlace = actions.get(newPosition);
            onMoveTool(new MoveToolEvent(draggedTool, false, draggedPlace, oldPosition > newPosition));
        }
    }

    /**
     * Провайдер данных в таблицу, представляющую собой список действий в контенте
     *
     * <AUTHOR>
     * @since 08.05.2024
     */
    private class ItemsDataProvider extends AbstractDataProvider<ContentListActionTool>
    {
        @Override
        protected void onRangeChanged(final HasData<ContentListActionTool> display)
        {
            final List<ContentListActionTool> itemsList = new ArrayList<>();

            for (Tool tool : contentListActions.getTools())
            {
                if (tool instanceof ContentListActionTool)
                {
                    itemsList.add((ContentListActionTool)tool);
                }
            }

            display.setRowData(0, itemsList);
            display.setRowCount(itemsList.size(), true);
        }
    }

    /**
     * Стили элементов таблицы, представляющей собой список действий в контенте
     *
     * <AUTHOR>
     * @since 08.05.2024
     */
    private class ItemsRowStyles implements RowStyles<ContentListActionTool>
    {
        @Override
        @Nullable
        public String getStyleNames(final ContentListActionTool eventTool, final int rowIndex)
        {
            if (ContentListActionType.SORT.equals(eventTool.getActionType()))
            {
                return null;
            }
            if (ContentListActionType.ADD_OBJECT.equals(eventTool.getActionType())
                && eventTool.getActionContentCode() != null
                && isEnabledByTags(eventTool))
            {
                return null;
            }
            return cellTableStyle.itemRemoved();
        }

        private boolean isEnabledByTags(ContentListActionTool tool)
        {
            List<String> tags = tool.getTags();
            return tags.isEmpty() || tags.stream()
                    .map(tagsState::get)
                    .anyMatch(Boolean.TRUE::equals);
        }
    }

    private final ListEditorDnDControllerFactory dndControllerFactory;
    private final ObjectListColumnBuilder tableBuilder;
    private final MobileSettingsService mobileSettingsService;
    private final TagServiceAsync tagService;
    private final MetainfoUtils metainfoUtils;
    private final CommonMessages commonMessages;
    private final AdminMessages adminMessages;
    private final MobileSettingsMessages mobileSettingsMessages;
    private final EditableToolPanelMessages editableToolPanelMessages;
    private final FontIconFactory<ContentListActionTool> iconFactory;
    private final CatalogCellTableStyle cellTableStyle;
    private final ButtonFactory buttonFactory;
    private final Provider<MobileContentListsActionAddFormPresenter> addFormPresenterProvider;
    private final Provider<MobileContentListsActionEditFormPresenter> editFormPresenterProvider;
    private ListEditorDnDController dndController;
    private MobileObjectCardContext context;
    private MobileListContentBase listContent;
    private MobileCommandParam param; //NOSONAR
    private ToolBar contentListActions;
    private Map<String, Boolean> tagsState;

    protected AsyncCallback<ContentListActionTool> refreshCallback = new BasicCallback<ContentListActionTool>()
    {
        @Override
        public void handleSuccess(@Nullable ContentListActionTool result)
        {
            refreshDisplay();
        }
    };

    @Inject
    @SuppressWarnings("java:S107")
    public MobileEditActionListPresenter(
            final TableWithArrowsDisplay<ContentListActionTool> display,
            final EventBus eventBus,
            final ListEditorDnDControllerFactory dndControllerFactory,
            final ObjectListColumnBuilder tableBuilder,
            final MobileSettingsService mobileSettingsService,
            final TagServiceAsync tagService,
            final MetainfoUtils metainfoUtils,
            final CommonMessages commonMessages,
            final AdminMessages adminMessages,
            final MobileSettingsMessages mobileSettingsMessages,
            final FontIconFactory<ContentListActionTool> iconFactory,
            final EditableToolPanelMessages editableToolPanelMessages,
            final WithArrowsCellTableResources cellTableResources,
            final ButtonFactory buttonFactory,
            final Provider<MobileContentListsActionAddFormPresenter> addFormPresenterProvider,
            final Provider<MobileContentListsActionEditFormPresenter> editFormPresenterProvider)
    {
        super(display, eventBus);
        this.dndControllerFactory = dndControllerFactory;
        this.tableBuilder = tableBuilder;
        this.mobileSettingsService = mobileSettingsService;
        this.tagService = tagService;
        this.metainfoUtils = metainfoUtils;
        this.commonMessages = commonMessages;
        this.adminMessages = adminMessages;
        this.mobileSettingsMessages = mobileSettingsMessages;
        this.iconFactory = iconFactory;
        this.editableToolPanelMessages = editableToolPanelMessages;
        this.cellTableStyle = cellTableResources.cellTableStyle();
        this.buttonFactory = buttonFactory;
        this.addFormPresenterProvider = addFormPresenterProvider;
        this.editFormPresenterProvider = editFormPresenterProvider;

        getDisplay().setCaptionVisible(false);
    }

    /**
     * Инициализирует презентер формы "настройки действий" в контенте
     *
     * @param context контекст настройки карточки объекта в МК
     * @param listContent настройки контента на карточке объекта
     */
    public void init(MobileObjectCardContext context, MobileListContentBase listContent)
    {
        this.context = context;
        this.listContent = listContent;

        this.dndController = dndControllerFactory.create(new MobileEditActionListDnDController());
        this.param = new MobileCommandParam<MobileObjectCardContext, Void, ContentListActionTool>(context, null,
                refreshCallback);

        this.contentListActions = convertListActions(listContent.getContentListActions());
    }

    private static ToolBar convertListActions(ContentListActions actions)
    {
        ToolBar actionsToolBar = new ToolBar();

        for (ContentListAction action : actions.getActions())
        {
            actionsToolBar.addTool(new ContentListActionTool(action));
        }

        return actionsToolBar;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        tagService.getAllTags(new BasicCallback<List<DtObject>>(readyState)
        {
            @Override
            protected void handleSuccess(List<DtObject> tags)
            {
                actualTagsState(tags);
            }
        });
        mobileSettingsService.getMobileSettings(new BasicCallback<MobileSettingsDto>(readyState)
        {
            @Override
            protected void handleSuccess(MobileSettingsDto settings)
            {
                Map<String, AddForm> addForms = settings.getMobileSettings().getAddForms().stream()
                        .collect(Collectors.toMap(AddForm::getCode, Function.identity()));
                for (Tool tool : contentListActions.getTools())
                {
                    if (!(tool instanceof ContentListActionTool))
                    {
                        continue;
                    }
                    ContentListActionTool contentListAction = (ContentListActionTool)tool;
                    List<String> tags = getActualTags(contentListAction, addForms);
                    if (tags != null)
                    {
                        contentListAction.setTags(tags);
                    }
                }
            }
        });
    }

    @Nullable
    private static List<String> getActualTags(ContentListActionTool contentListAction, Map<String, AddForm> addForms)
    {
        if (!ContentListActionType.ADD_OBJECT.equals(contentListAction.getActionType())
            || contentListAction.getActionContentCode() == null)
        {
            return null;
        }
        AddForm addForm = addForms.get(contentListAction.getActionContentCode());
        if (addForm == null)
        {
            return null;
        }
        return addForm.getTags();
    }

    @Override
    protected void onBind()
    {
        registerHandler(eventBus.addHandler(MobileActionChangedEvent.TYPE, this));
        registerHandler(context.getEventBus().addHandler(MoveToolEvent.getType(), this));
        registerHandler(context.getEventBus().addHandler(ShowToolFormEvent.getType(), this));

        initAddButtons();
        initTable();
        refreshDisplay();
    }

    @SuppressWarnings("java:S3740")
    private void initAddButtons()
    {
        ButtonPresenter addButton = buttonFactory.create(ButtonCode.ADD, editableToolPanelMessages.addAction(), event ->
                loadAddFormsAndTags((addForms, tags) ->
                {
                    MobileContentListsActionAddFormPresenter presenter = addFormPresenterProvider.get();
                    presenter.init(listContent.getFqnOfClass(), listContent.getCases(), tags, addForms);
                    presenter.bind();
                    presenter.getDisplay().display();
                }));

        ToolBarDisplayMediator<?> toolBarWithAddButton = new ToolBarDisplayMediator<>(getDisplay().getToolBar());
        toolBarWithAddButton.add(addButton);
        toolBarWithAddButton.bind();
    }

    private void initTable()
    {
        DataTable<ContentListActionTool> table = getDisplay().getTable();
        table.asWidget().getElement().getStyle().setPaddingTop(10.0, Unit.PX);
        table.setVisibleRange(0, DefaultCellTable.DEFAULT_PAGESIZE);

        addActionColumn(MoveMobileObjectActionUpCommand.ID);
        addActionColumn(MoveMobileObjectActionDownCommand.ID);

        addPropertiesColumns(table);

        addActionColumn(EditMobileObjectActionCommand.ID);
        addActionColumn(DeleteMobileObjectActionCommand.ID);

        table.setRowStyles(new ItemsRowStyles());
        table.asWidget().ensureDebugId("mobile-action-list-table");
        new ItemsDataProvider().addDataDisplay(table);
    }

    private void addPropertiesColumns(DataTable<ContentListActionTool> table)
    {
        addActionTitleColumn(table);
        addActionNameColumn(table);
        addTransferGeoPositionColumn(table);
    }

    private void addActionTitleColumn(DataTable<ContentListActionTool> table)
    {
        Column<ContentListActionTool, SafeHtml> actionTitleColumn = new Column<ContentListActionTool, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(ContentListActionTool action)
            {
                String title = metainfoUtils.getLocalizedValue(action.getTitle());
                return new SafeHtmlBuilder().appendEscaped(title).toSafeHtml();
            }
        };
        table.addColumn(actionTitleColumn, commonMessages.title());
    }

    private void addActionNameColumn(final DataTable<ContentListActionTool> table)
    {
        final Column<ContentListActionTool, SafeHtml> actionColumn = new Column<ContentListActionTool, SafeHtml>(
                new ClickableSafeHtmlTextCell())
        {
            @Override
            public SafeHtml getValue(final ContentListActionTool action)
            {
                ContentListActionType actionType = action.getActionType();
                String actionTitle = actionType == null
                        ? StringUtilities.EMPTY
                        : MobileContentListActionTypeHelper.localize(actionType, mobileSettingsMessages);
                return new SafeHtmlBuilder().appendEscaped(actionTitle).toSafeHtml();
            }
        };
        table.addColumn(actionColumn, adminMessages.action());
    }

    private void addTransferGeoPositionColumn(DataTable<ContentListActionTool> action)
    {
        ToggleColumn<ContentListActionTool> geoPositionColumn = new ToggleColumn<ContentListActionTool>(iconFactory)
        {
            @Override
            protected boolean isEnabled(ContentListActionTool tool)
            {
                return tool.isGeoRequired();
            }
        };
        action.addColumn(geoPositionColumn, editableToolPanelMessages.transferDeviceGeostation());
    }

    private void addActionColumn(String... commands)
    {
        tableBuilder.addActionColumn(display, param, 10, commands);
    }

    @Override
    public void onActionChanged(MobileActionChangedEvent event)
    {
        int toolIndex = contentListActions.getTools().indexOf(event.getContentListAction());
        if (toolIndex == -1)
        {
            contentListActions.addTool(event.getContentListAction());
        }
        refreshDisplay();
    }

    @Override
    public void onMoveTool(final MoveToolEvent event)
    {
        final ContentListActionTool draggedTool = (ContentListActionTool)event.getDraggedTool();
        if (draggedTool.getParent() != null)
        {
            draggedTool.getParent().getTools().remove(draggedTool);
            attachToolToNewParent(draggedTool, event);
            refreshDisplay();
        }
    }

    private void attachToolToNewParent(ContentListActionTool draggedTool, @Nullable MoveToolEvent event)
    {
        int toolPos = event == null
                ? contentListActions.getTools().size()
                : getNewToolPos(contentListActions, event);
        contentListActions.getTools().add(toolPos, draggedTool);
    }

    private static int getNewToolPos(ToolBar toolBar, MoveToolEvent event)
    {
        if (event.getTool() == null)
        {
            return toolBar.getTools().size();
        }
        int toolPos = toolBar.getTools().indexOf(event.getTool());
        if (toolPos == -1)
        {
            return 0;
        }
        return !event.isToTheLeft() ? toolPos + 1 : toolPos;
    }

    @Override
    public void onShowToolForm(ShowToolFormEvent event)
    {
        ContentListActionTool contentListActionTool = (ContentListActionTool)event.getTool();

        loadAddFormsAndTags((hierarchies, tags) ->
        {
            MobileContentListsActionEditFormPresenter presenter = editFormPresenterProvider.get();
            presenter.init(listContent.getFqnOfClass(), listContent.getCases(), tags, hierarchies,
                    contentListActionTool);
            presenter.bind();
            presenter.getDisplay().display();
        });
    }

    private void loadAddFormsAndTags(AddFormsHierarchiesAndTagsCallback callback)
    {
        mobileSettingsService.getAddFormsHierarchies(new BasicCallback<GetMobileAddFormsHierarchiesResponse>()
        {
            @Override
            protected void handleSuccess(GetMobileAddFormsHierarchiesResponse hierarchies)
            {
                List<MobileAddFormsHierarchy> addFormsHierarchies = hierarchies.getHierarchies();
                tagService.getAllTags(new BasicCallback<List<DtObject>>()
                {
                    @Override
                    protected void handleSuccess(List<DtObject> tags)
                    {
                        actualTagsState(tags);
                        callback.callback(addFormsHierarchies, tags);
                    }
                });
            }
        });
    }

    private void actualTagsState(List<DtObject> tags)
    {
        tagsState = tags.stream().collect(Collectors.toMap(
                tag -> tag.getProperty(Tag.CODE),
                tag -> tag.getProperty(Tag.ENABLED, Boolean.TRUE)));
    }

    @Override
    public void refreshDisplay()
    {
        getDisplay().refresh();
        Scheduler.get().scheduleDeferred(dndController::update);
    }

    /**
     * Действия при сохранении формы "настройки действий" в контенте
     */
    public void onApply()
    {
        listContent.setContentListActions(new ContentListActions(contentListActions.getTools().stream()
                .map(tool -> ((ContentListActionTool)tool).toContentListAction())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll)));
    }
}
