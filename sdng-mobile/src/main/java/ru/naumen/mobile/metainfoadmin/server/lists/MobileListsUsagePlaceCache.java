package ru.naumen.mobile.metainfoadmin.server.lists;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.cache.transaction.readonly.CachedInReadOnlyTx;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileListLinkValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListUsageAttributePlace;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListUsageContentPlace;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListUsageNavigationItemPlace;
import ru.naumen.mobile.metainfoadmin.shared.lists.MobileListUsagePlace;

/**
 * Места использования списков мобильного приложения
 *
 * <AUTHOR>
 * @since 07.04.2020
 */
@Component
public class MobileListsUsagePlaceCache
{
    private final MetainfoService metainfoService;
    private final MetainfoUtils metainfoUtils;
    private final MobileSettingsService mobileSettingsService;

    @Inject
    public MobileListsUsagePlaceCache(MetainfoService metainfoService, MetainfoUtils metainfoUtils,
            MobileSettingsService mobileSettingsService)
    {
        this.metainfoUtils = metainfoUtils;
        this.metainfoService = metainfoService;
        this.mobileSettingsService = mobileSettingsService;
    }

    /**
     * Возвращает все места использования списков в мобильном приложении
     * Предполагается, что списки могут использоваться в трех местах: На карточке мобильного приложения (атрибут,
     * контент),в настройках навигационного меню мобильного приложения
     * Кэширует в транзакции, что позволяет строить коллекцию только один раз за транзакцию.
     */
    @CachedInReadOnlyTx
    public List<MobileListUsagePlace> getAllUsagePlaces(String listUuid)
    {
        List<MobileListUsagePlace> usagePlaces = new ArrayList<>();
        //Места на карточках
        for (ObjectCard card : mobileSettingsService.getSettingsOrEmpty().getObjectCards())
        {
            getCardUsagePlace(listUuid, usagePlaces, card);
        }
        //Места из навигационного меню
        for (MobileMenuItemValue item : mobileSettingsService.getSettingsOrEmpty().getNavigationSettings())
        {
            getNavMenuItemPlace(listUuid, usagePlaces, item);
        }
        //Подготовить title для мест использования типа атрибут
        usagePlaces.stream()
                .filter(MobileListUsageAttributePlace.class::isInstance)
                .forEach(mobileListUsagePlace -> mobileListUsagePlace.setTitle(metainfoService.getAttribute(
                        AttributeFqn.parse(mobileListUsagePlace.getCode())).getTitle()));

        //Отсортировать сначала по классу а потом по загололвку места использования
        Collections.sort(usagePlaces);
        return usagePlaces;
    }

    private void getNavMenuItemPlace(String listUuid, List<MobileListUsagePlace> usagePlaces,
            MobileMenuItemValue someItem)
    {
        if (someItem.getValue() instanceof MobileListLinkValue
            && ((MobileListLinkValue)someItem.getValue()).getListCardCode().equals(listUuid))
        {
            usagePlaces.add(new MobileListUsageNavigationItemPlace(someItem,
                    metainfoUtils.getLocalizedValue(someItem.getTitle())));
        }
        for (MobileMenuItemValue subItem : someItem.getChildren())
        {
            getNavMenuItemPlace(listUuid, usagePlaces, subItem);
        }
    }

    private void getCardUsagePlace(String listUuid, List<MobileListUsagePlace> usagePlaces, ObjectCard card)
    {
        for (Content childContent : card.getAllContents())
        {
            if (childContent instanceof MobilePropertiesListContent &&
                ((MobilePropertiesListContent)childContent).getAttributes() != null)
            {
                ((MobilePropertiesListContent)childContent).getAttributes().stream().
                        filter(attr -> listUuid.equals(attr.getMobileListUuid()))
                        .forEach(attribute -> usagePlaces.add(new MobileListUsageAttributePlace(attribute,
                                (MobilePropertiesListContent)childContent,
                                metainfoService.getAttribute(AttributeFqn.parse(attribute.getAttributeFqnString()))
                                        .getTitle()))
                        );
            }
            else if (childContent instanceof MobileListContentBase &&
                     ((MobileListContentBase)childContent).getLinkToMobileList() != null &&
                     ((MobileListContentBase)childContent).getLinkToMobileList().equals(listUuid))
            {
                usagePlaces.add(new MobileListUsageContentPlace((MobileListContentBase)childContent,
                        metainfoUtils.getLocalizedValue(childContent.getCaption())));
            }
        }
    }
}
