package ru.naumen.mobile.metainfoadmin.server.othersettings;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.server.spi.dispatch.GetAttributesHelper;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BooleanAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.mobile.metainfoadmin.shared.GetAttributesResponse;
import ru.naumen.mobile.metainfoadmin.shared.cards.GetEnablesMovementAttributesAction;

/**
 * Обработчик {@link GetEnablesMovementAttributesAction} для получения набора пар <Атрибут, Код метакласса (или типа)
 * в котором он определен>
 * <AUTHOR>
 * @since 25.06.2019
 */
@Component
public class GetEnablesMovementAttributesActionHandler extends
        TransactionalActionHandler<GetEnablesMovementAttributesAction, GetAttributesResponse>
{
    private final SnapshotService snapshotService;
    private final GetAttributesHelper getAttributesHelper;

    @Inject
    public GetEnablesMovementAttributesActionHandler(SnapshotService snapshotService,
            GetAttributesHelper getAttributesHelper)
    {
        this.snapshotService = snapshotService;
        this.getAttributesHelper = getAttributesHelper;
    }

    @Override
    public GetAttributesResponse executeInTransaction(GetEnablesMovementAttributesAction action,
            ExecutionContext context)
            throws DispatchException
    {
        List<Pair<Attribute, ClassFqn>> attributesInMetaClass = new ArrayList<>();

        final ArrayList<Attribute> attributes = getAttributesHelper.getAttributes(Constants.Employee.FQN,
                new ArrayList<>());
        attributes.stream()
                .filter(this::applyFilter)
                .collect(Collectors.toSet())
                .forEach(attr -> attributesInMetaClass.add(Pair.create(attr, attr.getDeclaredConcretMetaClass())));
        attributesInMetaClass.forEach(item -> item.setLeft(snapshotService.prepare(item.getLeft())));
        return new GetAttributesResponse(attributesInMetaClass);
    }

    private boolean applyFilter(Attribute attribute)
    {
        return attribute.getType().getCode().equals(BooleanAttributeType.CODE);
    }
}
