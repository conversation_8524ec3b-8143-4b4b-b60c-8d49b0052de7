package ru.naumen.mobile.metainfoadmin.server.lists;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.mobile.metainfoadmin.shared.lists.ResetMobileListFiltrationAction;
import ru.naumen.objectlist.server.advlist.AdvlistUtils;

/**
 *
 * <AUTHOR>
 * @since 05 июня 2015 г.
 */
@Component
public class ResetMobileListFiltrationActionHandler
        extends TransactionalActionHandler<ResetMobileListFiltrationAction, EmptyResult>
{
    @Inject
    private MobileSettingsService mobileSettingsService;
    @Inject
    private MobileSettingsLogService mobileSettingsLogService;
    @Inject
    private MetainfoModification metainfoModification;
    @Inject
    private AdvlistUtils advlistUtils;

    @Override
    public EmptyResult executeInTransaction(ResetMobileListFiltrationAction action, ExecutionContext context)
            throws DispatchException
    {
        MobileList list = mobileSettingsService.getMobileList(action.getUuid());
        advlistUtils.swapUuidToDtObject(list.getListFilter(), false);
        MapProperties oldProperties = mobileSettingsLogService.getMobileContentLogInfo(list);
        list.setListFilter(new ListFilter());
        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.saveContent(list))
        {
            mobileSettingsLogService.logEditView(list, oldProperties, "");
        }
        return new EmptyResult();
    }
}
