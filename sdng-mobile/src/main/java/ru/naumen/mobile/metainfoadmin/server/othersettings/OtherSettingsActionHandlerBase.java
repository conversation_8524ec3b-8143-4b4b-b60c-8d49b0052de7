package ru.naumen.mobile.metainfoadmin.server.othersettings;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.other.OtherSettings;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.othersettings.OtherSettingsActionBase;

/**
 * Обработчик события сохранения какой-то части  прочих настроек мобильного приложения
 *
 * <AUTHOR>
 * @since 13.03.2018
 */
public abstract class OtherSettingsActionHandlerBase<T extends OtherSettingsActionBase>
        extends TransactionalActionHandler<T, EmptyResult>
{
    @Inject
    protected MobileSettingsService mobileSettingsService;
    @Inject
    private MetainfoModification metainfoModification;

    @Override
    public EmptyResult executeInTransaction(T action, ExecutionContext context)
            throws DispatchException
    {
        OtherSettings newSettings = action.getOtherSettings();
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        OtherSettings oldSettings = mobileSettings.getOtherSettings();
        mobileSettings.setOtherSettings(newSettings);
        metainfoModification.modify(
                MetainfoModification.MetainfoRegion.MOBILE_SETTINGS);
        if (mobileSettingsService.importSettings(mobileSettings))
        {
            logSettings(newSettings, oldSettings);
        }
        return new EmptyResult();
    }

    protected abstract void logSettings(OtherSettings newSettings, OtherSettings oldSettings);
}