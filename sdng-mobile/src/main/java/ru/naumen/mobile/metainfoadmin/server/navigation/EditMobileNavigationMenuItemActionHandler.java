package ru.naumen.mobile.metainfoadmin.server.navigation;

import static ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType.chapter;
import static ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem.MobileMenuItemType.embeddedApplication;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.tags.usage.listeners.mobile.navigation.events.AfterAddMobileNavigationMenuItemEvent;
import ru.naumen.core.server.tags.usage.listeners.mobile.navigation.events.BeforeEditMobileNavigationMenuItemEvent;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.mobile.metainfoadmin.shared.navigation.EditMobileNavigationMenuItemAction;

/**
 * Обработчик действия редактирования/создания элемента навигационного меню.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class EditMobileNavigationMenuItemActionHandler extends
        MobileNavigationMenuItemActionHandler<EditMobileNavigationMenuItemAction>
{
    private final MappingService mappingService;
    private final MetainfoUtils metainfoUtils;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public EditMobileNavigationMenuItemActionHandler(
            final MappingService mappingService,
            final MetainfoUtils metainfoUtils, ApplicationEventPublisher eventPublisher)
    {
        this.mappingService = mappingService;
        this.metainfoUtils = metainfoUtils;
        this.eventPublisher = eventPublisher;
    }

    @Override
    protected void addAdminLogRecord(EditMobileNavigationMenuItemAction action, MapProperties oldProperties,
            MobileSettings settings)
    {
        if (action.isNew())
        {
            mobileSettingsLogService.logAddNavigationMenuItem(action.getItem(), "");
        }
        else
        {
            MobileMenuItemValue newParent = findMenuItem(action.getNewPath(), settings);
            mobileSettingsLogService.logEditNavigationMenuItem(action.getItem(), newParent, oldProperties, "");
        }
    }

    @Override
    protected MapProperties getOldProperties(EditMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        if (!action.isNew())
        {
            return mobileSettingsLogService.getMobileNavigationSettingsLogInfo(getMenuItem(settings, action), action
                    .getItem().getParent());
        }
        return new MapProperties();
    }

    @Override
    protected void processAction(MobileSettings settings, EditMobileNavigationMenuItemAction action)
    {
        checkPermission(EDIT);

        if (action.isNew())
        {
            MobileMenuItemValue item = mappingService.transform(action.getItem(), getMenuItem(settings, action));
            addMobileMenuItem(item, action.getNewPath(), settings);
            return;
        }
        editMobileMenuItem(action, settings);
    }

    private void addMobileMenuItem(MobileMenuItemValue mobileMenuItem, LinkedList<String> newPath,
            MobileSettings settings)
    {
        checkPermission(mobileMenuItem, CREATE);
        final MobileMenuItemValue parent = findMenuItem(newPath, settings);
        if (chapter.equals(mobileMenuItem.getType()))
        {
            validateChapterNesting(mobileMenuItem, newPath, parent);
        }
        getChildren(parent, settings).add(mobileMenuItem);
        eventPublisher.publishEvent(new AfterAddMobileNavigationMenuItemEvent(mobileMenuItem));
    }

    private void editMobileMenuItem(EditMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        final MobileMenuItemValue menuItemValue = getMenuItem(settings, action);
        checkPermission(menuItemValue, EDIT);

        final MobileMenuItemValue oldParent = findMenuItem(action.getPathToMenuItem(), settings);
        final MobileMenuItemValue newParent = findMenuItem(action.getNewPath(), settings);
        //получаем список меток прошлой версии элемента до мапинга, так произойдет синхронизация меток между версиями
        final List<String> oldMenuItemTags = getOldItemTags(action.getItem().getCode(), oldParent, settings);
        MobileMenuItemValue oldMenuItm = getMenuItm(action.getItem().getCode(), oldParent, settings);
        String oldSettingsSet = oldMenuItm == null ? null : oldMenuItm.getSettingsSet();
        final MobileMenuItemValue menuItem =
                mappingService.transform(action.getItem(), getMenuItem(settings, action));
        if (chapter.equals(menuItem.getType()))
        {
            validateChapterNesting(menuItem, action.getNewPath(), newParent);
        }

        eventPublisher.publishEvent(
                new BeforeEditMobileNavigationMenuItemEvent(menuItem, oldMenuItemTags, oldSettingsSet));

        int index = getItemIndex(menuItem, oldParent, settings);
        if (oldParent == newParent)
        {
            getChildren(oldParent, settings).set(index, menuItem);
        }
        else
        {
            getChildren(oldParent, settings).remove(menuItem);
            getChildren(newParent, settings).add(menuItem);
        }
    }

    private static List<String> getOldItemTags(String code, @Nullable MobileMenuItemValue oldParent,
            MobileSettings settings)
    {
        final List<MobileMenuItemValue> items = oldParent == null
                ? settings.getNavigationSettings()
                : oldParent.getChildren();

        final Optional<MobileMenuItemValue> menuItem = items.stream()
                .filter(item -> item.getCode().equals(code))
                .findFirst();
        if (menuItem.isEmpty())
        {
            return Collections.emptyList();
        }
        final List<String> tagCodes = menuItem.get().getTags();
        return tagCodes == null ? Collections.emptyList() : tagCodes;
    }

    @Nullable
    private static MobileMenuItemValue getMenuItm(String code, @Nullable MobileMenuItemValue oldParent,
            MobileSettings settings)
    {
        final List<MobileMenuItemValue> items = oldParent == null
                ? settings.getNavigationSettings()
                : oldParent.getChildren();

        final Optional<MobileMenuItemValue> menuItem = items.stream()
                .filter(item -> item.getCode().equals(code))
                .findFirst();
        return menuItem.orElse(null);
    }

    private int getItemIndex(MobileMenuItemValue item, MobileMenuItemValue oldParent, MobileSettings settings)
    {
        int index = getChildren(oldParent, settings).indexOf(item);
        if (index == -1)
        {
            throw new FxException(messages.getMessage("DeleteNavigationMenuItem.itemIsAbsent", item.getCode()));
        }
        return index;
    }

    @Override
    protected void validateSettings(EditMobileNavigationMenuItemAction action, MobileSettings settings)
    {
        MobileMenuItemValue parent = validateParent(action.getPathToMenuItem(), settings);
        if (!action.isNew())
        {
            validateItem(parent, action.getMenuItemCode(), settings);
        }

        if (!ObjectUtils.equals(action.getPathToMenuItem(), action.getNewPath()))
        {
            validateParent(action.getNewPath(), settings);
        }
    }

    private MobileMenuItemValue getMenuItem(MobileSettings settings, EditMobileNavigationMenuItemAction action)
    {
        if (action.isNew())
        {
            return embeddedApplication.equals(action.getItem().getType())
                    ? new MobileEmbeddedApplicationMenuItemValue()
                    : new MobileMenuItemValue();
        }
        MobileMenuItemValue oldParent = findMenuItem(action.getPathToMenuItem(), settings);
        return findChild(oldParent, action.getMenuItemCode(), settings);
    }

    private void validateChapterNesting(MobileMenuItemValue item, LinkedList<String> newPath,
            MobileMenuItemValue parent)
    {
        if (newPath.size() >= MobileMenuItem.CHAPTER_NESTING_LIMIT)
        {
            throw new FxException(
                    messages.getMessage("EditNavigationMenuItemActionHandler.chapterNestedInAnotherChapter",
                            metainfoUtils.getLocalizedValue(parent.getTitle())));
        }
        boolean hasChapterChildren = item.getChildren().stream()
                .anyMatch(menuItem -> menuItem.getType() == chapter);

        if (newPath.size() == MobileMenuItem.CHAPTER_NESTING_LIMIT - 1 && hasChapterChildren)
        {
            throw new FxException(
                    messages.getMessage("EditNavigationMenuItemActionHandler.chapterContainNestingChapters",
                            item.getTitle()));
        }
    }
}
