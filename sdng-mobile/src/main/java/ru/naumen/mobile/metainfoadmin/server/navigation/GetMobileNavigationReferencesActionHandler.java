package ru.naumen.mobile.metainfoadmin.server.navigation;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.MobileSettings;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.mobile.metainfo.MobileSettingsService;
import ru.naumen.mobile.metainfoadmin.shared.navigation.GetMobileNavigationReferencesAction;
import ru.naumen.mobile.metainfoadmin.shared.navigation.GetMobileNavigationReferencesResponse;

/**
 * Обработчик действия получения списка доступных карточек объекта для навигационного меню.
 * <AUTHOR>
 * @since 28.03.2018
 */
@Component
public class GetMobileNavigationReferencesActionHandler extends
        AbstractActionHandler<GetMobileNavigationReferencesAction, GetMobileNavigationReferencesResponse>
{
    private static final Set<ClassFqn> allowedClassFqns = Sets.newHashSet(Constants.Employee.FQN, Constants.OU.FQN,
            Constants.Root.FQN);

    @Inject
    private MobileSettingsService mobileSettingsService;

    @Override
    public GetMobileNavigationReferencesResponse execute(GetMobileNavigationReferencesAction action,
            ExecutionContext context) throws DispatchException
    {
        GetMobileNavigationReferencesResponse response = new GetMobileNavigationReferencesResponse();
        ArrayList<MobileReference> mobileReferences = new ArrayList<>();
        MobileSettings mobileSettings = mobileSettingsService.getSettingsOrEmpty();
        Collection<MobileReference> availableMobileReferences = Collections2.transform(Collections2.filter(
                mobileSettings
                        .getObjectCards(),
                new Predicate<ObjectCard>()
                {
                    @Override
                    public boolean apply(ObjectCard objCard)
                    {
                        if (objCard.getClazz() != null)
                        {
                            return allowedClassFqns.contains(objCard.getClazz());
                        }
                        Collection<ClassFqn> cases = objCard.getCases();
                        if (!CollectionUtils.isEmpty(cases))
                        {
                            return allowedClassFqns.contains(cases.iterator().next().fqnOfClass());
                        }
                        return false;
                    }
                }), new Function<ObjectCard, MobileReference>()
        {
            @Override
            public MobileReference apply(ObjectCard objCard)
            {
                return new MobileReference(objCard.getCode(), objCard.getCode());
            }
        });
        mobileReferences.addAll(availableMobileReferences);
        response.setMobileReferences(mobileReferences);
        return response;
    }
}
