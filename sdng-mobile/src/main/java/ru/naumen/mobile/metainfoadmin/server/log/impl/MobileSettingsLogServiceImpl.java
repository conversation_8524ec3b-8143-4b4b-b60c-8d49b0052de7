package ru.naumen.mobile.metainfoadmin.server.log.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.shared.Constants.Categories;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileEmbeddedApplicationMenuItem;
import ru.naumen.core.shared.mobile.navigationmenu.dto.MobileMenuItem;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.ProfileImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.mobile.AbstractMobileView;
import ru.naumen.metainfo.shared.mobile.CommonMobileView;
import ru.naumen.metainfo.shared.mobile.MobileAttribute;
import ru.naumen.metainfo.shared.mobile.actions.ContentListAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentListActionType;
import ru.naumen.metainfo.shared.mobile.actions.ContentObjectAction;
import ru.naumen.metainfo.shared.mobile.actions.ContentObjectActions;
import ru.naumen.metainfo.shared.mobile.actions.ObjectAction;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
import ru.naumen.metainfo.shared.mobile.contents.AbstractMobileContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileChildObjectsListContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileEmbeddedApplicationContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileListContentBase;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.metainfo.shared.mobile.contents.MobileRelObjectsListContent;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.mobile.lists.MobileList;
import ru.naumen.metainfo.shared.mobile.lists.MobileRelObjectsList;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileEmbeddedApplicationMenuItemValue;
import ru.naumen.metainfo.shared.mobile.navigationmenu.MobileMenuItemValue;
import ru.naumen.metainfo.shared.mobile.other.BarcodeScannerSettings;
import ru.naumen.metainfo.shared.mobile.other.GeoHistorySettings;
import ru.naumen.metainfo.shared.mobile.other.OtherSettings;
import ru.naumen.metainfo.shared.spi.store.Transition;
import ru.naumen.metainfo.shared.tags.Tag;
import ru.naumen.mobile.metainfoadmin.server.MobileContentDescriptionBuilder;
import ru.naumen.mobile.metainfoadmin.server.MobileContentTitleExtractor;
import ru.naumen.mobile.metainfoadmin.server.log.MobileSettingsLogService;
import ru.naumen.sec.server.admin.log.impl.AdminLogServiceBase;

/**
 * Реализация сервиса {@link MobileSettingsLogService}
 *
 * <AUTHOR>
 * @since 25.09.2015
 */
@Component
public class MobileSettingsLogServiceImpl extends AdminLogServiceBase implements MobileSettingsLogService
{
    private static final String ACTION_TYPE_CODE = "mobileSettings.actionType";
    private static final String SETTINGS_SET_CODE = "mobileMenuItem.settingsSet";

    private static final Function<Transition, String> TRANSITION_TO_STRING = input -> input.getFrom() + "->" + input
            .getTo();

    private static final Function<ObjectAction, String> OBJECT_ACTIONS_TO_STRING = input -> null != input.getCaption()
                                                                                            && !input.getCaption()
            .isEmpty()
            ? input.getCaption().getFirst().getValue()
            : "null";

    private static final String DELIMITER = ", ";

    @Inject
    private SecurityServiceBean securityService;
    @Inject
    private TagService tagService;
    @Inject
    private MobileContentTitleExtractor titleExtractor;
    @Inject
    private MobileContentDescriptionBuilder descriptionBuilder;

    private final Function<AbstractMobileContent, String> mobileContentToString = content ->
    {
        StringBuilder sb = new StringBuilder();
        sb.append("code:")
                .append(content.getCode())
                .append(';')
                .append("title:")
                .append(metainfoUtils.getLocalizedValue(content.getCaption()))
                .append(';')
                .append("contentType:")
                .append(content.getContentType().getCode())
                .append(';')
                .append("collapsed:")
                .append(formatters.yesNoFormatter(content.isCollapsed()))
                .append(';')
                .append("allowCollapse:")
                .append(formatters.yesNoFormatter(content.isAllowCollapse()))
                .append(';')
                .append("showTitle:")
                .append(formatters.yesNoFormatter(content.isShowCaption()))
                .append(';');
        if (content instanceof MobileEmbeddedApplicationContent)
        {
            sb.append("embeddedApplicationCode:")
                    .append(((MobileEmbeddedApplicationContent)content).getEmbeddedApplicationCode())
                    .append(';');
        }
        return sb.toString();
    };

    private enum Action
    {
        ADD("add"),
        EDIT("edit"),
        DELETE("delete"),
        COPY("copy");

        private final String code;

        Action(String code)
        {
            this.code = code;
        }

        public String getCode()
        {
            return code;
        }
    }

    private final UnaryOperator<String> profileTitleExtractor = input ->
    {
        final ProfileImpl profile = securityService.getProfile(input);
        return profile == null ? input + " (not found)" : profile.getTitle();
    };

    private final UnaryOperator<String> tagTitleExtractor = code ->
    {
        Tag tag = tagService.getTag(code);
        return tag == null ? code + " (not found)" : metainfoUtils.getLocalizedValue(tag.getTitle());
    };

    private final Function<ClassFqn, String> metaClassTitleExtractor = input ->
    {
        try
        {
            return metaInfoService.getMetaClass(input).getTitle();
        }
        catch (ClassMetainfoServiceException e)
        {
            return input.toString();
        }
    };

    private final Function<MobileAttribute, String> mobileAttributeStringExtractor = input -> input.getCode() + "#"
                                                                                              + input
                                                                                                      .getPresentation();

    public MobileSettingsLogServiceImpl()
    {
        categoriesWithActions.add(Categories.MOBILE_LIST_SETTINGS);
        categoriesWithActions.add(Categories.MOBILE_CARD_SETTINGS);
        categoriesWithActions.add(Categories.MOBILE_ADD_FORM_SETTINGS);
        categoriesWithActions.add(Categories.MOBILE_EDIT_FORM_SETTINGS);
        categoriesWithActions.add(Categories.MOBILE_NAVIGATION_SETTINGS);

        shortChangeNames.add("objectCard.transitions");
        shortChangeNames.add("objectCard.attributes");
        shortChangeNames.add("mobileList.attributes");
        shortChangeNames.add("mobileList.attrChain");
        shortChangeNames.add("mobileList.filter");
        shortChangeNames.add("mobileList.order");
        shortChangeNames.add("addForm.attributes");
        shortChangeNames.add("editForm.attributes");
        shortChangeNames.add("mobileBarcodeScannerSettings.availableInAttrs");
    }

    @Override
    public void logAddNavigationMenuItem(MobileMenuItem item, String details)
    {
        createAndSave(Categories.MOBILE_NAVIGATION_SETTINGS, details, "addElement", item.getTitle(), item.getTitle());
    }

    @Override
    public void logAddView(AbstractMobileView mobileContent, String details)
    {
        createAndSaveLogFor(mobileContent, Action.ADD, details, null);
    }

    @Override
    public void logCopyView(AbstractMobileView mobileContent, String titleOfOriginalContent)
    {
        createAndSaveLogFor(mobileContent, Action.COPY, "", titleOfOriginalContent);
    }

    @Override
    public void logDeleteNavigationMenuItem(String itemTitle, String details)
    {
        createAndSave(Categories.MOBILE_NAVIGATION_SETTINGS, details, "deleteElement", itemTitle, itemTitle);
    }

    @Override
    public void logDeleteView(AbstractMobileView mobileContent, String details)
    {
        createAndSaveLogFor(mobileContent, Action.DELETE, details, null);
    }

    @Override
    public void logEditBarcodeScannerSettings(@Nullable BarcodeScannerSettings newSettings,
            BarcodeScannerSettings oldSettings)
    {
        // newSettings может быть null в selenium-тестах, используется для обнуления настроек сканера штрихкодов
        if (newSettings == null)
        {
            return;
        }
        MapProperties newProperties = getBarcodeScannerSettingsLogInfo(newSettings);
        MapProperties oldProperties = getBarcodeScannerSettingsLogInfo(oldSettings);
        String changes = getChanges(oldProperties, newProperties);
        createAndSave(Categories.MOBILE_BARCODE_SCANNER_SETTINGS, "", Categories.EDIT_SETTINGS, changes);
    }

    @Override
    public void logEditContent(ObjectCard oldCard, ObjectCard newCard, AbstractMobileContent mobileContent,
            MapProperties oldProperties,
            String details)
    {
        switch (mobileContent)
        {
            case MobilePropertiesListContent propertiesList ->
                    logEditPropertiesList(oldCard, newCard, propertiesList, oldProperties);
            case MobileRelObjectsListContent listContent ->
                    logEditListContent(oldCard, newCard, listContent, oldProperties, "relObjectList");
            case MobileChildObjectsListContent listContent ->
                    logEditListContent(oldCard, newCard, listContent, oldProperties, "childObjectList");
            default ->
            {
                MapProperties newProperties = getMobileContentLogInfo(newCard);
                String changes = getChanges(oldProperties, newProperties);
                createAndSaveLogForContent(newCard, mobileContent, Action.EDIT, details, changes);
            }
        }
    }

    /**
     * Логирование изменения поля редактировать на контенте Параметры объекта.
     *
     * @param oldCard карточка, на которой произошло изменение.
     * @param newCard новое состояние карточки объекта.
     * @param content контент, на котором произошло изменение.
     */
    private void logEditPropertiesList(ObjectCard oldCard, ObjectCard newCard,
            MobilePropertiesListContent content, MapProperties oldProperties)
    {
        List<ContentObjectAction> oldActions = getContentActions(content.getCode(), oldCard);
        List<ContentObjectAction> newActions = getAllActions(content);

        List<String> changedActionMessages = new ArrayList<>();
        for (ObjectAction oldAction : oldActions)
        {
            if (isNotEditAction(oldAction))
            {
                continue;
            }

            Optional<ObjectAction> newAction = findObjectAction(oldAction.getUuid(), newActions);
            if (newAction.isPresent() && editFormChanged(oldAction, newAction.get()))
            {
                String changedFormMessageCode =
                        CATEGORY_NAME_PREFIX + "mobileCardSettings.changedEditFormOnContent" + DESCRIPTION_SUFFIX;
                changedActionMessages.add(messages.getMessage(changedFormMessageCode, localeUtils.getEventLocale(),
                        metainfoUtils.getLocalizedValue(newAction.get().getCaption()),
                        content.getCode(),
                        oldAction.getFormCode(),
                        newAction.get().getFormCode()));
            }
        }

        if (!changedActionMessages.isEmpty())
        {
            String changedActionsMessage = String.join("\n", changedActionMessages);
            createAndSaveLogFor(newCard, Action.EDIT, "", StringUtilities.trim(changedActionsMessage));
            return;
        }

        logOtherContentChanges(newCard, content, oldProperties);
    }

    private static List<ContentObjectAction> getContentActions(String contentCode, ObjectCard card)
    {
        Optional<MobilePropertiesListContent> foundContent = card.getContents().stream()
                .filter(content -> content.getCode().equals(contentCode))
                .map(MobilePropertiesListContent.class::cast)
                .findFirst();
        return foundContent.isPresent()
                ? getAllActions(foundContent.get())
                : List.of();
    }

    private static List<ContentObjectAction> getAllActions(MobilePropertiesListContent content)
    {
        ContentObjectActions actions = content.getContentObjectActions();
        return ListUtils.union(actions.getTopActions(), actions.getBottomActions()).stream()
                .flatMap(row -> row.getActions().stream())
                .toList();
    }

    private void logEditListContent(ObjectCard oldCard, ObjectCard newCard, MobileListContentBase content,
            MapProperties oldProperties, String contentTypeTitleCode)
    {
        List<ContentListAction> oldActions = getContentListActions(content, oldCard);
        List<ContentListAction> newActions = getContentListActions(content, newCard);

        String contentTypeTitle = messages.getMessage(contentTypeTitleCode, localeUtils.getEventLocale());

        List<String> changedActionMessages = new ArrayList<>();
        for (ContentListAction oldAction : oldActions)
        {
            if (!ContentListActionType.ADD_OBJECT.equals(oldAction.getActionType()))
            {
                continue;
            }

            Optional<ContentListAction> newAction = newActions.stream()
                    .filter(action -> action.getUuid().equals(oldAction.getUuid()))
                    .findFirst();
            if (newAction.isPresent() &&
                !Objects.equals(newAction.get().getActionContentCode(), oldAction.getActionContentCode()))
            {
                String changedFormMessageCode =
                        CATEGORY_NAME_PREFIX + "mobileCardSettings.changedAddFormOnContent" + DESCRIPTION_SUFFIX;
                changedActionMessages.add(messages.getMessage(changedFormMessageCode, localeUtils.getEventLocale(),
                        contentTypeTitle,
                        content.getCode(),
                        oldAction.getActionContentCode(),
                        newAction.get().getActionContentCode()));
            }
        }

        if (!changedActionMessages.isEmpty())
        {
            String changedActionsMessage = String.join("\n", changedActionMessages);
            createAndSaveLogFor(newCard, Action.EDIT, "", StringUtilities.trim(changedActionsMessage));
            return;
        }

        logOtherContentChanges(newCard, content, oldProperties);
    }

    private static List<ContentListAction> getContentListActions(MobileListContentBase content, ObjectCard card)
    {
        Optional<MobileListContentBase> listContent = card.getContents().stream()
                .filter(cardContent -> cardContent.getCode().equals(content.getCode()))
                .map(MobileListContentBase.class::cast)
                .findFirst();
        return listContent.isPresent()
                ? listContent.get().getContentListActions().getActions()
                : List.of();
    }

    private void logOtherContentChanges(ObjectCard newCard, AbstractMobileContent content, MapProperties oldProperties)
    {
        String changes = getChanges(oldProperties, getMobileContentLogInfo(newCard));
        createAndSaveLogForContent(newCard, content, Action.EDIT, "", changes);
    }

    @Override
    public void logEditObjectCard(ObjectCard oldCard, ObjectCard newCard, String details)
    {
        String changedActionsMessage = getChangedActionMessages(oldCard, newCard);
        String changes = getChanges(getMobileContentLogInfo(oldCard), getMobileContentLogInfo(newCard));
        createAndSaveLogFor(newCard, Action.EDIT, details,
                StringUtilities.trim(changes + "\n" + changedActionsMessage));
    }

    private String getChangedActionMessages(ObjectCard oldCard, ObjectCard newCard)
    {
        List<String> changedActionMessages = new ArrayList<>();
        for (ObjectAction oldAction : oldCard.getObjectActions().getActions())
        {
            if (isNotEditAction(oldAction))
            {
                continue;
            }

            Optional<ObjectAction> newAction = findObjectAction(oldAction.getUuid(),
                    newCard.getObjectActions().getActions());
            if (newAction.isPresent() && editFormChanged(oldAction, newAction.get()))
            {
                String changedFormMessageCode =
                        CATEGORY_NAME_PREFIX + "mobileCardSettings.changedEditForm" + DESCRIPTION_SUFFIX;
                changedActionMessages.add(messages.getMessage(changedFormMessageCode, localeUtils.getEventLocale(),
                        metainfoUtils.getLocalizedValue(newAction.get().getCaption()),
                        oldAction.getFormCode(),
                        newAction.get().getFormCode()));
            }
        }

        return String.join("\n", changedActionMessages);
    }

    private static boolean isNotEditAction(ObjectAction action)
    {
        return !ObjectAction.EDIT_CODE.equals(action.getCode());
    }

    private static Optional<ObjectAction> findObjectAction(String uuid, List<? extends ObjectAction> actions)
    {
        return actions.stream()
                .filter(action -> action.getUuid().equals(uuid))
                .map(ObjectAction.class::cast)
                .findFirst();
    }

    private static boolean editFormChanged(ObjectAction oldAction, ObjectAction newAction)
    {
        return !Objects.equals(oldAction.getFormCode(), newAction.getFormCode());
    }

    @Override
    public void logAddContent(ObjectCard view, AbstractMobileContent mobileContent, MapProperties oldProperties,
            String details)
    {
        MapProperties newProperties = getMobileContentLogInfo(view);
        String changes = getChanges(oldProperties, newProperties);
        createAndSaveLogForContent(view, mobileContent, Action.ADD, details, changes);
    }

    @Override
    public void logEditGeoLocationSettings(@Nullable GeoHistorySettings newSettings, GeoHistorySettings oldSettings)
    {
        // newSettings пожет быть null в selenium-тестах, используется для обнуления прочих настроек
        if (newSettings == null)
        {
            return;
        }
        MapProperties newProperties = getGeoLocationSettingsLogInfo(newSettings);
        MapProperties oldProperties = getGeoLocationSettingsLogInfo(oldSettings);
        String changes = getChanges(oldProperties, newProperties);
        createAndSave(Categories.MOBILE_GEO_LOCATION_SETTINGS, "", Categories.EDIT_SETTINGS, changes);
    }

    @Override
    public void logEditNavigationMenuItem(MobileMenuItem item, MobileMenuItemValue parent,
            MapProperties oldProperties, String details)
    {
        MapProperties newProperties = getMobileNavigationSettingsLogInfo(item, parent);
        String changes = getChanges(oldProperties, newProperties);
        createAndSave(Categories.MOBILE_NAVIGATION_SETTINGS, details, "editElement",
                item.getTitle(), item.getTitle(), changes);
    }

    @Override
    public void logEditOtherSettings(@Nullable OtherSettings newSettings, OtherSettings oldSettings)
    {
        // newSettings пожет быть null в selenium-тестах, используется для обнуления прочих настроек
        if (newSettings == null)
        {
            return;
        }
        MapProperties newProperties = getOtherSettingsLogInfo(newSettings);
        MapProperties oldProperties = getOtherSettingsLogInfo(oldSettings);
        String changes = getChanges(oldProperties, newProperties);
        createAndSave(Categories.MOBILE_OTHER_SETTINGS, "", Categories.EDIT_SETTINGS, changes);
    }

    @Override
    public void logEditSecuritySettings(@Nullable OtherSettings newSettings, OtherSettings oldSettings)
    {
        // newSettings пожет быть null в selenium-тестах, используется для обнуления прочих настроек
        if (newSettings == null)
        {
            return;
        }
        MapProperties newProperties = getSecuritySettingsLogInfo(newSettings);
        MapProperties oldProperties = getSecuritySettingsLogInfo(oldSettings);
        String changes = getChanges(oldProperties, newProperties);
        createAndSave(Categories.MOBILE_SECURITY_SETTINGS, "", Categories.EDIT_SETTINGS, changes);
    }

    @Override
    public void logEditView(AbstractMobileView view, MapProperties oldProperties, String details)
    {
        MapProperties newProperties = getMobileContentLogInfo(view);
        String changes = getChanges(oldProperties, newProperties);
        createAndSaveLogFor(view, Action.EDIT, details, changes);
    }

    @Override
    public MapProperties getMobileContentLogInfo(AbstractMobileView mobileContent)
    {
        MapProperties properties = new MapProperties();
        String cName = getCName(mobileContent, true);

        if (mobileContent.getClazz() != null)
        {
            properties.put(cName + ".class", metaInfoService.getMetaClass(mobileContent.getClazz()).getTitle());
        }
        properties.put(cName + ".settingsSet",
                settingsSetService.getSettingsSetLocalizedTitle(mobileContent.getSettingsSet()));
        properties.put(cName + ".cases", mobileContent.getCases()
                .stream()
                .map(metaClassTitleExtractor)
                .collect(Collectors.joining(DELIMITER)));

        properties.put(cName + ".profiles", mobileContent.getProfiles()
                .stream()
                .map(profileTitleExtractor)
                .collect(Collectors.joining(DELIMITER)));

        properties.put(cName + ".tags", mobileContent.getTags()
                .stream()
                .map(tagTitleExtractor)
                .collect(Collectors.joining(DELIMITER)));

        if (mobileContent instanceof CommonMobileView commonMobileView)
        {
            properties.put(cName + ".attributes", commonMobileView.getAttributes()
                    .stream()
                    .map(mobileAttributeStringExtractor)
                    .sorted()
                    .collect(Collectors.joining(DELIMITER)));
        }

        switch (mobileContent)
        {
            case ObjectCard card -> fillContentLogByObjectCard(card, properties, cName);
            case MobileList list -> fillContentLogByMobileList(list, properties, cName);
            case AddForm addForm -> fillContentLogByAddForm(addForm, properties, cName);
            default ->
            { //NOSONAR Idea ругается, на то, что покрыты не все кейсы
            }
        }
        return properties;
    }

    private void fillContentLogByObjectCard(ObjectCard card, MapProperties properties, String cName)
    {
        properties.put(cName + ".objectCaption", card.getObjectCardCaptionAttributeCode());

        List<Transition> copyTransition = card.getTransitions();
        copyTransition.sort(Transition.EXPORT_COMPARATOR);

        List<ObjectAction> copyObjctActions = card.getObjectActions().getActions();
        properties.put(cName + ".transitions", copyTransition.stream()
                .map(TRANSITION_TO_STRING)
                .collect(Collectors.joining(DELIMITER)));

        properties.put(cName + ".comments", formatters.yesNoFormatter(card.getCommentsSettings().isEnabled()));
        properties.put(cName + ".files", formatters.yesNoFormatter(card.getFilesSettings().isEnabled()));

        properties.put(cName + ".objectActions", copyObjctActions.stream()
                .map(OBJECT_ACTIONS_TO_STRING)
                .collect(Collectors.joining(DELIMITER)));

        properties.put(cName + ".useAvailableStates", formatters.yesNoFormatter(card.getWorkflowSettings()
                .getUseAvailableStates()));

        properties.put(cName + ".contents",
                card.getAllContents().stream().map(mobileContentToString).collect(Collectors.joining(",")));

        properties.put(cName + ".quickStateChangeAvailable",
                formatters.yesNoFormatter(card.isQuickStateChangeAvailable()));

        properties.put(cName + ".sendDeviceLocationAvailable",
                formatters.yesNoFormatter(card.isSendDeviceLocationAvailable()));
    }

    private void fillContentLogByMobileList(MobileList list, MapProperties properties, String cName)
    {
        properties.put(cName + ".title", metainfoUtils.getLocalizedValue(list.getTitle()));

        properties.put(cName + ".filter", list.getListFilter().getElements()
                .stream()
                .map(Object::toString)
                .collect(Collectors.joining(DELIMITER)));

        properties.put(cName + ".order", list.getOrders()
                .stream()
                .map(Object::toString)
                .collect(Collectors.joining(DELIMITER)));

        if (list instanceof MobileRelObjectsList relObjectsList)
        {
            properties.put(cName + ".attrChain", relObjectsList.getAttrsChain());
        }

        properties.put(cName + ".allowActionsWithObjects", formatters.yesNoFormatter(list
                .isAllowActionsWithObjects()));
    }

    private void fillContentLogByAddForm(AddForm addForm, MapProperties properties, String cName)
    {
        properties.put(cName + ".title", metainfoUtils.getLocalizedValue(addForm.getTitle()));
        properties.put(cName + ".showInNavigationMenu",
                addForm.isShowInNavigationMenu()); //NOSONAR не указана альтернатива
        properties.put(cName + ".isTransferDeviceGeoPosition", addForm.isTransferDeviceGeoPosition());
        properties.put(cName + ".isVoiceCreationInherited", addForm.getVoiceCreationInherited());
        properties.put(cName + ".isAttrsOnFormInherited", addForm.getAttrsOnFormInherited());
    }

    @Override
    public MapProperties getMobileNavigationSettingsLogInfo(Object item, Object parent)
    {
        final MapProperties properties = new MapProperties();
        if (item instanceof MobileMenuItem menuItem)
        {
            properties.put("mobileMenuItem.title", menuItem.getTitle());
            properties.put("mobileMenuItem.type", menuItem.getType().toString());
            properties.put("mobileMenuItem.tags", menuItem.getTags());
            properties.put(SETTINGS_SET_CODE,
                    settingsSetService.getSettingsSetLocalizedTitle(menuItem.getSettingsSet()));
            properties.put("mobileMenuItem.value", titleExtractor.extractMenuItemValueCode(item));
        }
        if (item instanceof MobileEmbeddedApplicationMenuItem menuItem)
        {
            properties.put("mobileMenuItem.contentCode", menuItem.getContentCode());
            properties.put("mobileMenuItem.embeddedApplication", menuItem.getEmbeddedApplicationCode());
            properties.put("mobileMenuItem.linkObject", menuItem.getLinkObject());
            properties.put("mobileMenuItem.linkObjectCase", menuItem.getLinkObjectCase());
            properties.put("mobileMenuItem.linkObjectAttrs", menuItem.getLinkObjectAttrs());
            properties.put("mobileMenuItem.profiles", menuItem.getProfiles());
            properties.put(SETTINGS_SET_CODE,
                    settingsSetService.getSettingsSetLocalizedTitle(menuItem.getSettingsSet()));
        }
        if (item instanceof MobileMenuItemValue menuItemValue)
        {
            properties.put("mobileMenuItem.title", titleExtractor.extractMenuItemTitle(menuItemValue));
            properties.put("mobileMenuItem.type", menuItemValue.getType().toString());
            properties.put("mobileMenuItem.tags", menuItemValue.getTags());
            properties.put("mobileMenuItem.value", titleExtractor.extractMenuItemValueCode(item));
            properties.put(SETTINGS_SET_CODE,
                    settingsSetService.getSettingsSetLocalizedTitle(menuItemValue.getSettingsSet()));
        }
        if (item instanceof MobileEmbeddedApplicationMenuItemValue menuItemValue)
        {
            properties.put("mobileMenuItem.code", menuItemValue.getContentCode());
            properties.put("mobileMenuItem.embeddedApplication", menuItemValue.getEmbeddedApplicationCode());
            properties.put("mobileMenuItem.linkObject", menuItemValue.getLinkObject());
            properties.put("mobileMenuItem.linkObjectCase", menuItemValue.getLinkObjectCase());
            properties.put("mobileMenuItem.linkObjectAttrs", menuItemValue.getLinkObjectAttrs());
            properties.put("mobileMenuItem.profiles", menuItemValue.getProfiles());
            properties.put(SETTINGS_SET_CODE,
                    settingsSetService.getSettingsSetLocalizedTitle(menuItemValue.getSettingsSet()));
        }
        if (parent instanceof MobileMenuItem)
        {
            properties.put("mobileMenuItem.parent", ((MobileMenuItem)parent).getTitle());
        }
        else if (parent instanceof MobileMenuItemValue)
        {
            properties.put("mobileMenuItem.parent", titleExtractor.extractMenuItemTitle((MobileMenuItemValue)parent));
        }
        return properties;
    }

    @Override
    protected String getActionTypeCode(String category)
    {
        return ACTION_TYPE_CODE;
    }

    @Override
    protected String getChanges(MapProperties oldProperties, MapProperties newProperties)
    {
        String inheritanceDetails = getBlockInheritanceDetails(oldProperties, newProperties,
                "VoiceCreation", "AttrsOnForm");
        return null == inheritanceDetails ? super.getChanges(oldProperties, newProperties) : inheritanceDetails;
    }

    private void createAndSaveLogFor(AbstractMobileView view, Action action, String details,
            @Nullable Object arg)
    {
        String cName = getCName(view, false);
        String category = getCategory(view);

        if (null == arg)
        {
            createAndSave(category, details, action.getCode() + cName,
                    titleExtractor.extractContentTitle(view),
                    descriptionBuilder.buildMobileContentDescription(view));
        }
        else
        {
            createAndSave(category, details, action.getCode() + cName,
                    titleExtractor.extractContentTitle(view),
                    descriptionBuilder.buildMobileContentDescription(view), arg);
        }
    }

    private void createAndSaveLogForContent(ObjectCard card, AbstractMobileContent mobileContent,
            Action action, String details, @Nullable Object arg)
    {
        String cName = getCName(false);
        String category = getCategory(card);

        if (null == arg)
        {
            createAndSave(category, details, action.getCode() + cName, card.getCode(),
                    metainfoUtils.getLocalizedValue(mobileContent.getCaption()), mobileContent.getCode(),
                    descriptionBuilder.buildMobileContentDescription(card));
        }
        else
        {
            createAndSave(category, details, action.getCode() + cName, card.getCode(),
                    metainfoUtils.getLocalizedValue(mobileContent.getCaption()), mobileContent.getCode(),
                    descriptionBuilder.buildMobileContentDescription(card), arg);
        }

    }

    private MapProperties getBarcodeScannerSettingsLogInfo(BarcodeScannerSettings scannerSettings)
    {
        MapProperties properties = new MapProperties();
        properties.put("mobileBarcodeScannerSettings.availableInAttrs", scannerSettings.getAvailableInAttrs()
                .toString());
        properties.put("mobileBarcodeScannerSettings.profiles", scannerSettings.getProfiles().stream().map(
                profileTitleExtractor).collect(Collectors.joining(DELIMITER)));
        return properties;
    }

    @Nullable
    private String getBlockInheritanceDetails(MapProperties oldProperties, MapProperties newProperties,
            String... blockCodes)
    {
        for (String blockCode : blockCodes)
        {
            String propertyCode = String.format("addForm.is%sInherited", blockCode);
            boolean inherit = Boolean.TRUE.equals(newProperties.remove(propertyCode));
            if (inherit ^ Boolean.TRUE.equals(oldProperties.remove(propertyCode)))
            {
                String messageCode = inherit ? "addForm.blockInheritanceRestored" : "addForm.blockInheritanceBroken";
                return messages.getMessage(messageCode, messages.getMessage("addForm.block." + blockCode));
            }
        }
        return null;
    }

    private static String getCName(boolean isFirstCharInLitleCase)
    {
        String cName = "MobileContent";
        if (isFirstCharInLitleCase)
        {
            cName = StringUtils.uncapitalize(cName);
        }
        return cName;
    }

    private static String getCName(AbstractMobileView view, boolean isFirstCharInLitleCase)
    {
        String cName = switch (view)
        {
            case MobileList ignored -> "MobileList";
            case ObjectCard ignored -> "ObjectCard";
            case EditForm ignored -> "EditForm";
            case AddForm ignored -> "AddForm";
            default -> view.getClass().getSimpleName();
        };

        if (isFirstCharInLitleCase)
        {
            cName = StringUtils.uncapitalize(cName);
        }

        return cName;
    }

    private static String getCategory(AbstractMobileView mobileContent)
    {
        return switch (mobileContent)
        {
            case MobileList ignored -> Categories.MOBILE_LIST_SETTINGS;
            case ObjectCard ignored -> Categories.MOBILE_CARD_SETTINGS;
            case AddForm ignored -> Categories.MOBILE_ADD_FORM_SETTINGS;
            case EditForm ignored -> Categories.MOBILE_EDIT_FORM_SETTINGS;
            default -> "mobileSettings";
        };
    }

    private MapProperties getGeoLocationSettingsLogInfo(GeoHistorySettings settings)
    {
        MapProperties properties = new MapProperties();

        String[] enableMovementData = settings.getEnablesMovementAttribute() == null ? null
                : settings.getEnablesMovementAttribute().split("@");
        String classCode = enableMovementData == null ? Constants.Employee.FQN.getCode() : enableMovementData[0];
        String attrCode = enableMovementData == null ? null : enableMovementData[1];
        String enablesMovementAttributeTitle = attrCode == null ? null
                : metaInfoService.getMetaClass(classCode).getAttribute(attrCode).getTitle();
        properties.put("mobileGeolocationSettings.enablesMovementAttribute", enablesMovementAttributeTitle);
        properties.put("mobileGeolocationSettings.frequency",
                replaceNegativeValueByEmpty(settings.getFrequency()));
        properties.put("mobileGeolocationSettings.maxVolume", replaceNegativeValueByEmpty(settings.getMaxVolume()));
        return properties;
    }

    private MapProperties getOtherSettingsLogInfo(OtherSettings settings)
    {
        MapProperties properties = new MapProperties();
        properties.put("mobileOtherSettings.direct", settings.getDirect());
        properties.put("mobileOtherSettings.voiceFillComment", settings.getVoiceFillComment());
        properties.put("mobileOtherSettings.voiceServicesAvailability",
                OtherSettings.OS_TITLES_TO_STRING_CONVERTER.apply(settings.getVoiceServicesAvailability()));
        properties.put("mobileOtherSettings.systemName",
                metainfoUtils.getLocalizedValue(settings.getLocalizedSystemName()));
        return properties;
    }

    private MapProperties getSecuritySettingsLogInfo(OtherSettings settings)
    {
        MapProperties properties = new MapProperties();
        properties.put("mobileSecuritySettings.accessKeyLifetime", settings.getAccessKeyLifetime());
        properties.put("mobileSecuritySettings.passwordStorageTime", settings.getPasswordStorageTime());
        properties.put("mobileSecuritySettings.additionalAuthentication", settings.getAdditionalAuthentication());
        properties.put("mobileSecuritySettings.loginAttemptsCount",
                replaceNegativeValueByEmpty(settings.getLoginAttemptsCount()));
        properties.put("mobileSecuritySettings.loginType", titleExtractor.getLoginTypeTitle(settings.getLoginType()));
        properties.put("mobileSecuritySettings.customLoginForm", settings.getCustomLoginFormCode());
        properties.put("mobileSecuritySettings.customLoginModule", settings.getCustomLoginModuleCode());
        return properties;
    }

    private static String replaceNegativeValueByEmpty(long value)
    {
        return value != -1 ? String.valueOf(value) : "";
    }
}
