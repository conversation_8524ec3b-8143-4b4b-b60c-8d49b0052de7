package ru.naumen.mobile.metainfoadmin.shared;

import net.customware.gwt.dispatch.shared.Action;

/**
 * Действие получения dto мобильной контента
 *
 * <AUTHOR>
 * @since 22.02.2017
 */
public class GetMobileContentDtoAction implements Action<GetMobileContentDtoResponse>
{
    private String uuid;
    private MobileContentType contentType;

    public GetMobileContentDtoAction()
    {
    }

    public GetMobileContentDtoAction(MobileContentType contentType, String uuid)
    {
        this.contentType = contentType;
        this.uuid = uuid;
    }

    public MobileContentType getContentType()
    {
        return contentType;
    }

    public String getUuid()
    {
        return uuid;
    }
}
