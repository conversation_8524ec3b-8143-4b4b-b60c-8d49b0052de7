package ru.naumen.mobile.metainfoadmin.server.othersettings;

import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.embeddedapplication.event.BeforeStopEmbeddedApplicationEvent;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.mobile.MobileSettings;

/**
 * Обработчик события остановки встроенного приложения.<br>
 * Проверка используется ли приложение на пользовательской форме входа в настройках мобильного приложения
 *
 * <AUTHOR>
 * @since 13.06.2023
 */
@Component
public class OtherSettingsBeforeStopEmbeddedApplicationEventListener
        implements ApplicationListener<BeforeStopEmbeddedApplicationEvent>
{
    private final MessageFacade messages;
    private final MetainfoService metainfoService;

    @Inject
    public OtherSettingsBeforeStopEmbeddedApplicationEventListener(
            MessageFacade messages,
            MetainfoService metainfoService)
    {
        this.messages = messages;
        this.metainfoService = metainfoService;
    }

    @Override
    public void onApplicationEvent(BeforeStopEmbeddedApplicationEvent event)
    {
        List<EmbeddedApplication> applications = event.getEmbeddedApplications();
        for (EmbeddedApplication application : applications)
        {
            if (application.getApplicationType() == EmbeddedApplicationType.CustomLoginFormApplication
                && isUsedInMobileCustomLoginForm(application))
            {
                event.addMessage(String.join("\n",
                        messages.getMessage("metainfoValidation.applicationUsedInMKReasonHeader") +
                        messages.getMessage(
                                "metainfoValidation.applicationUsedInOtherSettingsSecurityBlock")));
                event.cancel();
            }
        }
    }

    /**
     * Используется ли ВП на пользовательской форме входа (прочие настройки мобильного приложения)
     */
    private boolean isUsedInMobileCustomLoginForm(EmbeddedApplication application)
    {
        if (application.getApplicationType() == EmbeddedApplicationType.CustomLoginFormApplication)
        {
            final MobileSettings mobileSettings = metainfoService.getMobileSettings();
            return mobileSettings != null
                   && mobileSettings.getOtherSettings() != null
                   && application.getCode().equals(mobileSettings.getOtherSettings().getCustomLoginFormCode());
        }
        return false;
    }
}
