package ru.naumen.mobile.metainfoadmin.client.cards.contents.propertieslist;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiFactory;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;

import ru.naumen.admin.client.widgets.AdminWidgetResources;
import ru.naumen.core.client.content.tabbar.TabBarPopupResources;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplayImpl;
import ru.naumen.core.client.content.toolbar.display.factories.FontIconFactory;
import ru.naumen.core.client.widgets.NauGrid;
import ru.naumen.metainfo.shared.mobile.contents.MobilePropertiesListContent;
import ru.naumen.mobile.metainfoadmin.client.AbstractFlowContentDisplay;
import ru.naumen.mobile.metainfoadmin.client.CommandSelectListCell;
import ru.naumen.mobile.metainfoadmin.client.MobileSettingsMessages;

/**
 * Дисплей контента "Параметры объекта" на карточке мобильного объекта
 * <AUTHOR>
 * @since 21 февр. 2019 г.
 *
 */
public class MobilePropertiesListDisplayImpl extends
        AbstractFlowContentDisplay<MobilePropertiesListContent>
{
    private final NauGrid grid;
    private final HorizontalPanel infoPanel = new HorizontalPanel();
    private final HTML infoText = new HTML(); // NOPMD safe html
    private final FontIconFactory<String> fontIconFactory;

    @Inject
    public MobilePropertiesListDisplayImpl(FontIconFactory<String> fontIconFactory,
            Provider<CommandSelectListCell<MobilePropertiesListContent>> commandSelectListCellProvider,
            AdminWidgetResources widgetResources, MobileSettingsMessages mobileSettingsMessages,
            TabBarPopupResources popupResources)
    {
        super(fontIconFactory, commandSelectListCellProvider, widgetResources, mobileSettingsMessages, popupResources);
        this.fontIconFactory = fontIconFactory;
        grid = new NauGrid();
        grid.addStyleName(widgetResources.mobileContent().mobilePropertiesListAttributesTable());
        grid.resize(1, 2);
        addWidget(grid);

        infoPanel.setStyleName(widgetResources.mobileContent().infoPanel());
        infoPanel.add(infoText);
        add(infoPanel);

        setStyleName(widgetResources.mobileContent().mobileFlowContent());
        getPopup().addStyleName(widgetResources.mobileContent().mobileFlowContentPopup());
    }

    public void addCaptionStyle(String style)
    {
        captionPanel.addStyleName(style);
    }

    public NauGrid getGrid()
    {
        return grid;
    }

    @UiFactory
    public FontIconDisplayImpl<String> makeFontIconDisplayImpl(String code)
    {
        return (FontIconDisplayImpl<String>)fontIconFactory.create(code);
    }

    public void removeCaptionStyle(String style)
    {
        captionPanel.removeStyleName(style);
    }

    public void setInfo(String info)
    {
        infoText.setHTML(SafeHtmlUtils.fromString(info));
    }
}
