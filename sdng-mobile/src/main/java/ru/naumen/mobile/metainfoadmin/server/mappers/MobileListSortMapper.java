package ru.naumen.mobile.metainfoadmin.server.mappers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder;
import ru.naumen.metainfo.shared.mobile.lists.order.AttributeOrder.Order;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.metainfo.shared.ui.ListSortElement;
import ru.naumen.mobile.metainfoadmin.server.mappers.MobileSettingsDtoMapper.TransformationContext;

/**
 * Компонент, служащий для преобразования настроек фильтрации мобильного
 * списка объектов в настройки фильтрации адвлиста и обратно.
 *
 * <AUTHOR>
 * @since 11 июня 2015 г.
 */
@Component
public class MobileListSortMapper
{
    public ListSort transform(List<AttributeOrder> orders, final TransformationContext context)
    {
        ListSort listSort = new ListSort();
        if (CollectionUtils.isEmpty(orders))
        {
            return listSort;
        }
        listSort.setElements(Lists.newArrayList(Collections2.transform(orders, order ->
        {
            final Attribute attribute = context.getAttribute(order.getCode());
            final ListSortElement result = new ListSortElement(order.getCode(), null, order.getOrder().isAscending());
            if (attribute != null)
            {
                result.setAttrTitle(attribute.getTitle());
            }
            return result;
        })));
        return listSort;
    }

    public ArrayList<AttributeOrder> transform(ListSort listSort)
    {
        if (CollectionUtils.isEmpty(listSort.getElements()))
        {
            return new ArrayList<>();
        }
        return Lists.newArrayList(Collections2.transform(listSort.getElements(),
                element -> new AttributeOrder(element.getAttrCode(), element.isAscending() ? Order.ASC : Order.DESC)));
    }
}
