package ru.naumen.mobile.logging;

import static java.util.regex.Pattern.CASE_INSENSITIVE;
import static ru.naumen.core.server.util.log.LogPrepareUtils.BASE64_IMAGES_PLACEHOLDER;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.regex.Pattern;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.util.log.LogPrepareUtils;

/**
 * Утилитарные методы для мобильного API, выполняющие подготовку данных перед выводом в лог.
 * При изменении паттерна поиска base64-картинок в логе, необходимо выполнить аналогичные изменения для в
 * {@link LogPrepareUtils}.
 *
 * <AUTHOR>
 * @since 15.05.2022
 */
public final class MobileLogPrepareUtils
{
    private static final int MAX_STRING_LENGTH = 2000;
    private static final int SHOW_LENGTH = 50;

    // паттерн для удаления картинок в base64 из лога, поддерживающий unicode
    private static final Pattern BASE64_IMAGE_PATTERN_UNICODE = Pattern.compile(
            "(?:\\\\u003c|<)img\\s+?src(?:\\\\u003d|=)\\\\?\"data:[^;]+;base64,.*?(?:\\\\u003e|>)", CASE_INSENSITIVE);

    private MobileLogPrepareUtils()
    {
    }

    /**
     * Заменяет в строке изображения в base64 на надпись "<there is a base64 encoded image>".
     * Поддерживает управляющие HTML-символы, закодированные в Unicode.
     *
     * @param loggedString исходная строка
     * @return строка без изображений в base64
     */
    public static String replaceBase64UnicodeImages(final String loggedString)
    {
        return BASE64_IMAGE_PATTERN_UNICODE.matcher(loggedString).replaceAll(BASE64_IMAGES_PLACEHOLDER);
    }

    /**
     * Заменяет в строковых значениях словаря, изображения в base64 на надпись "<there is a base64 encoded image>".
     * Рассчитан на данные, полученные из JSON, по этой причине не имеет защиты от циклов в словаре.
     *
     * @param map словарь, который может содержать строковые значения с изображениями в base64
     * @return словарь, значения которого не содержат изображений в base64
     */
    @Nullable
    public static Map<String, Object> replaceBase64ImagesInValues(final @Nullable Map<String, Object> map)
    {
        if (map == null)
        {
            return null;
        }

        final Map<String, Object> preparedMap = new HashMap<>();
        for (Entry<String, Object> entry : map.entrySet())
        {
            preparedMap.put(entry.getKey(), replaceBase64ImagesInValue(entry.getValue()));
        }
        return preparedMap;
    }

    @SuppressWarnings("unchecked")
    private static Object replaceBase64ImagesInValue(final Object value)
    {
        if (value instanceof String asString)
        {
            return shortenLongString(LogPrepareUtils.replaceBase64Images(asString));
        }
        if (value instanceof Map<?, ?> asMap)
        {
            return replaceBase64ImagesInValues((Map<String, Object>)asMap);
        }
        return value;
    }

    /**
     * Обрезает логируемую строку, если она слишком длинная для логирования в исходном виде.
     * Строка обрезается до {@link #SHOW_LENGTH} символов, если её длина больше {@link #MAX_STRING_LENGTH} символов.
     * К обрезанной строке добавляется "<long string value(length = " + length + ")>".
     *
     * @param loggedString логируемая строка
     */
    private static String shortenLongString(final String loggedString)
    {
        final int length = loggedString.length();
        if (length > MAX_STRING_LENGTH)
        {
            return loggedString.substring(0, SHOW_LENGTH) + "...<long string value(length = " + length + ")>";
        }
        return loggedString;
    }
}