package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Arrays;
import java.util.NoSuchElementException;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonPrimitive;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.hibernate.HibernateConstants.JsonFunctions;
import ru.naumen.core.server.hibernate.type.JsonType;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Абстрактная реализация {@link HCriterion} для полей внутри JSON.
 * @param <P> тип параметров
 * @since Feb 02, 2024
 */
public abstract class AbstractJsonPathFieldCriterion<P> extends AbstractHCriterion
{
    protected final P[] parameters;
    protected final String fieldPath;

    protected AbstractJsonPathFieldCriterion(HColumn column, String fieldPath, P[] parameters)
    {
        super(column);
        this.parameters = parameters;
        this.fieldPath = fieldPath;
    }

    @Override
    public boolean equals(Object o)
    {
        return super.equals(o) && o instanceof AbstractJsonPathFieldCriterion<?> other
               && Arrays.equals(parameters, other.parameters)
               && Objects.equals(fieldPath, other.fieldPath);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), Arrays.hashCode(parameters), fieldPath);
    }

    @Override
    protected void append(StringBuilder sb, HBuilder builder, NameGenerator parameterCounter)
    {
        sb.append(JsonFunctions.JSON_QUERY)
                .append('(')
                .append(Objects.requireNonNull(property).getHQL(builder))
                .append(", '")
                .append(StringUtils.replace(fieldPath, "'", "''"))
                .append(" ? (");
        appendJsonQuery(sb);
        sb.append(")') is not null");
    }

    protected abstract void appendJsonQuery(StringBuilder sb);

    protected final String formatParameter(int i)
    {
        if (i >= parameters.length || i < 0)
        {
            throw new NoSuchElementException("No parameter " + i + " found.");
        }

        JsonElement element = toJson(parameters[i]);
        return StringUtils.replace((JsonType.GSON.toJson(element)), "'", "''");
    }

    private static JsonElement toJson(@Nullable Object parameter)
    {
        return switch (parameter)
        {
            case String string -> new JsonPrimitive(string);
            case Number number -> new JsonPrimitive(number);
            case Boolean bool -> new JsonPrimitive(bool);
            case Iterable<?> iterable ->
            {
                JsonArray jsonArray = new JsonArray();
                iterable.forEach(item -> jsonArray.add(toJson(item)));
                yield jsonArray;
            }
            case null, default -> JsonNull.INSTANCE;
        };
    }
}
