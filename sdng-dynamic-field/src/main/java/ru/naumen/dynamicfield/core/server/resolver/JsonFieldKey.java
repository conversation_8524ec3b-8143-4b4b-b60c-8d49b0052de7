package ru.naumen.dynamicfield.core.server.resolver;

import jakarta.annotation.Nullable;

/**
 * Дескриптор ключа динамического поля.
 * @param groupUuid UUID группы полей
 * @param templateUuid UUID шаблона динамического поля
 * @param typeId идентификатор типа значения
 *
 * <AUTHOR>
 * @since Dec 14, 2023
 */
public record JsonFieldKey(@Nullable String groupUuid, String templateUuid, @Nullable String typeId)
{
}
