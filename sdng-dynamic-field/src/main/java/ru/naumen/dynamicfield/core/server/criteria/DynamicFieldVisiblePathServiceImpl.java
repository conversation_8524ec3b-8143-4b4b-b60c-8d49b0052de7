package ru.naumen.dynamicfield.core.server.criteria;

import static ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.ANY_NAME;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynamicfield.core.server.dao.DynamicFieldDaoHelper;
import ru.naumen.dynamicfield.core.shared.DynamicFieldMetainfoHelper;
import ru.naumen.dynamicfield.core.shared.DynamicFieldUtils;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Реализация сервиса для работы с путями JSON в запросах к базе данных.
 * <AUTHOR>
 * @since Aor 01, 2024
 */
@Component
public class DynamicFieldVisiblePathServiceImpl implements DynamicFieldVisiblePathService
{
    private final DynamicFieldDaoHelper daoHelper;
    private final DynamicFieldMetainfoHelper metainfoHelper;

    public DynamicFieldVisiblePathServiceImpl(
            DynamicFieldDaoHelper daoHelper,
            DynamicFieldMetainfoHelper metainfoHelper)
    {
        this.daoHelper = daoHelper;
        this.metainfoHelper = metainfoHelper;
    }

    @Override
    public List<JsonFieldPath> getVisiblePaths(Attribute attribute)
    {
        String templateUuid = metainfoHelper.getTemplateUuid(attribute.getPropertyFqn());
        if (templateUuid == null || !UuidHelper.isValid(templateUuid))
        {
            return Collections.emptyList();
        }
        String typeId = DynamicFieldUtils.createTypeId(attribute);
        return daoHelper.getVisibleInListsPaths(Set.of(UuidHelper.toId(templateUuid))).stream()
                .map(templatePath -> new JsonFieldPath(
                        templatePath.groupUuid() == null ? ANY_NAME : templatePath.groupUuid(),
                        templatePath.templateUuid(),
                        typeId,
                        DateTimeIntervalAttributeType.CODE.equals(typeId) ? "ms" : null
                ))
                .toList();
    }

    @Override
    public HCriterion buildFinalRestriction(Attribute attribute, Function<JsonFieldPath, HCriterion> criterionFactory)
    {
        List<JsonFieldPath> visiblePaths = getVisiblePaths(attribute);
        if (visiblePaths.isEmpty())
        {
            return HRestrictions.alwaysTrue();
        }
        else if (visiblePaths.size() == 1)
        {
            return criterionFactory.apply(visiblePaths.getFirst());
        }
        else
        {
            return HRestrictions.or(visiblePaths.stream().map(criterionFactory).toList());
        }
    }
}
