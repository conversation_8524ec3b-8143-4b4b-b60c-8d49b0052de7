package ru.naumen.dynamicfield.core.server.filter;

import static ru.naumen.dynamicfield.core.server.filter.DynamicFieldFunctionUtils.getValueFunctionPattern;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.dynamicfield.core.server.criteria.JsonFieldPath;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Фабрика отдельных условий для фильтрации и сортировки динамических полей без фактической реализации.
 * <AUTHOR>
 * @since Apr 04, 2024
 */
public class DynamicAttributeEmptyRestrictionFactory implements DynamicAttributeRestrictionFactory
{
    @Override
    public HCriterion eq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion notEq(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion less(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion grater(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion lessOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion greaterOrEqual(HColumn column, Attribute attribute, JsonFieldPath path, Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion between(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object begin,
            @Nullable Object end)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion in(HColumn column, Attribute attribute, JsonFieldPath path, @Nullable Object value)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HCriterion contains(HColumn column, Attribute attribute, JsonFieldPath path, String substring,
            boolean ignoreCase)
    {
        return HRestrictions.alwaysTrue();
    }

    @Override
    public HColumn singleValue(HColumn column, Attribute attribute, JsonFieldPath path, String valueFunction)
    {
        return HHelper.getFunctionColumn(getValueFunctionPattern(valueFunction, path), column);
    }
}
