package ru.naumen.dynamicfield.core.server.dao;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.bo.AbstractDao;
import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;

/**
 * Реализация DAO для управления шаблонами динамических полей (системный класс).
 * <AUTHOR>
 * @since Aug 01, 2023
 */
@Component
public class SystemDynamicFieldTemplateDaoImpl extends AbstractDao<IDynamicFieldTemplate>
        implements SystemDynamicFieldTemplateDao
{
    public SystemDynamicFieldTemplateDaoImpl()
    {
        super(FieldTemplate.FQN);
    }
}
