package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.dynamicfield.core.server.criteria.JsonPathComparisonCriterion.Operation;

/**
 * Фабричные методы для наложения условий по полям JSON.
 * <AUTHOR>
 * @since Feb 01, 2024
 */
public final class JsonPathCriterions
{
    private JsonPathCriterions()
    {
    }

    /**
     * Условие «входит в диапазон».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param begin начальная точка диапазона
     * @param end конечная точка диапазона
     * @return результирующее условие
     */
    public static HCriterion between(HColumn column, String path, @Nullable Object begin, @Nullable Object end)
    {
        if (begin != null && end != null)
        {
            return new JsonPathBetweenCriterion(column, path, begin, end);
        }
        else if (begin != null)
        {
            return new JsonPathComparisonCriterion(column, path, begin, Operation.GE);
        }
        else if (end != null)
        {
            return new JsonPathComparisonCriterion(column, path, end, Operation.LE);
        }
        else
        {
            return HRestrictions.alwaysTrue();
        }
    }

    /**
     * Условие «равно».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion eq(HColumn column, String path, @Nullable Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.EQ);
    }

    /**
     * Условие «меньше».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion lt(HColumn column, String path, Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.LT);
    }

    /**
     * Условие «меньше или равно».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion le(HColumn column, String path, Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.LE);
    }

    /**
     * Условие «больше».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion gt(HColumn column, String path, Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.GT);
    }

    /**
     * Условие «больше или равно».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion ge(HColumn column, String path, Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.GE);
    }

    /**
     * Условие «содержит» для коллекций.
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value коллекция значений
     * @return результирующее условие
     */
    public static HCriterion in(HColumn column, String path, @Nullable Object value)
    {
        Set<Object> values;
        if (value instanceof Collection<?> iterable)
        {
            values = new HashSet<>(iterable);
        }
        else if (value == null)
        {
            values = Collections.emptySet();
        }
        else
        {
            values = Collections.singleton(value);
        }
        return new JsonPathInCriterion<>(column, path, values.toArray());
    }

    /**
     * Условие «не равно».
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param value значение, с которым происходит сравнение
     * @return результирующее условие
     */
    public static HCriterion notEq(HColumn column, String path, @Nullable Object value)
    {
        return new JsonPathComparisonCriterion(column, path, value, Operation.NOT_EQ);
    }

    /**
     * Условие «содержит» для строк.
     * @param column колонка, содержащая JSON
     * @param path путь внутри JSON
     * @param substring значение, с которым происходит сравнение
     * @param ignoreCase <code>true</code>, если поиск должен быть регистронезависимым, иначе <code>false</code>
     * @return результирующее условие
     */
    public static HCriterion contains(HColumn column, String path, String substring, boolean ignoreCase)
    {
        return new JsonPathContainsCriterion(column, path, substring, ignoreCase);
    }
}
