package ru.naumen.dynamicfield.core.server.bo;

import java.util.Collections;
import java.util.Date;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.HasFolders;
import ru.naumen.core.server.bo.ICoreBO;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.catalog.FolderCatalogItem;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.HasNumber;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;
import ru.naumen.dynamicfield.core.shared.DynamicFieldUtils;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обертка над объектом пользовательского класса, реализующая интерфейс шаблона динамического поля.
 * <AUTHOR>
 * @since Jul 26, 2023
 */
public class DynamicFieldTemplateWrapper implements IDynamicFieldTemplate
{
    private final ICoreBO delegate;
    private final transient DynamicFieldTemplateAccessorHelper dynamicFieldTemplateAccessorHelper;

    private FieldTypeIdWrapper fieldTypeId;

    DynamicFieldTemplateWrapper(ICoreBO delegate, DynamicFieldTemplateAccessorHelper dynamicFieldTemplateAccessorHelper)
    {
        this.delegate = delegate;
        this.dynamicFieldTemplateAccessorHelper = dynamicFieldTemplateAccessorHelper;
    }

    @Override
    public Employee getAuthor()
    {
        return delegate.getAuthor();
    }

    @Override
    public Date getCreationDate()
    {
        return delegate.getCreationDate();
    }

    @Override
    public IProperties getFlexes()
    {
        return delegate.getFlexes();
    }

    @Override
    public long getId()
    {
        return UuidHelper.toId(delegate.getUUID());
    }

    @Override
    public Long getNumber()
    {
        return delegate instanceof HasNumber withNumber ? withNumber.getNumber() : 0L;
    }

    @Override
    public void setNumber(Long number)
    {
        if (delegate instanceof HasNumber withNumber)
        {
            withNumber.setNumber(number);
        }
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return delegate.getPrefixLoaderUUIDPrefix();
    }

    @Override
    public void setAuthor(Employee author)
    {
        delegate.setAuthor(author);
    }

    @Override
    public String getDescription()
    {
        return dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.DESCRIPTION);
    }

    @Override
    public String getDisplayableTitle()
    {
        return delegate.getDisplayableTitle();
    }

    @Nullable
    @Override
    public String getEditPresentationCode()
    {
        HasCode presentation = dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.EDIT_PRESENTATION);
        return presentation == null ? null : presentation.getCode();
    }

    @Override
    public String getFieldTypeCode()
    {
        FieldTypeIdWrapper typeIdWrapper = getFieldTypeId();
        if (typeIdWrapper != null)
        {
            return DynamicFieldUtils.getTypeCode(typeIdWrapper.typeId());
        }
        return dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.FIELD_TYPE);
    }

    @Override
    public Set<FolderCatalogItem> getFolders()
    {
        return delegate instanceof HasFolders withFolders ? withFolders.getFolders() : Collections.emptySet();
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return delegate.getMetaClass();
    }

    @Override
    public Set<ClassFqn> getObjects()
    {
        FieldTypeIdWrapper typeIdWrapper = getFieldTypeId();
        if (typeIdWrapper != null)
        {
            ClassFqn linkedFqn = DynamicFieldUtils.getRelatedClassFqn(typeIdWrapper.typeId());
            if (linkedFqn != null)
            {
                return Set.of(linkedFqn);
            }
        }
        return dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.OBJECTS);
    }

    @Override
    public Long getOrder()
    {
        return dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.ORDER);
    }

    @Override
    public Date getRemovalDate()
    {
        return delegate.getRemovalDate();
    }

    @Override
    public String getTitle()
    {
        return delegate.getTitle();
    }

    @Override
    public boolean isRemoved()
    {
        return delegate.isRemoved();
    }

    @Override
    public void setRemovalDate(Date removalDate)
    {
        delegate.setRemovalDate(removalDate);
    }

    @Override
    public void setRemoved(boolean removed)
    {
        delegate.setRemoved(removed);
    }

    @Nullable
    @Override
    public String getViewPresentationCode()
    {
        FieldTypeIdWrapper typeIdWrapper = getFieldTypeId();
        if (typeIdWrapper != null && typeIdWrapper.presentationOverride() != null)
        {
            return typeIdWrapper.presentationOverride();
        }
        HasCode presentation = dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.VIEW_PRESENTATION);
        return presentation == null ? null : presentation.getCode();
    }

    @Override
    public boolean isEditable()
    {
        return Boolean.TRUE.equals(
                dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.EDITABLE,
                        true));
    }

    @Override
    public boolean isEditableInLists()
    {
        return Boolean.TRUE.equals(dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.EDITABLE_IN_LISTS, false));
    }

    @Override
    public boolean isHiddenWhenEmpty()
    {
        return Boolean.TRUE.equals(dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.HIDDEN_WHEN_EMPTY, false));
    }

    @Override
    public boolean isHiddenWhenNoPossibleValues()
    {
        return Boolean.TRUE.equals(dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.HIDDEN_WHEN_NO_POSSIBLE_VALUES, false));
    }

    @Override
    public boolean isRequired()
    {
        return Boolean.TRUE.equals(
                dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.REQUIRED,
                        false));
    }

    @Override
    public boolean isRequiredInInterface()
    {
        return Boolean.TRUE.equals(dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate,
                FieldTemplate.REQUIRED_IN_INTERFACE, false));
    }

    @Override
    public String getUUID()
    {
        return delegate.getUUID();
    }

    private FieldTypeIdWrapper getFieldTypeId()
    {
        if (fieldTypeId == null)
        {
            fieldTypeId = dynamicFieldTemplateAccessorHelper.getAttributeValue(delegate, FieldTemplate.FIELD_TYPE_ID);
        }
        return fieldTypeId;
    }
}
