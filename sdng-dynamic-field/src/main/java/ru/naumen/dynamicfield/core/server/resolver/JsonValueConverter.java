package ru.naumen.dynamicfield.core.server.resolver;

import static ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.COMMON_GROUP;
import static ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.VALUE_KEY;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.StreamSupport;

import org.springframework.stereotype.Component;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.hibernate.type.JsonType;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.dynamicfield.core.server.bo.JsonValueMap;
import ru.naumen.dynamicfield.core.server.config.DynamicFieldConfigurationLoader;
import ru.naumen.dynamicfield.core.server.dao.DynamicFieldTemplateDao;
import ru.naumen.dynamicfield.core.shared.DynamicFieldUtils;
import ru.naumen.dynamicfield.core.shared.GroupUuidChecker;
import ru.naumen.dynamicfield.core.shared.JsonAttributeDtObject;
import ru.naumen.dynamicfield.core.shared.TemplateUuidChecker;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Конвертер потенциального значения атрибута типа «Значение динамических полей» в нормализованный вид:
 * <ul>
 *     <li>на первом уровне — группа атрибутов (1 — идентификатор группы);</li>
 *     <li>в каждую группу вложен один или несколько шаблонов (2 — идентификатор шаблона);</li>
 *     <li>по каждому шаблону значения разделены по типам (3 — идентификатор типа);</li>
 *     <li>по каждому типу хранится список значений (4);</li>
 *     <li>каждое значение хранится в виде объекта с метаданными и непосредственно значением (5 — значение).</li>
 * </ul>
 * <AUTHOR>
 * @since Jan 11, 2023
 */
@Component
public class JsonValueConverter
{
    private final TemplateUuidChecker templateUuidChecker;
    private final GroupUuidChecker groupUuidChecker;
    private final DynamicFieldTemplateDao dynamicFieldTemplateDao;
    private final DynamicFieldConfigurationLoader configurationLoader;

    @FunctionalInterface
    public interface SingleValueConverter
    {
        Object convert(@Nullable Attribute parentAttribute, IDynamicFieldTemplate fieldTemplate,
                @Nullable String typeId, @Nullable Object value);
    }

    private record ConversionContext(
            @Nullable Attribute parentAttribute,
            Map<String, IDynamicFieldTemplate> templates,
            SingleValueConverter valueConverter)
    {
    }

    @Inject
    public JsonValueConverter(
            TemplateUuidChecker templateUuidChecker,
            GroupUuidChecker groupUuidChecker,
            DynamicFieldTemplateDao dynamicFieldTemplateDao,
            DynamicFieldConfigurationLoader configurationLoader)
    {
        this.templateUuidChecker = templateUuidChecker;
        this.groupUuidChecker = groupUuidChecker;
        this.dynamicFieldTemplateDao = dynamicFieldTemplateDao;
        this.configurationLoader = configurationLoader;
    }

    /**
     * Конвертирует потенциальное значение атрибута типа «Значение динамических полей» в нормализованный вид.
     * @param attribute атрибут типа «Значение динамических полей»
     * @param rawValue исходное значение
     * @param converter функция для преобразования в JSON значения отдельного динамического поля
     * @return преобразованное значение в нормальной форме
     */
    public JsonValueMap convert(@Nullable Attribute attribute, @Nullable Object rawValue,
            SingleValueConverter converter)
    {
        if (rawValue == null)
        {
            return new JsonValueMapImpl();
        }

        Object value = switch (rawValue)
        {
            case CharSequence jsonString -> JsonType.GSON.fromJson(jsonString.toString(), JsonObject.class);
            case JsonAttributeDtObject dto -> dto.toHierarchy();
            default -> rawValue;
        };

        Set<Long> templateIds = new HashSet<>();
        collectTemplateIds(value, templateIds);
        Map<String, IDynamicFieldTemplate> templates = dynamicFieldTemplateDao.getAsMap(templateIds);
        var context = new ConversionContext(attribute, templates, converter);

        return convert(value, context, true);
    }

    private JsonValueMap convert(@Nullable Object rawValue, ConversionContext context, boolean includeGroups)
    {
        JsonValueMap result = new JsonValueMapImpl();
        forEachEntry(rawValue, (key, value) ->
        {
            if (templateUuidChecker.isTemplateUuid(key))
            {
                JsonValueMap destination;
                if (includeGroups)
                {
                    if (configurationLoader.getConfiguration().getGroupClassFqn() != null)
                    {
                        throw new IllegalArgumentException(String.format("No group specified for template '%s'.", key));
                    }
                    if (result.containsKey(COMMON_GROUP) && result.get(COMMON_GROUP) instanceof JsonValueMap map)
                    {
                        destination = map;
                    }
                    else
                    {
                        destination = new JsonValueMapImpl();
                        result.setProperty(COMMON_GROUP, destination);
                    }
                }
                else
                {
                    destination = result;
                }

                IDynamicFieldTemplate template = context.templates().get(key);
                destination.setProperty(key, convertTemplateValues(template, value, context));
            }
            else if (includeGroups && groupUuidChecker.isGroupUuid(key))
            {
                result.setProperty(key, convert(value, context, false));
            }
            else
            {
                result.setProperty(key, value);
            }
        });
        return result;
    }

    private static JsonValueMap convertTemplateValues(IDynamicFieldTemplate template, @Nullable Object rawValue,
            ConversionContext context)
    {
        JsonValueMap result = new JsonValueMapImpl();
        String templateTypeId = DynamicFieldUtils.createTypeId(template.getFieldTypeCode(), template.getObjects());
        if (isCollectionOfComplexValues(rawValue) || isComplexValue(rawValue))
        {
            result.setProperty(templateTypeId, convertCollection(template, null, rawValue, context));
        }
        else if (isTypedMap(rawValue))
        {
            forEachEntry(rawValue, (key, value) -> result.setProperty(key,
                    convertCollection(template, key, value, context)));
        }
        else
        {
            JsonValueMap valueObject = new JsonValueMapImpl();
            valueObject.setProperty(VALUE_KEY, convertSimpleValue(template, null, rawValue, context));
            List<JsonValueMap> values = new ArrayList<>();
            values.add(valueObject);
            result.setProperty(templateTypeId, values);
        }
        return result;
    }

    private void collectTemplateIds(@Nullable Object rawValue, Set<Long> ids)
    {
        forEachEntry(rawValue, (key, value) ->
        {
            if (templateUuidChecker.isTemplateUuid(key))
            {
                ids.add(UuidHelper.toId(key));
            }
            else if (groupUuidChecker.isGroupUuid(key))
            {
                collectTemplateIds(value, ids);
            }
        });
    }

    private static void forEachEntry(@Nullable Object rawValue, BiConsumer<String, Object> handler)
    {
        if (rawValue instanceof Map<?, ?> map)
        {
            map.forEach((key, value) -> handler.accept(Objects.toString(key), value));
        }
        else if (rawValue instanceof JsonObject jsonObject)
        {
            jsonObject.entrySet().forEach(entry -> handler.accept(entry.getKey(), entry.getValue()));
        }
    }

    private static void forEachItem(@Nullable Object rawValue, Consumer<Object> handler)
    {
        if (rawValue instanceof Iterable<?> iterable)
        {
            iterable.forEach(handler);
        }
    }

    private static boolean isComplexValue(@Nullable Object rawValue)
    {
        if (rawValue instanceof IUUIDIdentifiable)
        {
            return false;
        }
        if (rawValue instanceof Map<?, ?> map)
        {
            return map.containsKey(VALUE_KEY);
        }
        else if (rawValue instanceof JsonObject jsonObject)
        {
            return jsonObject.has(VALUE_KEY);
        }
        return false;
    }

    private static boolean isCollectionOfComplexValues(@Nullable Object rawValue)
    {
        if (rawValue instanceof Iterable<?> iterable)
        {
            return StreamSupport.stream(iterable.spliterator(), false).allMatch(JsonValueConverter::isComplexValue);
        }
        return false;
    }

    private static boolean isTypedMap(@Nullable Object rawValue)
    {
        if (rawValue instanceof IUUIDIdentifiable)
        {
            return false;
        }
        if (rawValue instanceof Map<?, ?> map)
        {
            return map.values().stream().allMatch(value -> isComplexValue(value) || isCollectionOfComplexValues(value));
        }
        else if (rawValue instanceof JsonObject jsonObject)
        {
            return jsonObject.entrySet().stream().map(Entry::getValue)
                    .allMatch(value -> isComplexValue(value) || isCollectionOfComplexValues(value));
        }
        return false;
    }

    private static JsonValueMap convertComplexValue(IDynamicFieldTemplate dynamicFieldTemplate, @Nullable String typeId,
            @Nullable Object rawValue, ConversionContext context)
    {
        JsonValueMap result = new JsonValueMapImpl();
        forEachEntry(rawValue, (key, value) ->
        {
            if (VALUE_KEY.equals(key))
            {
                result.setProperty(key, convertSimpleValue(dynamicFieldTemplate, typeId, value, context));
            }
            else
            {
                result.setProperty(key, value instanceof JsonElement jsonElement
                        ? JsonResolverUtils.convertFromJson(jsonElement) : value);
            }
        });
        return result;
    }

    private static Object convertSimpleValue(IDynamicFieldTemplate dynamicFieldTemplate, @Nullable String typeId,
            @Nullable Object rawValue, ConversionContext context)
    {
        Attribute parentAttribute = context.parentAttribute();
        return context.valueConverter().convert(parentAttribute, dynamicFieldTemplate, typeId, rawValue);
    }

    private static List<JsonValueMap> convertCollection(IDynamicFieldTemplate dynamicFieldTemplate,
            @Nullable String typeId, @Nullable Object rawValue, ConversionContext context)
    {
        List<JsonValueMap> result = new ArrayList<>();
        if (isComplexValue(rawValue))
        {
            result.add(convertComplexValue(dynamicFieldTemplate, typeId, rawValue, context));
        }
        else if (isCollectionOfComplexValues(rawValue))
        {
            forEachItem(rawValue, value -> result.add(
                    convertComplexValue(dynamicFieldTemplate, typeId, value, context)));
        }
        return result;
    }
}
