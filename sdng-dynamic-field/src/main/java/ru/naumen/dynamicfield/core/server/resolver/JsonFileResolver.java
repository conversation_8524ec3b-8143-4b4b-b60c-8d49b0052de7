package ru.naumen.dynamicfield.core.server.resolver;

import java.util.Optional;
import java.util.stream.StreamSupport;

import org.springframework.stereotype.Component;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.IUUIDIdentifiable;

@Component
public class JsonFileResolver
{
    public JsonArray resolve(@Nullable Object rawValue)
    {
        JsonArray result = new JsonArray();
        if (rawValue instanceof Iterable<?> collection)
        {
            StreamSupport.stream(collection.spliterator(), false)
                    .map(JsonFileResolver::resolveSingleValue)
                    .forEach(value -> value.ifPresent(result::add));
        }
        else
        {
            resolveSingleValue(rawValue).ifPresent(result::add);
        }
        return result;
    }

    private static Optional<JsonElement> resolveSingleValue(@Nullable Object rawValue)
    {
        if (rawValue == null)
        {
            return Optional.empty();
        }

        return switch (rawValue)
        {
            case IUUIDIdentifiable hasUuid -> Optional.of(new JsonPrimitive(hasUuid.getUUID()));
            case Number number -> Optional.of(new JsonPrimitive(number.longValue()));
            case String uuid -> Optional.of(new JsonPrimitive(uuid));
            case JsonPrimitive jsonPrimitive -> jsonPrimitive.isNumber() || jsonPrimitive.isString()
                    ? Optional.of(jsonPrimitive) : Optional.empty();
            default -> Optional.empty();
        };
    }
}
