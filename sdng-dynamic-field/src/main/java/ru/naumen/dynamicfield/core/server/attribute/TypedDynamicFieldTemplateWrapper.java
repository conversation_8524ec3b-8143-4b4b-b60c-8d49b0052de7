package ru.naumen.dynamicfield.core.server.attribute;

import java.util.Collections;
import java.util.Date;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.Snapshot;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.catalog.FolderCatalogItem;
import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обертка над шаблоном, позволяющая переопределить информацию о типе.
 * <AUTHOR>
 * @since Dec 25, 2023
 */
public class TypedDynamicFieldTemplateWrapper implements IDynamicFieldTemplate
{
    private final IDynamicFieldTemplate delegate;
    private final String typeCode;
    private final ClassFqn relatedClassFqn;

    public TypedDynamicFieldTemplateWrapper(IDynamicFieldTemplate delegate, String typeCode,
            @Nullable ClassFqn relatedClassFqn)
    {
        this.delegate = delegate;
        this.typeCode = typeCode;
        this.relatedClassFqn = relatedClassFqn;
    }

    @Override
    public long getId()
    {
        return delegate.getId();
    }

    @Override
    public String getUUID()
    {
        return delegate.getUUID();
    }

    @Override
    public String getDescription()
    {
        return delegate.getDescription();
    }

    @Override
    public String getEditPresentationCode()
    {
        return null;
    }

    @Override
    public String getFieldTypeCode()
    {
        return typeCode;
    }

    @Override
    public Set<ClassFqn> getObjects()
    {
        return relatedClassFqn == null ? Collections.emptySet() : Collections.singleton(relatedClassFqn);
    }

    @Override
    public Long getOrder()
    {
        return delegate.getOrder();
    }

    @Override
    public String getViewPresentationCode()
    {
        return null;
    }

    @Override
    public boolean isEditable()
    {
        return delegate.isEditable();
    }

    @Override
    public boolean isEditableInLists()
    {
        return delegate.isEditableInLists();
    }

    @Override
    public boolean isHiddenWhenEmpty()
    {
        return delegate.isHiddenWhenEmpty();
    }

    @Override
    public boolean isHiddenWhenNoPossibleValues()
    {
        return delegate.isHiddenWhenNoPossibleValues();
    }

    @Override
    public boolean isRequired()
    {
        return delegate.isRequired();
    }

    @Override
    public boolean isRequiredInInterface()
    {
        return delegate.isRequiredInInterface();
    }

    @Override
    public Date getRemovalDate()
    {
        return delegate.getRemovalDate();
    }

    @Override
    public boolean isRemoved()
    {
        return delegate.isRemoved();
    }

    @Override
    public void setRemovalDate(Date removalDate)
    {
        delegate.setRemovalDate(removalDate);
    }

    @Override
    public void setRemoved(boolean removed)
    {
        delegate.setRemoved(removed);
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return delegate.getMetaClass();
    }

    @Override
    @Snapshot
    public String getTitle()
    {
        return delegate.getTitle();
    }

    @Override
    public Set<FolderCatalogItem> getFolders()
    {
        return delegate.getFolders();
    }

    @Override
    public String getDisplayableTitle()
    {
        return delegate.getDisplayableTitle();
    }

    @Override
    public Employee getAuthor()
    {
        return delegate.getAuthor();
    }

    @Override
    public void setAuthor(Employee author)
    {
        delegate.setAuthor(author);
    }

    @Override
    public IProperties getFlexes()
    {
        return delegate.getFlexes();
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return delegate.getPrefixLoaderUUIDPrefix();
    }

    @Override
    @Snapshot
    public Long getNumber()
    {
        return delegate.getNumber();
    }

    @Override
    public void setNumber(Long number)
    {
        delegate.setNumber(number);
    }

    @Override
    public Date getCreationDate()
    {
        return delegate.getCreationDate();
    }
}
