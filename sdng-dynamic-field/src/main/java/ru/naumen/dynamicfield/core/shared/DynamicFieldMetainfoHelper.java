package ru.naumen.dynamicfield.core.shared;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Компонент для получения метаинформации, связанной с шаблонами динамических полей.
 * <AUTHOR>
 * @since Jul 31, 2023
 */
public interface DynamicFieldMetainfoHelper extends TemplateUuidChe<PERSON>, GroupUuidChecker
{
    ClassFqn getTemplateClassFqn();

    @Nullable
    ClassFqn getGroupClassFqn();

    default String getTemplateClassId()
    {
        return getTemplateClassFqn().getId();
    }

    @Nullable
    default String getGroupClassId()
    {
        return getGroupClassFqn() == null ? null : getGroupClassFqn().getId();
    }

    default boolean isCustomTemplateClassDefined()
    {
        return !getTemplateClassFqn().equals(FieldTemplate.FQN);
    }

    @Override
    default boolean isTemplateUuid(String uuid)
    {
        return UuidHelper.isValid(uuid) && ClassFqnHelper.toClassId(uuid).equals(getTemplateClassFqn());
    }

    @Override
    default boolean isGroupUuid(String uuid)
    {
        ClassFqn fqn = getGroupClassFqn();
        return DynamicFieldConstants.COMMON_GROUP.equals(uuid)
               || fqn != null && UuidHelper.isValid(uuid) && fqn.equals(ClassFqnHelper.toClassId(uuid));
    }

    @Nullable
    default String getTemplateUuid(String fieldName)
    {
        String[] parts = fieldName.split(DynamicFieldUtils.FIELD_DELIMITER_REGEX, 3);
        if (parts.length <= 1)
        {
            return null;
        }
        return UuidHelper.toUuid(parts[1], getTemplateClassId());
    }
}
