package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Objects;

import ru.naumen.core.server.hibernate.HibernateConstants.JsonFunctions;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Реализация условия фильтрации по наличию пустых значений с помощью SQL-функции.
 * <AUTHOR>
 * @since Mar 25, 2024
 */
public class JsonSqlNullFilterCriterion extends AbstractJsonSqlFunctionFilterCriterion<String>
{
    public JsonSqlNullFilterCriterion(HColumn property, JsonFieldPath fieldPath)
    {
        super(property, JsonFunctions.JSON_FILTER_NULL, fieldPath, new String[0]);
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new JsonSqlNullFilterCriterion(Objects.requireNonNull(property), fieldPath);
    }
}
