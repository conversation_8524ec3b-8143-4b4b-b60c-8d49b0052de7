package ru.naumen.dynamicfield.core.server.attribute;

import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.cache.transaction.readonly.TransactionReadOnlyCache;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * Кэш снэпшотов для метаклассов и моделей атрибутов динамических полей.
 * Позволяет избежать повторного создания одних и тех же снэпшотов.
 * <AUTHOR>
 * @since Jul 19, 2024
 */
@Component
public class DynamicAttributeSnapshotCache
{
    private final TransactionReadOnlyCache transactionCache;

    private record SnapshotCacheKey(String fqn, ValueType type)
    {
    }

    private enum ValueType
    {
        METACLASS_LITE, ATTRIBUTE;
    }

    @Inject
    public DynamicAttributeSnapshotCache(TransactionReadOnlyCache transactionCache)
    {
        this.transactionCache = transactionCache;
    }

    public Attribute getAttribute(AttributeFqn fqn, Supplier<Attribute> snapshotProvider)
    {
        return computeIfAbsent(new SnapshotCacheKey(fqn.toString(), ValueType.ATTRIBUTE), snapshotProvider,
                Attribute.class);
    }

    public MetaClassLite getMetaClassLite(ClassFqn fqn, Supplier<MetaClassLite> snapshotProvider)
    {
        return computeIfAbsent(new SnapshotCacheKey(fqn.asString(), ValueType.METACLASS_LITE), snapshotProvider,
                MetaClassLite.class);
    }

    private <T> T computeIfAbsent(SnapshotCacheKey cacheKey, Supplier<T> snapshotProvider, Class<T> valueClass)
    {
        T value = transactionCache.get(cacheKey, valueClass);
        if (value == null)
        {
            value = snapshotProvider.get();
            transactionCache.put(cacheKey, value);
        }
        return value;
    }
}
