package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Objects;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Условие «содержится в диапазоне» для значений в динамических полях.
 * <AUTHOR>
 * @since Feb 01, 2024
 */
public class JsonPathBetweenCriterion extends AbstractJsonPathFieldCriterion<Object>
{
    public JsonPathBetweenCriterion(HColumn column, String fieldPath, Object begin, Object end)
    {
        super(column, fieldPath, new Object[] { begin, end });
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new JsonPathBetweenCriterion(Objects.requireNonNull(property), fieldPath, parameters[0], parameters[1]);
    }

    @Override
    protected void appendJsonQuery(StringBuilder sb)
    {
        sb.append("@ >= ")
                .append(formatParameter(0))
                .append(" && @ <= ")
                .append(formatParameter(1));
    }
}
