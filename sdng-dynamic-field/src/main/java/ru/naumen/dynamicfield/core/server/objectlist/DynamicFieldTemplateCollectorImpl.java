package ru.naumen.dynamicfield.core.server.objectlist;

import java.util.Set;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.dynamicfield.core.server.dao.DynamicFieldDaoHelper;
import ru.naumen.dynamicfield.core.server.dao.TemplatePath;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListSort;
import ru.naumen.objectlist.server.advlist.dataprovider.criteriagenerator.ObjectListCriteriaGenerator;
import ru.naumen.objectlist.shared.ObjectListClientState;
import ru.naumen.objectlist.shared.ObjectListDataContext;
import ru.naumen.objectlist.shared.ObjectListDataContextImpl;

/**
 * Реализация сборщика шаблонов динамических полей, используемых среди объектов списка.
 * <AUTHOR>
 * @since Jul 20, 2024
 */
@Component
public class DynamicFieldTemplateCollectorImpl implements DynamicFieldTemplateCollector
{
    private final DynamicFieldDaoHelper dynamicFieldDaoHelper;
    private final ObjectListCriteriaGenerator criteriaGenerator;

    @Inject
    public DynamicFieldTemplateCollectorImpl(
            DynamicFieldDaoHelper dynamicFieldDaoHelper,
            ObjectListCriteriaGenerator criteriaGenerator)
    {
        this.dynamicFieldDaoHelper = dynamicFieldDaoHelper;
        this.criteriaGenerator = criteriaGenerator;
    }

    @Override
    public Set<TemplatePath> collectTemplates(Attribute jsonAttribute, ObjectListDataContext dataContext)
    {
        ObjectListClientState clientState = new ObjectListClientState();
        clientState.setFormObjectUuid(dataContext.getClientSettings().getFormObjectUuid());
        clientState.setFormObjectFqn(dataContext.getClientSettings().getFormObjectFqn());
        clientState.setFormCode(dataContext.getClientSettings().getFormCode());
        clientState.setOnAddForm(dataContext.getClientSettings().isOnAddForm());

        ObjectListDataContextImpl context = new ObjectListDataContextImpl(dataContext.getContent(), new ListFilter(),
                new ListSort(), clientState);
        DtoCriteria criteria = criteriaGenerator.generate(context);

        return dynamicFieldDaoHelper.getVisibleInListPaths(criteria.getClassFqn(), jsonAttribute.getFqn(),
                criteria.getFilters());
    }
}
