package ru.naumen.dynamicfield.core.shared.objectlist;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.dynamicfield.core.shared.DynamicFieldDto;
import ru.naumen.dynamicfield.core.shared.DynamicFieldUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.objectlist.shared.attribute.MultiColumnAttribute;

/**
 * Динамический атрибут, который содержит в себе несколько колонок.
 * <AUTHOR>
 * @since Aug 14, 2022
 */
public class DynamicMultiColumnAttribute implements MultiColumnAttribute
{
    @Serial
    private static final long serialVersionUID = -4975759747102448294L;

    private String code;
    private Map<String, DynamicFieldDto> fieldMap = new HashMap<>();
    private Map<String, Attribute> attributeMap = new HashMap<>(); //NOSONAR
    private MapProperties permissionMetaData;
    private Integer defaultColumnLimit;

    public DynamicMultiColumnAttribute()
    {
        // for serialization
    }

    @Override
    public boolean equals(Object other)
    {
        if (other instanceof DynamicMultiColumnAttribute) // NOSONAR
        {
            DynamicMultiColumnAttribute attribute = (DynamicMultiColumnAttribute)other;
            return Objects.equals(getCode(), attribute.getCode())
                   && Objects.equals(getAttributeCodes(), attribute.getAttributeCodes());
        }
        return false;
    }

    @Override
    public boolean compatibleWith(MultiColumnAttribute other)
    {
        return other instanceof DynamicMultiColumnAttribute;
    }

    @Override
    public void mergeWith(MultiColumnAttribute other)
    {
        if (other instanceof DynamicMultiColumnAttribute) // NOSONAR
        {
            DynamicMultiColumnAttribute otherAttribute = (DynamicMultiColumnAttribute)other;
            fieldMap.putAll(otherAttribute.fieldMap);
            updateAttributeMap();
            if (otherAttribute.permissionMetaData != null)
            {
                otherAttribute.permissionMetaData.forEach(this::setPermissionMetaData);
            }
        }
    }

    @Override
    public List<String> getAttributeCodes()
    {
        return fieldMap.entrySet().stream()
                .sorted(Entry.comparingByValue())
                .map(Entry::getKey)
                .collect(Collectors.toCollection(ArrayList::new));
    }

    @Override
    public List<String> getDefaultAttributeCodes()
    {
        Integer limit = getDefaultColumnLimit();
        List<String> attributeCodes = getAttributeCodes();
        return limit == null
                ? attributeCodes
                : attributeCodes.subList(0, Math.min(limit, attributeCodes.size()));
    }

    @Override
    public Map<String, Attribute> getAttributeMap()
    {
        return Collections.unmodifiableMap(attributeMap);
    }

    @Override
    public boolean mayContain(String attributeCode)
    {
        return fieldMap.containsKey(attributeCode) || DynamicFieldUtils.isDynamicFieldName(attributeCode)
                                                      && DynamicFieldUtils.getAttributeCode(attributeCode)
                                                              .equals(getCode());
    }

    public Integer getDefaultColumnLimit()
    {
        return defaultColumnLimit;
    }

    public String getCode()
    {
        return code;
    }

    @Nullable
    @Override
    public <R> R getPermissionMetaData(String key)
    {
        return permissionMetaData == null ? null : permissionMetaData.getProperty(key);
    }

    @Override
    public Set<String> permissionMetaDataKeys()
    {
        return permissionMetaData == null
                ? Collections.emptySet()
                : Collections.unmodifiableSet(permissionMetaData.keySet());
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(getCode(), getAttributeCodes());
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setFieldMap(Map<String, DynamicFieldDto> fieldMap)
    {
        this.fieldMap = fieldMap;
        updateAttributeMap();
    }

    private void updateAttributeMap()
    {
        attributeMap = new HashMap<>();
        fieldMap.forEach((fqn, fieldDto) -> attributeMap.put(fqn, fieldDto.getAttribute()));
    }

    public void setPermissionMetaData(String key, Object value)
    {
        if (permissionMetaData == null)
        {
            permissionMetaData = new MapProperties();
        }
        permissionMetaData.setProperty(key, value);
    }

    public void setDefaultColumnLimit(@Nullable Integer defaultColumnLimit)
    {
        this.defaultColumnLimit = defaultColumnLimit;
    }
}
