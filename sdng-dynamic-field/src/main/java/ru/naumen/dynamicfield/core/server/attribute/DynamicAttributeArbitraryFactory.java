package ru.naumen.dynamicfield.core.server.attribute;

import org.springframework.stereotype.Component;

import ru.naumen.dynamicfield.core.server.bo.IDynamicFieldTemplate;
import ru.naumen.metainfo.shared.Constants.ArbitraryAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MutableAttributeType_SnapshotObject;

/**
 * Логика инициализации атрибутов для логических полей произвольного типа.
 * <AUTHOR>
 * @since Feb 13, 2024
 */
@Component
public class DynamicAttributeArbitraryFactory implements DynamicAttributeTypeFactory
{
    @Override
    public String getTypeCode()
    {
        return ArbitraryAttributeType.CODE;
    }

    @Override
    public AttributeType create(Attribute dynAttribute, IDynamicFieldTemplate fieldTemplate)
    {
        MutableAttributeType_SnapshotObject attributeType = new MutableAttributeType_SnapshotObject();
        attributeType.__init__code(ArbitraryAttributeType.CODE);
        attributeType.__init__attribute(dynAttribute);
        return attributeType;
    }
}
