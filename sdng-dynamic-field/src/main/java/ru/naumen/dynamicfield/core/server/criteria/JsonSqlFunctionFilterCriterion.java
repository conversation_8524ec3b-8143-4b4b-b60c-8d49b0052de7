package ru.naumen.dynamicfield.core.server.criteria;

import java.util.Objects;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Реализация условия фильтрации с помощью SQL-функции (с одним значащим параметром).
 * <AUTHOR>
 * @since Mar 25, 2024
 */
public class JsonSqlFunctionFilterCriterion extends AbstractJsonSqlFunctionFilterCriterion<Object>
{
    public JsonSqlFunctionFilterCriterion(HColumn property, String functionName, JsonFieldPath fieldPath, Object value)
    {
        super(property, functionName, fieldPath, new Object[] { value });
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new JsonSqlFunctionFilterCriterion(Objects.requireNonNull(property), functionName, fieldPath,
                parameters[0]);
    }
}
