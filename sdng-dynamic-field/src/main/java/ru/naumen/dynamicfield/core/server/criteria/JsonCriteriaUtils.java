package ru.naumen.dynamicfield.core.server.criteria;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.DateWithoutTimeDto;
import ru.naumen.core.server.attrdescription.resolvers.DateTimeResolver;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.DateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Утилитарные методы для работы с условиями по динамическим полям.
 * <AUTHOR>
 * @since Apr 04, 2024
 */
public final class JsonCriteriaUtils
{
    private JsonCriteriaUtils()
    {
    }

    @Nullable
    private static Object prepareDateValue(Attribute attribute, @Nullable Object value)
    {
        if (!(value instanceof Date date))
        {
            return value;
        }
        if (date instanceof DateWithoutTimeDto || DateAttributeType.CODE.equals(attribute.getType().getCode()))
        {
            SimpleDateFormat dateFormat = new SimpleDateFormat(DateTimeResolver.DATE_FORMAT_INT);
            return Long.parseLong(dateFormat.format(date));
        }
        return date.getTime();
    }

    @Nullable
    public static Object prepareValue(Attribute attribute, @Nullable Object value)
    {
        if (value instanceof Collection<?> collection)
        {
            return collection.stream()
                    .map(object -> prepareValue(attribute, object))
                    .collect(Collectors.toSet());
        }
        if (Constants.DATE_TIME_TYPES.contains(attribute.getType().getCode()))
        {
            return prepareDateValue(attribute, value);
        }
        if (Constants.OBJECT_ATTR_TYPES.contains(attribute.getType().getCode()))
        {
            if (value instanceof IUUIDIdentifiable hasUuid)
            {
                return UuidHelper.toId(hasUuid.getUUID());
            }
            if (value instanceof String uuid)
            {
                return UuidHelper.toId(uuid);
            }
        }
        if (value instanceof DateTimeInterval dateTimeInterval)
        {
            return dateTimeInterval.getMs();
        }
        return value;
    }

    public static Object prepareNonNullValue(Attribute attribute, Object value)
    {
        return Objects.requireNonNull(prepareValue(attribute, value));
    }
}
