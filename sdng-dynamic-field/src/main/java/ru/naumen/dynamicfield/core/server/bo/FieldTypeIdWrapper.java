package ru.naumen.dynamicfield.core.server.bo;

import java.io.Serializable;

import jakarta.annotation.Nullable;

/**
 * Обертка для идентификатора типа.
 * @param typeId идентификатор типа
 * @param presentationOverride код переопределенного представления, если есть
 * <AUTHOR>
 * @since Aug 31, 2024
 */
public record FieldTypeIdWrapper(String typeId, @Nullable String presentationOverride)
        implements Serializable
{
}
