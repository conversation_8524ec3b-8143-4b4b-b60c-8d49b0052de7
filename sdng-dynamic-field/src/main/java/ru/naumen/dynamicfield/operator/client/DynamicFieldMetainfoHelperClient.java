package ru.naumen.dynamicfield.operator.client;

import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.FieldTemplate;
import ru.naumen.dynamicfield.core.shared.DynamicFieldConstants.SharedSettings;
import ru.naumen.dynamicfield.core.shared.DynamicFieldMetainfoHelper;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Браузерная реализация компонента для получения метаинформации, связанной с шаблонами динамических полей.
 * <AUTHOR>
 * @since Jul 31, 2023
 */
@Singleton
public class DynamicFieldMetainfoHelperClient implements DynamicFieldMetainfoHelper
{
    @Override
    public ClassFqn getTemplateClassFqn()
    {
        String classId = getSharedValue(SharedSettings.TEMPLATE_CLASS_ID);
        return classId == null ? FieldTemplate.FQN : ClassFqn.parse(classId);
    }

    @Nullable
    @Override
    public ClassFqn getGroupClassFqn()
    {
        String classId = getSharedValue(SharedSettings.GROUP_CLASS_ID);
        return classId == null ? null : ClassFqn.parse(classId);
    }

    @Nullable
    private static native String getSharedValue(String key)
    /*-{
        return $wnd.sharedSettings[key];
    }-*/;
}
