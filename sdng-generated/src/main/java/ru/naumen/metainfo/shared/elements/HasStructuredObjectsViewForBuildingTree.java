package ru.naumen.metainfo.shared.elements;

import jakarta.annotation.Nullable;
import ru.naumen.common.shared.Snapshotable;

/**
 * Интерфейс объекта, имеющего свойство "Структура для построения дерева".
 *
 * <AUTHOR>
 * @since 19.03.2025
 */
public interface HasStructuredObjectsViewForBuildingTree extends Snapshotable
{
    /**
     * @return код структуры для построения дерева
     */
    @Nullable
    String getStructuredObjectsViewForBuildingTreeCode();

    /**
     * Задать код структуры для построения дерева.
     * @param structuredObjectsViewForBuildingTreeCode код структуры
     */
    void setStructuredObjectsViewForBuildingTreeCode(@Nullable String structuredObjectsViewForBuildingTreeCode);
}