package ru.naumen.metainfo.shared.permissioncheck;

import java.io.Serializable;

import com.google.gwt.user.client.rpc.IsSerializable;

import ru.naumen.common.shared.Snapshotable;

/**
 * Объект с возможностью проверки прав в интерфейсе администратора.
 *
 * <AUTHOR>
 * @since 28.06.2024
 */
public interface HasAdminPermissionCategory extends IsSerializable, Serializable, Snapshotable
{
    /*
     * Получить информацию о категории прав, к которой относиться объект, для возможности проверки прав
     * в интерфейсе администратора.
     */
    String getAdminPermissionCategory();
}