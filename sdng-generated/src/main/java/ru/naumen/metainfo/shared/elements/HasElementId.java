package ru.naumen.metainfo.shared.elements;

/**
 * Эле<PERSON><PERSON><PERSON><PERSON>, который может быть идентифицирован по типу и локально-уникальному коду.
 * <AUTHOR>
 * @since Jul 24, 2024
 */
public interface HasElementId
{
    /**
     * Возвращает тип элемента.
     * @return код типа элемента
     */
    String getElementType();

    /**
     * Возвращает локальный идентификатор элемента (код).
     * @return локально-уникальный идентификатор элемента
     */
    String getElementCode();
}
