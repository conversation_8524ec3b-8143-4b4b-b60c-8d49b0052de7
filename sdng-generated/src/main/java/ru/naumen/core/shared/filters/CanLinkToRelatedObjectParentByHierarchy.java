package ru.naumen.core.shared.filters;

/**
 * Фильтры, учитывающие в своей работе, что атрибут является атрибутом связанного объекта 'Родитель'
 * и указано, что необходимо пройтись по родителям без ограничения иерархии
 *
 * <AUTHOR>
 * @since 16.04.2025
 */
@FunctionalInterface
public interface CanLinkToRelatedObjectParentByHierarchy
{
    /**
     * Проставить флаг, что атрибут является атрибутом связанного объекта 'Родитель'
     * и указано, что необходимо пройтись по родителям без ограничения иерархии
     */
    void setLinkToRelatedObjectParent(boolean isLinkToRelatedObjectParent);
}
