package ru.naumen.core.shared.utils;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Вспомогательные методы для работы с FQN класса
 * <AUTHOR>
 * @since 25.11.2024
 */
public final class ClassFqnHelper
{
    private ClassFqnHelper()
    {
    }

    /**
     * Формирует FQN класса объекта из его уникального идентификатора
     * @param uuid идентификатор объекта
     */
    public static ClassFqn toClassId(String uuid)
    {
        return ClassFqn.parse(UuidHelper.toPrefix(uuid));
    }

    /**
     * Формирует FQN класса объекта из его UUID
     *
     * @param uuid идентификатор объекта
     * @return извлечённый FQN или null, если переданный UUID - null
     */
    @Nullable
    public static ClassFqn toClassIdSafe(@Nullable String uuid)
    {
        return (uuid != null) ? toClassId(uuid) : null;
    }
}
