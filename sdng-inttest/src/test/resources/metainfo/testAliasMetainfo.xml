<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<metainfoContainer>
    <head>
        <date>2024-04-04T17:43:42.712+03:00</date>
        <exportMode>partial</exportMode>
        <version>4.18.5-SNAPSHOT</version>
    </head>
    <tags/>
    <sets/>
    <system-metaclass seg-detach="true" seg-id="employee" seg-type="metaclasses">
        <fqn>
            <id>employee</id>
        </fqn>
        <parent>
            <id>abstractBO</id>
        </parent>
        <title lang="en">Employee</title>
        <title lang="de">Mitarbeiter</title>
        <title lang="ru">Сотрудник</title>
        <properties/>
        <tags/>
        <responsibilityTransferTableEnabled>true</responsibilityTransferTableEnabled>
        <attributes>
            <attribute seg-detach="true" seg-id="alias" seg-type="attribute">
                <code>alias</code>
                <hiddenAttrCaption>false</hiddenAttrCaption>
                <computable>false</computable>
                <editable>true</editable>
                <editableInLists>false</editableInLists>
                <required>false</required>
                <requiredInInterface>false</requiredInInterface>
                <unique>false</unique>
                <determinable>false</determinable>
                <title lang="ru">testAttrString</title>
                <description lang="ru"/>
                <type>
                    <code>string</code>
                    <property code="code">string</property>
                    <property code="inputMask"/>
                    <property code="inputMaskMode"/>
                    <property code="string">255</property>
                </type>
                <filteredByScript>false</filteredByScript>
                <computableOnForm>false</computableOnForm>
                <viewPresentation>
                    <code>stringView</code>
                </viewPresentation>
                <editPresentation>
                    <code>stringEdit</code>
                </editPresentation>
                <useGenerationRule>false</useGenerationRule>
                <accessor>flexAccessor</accessor>
                <defaultByScript>false</defaultByScript>
                <systemEditable>true</systemEditable>
                <searchSetting seg-detach="true" seg-id="alias_alias" seg-type="search-settings">
                    <simpleSearchableForLicensed>false</simpleSearchableForLicensed>
                    <simpleSearchableForNotLicensed>false</simpleSearchableForNotLicensed>
                    <extendedSearchableForNotLicensed>false</extendedSearchableForNotLicensed>
                    <extendedSearchableForLicensed>false</extendedSearchableForLicensed>
                    <searchBoost>1.0</searchBoost>
                    <code>alias</code>
                    <declaredMetaClass>employee</declaredMetaClass>
                    <attrCode>alias</attrCode>
                </searchSetting>
                <exportNDAP>false</exportNDAP>
                <hiddenWhenEmpty>false</hiddenWhenEmpty>
                <hiddenWhenNoPossibleValues>false</hiddenWhenNoPossibleValues>
                <advlistSemanticFiltering>false</advlistSemanticFiltering>
                <editOnComplexFormOnly>false</editOnComplexFormOnly>
                <hideArchived>true</hideArchived>
            </attribute>
        </attributes>
        <attribute-overrides/>
        <permittedTypesInfo/>
        <attribute-groups/>
        <workflow seg-detach="true" seg-id="workflow" seg-type="workflow"/>
        <maxSearchResults>3</maxSearchResults>
        <searchOrder>3</searchOrder>
    </system-metaclass>
    <mail-processor-rules/>
    <style-templates/>
    <list-templates/>
    <content-templates/>
    <user-events/>
    <event-actions/>
    <system-jmsqueues/>
    <user-jmsqueues/>
    <embedded-applications/>
    <custom-forms/>
    <advimport/>
    <script-modules/>
    <scripts/>
    <customJSElements/>
    <fast-link-settings/>
    <objects/>
    <transfer-values/>
    <structured-objects-views/>
    <libraries/>
    <eventStorageRules/>
</metainfoContainer>
