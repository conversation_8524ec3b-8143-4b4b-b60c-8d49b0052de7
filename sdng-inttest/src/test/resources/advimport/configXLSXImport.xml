<?xml version="1.0" encoding="UTF-8"?>
<config description="Example" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../../target/classes/advimport/schema1.xsd" save-log="true" threads-number="1" skip-workflow="false">
	<mode>CREATE</mode>
	<mode>UPDATE</mode>
	<gui-parameter name="uploadedFile" type="FILE" title="Файл для импорта в XLS" />
	<class name="userclass" threads-number="1">
		<xlsx-data-source file-name="$uploadedFile" url-timeout="30" sheet-number="0" start-row="0" id-column="title">
			<column name="title" src-key="0"/>
		</xlsx-data-source>
		<constant-metaclass-resolver metaclass="%s"/>
		<object-searcher attr="title" metaclass="%s"/>
		<attr name="title" column="title"/>
	</class>
</config>