<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <templateClassFqn>attrs</templateClassFqn>
    <templateAttributes>
        <attribute name="fieldTypeId">metaClass</attribute>
        <attribute name="order">sequenceAttr</attribute>
    </templateAttributes>
    <templateTypes>
        <type from="attrString" to="double">
         <when>
                <attribute name="visor">attrDouble</attribute>
         </when>
        </type>
        <type from="attrString" to="integer">
         <when>
                <attribute name="visor">attrInteger</attribute>
         </when>
         </type>
        <type from="attrString" to="string" />
        <type from="attrText" to="text" />
        <type from="attrRichtext" to="richtext" />
        <type from="attrBool" to="bool" />
        <type from="attrBoEm" to="object:employee" />
        <type from="attrDtInterval" to="dtInterval" />
        <type from="attrDateTime" to="dateTime" />
        <type from="attrData" to="date" />
        <type from="attrFile" to="file" />
        <type from="attrMultOu" to="boLinks:ou" />
        <type from="attrInteger" to="integer" />
        <type from="attrDouble" to="double" />
    </templateTypes>
</configuration>
