<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <templateClassFqn>attrs</templateClassFqn>
    <templateAttributes>
        <attribute name="fieldTypeId">metaClass</attribute>
        <attribute name="order">sequenceAttr</attribute>
    </templateAttributes>
    <templateTypes>
        <type from="attrString" to="string" />
        <type from="attrInteger" to="integer" />
    </templateTypes>
    <pathToGroups>attrGroups</pathToGroups>
    <visibleInListConditions>
        <templateAttribute>visible</templateAttribute>
        <groupAttribute>visible</groupAttribute>
    </visibleInListConditions>
</configuration>
