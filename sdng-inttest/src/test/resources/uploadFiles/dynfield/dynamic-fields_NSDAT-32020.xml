<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <templateClassFqn>attrs</templateClassFqn>
    <templateAttributes>
        <attribute name="fieldTypeId">metaClass</attribute>
        <attribute name="order">sequenceAttr</attribute>
    </templateAttributes>
    <templateTypes>
        <type from="attrString" to="string" presentation="passwordView">
         <when>
                <attribute name="visor">attrPasswordView</attribute>
         </when>
        </type>
        <type from="attrString" to="string" presentation="stringView">
         <when>
                <attribute name="visor">attrStringView</attribute>
         </when>
         </type>
        <type from="attrDateTime" to="dateTime" presentation="dateTimeView">
         <when>
                <attribute name="visor">attrDateTimeView</attribute>
         </when>
        </type>
        <type from="attrDateTime" to="dateTime" presentation="dateTimeWithSecondsView">
         <when>
                <attribute name="visor">attrDateTimeWithSecondsView</attribute>
         </when>
        </type>
        <type from="attrDateTime" to="dateTime" presentation="dateTimeWithMillisView">
         <when>
                <attribute name="visor">attrDateTimeWithMillisView</attribute>
         </when>
        </type>
       <type from="attrBool" to="bool" presentation="oneZero">
         <when>
                <attribute name="visor">attrOneZero</attribute>
         </when>
        </type>
        <type from="attrBool" to="bool" presentation="yesNo">
         <when>
                <attribute name="visor">attrYesNo</attribute>
         </when>
        </type> 
    </templateTypes>
    <visibleInListConditions>
        <templateAttribute>visible</templateAttribute>
    </visibleInListConditions>
</configuration>
