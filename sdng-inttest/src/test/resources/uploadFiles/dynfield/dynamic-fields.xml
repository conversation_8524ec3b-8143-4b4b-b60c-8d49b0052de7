<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <templateClassFqn>attrs</templateClassFqn>
    <templateAttributes>
        <attribute name="fieldTypeId">metaClass</attribute>
        <attribute name="order">sequenceAttr</attribute>
    </templateAttributes>
    <templateTypes>
        <type from="attrString" to="integer">
            <when>
                <attribute name="visor">attrInteger</attribute>
            </when>
        </type>
        <type from="attrString" to="double">
            <when>
                <attribute name="visor">attrDouble</attribute>
            </when>
        </type>
        <type from="attrString" to="string" />
        <type from="attrDtInterval" to="dtInterval" />
        <type from="attrText" to="text" />
        <type from="attrFile" to="file" />
        <type from="attrMultOu" to="boLinks:ou" />
        <type from="attrBoEm" to="object:employee" />
		<type from="" to="object:employee" />
    </templateTypes>
    <pathToGroups>attrGroups</pathToGroups>
    <visibleInListConditions>
        <templateAttribute>visible</templateAttribute>
        <groupAttribute>visible</groupAttribute>
    </visibleInListConditions>
	<initialTemplatesInListLimit>20</initialTemplatesInListLimit>
</configuration>
