[BEGIN IMPORT]
com.google.common.collect.Lists;
ru.naumen.mobile.metainfoadmin.shared.addforms.SaveMobileAddFormAction;
ru.naumen.metainfo.shared.mobile.addforms.AddForm;
ru.naumen.metainfo.shared.ClassFqn;
ru.naumen.metainfo.shared.mobile.MobileAttribute;
ru.naumen.commons.shared.utils.CollectionUtils;
[END IMPORT]
[BEGIN DECLARATION]
editMobileAddForm('%s')
[END DECLARATION]
[BEGIN BODY]

/**
 * Добавить мобильную форму добавления объекта
 * @param dataForScript данные для скрипта
 * @return uuid списка
 */
def editMobileAddForm(def dataForScript)
{
    Map data = GSON.SELF.fromJson(dataForScript, GSON.MAP_TYPE);

    def title = data.title;
    AddForm addForm = new AddForm();
    addForm.setUuid(data.uuid)

    def cases = prepareCases(data.cases);
    if (CollectionUtils.isEmpty(cases))
    {
        addForm.setClazz(ClassFqn.parse(data.fqnOfClass));
    }
    addForm.setCases(cases);

    if (data.parent)
    {
        AddForm parentAddForm = new AddForm();
        parentAddForm.setUuid(data.parent);
        addForm.setParent(parentAddForm);
    }

    if (data.profiles)
    {
        addForm.setProfiles(Lists.newArrayList(data.profiles));
    }

    if (data.tags)
    {
        addForm.setTags(Lists.newArrayList(data.tags));
    }

    if (data.mobileAttributes)
    {
        def mobileAttributes = data.mobileAttributes.split(':');
        mobileAttributes.each
            {
                if(it != "")
                {
                    def mobileAttribute = new MobileAttribute();
                    mobileAttribute.setCode(it);
                    addForm.addAttribute(mobileAttribute);
                }
            }
    }

    addForm.setAttrsOnFormInherited(data.attrsOnFormInherited);
    addForm.setShowInNavigationMenu(data.showInNavigationMenu);
    addForm.setVoiceCreationInherited(data.voiceCreationInherited);
    addForm.setAllowVoiceCreation(data.allowVoiceCreation);
    addForm.setVoiceDecryptionAttribute(data.voiceDecryptionAttribute);
    addForm.setVoiceCreationScript(data.voiceCreationScript);
    addForm.setTransferDeviceGeoPosition(data.isTransferDeviceGeoPosition);

    dispatch.execute(new SaveMobileAddFormAction(title, addForm, false));
}

def prepareCases(def value)
{
    def resultList = [];

    if (!value)
        return resultList;

    value.each {
        resultList.add(ClassFqn.parse(it));
    }
    return resultList;
}

[END BODY]