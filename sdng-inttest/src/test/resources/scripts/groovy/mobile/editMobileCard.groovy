[BEGIN IMPORT]
ru.naumen.commons.shared.utils.CollectionUtils;
ru.naumen.core.shared.utils.ILocaleInfo;
ru.naumen.mobile.metainfoadmin.shared.cards.EditMobileObjectCardAction;
ru.naumen.metainfo.shared.ClassFqn;
ru.naumen.metainfo.shared.mobile.cards.CommentsSettings;
ru.naumen.metainfo.shared.mobile.cards.FilesSettings;
ru.naumen.metainfo.shared.mobile.cards.ObjectCard;
ru.naumen.metainfo.shared.mobile.cards.WorkflowSettings;
ru.naumen.metainfo.shared.spi.store.Transition;
ru.naumen.metainfo.shared.mobile.actions.ObjectAction
ru.naumen.metainfo.shared.mobile.actions.ObjectActions
ru.naumen.metainfo.shared.ui.LocalizedString
[END IMPORT]
[BEGIN DECLARATION]
editMobileCard('%s')
[END DECLARATION]
[BEGIN BODY]

/**
 * Добавить мобильную карточку объектов
 * @param dataForScript данные для скрипта
 * @return uuid карточки
 */
def editMobileCard(def dataForScript)
{
    Map data = GSON.SELF.fromJson(dataForScript, GSON.MAP_TYPE);

    ObjectCard objectCard = new ObjectCard();
    objectCard.setUuid(data.uuid);
    def cases = prepareCases(data.cases);
    if (CollectionUtils.isEmpty(cases))
    {
        objectCard.setClazz(ClassFqn.parse(data.fqnOfClass));
    }
    objectCard.setCases(cases);
    
    if(data.objectCaption)
    {
        objectCard.setObjectCardCaptionAttributeCode(data.objectCaption);
    }

    if (data.profiles)
        objectCard.setProfiles(Lists.newArrayList(data.profiles));
    
    if (data.tags)
        objectCard.setTags(Lists.newArrayList(data.tags));
    
    objectCard.setCommentsSettings(new CommentsSettings(data.commentsMenuAvailable));
    objectCard.setFilesSettings(new FilesSettings(data.filesMenuAvailable));
    objectCard.setWorkflowSettings(new WorkflowSettings(data.useAvailableStates));
    objectCard.setTransitions(prepareTransitions(data.transitions));
    objectCard.setQuickStateChangeAvailable(data.quickStateChangeAvailable);
    objectCard.setSendDeviceLocationAvailable(data.sendDeviceLocationAvailable)

    if(data.objectActions)
    {
        List<ObjectAction> objectActions = []
        data.objectActions.each
        {
            ArrayList<LocalizedString> caption = List.of(new LocalizedString(ILocaleInfo.RUSSIAN, it.caption))
            ObjectAction objectAction = new ObjectAction(it.code, it.type, caption)
            if (it.eventUuid != '')
            {
                objectAction.setEventUuid(it.eventUuid)
            }
            objectAction.setSettingsSet(it.settingsSet)
            objectActions.add(objectAction)
        }
        objectCard.setObjectActions(new ObjectActions(objectActions))
    }

    dispatch.execute(new EditMobileObjectCardAction(objectCard.getUuid(), objectCard));
    return objectCard.getUuid();
}

def prepareCases(def value)
{
    def resultList = [];

    if (!value)
        return resultList;

    value.each {
        resultList.add(ClassFqn.parse(it))
    }
    return resultList;
}

def prepareTransitions(def value)
{
    def resultList = [];

    if (!value)
        return resultList;

    value.each {
        def states = it.split(":");
        Transition transition = new Transition();
        transition.setFrom(states[0]);
        transition.setTo(states[1]);
        resultList << transition;
    }

    return resultList;
}

[END BODY]