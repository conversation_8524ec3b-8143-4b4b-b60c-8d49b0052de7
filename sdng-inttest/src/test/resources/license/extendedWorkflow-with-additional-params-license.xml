<?xml version="1.0" encoding="UTF-8"?><ns2:naumen-license xmlns:ns2="http://naumen.ru/license"> <creationDate>2024.08.07</creationDate><author>edem<PERSON><PERSON></author>
	
	
	<baseLevel>server</baseLevel>
	<client>Лицензия для внутренних нужд</client>
	<superuser code="superuser" count="2" expirationDate="2024.08.13">Супер пользователь</superuser>
	<named code="named" count="10" expirationDate="2024.08.13">Именная</named>
	<named code="named2" count="10" expirationDate="2024.08.13">Именная2</named>
	<concurrent code="concurrent" count="10" expirationDate="2024.08.13">Конкурентная</concurrent>
	<parameter name="rolesForUnlicensedUsers">
		currentUser, employee, commentAuthor, fileAuthor, ServiceCallClient, ouMember, AgreementRecipient,
		slmServiceRecipient
	</parameter>
	<parameter name="permissionsSetForUnlicensedUsers">
		extendedWorkflow
	</parameter>
	<parameter name="allowedClassesToUnlicensedUsers">
		*ALL*, userClass$userCase3
	</parameter>
	<parameter name="forbiddenClassesToUnlicensedUsers">
		userClass$userCase, questionary
	</parameter>
	<parameter name="editableAttributesToUnlicensedUser">
		userClass: author, responsible;
		userClass$userCase: author, textRTFCustom, aggrCustom;
		*ATTRIBUTES_TYPES* : backBOLinks, double;
	</parameter>
	<parameter name="transitionsByStateToUnlicensedUser">
		*ROOT* : *FINAL_STATE*;
	</parameter>
	<parameter name="allowedTransitionsBetweenFinalState">
		false
	</parameter>
<Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315#WithComments"/><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha512"/><DigestValue>+tYuMnHGiDzx6gaiM0gzFl8Y1DpOtsVQfaRIHuT7GpBxFNRfUJuftvUVprc8y3hmFpOFSMsBcB9d&#13;
8bbPpXQXmw==</DigestValue></Reference></SignedInfo><SignatureValue>CYBZ4AIUc5nzGzxllq8QsNa7gQC7o6j1c8FXJoizzOcljet97qzknBn2cYJ+vXlc8pXn8PuvAVaQ&#13;
nIeZfam/Lcr0AAOG06iX8JLxyJN7ubBBtU3bZjXdiyac/criVnbHgOfaNBfMq7EbMnJo6UariaaO&#13;
4EfWNCBgZll3XEbjze97bQIWp4MFpXE8g/PcpKkOWS2GH2AuiEGOn2XYj08PagXN98kewl+oAsli&#13;
BaRVeJBE08e8gwMmBC74nl6G1UOLhAwDpBFC1uMv+Lh77Sb3iYlF9GUW9/qXjxkkXbPc/hrSQPlq&#13;
fJUOA2bdGptKoS6QWvMDPuWVv0jikp1T3XwtXg==</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>x+2KvhLoyEmGYjNFiAZimcM+TX+wKoCFY4BA+4cHYt3vP6cXxG158hc1mJdWZBK/YMax6hOmfaT0&#13;
KpJSWf8SGtG3MDV7B7EPDaKciVOu+Era9DrepsejZ/TaiZinooBZUui32S+abyfp2t3wC4VKgtTX&#13;
Q+8KYyJC74pwgezytOe9nrhd9L/yrBK5uZt6FZBGNttTGA7FcU2h0i/8zsKNZUxV11JAk3mIVl2Z&#13;
9vu+EWho5mvom9gDdcFJcUVSUtGLToK4xPjTwiXTJrmUQQZB/2p7DpYwOUVUeuVUn8FkT5I6Yuji&#13;
InsfZJSHqQ5v53NmSA3h7c/q7Lr30WlYWVi7Pw==</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue></KeyInfo></Signature></ns2:naumen-license>