package ru.naumen.selenium.cases.operator.classes.content;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование фильтрации в сложных списках в ограничении содержимого списка
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
 *
 * <AUTHOR>
 * @since 23.09.2024
 */
public class AdvListFilter11Test extends AbstractTestCase
{
    private static Attribute boLinkUserClassAttribute;
    private static Attribute boLinkEmployeeAttribute;
    private static Attribute boLinksEmployeeAttribute;
    private static ContentForm objectList, objectListAddForm;
    private static Bo employee1, employee2, employee3, employee4;
    private static Bo uc1, uc2, uc3;
    private static MetaClass employeeCase;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Создать тип ouCase в классе "Отдел"</li>
     * <li>Создать класс "UserClass" userClass</li>
     * <li>Создать атрибут boLinkUserClassAttribute типа "Ссылка на БО" в классе userClass</li>
     * <li>Создать тип employeeCase в классе "Сотрудник"</li>
     * <li>Создать атрибут boLinkUserClassAttribute типа "Ссылка на БО" в типе employeeCase</li>
     * <li>Создать список объектов objectList на карточке "Сотрудник":
     *      <ul>
     *          <li>класс userClass</li>
     *          <li>представление - Сложный список</li>
     *      </ul>
     * </li>
     * <li>Создать объекты типа ouCase: ou1, ou2, ou3</li>
     * <li>Создать объекты типа employeeCase: employee1, employee2</li>
     * <li>Создать объекты класса userClass: uc1, uc2, uc3</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.createInSelf();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        MetaClass ouCase = DAOOuCase.create();
        employeeCase = DAOEmployeeCase.create();
        DSLMetaClass.add(ouCase, employeeCase);
        boLinkUserClassAttribute = DAOAttribute.createObjectLink(userClass, userClass);
        boLinkEmployeeAttribute = DAOAttribute.createObjectLink(employeeCase, userClass);
        boLinksEmployeeAttribute = DAOAttribute.createBoLinks(employeeCase, userClass);
        DSLMetainfo.add(boLinkUserClassAttribute, boLinkEmployeeAttribute, boLinksEmployeeAttribute);

        objectList = DAOContentCard.createObjectList(employeeCase.getFqn(), userClass);
        objectList.setPresentation(PresentationContent.ADVLIST.get());
        objectListAddForm = DAOContentForm.createChildObjectListOrObjectListOrRelatedList(employeeCase.getFqn(), true,
                ContentType.OBJECT_LIST, PositionContent.FULL, PresentationContent.ADVLIST, null,
                userClass, null, DAOGroupAttr.createSystem(), MetaclassCardTab.NEWENTRYFORM);
        DSLContent.add(objectList, objectListAddForm);

        Bo bo1 = DAOUserBo.create(userCase);
        DSLBo.add(bo1);
        Bo bo2 = DAOUserBo.createWithParent(userCase, bo1);
        DSLBo.add(bo2);
        Bo bo3 = DAOUserBo.create(userCase);
        DSLBo.add(bo3);

        employee1 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee1.setPassword(ModelUtils.createPassword());
        employee1.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        boLinkEmployeeAttribute.setBoValue(bo1);
        employee1.setUserAttribute(boLinkEmployeeAttribute);

        employee2 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee2.setPassword(ModelUtils.createPassword());
        employee2.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        boLinkEmployeeAttribute.setBoValue(bo3);
        employee2.setUserAttribute(boLinkEmployeeAttribute);

        employee3 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee3.setPassword(ModelUtils.createPassword());
        employee3.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        boLinksEmployeeAttribute.setBoValue(bo1);
        employee3.setUserAttribute(boLinksEmployeeAttribute);

        employee4 = DAOEmployee.create(employeeCase, SharedFixture.ou(), true);
        employee4.setPassword(ModelUtils.createPassword());
        employee4.setLicenseCode(DAOBo.CONCURRENT_LICENSE_SET);
        boLinksEmployeeAttribute.setBoValue(null);
        employee4.setUserAttribute(boLinksEmployeeAttribute);
        DSLBo.add(employee1, employee2, employee3, employee4);

        uc1 = DAOUserBo.create(userCase);
        boLinkUserClassAttribute.setBoValue(bo1);
        uc1.setUserAttribute(boLinkUserClassAttribute);
        uc2 = DAOUserBo.create(userCase);
        boLinkUserClassAttribute.setBoValue(bo2);
        uc2.setUserAttribute(boLinkUserClassAttribute);
        uc3 = DAOUserBo.create(userCase);
        boLinkUserClassAttribute.setBoValue(bo3);
        uc3.setUserAttribute(boLinkUserClassAttribute);
        DSLBo.add(uc1, uc2, uc3);
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего объекта (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - bo</li>
     *     <li>критерий - содержит атрибут текущего объекта (включая вложенные) = Сотрудник / Отдел</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Перейти в интерфейс оператора</li>
     * <li>Перейти на карточку сотрудника employee2</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc3</li>
     * <li>Проверить, что в списке objectList отсутствуют объекты: uc1, uc2</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsSubjectBoAttributeWithNestedCondition()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectList);
        GUIContent.clickFiltrationIcon(objectList);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinkEmployeeAttribute);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        // Выполнение действий и проверок
        GUINavigational.goToOperatorUI();
        GUIBo.goToCard(employee2);
        GUIAdvListUtil advList = objectList.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc3);
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего пользователя (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - bo</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под пользователем employee1</li>
     * <li>Перейти на карточку сотрудника employee1</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc1, uc2</li>
     * <li>Проверить, что в списке objectList отсутствуют объекты: uc3</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsUserBoAttributeWithNestedCondition1()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectList);
        GUIContent.clickFiltrationIcon(objectList);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinkEmployeeAttribute);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();
        GUILogon.logout();

        // Выполнение действий и проверок
        GUILogon.login(employee1);
        GUIBo.goToCard(employee1);
        GUIAdvListUtil advList = objectList.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc1);
        advList.content().asserts().rowsAbsence(uc3);
        GUILogon.logout();
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего пользователя (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - bo</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под пользователем employee2</li>
     * <li>Перейти на карточку сотрудника employee2</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc3</li>
     * <li>Проверить, что в списке objectList отсутствуют объекты: uc1, uc2</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsUserBoAttributeWithNestedCondition2()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectList);
        GUIContent.clickFiltrationIcon(objectList);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinkEmployeeAttribute);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();
        GUILogon.logout();

        // Выполнение действий и проверок
        GUILogon.login(employee2);
        GUIBo.goToCard(employee2);
        GUIAdvListUtil advList = objectList.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc3);
        advList.content().asserts().rowsAbsence(uc1, uc2);
        GUILogon.logout();
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего пользователя (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$291853717
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - nbo</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под пользователем employee3</li>
     * <li>Перейти на карточку сотрудника employee3</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc1</li>
     * <li>Проверить, что в списке objectList отсутствуют объекты: uc3</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsUserNboAttributeWithNestedCondition()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectList);
        GUIContent.clickFiltrationIcon(objectList);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinksEmployeeAttribute);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();
        GUILogon.logout();

        // Выполнение действий и проверок
        GUILogon.login(employee3);
        GUIBo.goToCard(employee3);
        GUIAdvListUtil advList = objectList.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc1);
        advList.content().asserts().rowsAbsence(uc3);
        GUILogon.logout();
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего пользователя (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$291853717
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - nbo</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     *     <li>параметр "Игнорировать, если пуст" - истина</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под пользователем employee4</li>
     * <li>Перейти на карточку сотрудника employee4</li>
     * <li>Перейти к контенту objectList</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc1, uc2, uc3</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsUserNboAttributeWithNestedConditionAndIgnoreIfEmpty()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectList);
        GUIContent.clickFiltrationIcon(objectList);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinksEmployeeAttribute);
        GUIContent.objectListFilter().setIgnoreIfEmpty(1, 1, true);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();
        GUILogon.logout();

        // Выполнение действий и проверок
        GUILogon.login(employee4);
        GUIBo.goToCard(employee4);
        GUIAdvListUtil advList = objectList.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc1, uc2, uc3);
        GUILogon.logout();
    }

    /**
     * Тестирование фильтрации для списка объектов, если настроено
     * ограничение содержимого списка по критерию "содержит атрибут текущего пользователя (включая вложенные)"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$291853717
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Войти под суперпользователем naumen</li>
     * <li>Перейти к контенту objectListAddForm</li>
     * <li>Настроить ограничения содержимого списка
     * <br>
     * <ul>
     *     <li>атрибут - nbo</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     *     <li>критерий - содержит атрибут текущего пользователя (включая вложенные) = Сотрудник / Отдел</li>
     * </ul>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти под пользователем employee3</li>
     * <li>Перейти на карточку добавления сотрудника employee3</li>
     * <li>Перейти к контенту objectListAddForm</li>
     * <li>Проверить, что в списке objectList присутствуют объекты: uc1</li>
     * <li>Проверить, что в списке objectList отсутствуют объекты: uc3</li>
     * </ol>
     */
    @Test
    public void testFiltrationListByContainsUserAttributeWithNestedConditionAndOpenAddForm()
    {
        // Подготовка
        GUILogon.asSuper();
        GUIContent.goToContent(objectListAddForm);
        GUIContent.clickFiltrationIcon(objectListAddForm);
        GUIContent.objectListFilter().clickChange();
        GUIContent.objectListFilter().addAttrFromTree(1, 1, boLinkUserClassAttribute);
        GUIContent.objectListFilter().selectCondition(1, 1, FilterCondition.CONTAINS_USER_ATTRIBUTE_WITH_NESTED);
        GUIContent.objectListFilter().setAttributeTree(1, 1, boLinksEmployeeAttribute);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();
        GUILogon.logout();

        // Выполнение действий и проверок
        GUILogon.login(employee3);
        GUIBo.goToAddForm(employeeCase);
        GUIAdvListUtil advList = objectListAddForm.advlist();
        tester.waitAsyncCall();
        advList.content().asserts().rowsPresence(uc1);
        advList.content().asserts().rowsAbsence(uc3);

        GUILogon.logout();
    }
}