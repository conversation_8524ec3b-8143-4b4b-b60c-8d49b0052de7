package ru.naumen.selenium.cases.operator.files;

import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.GUIExportUtils;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование выгрузки файлов из системы
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class FilesDownloadTest extends AbstractTestCase
{
    /**
     * Тестирование того, что файл с названием, содержащем кириллицу,
     * сохраняется из системы на машину пользователя с тем же именем, с которым прикрепляется <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00598 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$271350934
     * <ol>
     * <b>Подготовка</b>
     * <li>Добавить на карточку компании контент "Список файлов"</li>
     * <li>Прикрепить к компании файл, в названии которого есть кириллица</li>
     * <b>Действие</b>
     * <li>Войти в систему</li>
     * <li>Перейти на карточку компании</li>
     * <li>Скачать прикрепленный файл</li>
     * <b>Проверка</b>
     * <li>Проверить, что название скачанного файла совпадает с названием файла, который прикрепляли</li>
     * </ol>
     */
    @Test
    public void testFileSavedWithCorrectName()
    {
        // Подготовка
        ContentForm fileList = DAOContentCard.createFileList(SystemClass.ROOT.getCode());
        DSLContent.add(fileList);
        SdFile file = DSLFile.add(SharedFixture.root(), DSLFile.TXT_FILE_WITH_CYRILLIC_NAME);

        //Выполнение действий
        GUILogon.asTester();
        GUIBo.goToCard(SharedFixture.root());
        GUIFileList.clickDownload(fileList, file);

        //Проверка
        GUIExportUtils.assertDownloaded(file);
    }
}