package ru.naumen.selenium.cases.admin.classes.content;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsViewItem;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование настройки ограничения списка объектов.
 * <AUTHOR>
 * @since 31.01.2025
 */
class AdvlistObjectFilter1Test extends AbstractTestCaseJ5
{
    private static MetaClass userClass2, userClass1, userCase1;
    private static GroupAttr systemAttrGroup1, systemAttrGroup2;
    private static Attribute objectLinkAttr2, boLinksAttr2, boLinksAttrUserClass1, objectLinkAttrUserClass1;
    private static Bo userBoUserClass1, userBoUserClass11, userBoUserClass12, userBo21, userBo22;
    private static ContentForm childObjectList;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>В ИА создать пользовательский класс:</li>
     * <ul>
     *     <li>Название / код - UserClass1</li>
     *     <li>Объекты вложены в - В объекты своего класса</li>
     * </ul>
     * <li>В UserClass1 создать тип userCase1</li>
     * <li>В классе UserClass1 создать атрибут типа “Ссылка на БО”</li>
     * <ul>
     *     <li>Название \ код - objectLinkAttrUserClass1</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>Добавить атрибут objectLinkAttrUserClass1 в Системную группу атрибутов</li>
     * <li>В классе UserClass1 на карточку объекта добавить контент:</li>
     * <ul>
     *     <li>Тип - список вложенных объектов</li>
     *     <li>Название / код - childObjectList</li>
     *     <li>Класс объектов - UserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Создать класс userClass2 и в нем тип userCase</li>
     * <li>В классе userClass2 создать атрибут типа “Набор ссылок на БО”</li>
     * <ul>
     *     <li>Название \ код - boLinksAttr2</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>В классе userClass2 создать атрибут типа “Ссылка на БО”</li>
     * <ul>
     *     <li>Название \ код - objectLinkAttr2</li>
     *     <li>Класс объекта - UserClass1</li>
     * </ul>
     * <li>Добавить атрибуты boLinksAttr2 и objectLinkAttr2 в Системную группу атрибутов</li>
     * <li>В классе UserClass1 создать атрибут типа “Набор ссылок на БО”:</li>
     * <ul>
     *     <li>Название / код - boLinksAttrUserClass1</li>
     *     <li>Класс объектов - userClass2</li>
     * </ul>
     * <li>Добавить атрибут boLinksAttrUserClass1 в Системную группу атрибутов</li>
     * <li>Вывести на карточку компании список вложенных объектов:</li>
     * <ul>
     *     <li>Название / код - userClass2</li>
     *     <li>Класс объектов - userClass2</li>
     *     <li>Группа атрибутов - системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Вывести на карточку компании список вложенных объектов:</li>
     * <ul>
     *     <li>Название / код - UserClass1</li>
     *     <li>Класс объектов - UserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>В ИО создать 1 объект класса UserClass1:</li>
     * <ul>
     *     <li>userBoUserClass1</li>
     * </ul>
     * <li>Создать 2 объекта класса UserClass1 вложенные в объект userBoUserClass1</li>
     * <ul>
     *     <li>userBoUserClass1.1</li>
     *     <li>userBoUserClass1.2</li>
     * </ul>
     * <li>Создать 2 объекта класса userClass2:</li>
     * <ul>
     *     <li>userBo21</li>
     *     <li>userBo22</li>
     * </ul>
     * <li>Для объектов класса userClass2 заполнить атрибуты boLinksAttr2 / objectLinkAttr2</li>
     * <ul>
     *     <li>Для userBo21 - userBoUserClass1</li>
     *     <li>Для userBo22 - userBoUserClass1.1</li>
     * </ul>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        userClass1 = DAOUserClass.createInSelf();
        userCase1 = DAOUserCase.create(userClass1);
        DSLMetainfo.add(userClass1, userCase1);

        objectLinkAttrUserClass1 = DAOAttribute.createObjectLink(userClass1, userClass1);
        DSLAttribute.add(objectLinkAttrUserClass1);

        systemAttrGroup1 = DAOGroupAttr.createSystem(userClass1);
        DSLGroupAttr.edit(systemAttrGroup1, new Attribute[] { objectLinkAttrUserClass1 }, new Attribute[] {});

        childObjectList = DAOContentCard.createChildObjectList(userClass1.getFqn(),
                userClass1, systemAttrGroup1);
        childObjectList.setPresentation(PresentationContent.ADVLIST.get());
        DSLContent.add(childObjectList);

        userClass2 = DAOUserClass.create();
        MetaClass userCase2 = DAOUserCase.create(userClass2);
        DSLMetainfo.add(userClass2, userCase2);

        boLinksAttr2 = DAOAttribute.createBoLinks(userClass2, userClass1);
        objectLinkAttr2 = DAOAttribute.createObjectLink(userClass2, userClass1);
        DSLAttribute.add(boLinksAttr2, objectLinkAttr2);

        systemAttrGroup2 = DAOGroupAttr.createSystem(userClass2);
        DSLGroupAttr.edit(systemAttrGroup2, new Attribute[] { boLinksAttr2, objectLinkAttr2 }, new Attribute[] {});

        boLinksAttrUserClass1 = DAOAttribute.createBoLinks(userClass1, userClass2);
        DSLAttribute.add(boLinksAttrUserClass1);

        DSLGroupAttr.edit(systemAttrGroup1, new Attribute[] { boLinksAttrUserClass1 }, new Attribute[] {});

        GroupAttr systemAttrGroupRoot = DAOGroupAttr.createSystem(DAORootClass.create());
        ContentForm contentUserClass = DAOContentCard.createChildObjectList(DAORootClass.create().getFqn(),
                userClass2, systemAttrGroupRoot);
        contentUserClass.setPresentation(PresentationContent.ADVLIST.get());
        DSLContent.add(contentUserClass);

        GroupAttr systemAttrGroup4 = DAOGroupAttr.createSystem(DAORootClass.create());
        ContentForm userClass1content = DAOContentCard.createChildObjectList(DAORootClass.create().getFqn(),
                userClass1, systemAttrGroup4);
        userClass1content.setPresentation(PresentationContent.ADVLIST.get());
        DSLContent.add(userClass1content);

        userBoUserClass1 = DAOUserBo.create(userCase1);
        DSLBo.add(userBoUserClass1);

        userBoUserClass11 = DAOUserBo.create(userCase1);
        userBoUserClass11.setParentUuid(userBoUserClass1.getUuid());
        userBoUserClass12 = DAOUserBo.create(userCase1);
        userBoUserClass12.setParentUuid(userBoUserClass1.getUuid());
        DSLBo.add(userBoUserClass11, userBoUserClass12);

        userBo21 = DAOUserBo.create(userCase2);
        userBo22 = DAOUserBo.create(userCase2);
        DSLBo.add(userBo21, userBo22);

        boLinksAttr2.setValue(Json.listToString(userBoUserClass1.getUuid()));
        objectLinkAttr2.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBo21, boLinksAttr2, objectLinkAttr2);

        boLinksAttr2.setValue(Json.listToString(userBoUserClass11.getUuid()));
        objectLinkAttr2.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBo22, boLinksAttr2, objectLinkAttr2);
    }

    /**
     * Возврат настроек для объектов, созданных в общей подготовке
     */
    @AfterEach
    void resetSettings()
    {
        boLinksAttrUserClass1.setValue(Json.listToString());
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass1);

        objectLinkAttrUserClass1.setValue(null);
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, objectLinkAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, objectLinkAttrUserClass1);

        boLinksAttr2.setValue(Json.listToString(userBoUserClass1.getUuid()));
        objectLinkAttr2.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBo21, boLinksAttr2, objectLinkAttr2);

        boLinksAttr2.setValue(Json.listToString(userBoUserClass11.getUuid()));
        objectLinkAttr2.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBo22, boLinksAttr2, objectLinkAttr2);

        childObjectList.setObjectListFilter(new ListFilter());
        DSLContent.edit(childObjectList);
    }

    /**
     * Тестирование критерия “Равно текущему объекту (включая вложенные)” для ограничения содержимого списка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список объектов</li>
     *     <li>Название / код - objectList</li>
     *     <li>Класс объектов - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * </ol>
     * <li>Открыть настройки ограничений при фильтрации для контента objectList:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttr2</li>
     *     <li>Во втором критерий фильтрации - “Равно текущему объекту (включая вложенные)</li>
     * </ul>
     * <li>Сохранить все настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: в списке objectList отображается 2 объекта - useBo1 и useBo2</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: в списке objectList отображается 1 объект - useBo2</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: список objectList пустой</li>
     * </ol>
     */
    @Test
    void testObjectAdvListWithEqualsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        ContentForm objectAdvList = DAOContentCard.createObjectAdvList(userClass1, systemAttrGroup2, userClass2);
        objectAdvList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr2,
                FilterCondition.EQUALS_SUBJECT_WITH_NESTED, false))));
        DSLContent.add(objectAdvList);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        objectAdvList.advlist().content().asserts().rowsPresence(userBo21, userBo22);

        GUIBo.goToCard(userBoUserClass11);
        objectAdvList.advlist().content().asserts().rowsPresence(userBo22);
        objectAdvList.advlist().content().asserts().rowsAbsence(userBo21);

        GUIBo.goToCard(userBoUserClass12);
        objectAdvList.advlist().content().asserts().rowsAbsence(userBo21, userBo22);
    }

    /**
     * Тестирование критерия “Равно текущему объекту (включая вложенные)” для ограничения содержимого списка типа
     * “Иерархическое дерево”
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в настройках системы перейти в класс - Структуры</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - userClass2</li>
     *     <li>Объекты - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>Сохранить все настройки</li>
     * <li> В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - Иерархическое дерево</li>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     *     <li>Фокусироваться на текущем объекте - Выключена</li>
     * </ul>
     * <li>Открыть форму настройки ограничения содержимого элемента:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttr2</li>
     *     <li>Во втором критерий фильтрации - “Равно текущему объекту (включая вложенные)</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается 2 объекта - useBo1 и useBo2</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается 1 объект - useBo2</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: список objectList пустой</li>
     * </ol>
     */
    @Test
    void testHierarchyGridWithEqualsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem userClassItem = DAOStructuredObjectsView.createItem(null, userClass2,
                null, DAOGroupAttr.createSystem(userClass2), false, false);
        structure.setItems(userClassItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(userClass1, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        hierarchyGrid.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr2,
                FilterCondition.EQUALS_SUBJECT_WITH_NESTED, false))));
        DSLContent.add(hierarchyGrid);

        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(objectLinkAttr2, 1, 1,
                FilterCondition.EQUALS_SUBJECT_WITH_NESTED);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        // Действия и проверки
        GUIBo.goToCard(userBoUserClass1);
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo21, userBo22), false, false);

        GUIBo.goToCard(userBoUserClass11);
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo22), false, true);

        GUIBo.goToCard(userBoUserClass12);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);
    }

    /**
     * Тестирование критерия “Равно текущему объекту (включая вложенные)” для условия отображения контента типа
     * “Список связанных объектов”
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список связанных объектов</li>
     *     <li>Название / код - relatedObjectList</li>
     *     <li>Атрибут - boLinksAttrUserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму редактирования контента</li>
     * <li>Открыть форму настройки условия отображения контента</li>
     * <ul>
     *     <li>В первом поле выбрать - boLinksAttrUserClass1 / objectLinkAttr2</li>
     *     <li>Во втором поле критерий фильтрации - Равно текущему объекту (включая вложенные)</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <li>Для всех объектов класса UserClass1 заполнить атрибут boLinksAttrUserClass1 - значением - userBo21,
     * userBo22</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 отображается контент relatedObjectList</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1.1 отображается контент relatedObjectList</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: на карточке объекта userBoUserClass1.2 не отображается контент relatedObjectList</li>
     * </ol>
     */
    @Test
    void testRelatedObjectAdvListWithEqualsSubjectWithNestedVisibilityCondition()
    {
        // Подготовка
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectAdvList(userClass1,
                userClass1.getFqn() + "@" + boLinksAttrUserClass1.getCode(), systemAttrGroup2);
        relatedObjectList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(new FilterBlockOr(
                Lists.newArrayList(boLinksAttrUserClass1, objectLinkAttr2), FilterCondition.EQUALS_SUBJECT_WITH_NESTED,
                false))));
        DSLContent.add(relatedObjectList);

        boLinksAttrUserClass1.setValue(Json.listToString(userBo21.getUuid(), userBo22.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        GUIContent.assertPresent(relatedObjectList);

        GUIBo.goToCard(userBoUserClass11);
        GUIContent.assertPresent(relatedObjectList);

        GUIBo.goToCard(userBoUserClass12);
        GUIContent.assertAbsence(relatedObjectList);
    }

    /**
     * Тестирование критерия “Равно текущему объекту (включая вложенные)" для ограничения содержимого списка
     * вложенных объектов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li> В классе UserClass1 открыть форму “ Настройка ограничений при фильтрации”:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttrUserClass1</li>
     *     <li>Во втором критерий фильтрации - “Равно текущему объекту (включая вложенные)</li>
     * </ul>
     * <li>Создать объект класса UserClass1 вложенные в объект userBoUserClass1.1</li>
     * <ul>
     *     <li>userBoUserClass1.1.1</li>
     * </ul>
     * <li>Для объектов класса UserClass1 заполнить атрибут objectLinkAttrUserClass1</li>
     * <ul>
     *     <li>Для userBoUserClass1.1 - userBoUserClass1</li>
     *     <li>Для userBoUserClass1.2 - userBoUserClass1.1</li>
     *     <li>Для userBoUserClass1.1.1 - userBoUserClass1</li>
     * </ul>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на контенте childObjectList отображается 2 объекта - userBoUserClass1.1 и userBoUserClass1.2;</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: список childObjectList пуст</li>
     * </ol>
     */
    @Test
    void testChildObjectListWithEqualsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        childObjectList.setObjectListFilter(new ListFilter(
                new FilterBlockAnd(
                        new FilterBlockOr(objectLinkAttrUserClass1, FilterCondition.EQUALS_SUBJECT_WITH_NESTED,
                                false))));
        DSLContent.edit(childObjectList);

        Bo userBoUserClass111 = DAOUserBo.create(userCase1);
        userBoUserClass111.setParentUuid(userBoUserClass11.getUuid());
        DSLBo.add(userBoUserClass111);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass11, objectLinkAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass111, objectLinkAttrUserClass1);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass12, objectLinkAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass11, userBoUserClass12);
        childObjectList.advlist().content().asserts().rowsAbsence(userBoUserClass1, userBoUserClass111);

        GUIBo.goToCard(userBoUserClass11);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass12,
                        userBoUserClass111);
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего объекта (включая вложенные)" для ограничения содержимого списка
     * связанных объектов с значением параметра “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список связанных объектов</li>
     *     <li>Название / код - relatedObjectList</li>
     *     <li>Атрибут - boLinksAttrUserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму “Настройка ограничений при фильтрации”:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttr2</li>
     *     <li>Во втором критерий фильтрации - “Равно атрибуту текущего объекта (включая вложенные)”</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrUserClass1</li>
     *     <li>“Игнорировать, если атрибут пуст” = true</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>Открыть карточку карточку объекта userBoUserClass1</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту boLinksAttrUserClass1
     * значение - userBo21 и userBo22</li>
     * <li>Сохранить настройки</li>
     * <li>Проверка: на контенте relatedObjectList отображается два объекта - userBo21 и userBo22</li>
     * <li> Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1, сохранить</li>
     * <li>Проверка: на контенте relatedObjectList отображается два объекта - userBo21 и userBo22</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.1 , сохранить</li>
     * <li>Проверка: на контенте relatedObjectList отображается один объект - userBo22</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.2 , сохранить</li>
     * <li>Проверка: контент relatedObjectList пуст</li>
     * </ol>
     */
    @Test
    void testRelatedObjectAdvListWithEqualsSubjectAttributeWithNestedObjectListFilter()
    {
        // Подготовка
        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectAdvList(userClass1,
                userClass1.getFqn() + "@" + boLinksAttrUserClass1.getCode(), systemAttrGroup2);
        relatedObjectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttr2, true,
                FilterCondition.EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrUserClass1.getFqn()))));
        DSLContent.add(relatedObjectList);

        // Действия и проверки
        GUILogon.asSuper();
        boLinksAttrUserClass1.setValue(Json.listToString(userBo21.getUuid(), userBo22.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        GUIBo.goToCard(userBoUserClass1);
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo21, userBo22);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo21, userBo22);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        relatedObjectList.advlist().content().asserts().rowsPresence(userBo22);
        relatedObjectList.advlist().content().asserts().rowsAbsence(userBo21);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        relatedObjectList.advlist().content().asserts().rowsAbsence(userBo21, userBo22);
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего объекта (включая вложенные)" для условия отображения контента
     * типа “Cписок объектов”
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список объектов</li>
     *     <li>Название / код - objectList</li>
     *     <li>Класс объектов - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму редактирования контента</li>
     * <li>Открыть форму настройки условия отображения контента</li>
     * <ul>
     *     <li>В первом поле выбрать - boLinksAttrUserClass1 / objectLinkAttr2</li>
     *     <li>Во втором поле критерий фильтрации - Равно атрибуту текущего объекта (включая вложенные)</li>
     *     <li>В третьем поле выбрать - objectLinkAttrUserClass1</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <li>Для всех объектов класса UserClass1 заполнить атрибут boLinksAttrUserClass1 - значением - userBo21,
     * userBo22</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 не отображается контент objectList</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 отображается контент objectList</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 отображается контент objectList</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.2</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 не отображается контент objectList</li>
     * </ol>
     */
    @Test
    void testObjectAdvListWithEqualsSubjectAttributeWithNestedVisibilityCondition()
    {
        // Подготовка
        ContentForm objectAdvList = DAOContentCard.createObjectAdvList(userClass1, systemAttrGroup2, userClass2);
        objectAdvList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(new FilterBlockOr(
                Lists.newArrayList(boLinksAttrUserClass1, objectLinkAttr2),
                FilterCondition.EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrUserClass1.getFqn()))));
        DSLContent.add(objectAdvList);

        boLinksAttrUserClass1.setValue(Json.listToString(userBo21.getUuid(), userBo22.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        GUIContent.assertAbsence(objectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(objectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(objectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        GUIContent.assertAbsence(objectAdvList);
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего объекта (включая вложенные)” для ограничения содержимого
     * списка иерархического дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в настройках системы перейти в класс - Структуры</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - userClass2</li>
     *     <li>Объекты - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>Сохранить все настройки</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - Иерархическое дерево</li>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     *     <li>Фокусироваться на текущем объекте - Выключена</li>
     * </ul>
     * <li>Открыть форму настройки ограничения содержимого элемента:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttr2</li>
     *     <li>Во втором критерий фильтрации - ““Равно атрибуту текущего объекта (включая вложенные)</li>
     *     <li>В третьем поле выбрать - objectLinkAttrUserClass1</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: контент HierarchicalTree пуст</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается два объекта - userBo21 и userBo22</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.2</li>
     * <li>Проверка: список HierarchicalTree пустой </li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается один объект - userBo22</li>
     * </ol>
     */
    @Test
    void testHierarchyGridWithEqualsSubjectAttributeWithNestedObjectListFilter()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem userClassItem = DAOStructuredObjectsView.createItem(null, userClass2,
                null, DAOGroupAttr.createSystem(userClass2), false, false);
        structure.setItems(userClassItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(userClass1, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(objectLinkAttr2, 1, 1,
                FilterCondition.EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED);
        GUIHierarchyGrid.objectFilterSettingsForm().setAttributeTree(1, 1, objectLinkAttrUserClass1);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        // Действия и проверки
        GUIBo.goToCard(userBoUserClass1);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo21, userBo22), false, true);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo22), false, true);
    }

    /**
     * Тестирование критерия “Равно атрибуту текущего объекта (включая вложенные)” для ограничения содержимого
     * списка вложенных объектов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 открыть форму “ Настройка ограничений при фильтрации”:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - objectLinkAttrUserClass1</li>
     *     <li>Во втором критерий фильтрации - “Равно атрибуту текущего объекта (включая вложенные)”</li>
     *     <li>В третьем поле выбрать - objectLinkAttrUserClass1</li>
     * </ul>
     * </ol>
     * <li><Для объектов класса UserClass1 заполнить атрибут objectLinkAttrUserClass1/li>
     * <ul>
     *     <li>Для userBoUserClass1.1 - userBoUserClass1</li>
     *     <li>Для userBoUserClass1.2 - userBoUserClass1.1</li>
     *     <li></li>
     * </ul>
     * </ol>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>Открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: контент childObjectList пуст</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1</li>
     * <li>Проверка: на контенте childObjectList отображается два объекта userBoUserClass1.1 и userBoUserClass1.2</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.1</li>
     * <li>Проверка: на контенте childObjectList отображается один объект userBoUserClass1.2</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, заполнить атрибут objectLinkAttrUserClass1
     * значением - userBoUserClass1.2</li>
     * <li>Проверка: контент childObjectList пуст</li>
     * </ol>
     */
    @Test
    void testChildObjectListWithEqualsSubjectAttributeWithNestedObjectListFilter()
    {
        // Подготовка
        childObjectList.setObjectListFilter(
                new ListFilter(new FilterBlockAnd(new FilterBlockOr(objectLinkAttrUserClass1,
                        FilterCondition.EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED, false,
                        objectLinkAttrUserClass1.getFqn()))));
        DSLContent.edit(childObjectList);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass11, objectLinkAttrUserClass1);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass12, objectLinkAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass12);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass11, userBoUserClass12);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass12);
        childObjectList.advlist().content().asserts().rowsAbsence(userBoUserClass1, userBoUserClass11);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass12);
    }

    /**
     * Тестирование критерия “Содержит текущий объект (включая вложенные)” для ограничения содержимого списка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список объектов</li>
     *     <li>Название / код - objectList</li>
     *     <li>Класс объектов - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть настройки ограничений при фильтрации для контента objectList:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttr2</li>
     *     <li>Во втором критерий фильтрации - “Содержит текущий объект (включая вложенные)</li>
     * </ul>
     * <li>Сохранить все настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: в списке objectList отображается 2 объекта - useBo1 и useBo2</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: в списке objectList отображается 1 объект - useBo2</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: список objectList пустой</li>
     * </ol>
     */
    @Test
    void testObjectAdvListWithContainsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        ContentForm objectAdvList = DAOContentCard.createObjectAdvList(userClass1, systemAttrGroup2, userClass2);
        objectAdvList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttr2,
                FilterCondition.CONTAINS_SUBJECT_WITH_NESTED, false))));
        DSLContent.add(objectAdvList);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        objectAdvList.advlist().content().asserts().rowsPresence(userBo21, userBo22);

        GUIBo.goToCard(userBoUserClass11);
        objectAdvList.advlist().content().asserts().rowsPresence(userBo22);
        objectAdvList.advlist().content().asserts().rowsAbsence(userBo21);

        GUIBo.goToCard(userBoUserClass12);
        objectAdvList.advlist().content().asserts().rowsAbsence(userBo21, userBo22);
    }

    /**
     * Тестирование критерия “Содержит текущий объект (включая вложенные)” для ограничения содержимого списка
     * иерархического дерева
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в настройках системы перейти в класс - Структуры</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - userClass2</li>
     *     <li>Объекты - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>Сохранить все настройки</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - Иерархическое дерево</li>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     *     <li>Фокусироваться на текущем объекте - Выключена</li>
     * </ul>
     * <li>Открыть форму настройки ограничения содержимого элемента:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttr2</li>
     *     <li>Во втором критерий фильтрации - “Содержит текущий объект (включая вложенные)</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается 2 объекта - useBo1 и useBo2</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: в контенте HierarchicalTree отображается 1 объект - useBo2</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: список objectList пустой</li>
     * </ol>
     */
    @Test
    void testHierarchyGridWithContainsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem userClassItem = DAOStructuredObjectsView.createItem(null, userClass2,
                null, DAOGroupAttr.createSystem(userClass2), false, false);
        structure.setItems(userClassItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(userClass1, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(boLinksAttr2, 1, 1,
                FilterCondition.CONTAINS_SUBJECT_WITH_NESTED);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        // Действия и проверки
        GUIBo.goToCard(userBoUserClass1);
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo21, userBo22), false,
                true);

        GUIBo.goToCard(userBoUserClass11);
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo22), false, true);

        GUIBo.goToCard(userBoUserClass12);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);
    }

    /**
     * Тестирование критерия “Содержит текущий объект (включая вложенные)” для условия отображения контента типа
     * "Список связанных объектов "
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список связанных объектов</li>
     *     <li>Название / код - relatedObjectList</li>
     *     <li>Атрибут - boLinksAttrUserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму редактирования контента</li>
     * <li>Открыть форму настройки условия отображения контента</li>
     * <ul>
     *     <li>В первом поле выбрать - boLinksAttrUserClass1 / boLinksAttr2</li>
     *     <li>Во втором поле критерий фильтрации - Содержит текущий объект (включая вложенные)</li>
     * </ul>
     * <li>Для всех объектов класса UserClass1 заполнить атрибут boLinksAttrUserClass1 - значением - userBo21,
     * userBo22</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1 отображается контент relatedObjectList</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: на карточке объекта userBoUserClass1.1 отображается контент relatedObjectList</li>
     * <li>Открыть карточку объекта useBoUserClass1.2</li>
     * <li>Проверка: на карточке объекта userBoUserClass1.2 не отображается контент relatedObjectList</li>
     * </ol>
     */
    @Test
    void testRelatedObjectAdvListWithContainsSubjectWithNestedVisibilityCondition()
    {
        // Подготовка
        ContentForm relatedObjectAdvList = DAOContentCard.createRelatedObjectAdvList(userClass1,
                userClass1.getFqn() + "@" + boLinksAttrUserClass1.getCode(), systemAttrGroup2);
        relatedObjectAdvList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(new FilterBlockOr(
                Lists.newArrayList(boLinksAttrUserClass1, boLinksAttr2), FilterCondition.CONTAINS_SUBJECT_WITH_NESTED,
                true))));
        DSLContent.add(relatedObjectAdvList);

        boLinksAttrUserClass1.setValue(Json.listToString(userBo21.getUuid(), userBo22.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        GUIContent.assertPresent(relatedObjectAdvList);

        GUIBo.goToCard(userBoUserClass11);
        GUIContent.assertPresent(relatedObjectAdvList);

        GUIBo.goToCard(userBoUserClass12);
        GUIContent.assertAbsence(relatedObjectAdvList);
    }

    /**
     * Тестирование критерия “Содержит текущий объект (включая вложенные)” для ограничения содержимого списка
     * вложенных объектов.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 создать атрибут типа "Набор ссылок на БО":</li>
     * <ul>
     *     <li>Название / код - boLinksAttrAttrUserClass1</li>
     *     <li>Класс объектов - UserClass1</li>
     * </ul>
     * <li>Добавить атрибут boLinksAttrUserClass11 в Системную группу атрибутов</li>
     * <li>В классе UserClass1 открыть форму “ Настройка ограничений при фильтрации”:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttrUserClass11</li>
     *     <li>Во втором критерий фильтрации - “Содержит текущий объект (включая вложенные)</li>
     * </ul>
     * <li>Создать объект класса UserClass1 вложенные в объект userBoUserClass1.1</li>
     * <ul>
     *     <li>userBoUserClass1.1.1</li>
     * </ul>
     * <li>Для объектов класса UserClass1 заполнить атрибут boLinksAttrAttrUserClass1</li>
     * <ul>
     *     <li>Для userBoUserClass1.1 - userBoUserClass1</li>
     *     <li>Для userBoUserClass1.2 - userBoUserClass1.1</li>
     *     <li>Для userBoUserClass1.1.1 - userBoUserClass1</li>
     * </ul>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на контенте childObjectList отображается 2 объекта - userBoUserClass1.1 и userBoUserClass1.2</li>
     * <li>Открыть карточку объекта userBoUserClass1.1</li>
     * <li>Проверка: список childObjectList пуст</li>
     * </ol>
     */
    @Test
    void testChildObjectListWithContainsSubjectWithNestedObjectListFilter()
    {
        // Подготовка
        Attribute boLinksAttrUserClass11 = DAOAttribute.createBoLinks(userClass1, userClass1);
        DSLAttribute.add(boLinksAttrUserClass11);

        DSLGroupAttr.edit(systemAttrGroup1, new Attribute[] { boLinksAttrUserClass11 }, new Attribute[] {});

        childObjectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttrUserClass11,
                FilterCondition.CONTAINS_SUBJECT_WITH_NESTED, true))));
        DSLContent.edit(childObjectList);

        Bo userBoUserClass111 = DAOUserBo.create(userCase1);
        userBoUserClass111.setParentUuid(userBoUserClass11.getUuid());
        DSLBo.add(userBoUserClass111);

        boLinksAttrUserClass11.setValue(Json.listToString(userBoUserClass1.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass11);
        DSLBo.editAttributeValue(userBoUserClass111, boLinksAttrUserClass11);

        boLinksAttrUserClass11.setValue(Json.listToString(userBoUserClass11.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass11);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass11, userBoUserClass12);
        childObjectList.advlist().content().asserts().rowsAbsence(userBoUserClass1, userBoUserClass111);

        GUIBo.goToCard(userBoUserClass11);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass12,
                        userBoUserClass111);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего объекта (включая вложенные)" для ограничения содержимого
     * списка вложенных объектов с параметром “Игнорировать, если атрибут пуст” = true
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 создать атрибут типа "Набор ссылок на БО":</li>
     * <ul>
     *     <li>Название / код - boLinksAttrAttrUserClass1</li>
     *     <li>Класс объектов - UserClass1</li>
     * </ul>
     * <li>Добавить атрибут boLinksAttrUserClass11 в Системную группу атрибутов</li>
     * <li>В классе UserClass1 открыть форму “ Настройка ограничений при фильтрации”:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttrUserClass11</li>
     *     <li>Во втором критерий фильтрации - “Содержит текущий объект (включая вложенные)</li>
     *     <li>В третьем поле выбрать - objectLinkAttrUserClass1</li>
     *     <li>Игнорировать, если атрибут пуст = true</li>
     * </ul>
     * <li>Создать объект класса UserClass1 вложенные в объект userBoUserClass1.1</li>
     * <ul>
     *     <li>userBoUserClass1.1.1</li>
     * </ul>
     * <li>Для объектов класса UserClass1 заполнить атрибут boLinksAttrAttrUserClass1</li>
     * <ul>
     *     <li>Для userBoUserClass1.1 - userBoUserClass1</li>
     *     <li>Для userBoUserClass1.2 - userBoUserClass1.1</li>
     * </ul>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: на контенте childObjectList отображается 2 объекта - userBoUserClass1.1 и userBoUserClass1.2</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1</li>
     * <li>Сохранить настройки</li>
     * <li>Проверка: на контенте childObjectList отображается 2 объекта - userBoUserClass1.1 и userBoUserClass1.2</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.1</li>
     * <li>Сохранить настройки</li>
     * <li>Проверка: на контенте childObjectList отображается 1 объект - userBoUserClass1.2</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.1.1</li>
     * <li>Проверка: контент childObjectList пуст</li>
     * </ol>
     */
    @Test
    void testChildObjectListWithContainsSubjectAttributeWithNestedObjectListFilter()
    {
        // Подготовка
        Attribute boLinksAttrUserClass11 = DAOAttribute.createBoLinks(userClass1, userClass1);
        DSLAttribute.add(boLinksAttrUserClass11);

        DSLGroupAttr.edit(systemAttrGroup1, new Attribute[] { boLinksAttrUserClass11 }, new Attribute[] {});

        childObjectList.setObjectListFilter(new ListFilter(new FilterBlockAnd(new FilterBlockOr(boLinksAttrUserClass11,
                true, FilterCondition.CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED, false,
                objectLinkAttrUserClass1.getFqn()))));
        DSLContent.edit(childObjectList);

        Bo userBoUserClass111 = DAOUserBo.create(userCase1);
        userBoUserClass111.setParentUuid(userBoUserClass11.getUuid());
        DSLBo.add(userBoUserClass111);

        boLinksAttrUserClass11.setValue(Json.listToString(userBoUserClass1.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass11);

        boLinksAttrUserClass11.setValue(Json.listToString(userBoUserClass11.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass11);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass11, userBoUserClass12);
        childObjectList.advlist().content().asserts().rowsAbsence(userBoUserClass1, userBoUserClass111);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass11, userBoUserClass12);
        childObjectList.advlist().content().asserts().rowsAbsence(userBoUserClass1, userBoUserClass111);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        childObjectList.advlist().content().asserts().rowsPresence(userBoUserClass12);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass111);

        objectLinkAttrUserClass1.setValue(userBoUserClass111.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        childObjectList.advlist()
                .content()
                .asserts()
                .rowsAbsence(userBoUserClass1, userBoUserClass11, userBoUserClass12,
                        userBoUserClass111);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего объекта (включая вложенные)" для условия отображения
     * контента тип "Список связанных объектов"
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе UserClass1 вывести контент:</li>
     * <ul>
     *     <li>Тип - список связанных объектов</li>
     *     <li>Название / код - relatedObjectList</li>
     *     <li>Атрибут - boLinksAttrUserClass1</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     *     <li>Представление - Сложный список</li>
     * </ul>
     * <li>Открыть форму настройки редактирования контента relatedObjectList</li>
     * <li>Открыть форму “Настройки условия отображения контента”</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttrUserClass1 / boLinksAttr2</li>
     *     <li>Во втором критерий фильтрации - “Содержит атрибут текущего объекта (включая вложенные)”</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrUserClass1</li>
     * </ul>
     * <li>Сохранить настройки</li>
     * <li>В ИО для всех объектов класса UserClass1 заполнить атрибут boLinksAttrUserClass1 значением - userBo21 и
     * userBo22</li>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: контент relatedObjectList не отображается</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1</li>
     * <li>Сохранить настройки</li>
     * <li>Проверка: контент relatedObjectList отображается</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.1</li>
     * <li>Сохранить настройки</li>
     * <li>Проверка: контент relatedObjectList отображается</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.2</li>
     * <li>Сохранить настройки</li>
     * <li> Проверка: контент relatedObjectList не отображается</li>
     * </ol>
     */
    @Test
    void testRelatedObjectAdvListWithContainsSubjectAttributeWithNestedVisibilityCondition()
    {
        // Подготовка
        ContentForm relatedObjectAdvList = DAOContentCard.createRelatedObjectAdvList(userClass1,
                userClass1.getFqn() + "@" + boLinksAttrUserClass1.getCode(), systemAttrGroup2);
        relatedObjectAdvList.setVisibilityCondition(new ListFilter(new FilterBlockAnd(new FilterBlockOr(
                Lists.newArrayList(boLinksAttrUserClass1, boLinksAttr2),
                FilterCondition.CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED, false, objectLinkAttrUserClass1.getFqn()))));
        DSLContent.add(relatedObjectAdvList);

        boLinksAttrUserClass1.setValue(Json.listToString(userBo21.getUuid(), userBo22.getUuid()));
        DSLBo.editAttributeValue(userBoUserClass1, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass11, boLinksAttrUserClass1);
        DSLBo.editAttributeValue(userBoUserClass12, boLinksAttrUserClass1);

        // Действия и проверки
        GUILogon.asSuper();
        GUIBo.goToCard(userBoUserClass1);
        GUIContent.assertAbsence(relatedObjectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(relatedObjectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(relatedObjectAdvList);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        GUIContent.assertAbsence(relatedObjectAdvList);
    }

    /**
     * Тестирование критерия “Содержит атрибут текущего объекта (включая вложенные)" для ограничения содержимого
     * списка типа "Иерархическое дерево".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00782
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$257962492
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В ИА в настройках системы открыть класс Структуры</li>
     * <li>Добавить структуру:</li>
     * <ul>
     *     <li>Название / код - Structure</li>
     * </ul>
     * <li>Добавить элемент структуры:</li>
     * <ul>
     *     <li>Название / код - userClass2</li>
     *     <li>Объекты - userClass2</li>
     *     <li>Группа атрибутов - Системные атрибуты</li>
     * </ul>
     * <li>В классе UserClass1 вывести контент типа “Иерархическое дерево”</li>
     * <ul>
     *     <li>Название / код - HierarchicalTree</li>
     *     <li>Структура - Structure</li>
     * </ul>
     * <li>Открыть форму “Настройки ограничения содержимого списка”</li>
     * <li>Открыть форму ограничения элемента userClass2:</li>
     * <ul>
     *     <li>В первом поле выбрать атрибут - boLinksAttr2</li>
     *     <li>Во втором критерий фильтрации - “Содержит атрибут текущего объекта (включая вложенные)”</li>
     *     <li>В третьем поле выбрать атрибут - objectLinkAttrUserClass1</li>
     * </ul>
     * <br>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под naumen</li>
     * <li>В ИО открыть карточку объекта userBoUserClass1</li>
     * <li>Проверка: контент HierarchicalTree пуст</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1</li>
     * <li>Проверка: на контенте HierarchicalTree отображается 2 объекта - userBo21 и userBo22;</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.1</li>
     * <li>Проверка: на контенте HierarchicalTree отображается 1 объекта - userBo22</li>
     * <li>Открыть форму редактирования контента “Основные параметры”, выставить атрибуту objectLinkAttrUserClass1
     * значение - userBoUserClass1.2</li>
     * <li>Проверка: контент HierarchicalTree пуст</li>
     * </ol>
     */
    @Test
    void testHierarchyGridWithContainsSubjectAttributeWithNestedObjectListFilter()
    {
        // Подготовка
        StructuredObjectsView structure = DAOStructuredObjectsView.create();
        StructuredObjectsViewItem userClassItem = DAOStructuredObjectsView.createItem(null, userClass2,
                null, DAOGroupAttr.createSystem(userClass2), false, false);
        structure.setItems(userClassItem);
        DSLStructuredObjectsView.add(structure);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(userClass1, true, PositionContent.FULL,
                structure, true, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        GUILogon.asSuper();
        GUIContent.goToContent(hierarchyGrid);
        GUIContent.openFiltrationSettings(hierarchyGrid);
        GUIHierarchyGrid.filterSettingsTable().clickObjectIcon(userClassItem, "edit");
        GUIHierarchyGrid.objectFilterSettingsForm().addAttr(boLinksAttr2, 1, 1,
                FilterCondition.CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED);
        GUIHierarchyGrid.objectFilterSettingsForm().setAttributeTree(1, 1, objectLinkAttrUserClass1);
        GUIForm.applyLastModalForm();
        GUIForm.closeDialog();

        // Действия и проверки
        GUIBo.goToCard(userBoUserClass1);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);

        objectLinkAttrUserClass1.setValue(userBoUserClass1.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo21, userBo22), false, true);

        objectLinkAttrUserClass1.setValue(userBoUserClass11.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIHierarchyGrid.assertRows(hierarchyGrid, userClassItem, Lists.newArrayList(userBo22), false, true);

        objectLinkAttrUserClass1.setValue(userBoUserClass12.getUuid());
        DSLBo.editAttributeValue(userBoUserClass1, objectLinkAttrUserClass1);
        tester.refresh();
        GUIContent.assertPresent(childObjectList);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo21);
        GUIHierarchyGrid.assertAbsenceElement(hierarchyGrid, userClassItem, userBo22);
    }
}