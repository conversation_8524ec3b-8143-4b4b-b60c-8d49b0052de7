package ru.naumen.selenium.cases.admin.permission;

import static ru.naumen.selenium.casesutil.content.GUIContent.CONTENT_PATTERN;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker.*;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.CREATE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.DELETE;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.EDIT;
import static ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType.VIEW;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.admin.GUIAdminNavigationTree;
import ru.naumen.selenium.casesutil.admin.GUIWfProfile;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.attr.GUIGroupAttr;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUICustomForm;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIMarkerForm;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIProfileForm;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.script.GUIScriptCatalogList;
import ru.naumen.selenium.casesutil.script.GUIScriptModule;
import ru.naumen.selenium.casesutil.script.GUIScriptModulesList;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerViewAttrs;

/**
 * Тестирование прав на действие в интерфейсе администратора по маркерам доступа в профиле администрирования.
 *
 * <AUTHOR>
 * @since 06.12.2024
 */
public class AdminProfileAccessMarker1Test extends AbstractTestCase
{
    private SuperUser superUser;
    private AdminProfile adminProfile;
    private AdminProfileAccessMarkerMatrix accessMarkerMatrix;
    private MetaClass userClass;

    /**
     * <ol>
     * <b>Общая подготовка для всех тестов</b>
     * <li>Включить доступность профилей администрирования на стенде (customAdminProfiles - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareAllFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
    }

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать пользовательский класс userClass</li>
     * <li>Создать профиль администрирования adminProfile</li>
     * <li>Создать суперпользователя superUser и назначить ему профиль администрирования adminProfile</li>
     * <li>Создать модель матрицы маркеров доступа accessMarkerMatrix
     * и добавить в нее право на доступ к маркеру доступа "Интерфейс администратора"</li>
     * <li>Установить в профиль администрирования adminProfile матрицу маркеров доступа accessMarkerMatrix</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        userClass = DAOUserClass.create();
        DSLMetaClass.add(userClass);

        adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(adminProfile);
        DSLSuperUser.add(superUser);

        accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAdministrationInterfacePermission();
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Управление схемой базы данных". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что в левом навигационном меню отсутствует раздел "Классы"</li>
     * <li>Перейти в раздел "Классы"</li>
     * <li>Проверить, что отсутствуют контенты "Системные классы" и "Пользовательские классы"</li>
     * <li>Перейти в карточку класса userClass</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует вкладка "Атрибуты"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует вкладка "Атрибуты"</li>
     * <li>Проверить, что в левом навигационном меню присутствует раздел "Классы"</li>
     * <li>Перейти в раздел "Классы"</li>
     * <li>Проверить, что присутствуют контенты "Системные классы" и "Пользовательские классы"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionDatabaseManagementAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.CLASSES_ITEM_ID);

        GUINavigational.goToClasses();
        GUIMetaClass.assertPresentSystemClassesBlock(false);
        GUIMetaClass.assertPresentUserClassesBlock(false);

        GUIMetaClass.goToCard(userClass);
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        Assert.assertTrue("Присутствует вкладка \"Атрибуты\"", tester.waitDisappear(Div.CLASS_ATTRIBUTE_TAB));

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        Assert.assertTrue("Отсутствует вкладка \"Атрибуты\"",
                tester.find(Div.CLASS_ATTRIBUTE_TAB).isDisplayed());
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.CLASSES_ITEM_ID);

        GUINavigational.goToClasses();
        GUIMetaClass.assertPresentSystemClassesBlock(true);
        GUIMetaClass.assertPresentUserClassesBlock(true);
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Управление схемой базы данных". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Открыть и заполнить форму добавления типа для класса userClass, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый тип createdUserCase отсутствует в системе</li>
     * <li>Перейти в карточку класса userClass</li>
     * <li>Нажать на кнопку "Копировать" и заполнить форму копирования метакласса, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что копируемый класс copiedUserClass отсутствует в системе</li>
     * <li>Перейти в карточку класса userClass</li>
     * <li>Открыть и заполнить форму добавления атрибута в классе userClass, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый атрибут createdTextAttribute отсутствует в системе</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <li>Открыть и заполнить форму добавления типа для класса userClass,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый тип createdUserCase существует в системе</li>
     * <li>Перейти в карточку класса userClass</li>
     * <li>Нажать на кнопку "Копировать" и заполнить форму копирования,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что копируемый класс copiedUserClass существует в системе</li>
     * <li>Открыть и заполнить форму добавления атрибута в классе userClass,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый атрибут createdTextAttribute существует в классе userClass</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionDatabaseManagementAccessMarker()
    {
        // Подготовка
        MetaClass createdUserCase = DAOUserCase.create(userClass);
        MetaClass copiedUserClass = DAOUserClass.create();
        Attribute createdTextAttribute = DAOAttribute.createText(userClass);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        clickAddMetaClassAndFillForm(userClass, createdUserCase);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMetaClass.assertAbsence(createdUserCase);

        GUIMetaClass.goToCard(userClass);
        clickCopyMetaClassAndFillForm(copiedUserClass);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMetaClass.assertAbsence(copiedUserClass);

        GUIMetaClass.goToCard(userClass);
        clickAddAttributeAndFillForm(createdTextAttribute);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAttribute.assertAbsence(createdTextAttribute);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, CREATE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        clickAddMetaClassAndFillForm(createdUserCase, createdUserCase);
        GUIForm.applyForm();
        GUIMetaClass.assertPresent(createdUserCase);

        GUIMetaClass.goToCard(userClass);
        clickCopyMetaClassAndFillForm(copiedUserClass);
        GUIForm.applyForm();
        GUIMetaClass.assertPresent(copiedUserClass);

        clickAddAttributeAndFillForm(createdTextAttribute);
        GUIForm.applyForm();
        GUIAttribute.assertPresent(createdTextAttribute);
        createdTextAttribute.setExists(true);
    }

    /**
     * Тестирование действия "Редактирование" маркера доступа "Управление схемой базы данных". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать тип userCase в классе userClass</li>
     * <li>В типе userCase создать атрибут textAttribute типа "Текст"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в карточку типа userCase</li>
     * <li>Открыть форму редактирования типа userCase и в поле "Название" установить значение userCaseNewTitle,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название типа userCase не изменилось</li>
     * <li>Открыть форму редактирования атрибута textAttribute</li>
     * <li>В поле "Название" установить значение textAttributeNewTitle, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название атрибута textAttribute не изменилось</li>
     * <li>Нажать кнопку "Сбросить настройки системных атрибутов"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Нажать кнопку "Сбросить настройки пользовательских атрибутов"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <li>Открыть форму редактирования типа userCase</li>
     * <li>В поле "Название" установить значение userCaseNewTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название типа userCase - userCaseNewTitle</li>
     * <li>Открыть форму редактирования атрибута textAttribute</li>
     * <li>В поле "Название" установить значение textAttributeNewTitle,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название атрибута textAttribute - textAttributeNewTitle</li>
     * </ol>
     */
    @Test
    public void testEditPermissionDatabaseManagementAccessMarker()
    {
        // Подготовка
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userCase);

        Attribute textAttribute = DAOAttribute.createText(userCase);
        DSLAttribute.add(textAttribute);

        String userCaseNewTitle = ModelUtils.createTitle();
        String textAttributeNewTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userCase);
        clickEditMetaClassAndFillTitle(userCase, userCaseNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMetaClass.assertMetaClassTitle(userCase.getTitle());

        clickEditAttributeAndFillTitle(textAttribute, textAttributeNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAttribute.assertAttributeTitle(textAttribute.getTitle(), textAttribute.getCode());

        GUIMetaClass.clickResetSystemAttributeSettings();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIMetaClass.clickResetUserAttributeSettings();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickEditMetaClassAndFillTitle(userCase, userCaseNewTitle);
        GUIForm.applyForm();
        GUIMetaClass.assertMetaClassTitle(userCaseNewTitle);

        clickEditAttributeAndFillTitle(textAttribute, textAttributeNewTitle);
        GUIForm.applyForm();
        GUIAttribute.assertAttributeTitle(textAttributeNewTitle, textAttribute.getCode());
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Управление схемой базы данных". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе userClass создать атрибут textAttribute типа "Текст"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в карточку класса userClass</li>
     * <li>Нажать кнопку "Удалить", затем кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что класс userClass существует в системе</li>
     * <li>Нажать кнопку "Удалить" у атрибута textAttribute, затем кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что атрибут textAttribute существует в классе userClass</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <li>Нажать кнопку "Удалить", затем кнопку "Да" на форме подтверждения удаления
     * и проверить, что форма исчезла</li>
     * <li>Проверить, что класс userClass отсутствует в системе</li>
     * <li>Нажать кнопку "Удалить" у атрибута textAttribute,
     * затем кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что атрибут textAttribute отсутствует в классе userClass</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionDatabaseManagementAccessMarker()
    {
        // Подготовка
        Attribute textAttribute = DAOAttribute.createText(userClass);
        DSLAttribute.add(textAttribute);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        GUIMetaClass.clickDelete();
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIMetaClass.assertPresent(userClass);

        GUIAttribute.clickDelete(textAttribute);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAttribute.assertPresent(textAttribute);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIAttribute.clickDelete(textAttribute);
        GUIForm.confirmByYes();
        GUIAttribute.assertAbsence(textAttribute);

        GUIMetaClass.clickDelete();
        GUIForm.confirmByYes();
        GUIMetaClass.assertAbsence(userClass);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Пользовательский интерфейс". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что отсутствуют вкладки "Группа атрибутов", "Карточка объекта", "Форма добавления",
     * "Форма редактирования", "Другие формы"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует контент "Комментарии"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Пользовательский интерфейс"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и раздел "Администрирование"</li>
     * <li>Проверить, что присутствует контент "Комментарии"</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что присутствуют вкладки "Группа атрибутов", "Карточка объекта", "Форма добавления",
     * "Форма редактирования", "Другие формы"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionUserInterfaceAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Присутствует вкладка \"Группа атрибутов\"",
                tester.waitDisappear(Div.CLASS_ATTRIBUTE_GROUP_TAB));
        Assert.assertTrue("Присутствует вкладка \"Карточка объекта\"",
                tester.waitDisappear(Div.CLASS_CARD_OBJECT_TAB));
        Assert.assertTrue("Присутствует вкладка \"Форма добавления\"",
                tester.waitDisappear(Div.CLASS_ADD_FORM_TAB));
        Assert.assertTrue("Присутствует вкладка \"Форма редактирования\"",
                tester.waitDisappear(Div.CLASS_EDIT_FORM_TAB));
        Assert.assertTrue("Присутствует вкладка \"Другие формы\"",
                tester.waitDisappear(Div.CLASS_CUSTOM_FORM_TAB));

        GUINavigational.goToAdministration();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAbsenceCommentsSettingsBlock();

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdmin.assertPresentCommentsSettingsBlock();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.ADMINISTRATION_ITEM_ID);

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Отсутствует вкладка \"Группа атрибутов\"",
                tester.find(Div.CLASS_ATTRIBUTE_GROUP_TAB).isDisplayed());
        Assert.assertTrue("Отсутствует вкладка \"Карточка объекта\"",
                tester.find(Div.CLASS_CARD_OBJECT_TAB).isDisplayed());
        Assert.assertTrue("Отсутствует вкладка \"Форма добавления\"",
                tester.find(Div.CLASS_ADD_FORM_TAB).isDisplayed());
        Assert.assertTrue("Отсутствует вкладка \"Форма редактирования\"",
                tester.find(Div.CLASS_EDIT_FORM_TAB).isDisplayed());
        Assert.assertTrue("Отсутствует вкладка \"Другие формы\"",
                tester.find(Div.CLASS_CUSTOM_FORM_TAB).isDisplayed());
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Пользовательский интерфейс". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Пользовательский интерфейс"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Открыть и заполнить форму добавления группы атрибутов, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая группа атрибутов attrGroup отсутствует в системе</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта", затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * отсутствует на вкладке "Карточка объекта"</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта", затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * отсутствует на вкладке "Форма добавления"</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта", затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * отсутствует на вкладке "Форма редактирования"</li>
     * <li>Перейти на вкладку "Другие формы" класса userClass</li>
     * <li>Открыть и заполнить форму добавления формы смены типа, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемая форма quickActionForm отсутствует в списке "Другие формы"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Пользовательский интерфейс"</li>
     * <li>Открыть и заполнить форму добавления формы смены типа, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что создаваемая форма quickActionForm присутствует в списке "Другие формы"</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Открыть и заполнить форму добавления группы атрибутов, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что создаваемая группа атрибутов attrGroup присутствует в системе</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта",
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * присутствует на вкладке "Карточка объекта"</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта",
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * присутствует на вкладке "Форма добавления"</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Открыть и заполнить форму добавления контента типа "Параметры объекта",
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что создаваемый контент propertyListContent
     * присутствует на вкладке "Форма редактирования"</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionUserInterfaceAccessMarker()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        ContentForm propertyListContent = DAOContentCard.createPropertyList(userClass);
        propertyListContent.setXpathId(String.format(CONTENT_PATTERN, propertyListContent.getType(),
                propertyListContent.getCode()));
        CustomForm quickActionFormContent = DAOCustomForm.createQuickActionForm(
                DAOGroupAttr.createSystem(userClass), userClass);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        clickAddAttrGroupAndFillForm(attrGroup);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIGroupAttr.assertAbsence(attrGroup);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIContent.assertAbsence(propertyListContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIForm.clickCancel();
        GUIContent.assertAbsence(propertyListContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIForm.clickCancel();
        GUIContent.assertAbsence(propertyListContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);
        GUICustomForm.clickButtonAddCustomForm();
        GUICustomForm.setFormType(quickActionFormContent.getFormType().getFormTypeId());
        GUICustomForm.setFormTitle(quickActionFormContent.getTitle());
        GUICustomForm.setFormCode(quickActionFormContent.getCode());
        GUICustomForm.setAttrGroup(quickActionFormContent.getAttributeGroupCode());
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICustomForm.getAdvList().content().asserts().rowsAbsence(quickActionFormContent);

        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUICustomForm.clickButtonAddCustomForm();
        GUICustomForm.setFormType(quickActionFormContent.getFormType().getFormTypeId());
        GUICustomForm.setFormTitle(quickActionFormContent.getTitle());
        GUICustomForm.setFormCode(quickActionFormContent.getCode());
        GUICustomForm.setAttrGroup(quickActionFormContent.getAttributeGroupCode());
        GUIForm.clickApply();
        GUICustomForm.getAdvList().content().asserts().rowsPresence(quickActionFormContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        clickAddAttrGroupAndFillForm(attrGroup);
        GUIForm.clickApply();
        GUIGroupAttr.assertPresentOnAttrGroupsCard(attrGroup);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApply();
        GUIContent.assertPresent(propertyListContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApply();
        GUIContent.assertPresent(propertyListContent);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        clickAddContentAndFillForm(propertyListContent);
        GUIForm.clickApply();
        GUIContent.assertPresent(propertyListContent);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Пользовательский интерфейс". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать группу атрибутов attrGroup в классе userClass</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentObjectCardTab в классе userClass</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentAddFormTab в классе userClass
     * и поместить его на вкладку "Форма добавления"</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentEditFormTab в классе userClass
     * и поместить его на вкладку "Форма редактирования"</li>
     * <li>Создать форму "Быстрого добавления и редактирования" в классе userClass</li>
     * <li>Выключить настройку "Inline добавление комментария"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Пользовательский интерфейс"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у группы атрибутов attrGroup
     * и установить название attrGroupNewTitle на форме редактирования, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название группы атрибутов attrGroup не изменилось</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentObjectCardTab
     * и установить название propertyListContentObjectCardTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название контента propertyListContentObjectCardTab не изменилось</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentAddFormTab
     * и установить название propertyListContentAddFormTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название контента propertyListContentAddFormTab не изменилось</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentEditFormTab
     * и установить название propertyListContentEditFormTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название контента propertyListContentEditFormTab не изменилось</li>
     * <li>Перейти на вкладку "Другие формы" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у формы quickActionForm в списке других форм
     * и установить название quickActionFormNewTitle на форме редактирования, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти в раздел "Администрирование"</li>
     * <li>Открыть форму редактирования настроек в блоке "Комментарии"</li>
     * <li>Включить настройку "Inline добавление комментария", затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что настройка "Inline добавление комментария" выключена</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Пользовательский интерфейс"</li>
     * <li>Открыть форму редактирования настроек в блоке "Комментарии"</li>
     * <li>Включить настройку "Inline добавление комментария", затем нажать кнопку "Сохранить"
     * и проверить, что форма исчезла</li>
     * <li>Проверить, что настройка "Inline добавление комментария" включена</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у группы атрибутов attrGroup
     * и установить название attrGroupNewTitle на форме редактирования, затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что название группы атрибутов attrGroup - attrGroupNewTitle</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentObjectCardTab
     * и установить название propertyListContentObjectCardTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что название контента
     * propertyListContentObjectCardTab - propertyListContentObjectCardTabNewTitle</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentAddFormTab
     * и установить название propertyListContentAddFormTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что название контента propertyListContentAddFormTab - propertyListContentAddFormTabNewTitle</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у контента propertyListContentEditFormTab
     * и установить название propertyListContentEditFormTabNewTitle на форме редактирования,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что название контента propertyListContentEditFormTab - propertyListContentEditFormTabNewTitle</li>
     * <li>Перейти на вкладку "Другие формы" класса userClass</li>
     * <li>Нажать на пиктограмму "Редактировать" у формы quickActionForm в списке других форм
     * и установить название quickActionFormNewTitle на форме редактирования, затем нажать кнопку "Сохранить"</li>
     * </ol>
     */
    @Test
    public void testEditPermissionUserInterfaceAccessMarker()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup);

        ContentForm propertyListContentObjectCardTab = DAOContentCard.createPropertyList(userClass);
        ContentForm propertyListContentAddFormTab = DAOContentCard.createPropertyList(userClass);
        propertyListContentAddFormTab.setTab(MetaclassCardTab.NEWENTRYFORM.get());
        ContentForm propertyListContentEditFormTab = DAOContentCard.createPropertyList(userClass);
        propertyListContentEditFormTab.setTab(MetaclassCardTab.EDITFORM.get());
        DSLContent.add(propertyListContentObjectCardTab, propertyListContentAddFormTab, propertyListContentEditFormTab);

        CustomForm quickActionForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass),
                userClass);
        DSLCustomForm.add(quickActionForm);

        String attrGroupNewTitle = ModelUtils.createTitle();
        String propertyListContentObjectCardTabNewTitle = ModelUtils.createTitle();
        String propertyListContentAddFormTabNewTitle = ModelUtils.createTitle();
        String propertyListContentEditFormTabNewTitle = ModelUtils.createTitle();
        String quickActionFormNewTitle = ModelUtils.createTitle();

        DSLAdmin.setAddCommentInlineForm(false);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        clickEditAttrGroupAndFillTitle(attrGroup, attrGroupNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIGroupAttr.assertGroupAttrTitle(attrGroup.getTitle());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        clickEditContentAndFillTitle(propertyListContentObjectCardTab, propertyListContentObjectCardTabNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIContent.assertContentTitle(propertyListContentObjectCardTab, propertyListContentObjectCardTab.getTitle());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        clickEditContentAndFillTitle(propertyListContentAddFormTab, propertyListContentAddFormTabNewTitle);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIForm.clickCancel();
        GUIContent.assertContentTitle(propertyListContentAddFormTab, propertyListContentAddFormTab.getTitle());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        clickEditContentAndFillTitle(propertyListContentEditFormTab, propertyListContentEditFormTabNewTitle);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIForm.clickCancel();
        GUIContent.assertContentTitle(propertyListContentEditFormTab, propertyListContentEditFormTab.getTitle());

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);
        GUICustomForm.editCustomFormByUuid(quickActionForm);
        GUICustomForm.setFormTitle(quickActionFormNewTitle);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUINavigational.goToAdministration();
        GUIAdmin.openEditCommentSettingsForm();
        GUIAdmin.setAddCommentInline(true);
        GUIForm.clickApplyWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAdmin.assertAddCommentInlineDisabled();

        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIAdmin.openEditCommentSettingsForm();
        GUIAdmin.setAddCommentInline(true);
        GUIForm.applyForm();
        GUIAdmin.assertAddCommentInlineEnabled();
        Cleaner.afterTest(() -> DSLAdmin.setAddCommentInlineForm(false));

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        clickEditAttrGroupAndFillTitle(attrGroup, attrGroupNewTitle);
        GUIForm.clickApply();
        GUIGroupAttr.assertGroupAttrTitle(attrGroupNewTitle);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        clickEditContentAndFillTitle(propertyListContentObjectCardTab, propertyListContentObjectCardTabNewTitle);
        GUIForm.clickApply();
        GUIContent.assertContentTitle(propertyListContentObjectCardTab, propertyListContentObjectCardTabNewTitle);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        clickEditContentAndFillTitle(propertyListContentAddFormTab, propertyListContentAddFormTabNewTitle);
        GUIForm.clickApply();
        GUIContent.assertContentTitle(propertyListContentAddFormTab, propertyListContentAddFormTabNewTitle);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        clickEditContentAndFillTitle(propertyListContentEditFormTab, propertyListContentEditFormTabNewTitle);
        GUIForm.clickApply();
        GUIContent.assertContentTitle(propertyListContentEditFormTab, propertyListContentEditFormTabNewTitle);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);
        GUICustomForm.editCustomFormByUuid(quickActionForm);
        GUICustomForm.setFormTitle(quickActionFormNewTitle);
        GUIForm.clickApply();
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Пользовательский интерфейс". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать группу атрибутов attrGroup в классе userClass</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentObjectCardTab в классе userClass</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentAddFormTab в классе userClass
     * и поместить его на вкладку "Форма добавления"</li>
     * <li>Создать контент типа "Параметры объекта" propertyListContentEditFormTab в классе userClass
     * и поместить его на вкладку "Форма редактирования"</li>
     * <li>Создать форму "Быстрого добавления и редактирования" в классе userClass</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Пользовательский интерфейс"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у группы атрибутов attrGroup
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что группа атрибутов attrGroup присутствует в системе</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentObjectCardTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что контент propertyListContentObjectCardTab существует на вкладке "Карточка объекта"</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentAddFormTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что контент propertyListContentAddFormTab существует на вкладке "Форма добавления"</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentEditFormTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что контент propertyListContentEditFormTab существует на вкладке "Форма редактирования"</li>
     * <li>Перейти на вкладку "Другие формы" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента quickActionForm,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что контент quickActionForm существует в списке другие формы</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркерам доступа "Пользовательский интерфейс"</li>
     * <li>Нажать на пиктограмму "Удалить" у контента quickActionForm,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что контент quickActionForm отсутствует в списке другие формы</li>
     * <li>Перейти на вкладку "Группы атрибутов" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у группы атрибутов attrGroup
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что группа атрибутов attrGroup отсутствует в системе</li>
     * <li>Перейти на вкладку "Карточка объекта" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentObjectCardTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что контент propertyListContentObjectCardTab отсутствует на вкладке "Карточка объекта"</li>
     * <li>Перейти на вкладку "Форма добавления" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentAddFormTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что контент propertyListContentAddFormTab отсутствует на вкладке "Форма добавления"</li>
     * <li>Перейти на вкладку "Форма редактирования" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у контента propertyListContentEditFormTab,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что контент propertyListContentEditFormTab отсутствует на вкладке "Форма редактирования"</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionUserInterfaceAccessMarker()
    {
        // Подготовка
        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup);

        ContentForm propertyListContentObjectCardTab = DAOContentCard.createPropertyList(userClass);
        ContentForm propertyListContentAddFormTab = DAOContentCard.createPropertyList(userClass);
        propertyListContentAddFormTab.setTab(MetaclassCardTab.NEWENTRYFORM.get());
        ContentForm propertyListContentEditFormTab = DAOContentCard.createPropertyList(userClass);
        propertyListContentEditFormTab.setTab(MetaclassCardTab.EDITFORM.get());
        DSLContent.add(propertyListContentObjectCardTab, propertyListContentAddFormTab, propertyListContentEditFormTab);

        CustomForm quickActionForm = DAOCustomForm.createQuickActionForm(DAOGroupAttr.createSystem(userClass),
                userClass);
        DSLCustomForm.add(quickActionForm);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIGroupAttr.clickDeleteButton(attrGroup);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIGroupAttr.assertPresent(attrGroup);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickDeleteIcon(propertyListContentObjectCardTab);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIContent.assertPresent(propertyListContentObjectCardTab);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickDeleteIcon(propertyListContentAddFormTab);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIContent.assertPresent(propertyListContentAddFormTab);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        GUIContent.clickDeleteIcon(propertyListContentEditFormTab);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIContent.assertPresent(propertyListContentEditFormTab);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.CUSTOMFORM);
        GUICustomForm.getAdvList().content().clickPict(quickActionForm, GUICustomForm.DELETE_CUSTOM_FORM_CODE);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUICustomForm.getAdvList().content().asserts().rowsPresence(quickActionForm);

        accessMarkerMatrix.addAccessMarkerPermission(USER_INTERFACE, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUICustomForm.getAdvList().content().clickPict(quickActionForm, GUICustomForm.DELETE_CUSTOM_FORM_CODE);
        GUIForm.confirmByYes();
        GUICustomForm.getAdvList().content().asserts().rowsAbsence(quickActionForm);
        quickActionForm.setExists(false);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.ATTRIBUTGROUPS);
        GUIGroupAttr.clickDeleteButton(attrGroup);
        GUIForm.confirmByYes();
        GUIGroupAttr.assertAbsence(attrGroup);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.clickDeleteIcon(propertyListContentObjectCardTab);
        GUIForm.confirmByYes();
        GUIContent.assertAbsence(propertyListContentObjectCardTab);
        propertyListContentObjectCardTab.setExists(false);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.NEWENTRYFORM);
        GUIContent.clickDeleteIcon(propertyListContentAddFormTab);
        GUIForm.confirmByYes();
        GUIContent.assertAbsence(propertyListContentAddFormTab);
        propertyListContentAddFormTab.setExists(false);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.EDITFORM);
        GUIContent.clickDeleteIcon(propertyListContentEditFormTab);
        GUIForm.confirmByYes();
        GUIContent.assertAbsence(propertyListContentEditFormTab);
        propertyListContentEditFormTab.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Права доступа". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что отсутствует вкладка "Права доступа"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Права доступа"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствуют вкладка "Права доступа"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionAccessRightsAccessMarker()
    {
        // Подготовка
        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Присутствует вкладка \"Права доступа\"",
                tester.waitDisappear(Div.CLASS_PERMISSION_SETTINGS_TAB));

        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        Assert.assertTrue("Отсутствует вкладка \"Права доступа\"",
                tester.find(Div.CLASS_PERMISSION_SETTINGS_TAB).isDisplayed());
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Права доступа". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных", "Роли" и "Права доступа"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass, вкладка "Права доступа"</li>
     * <li>Нажать кнопку добавления нового профиля и заполнить форму добавления профиля</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый профиль отсутствует в метаклассе userClass</li>
     * <li>Нажать кнопку добавления нового маркера в группе маркеров "Просмотр атрибутов"
     * и заполнить форму добавления маркера</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый маркер отсутствует в матрице прав</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Права доступа"</li>
     * <li>Нажать кнопку добавления нового профиля и заполнить форму добавления профиля,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый профиль securityProfile присутствует в метаклассе userClass</li>
     * <li>Нажать кнопку добавления нового маркера в группе маркеров "Просмотр атрибутов"
     * и заполнить форму добавления маркера, затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый маркер присутствует в матрице прав</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionAccessRightsAccessMarker()
    {
        // Подготовка
        SecurityRole employeeRole = SysRole.employee();
        SecurityProfile securityProfile = DAOSecurityProfile.create(false, null, employeeRole);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.goToAccessMatrixTab();
        clickAddProfileAndFillForm(securityProfile, employeeRole);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertAbsence(securityProfile);

        String markerTitle = ModelUtils.createTitle();
        clickAddMarkerAndFillForm(markerTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertMarkerPresentByTitle(markerTitle, false);

        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddProfileAndFillForm(securityProfile, employeeRole);
        GUIForm.applyForm();
        GUIAccessMatrix.assertPresent(securityProfile);

        Cleaner.afterTest(() ->
        {
            if (DSLSecurityProfile.isPresentByTitle(securityProfile))
            {
                DSLSecurityProfile.delete(securityProfile);
            }
        });

        clickAddMarkerAndFillForm(markerTitle);
        GUIForm.applyForm();
        GUIAccessMatrix.assertMarkerPresentByTitle(markerTitle, true);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Права доступа". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать маркер типа "Просмотр атрибутов" marker в классе userClass</li>
     * <li>В матрице прав класса userClass добавить профиль securityProfile с ролью – "Сотрудник"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных", "Роли" и "Права доступа"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass, вкладка "Права доступа"</li>
     * <li>Открыть форму редактирования профиля securityProfile в матрице прав,
     * установить название securityProfileNewTitle затем нажать "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название профиля securityProfile не изменилось</li>
     * <li>Открыть форму редактирования маркера marker в матрице прав,
     * установить название markerNewTitle затем нажать "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что название маркера marker не изменилось</li>
     * <li>Установить чекбокс в ячейке securityProfile-marker, затем нажать кнопку "Сохранить матрицу"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <lI>Нажать кнопку "Отменить изменения"</lI>
     * <li>Проверить, что в ячейке securityProfile-marker чекбокс не установлен</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Права доступа"</li>
     * <li>Открыть форму редактирования профиля securityProfile в матрице прав,
     * установить название securityProfileNewTitle затем нажать "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название профиля securityProfile - securityProfileNewTitle</li>
     * <li>Открыть форму редактирования маркера marker в матрице прав,
     * установить название markerNewTitle затем нажать "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что название маркера marker - markerNewTitle</li>
     * <li>Установить чекбокс в ячейке securityProfile-marker, затем нажать кнопку "Сохранить матрицу"</li>
     * <lI>Нажать кнопку "Отменить изменения"</lI>
     * <li>Проверить, что в ячейке securityProfile-marker чекбокс установлен</li>
     * </ol>
     */
    @Test
    public void testEditPermissionAccessRightsAccessMarker()
    {
        // Подготовка
        SecurityMarker marker = new SecurityMarkerViewAttrs(userClass).apply();

        SecurityRole employeeRole = SysRole.employee();
        SecurityProfile securityProfile = DAOSecurityProfile.create(false, null, employeeRole);
        DSLSecurityProfile.add(securityProfile);

        String markerNewTitle = ModelUtils.createTitle();
        String securityProfileNewTitle = ModelUtils.createTitle();

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(SECURITY_ROLE, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.goToAccessMatrixTab();

        clickEditProfileAndFillTitle(securityProfile, securityProfileNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertProfileTitle(securityProfile, securityProfile.getTitle());

        clickEditMarkerAndFillTitle(marker, markerNewTitle);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertMarkerTitle(marker, marker.getTitle());

        GUIAccessMatrix.clickMatrixCellCheckbox(marker.getRightCode(), securityProfile.getCode());
        GUIAccessMatrix.clickSave();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.clickErase();
        GUIAccessMatrix.assertCheckBoxValue(marker.getRightCode(), securityProfile.getCode(), false);

        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickEditProfileAndFillTitle(securityProfile, securityProfileNewTitle);
        GUIForm.applyForm();
        GUIAccessMatrix.assertProfileTitle(securityProfile, securityProfileNewTitle);

        clickEditMarkerAndFillTitle(marker, markerNewTitle);
        GUIForm.applyForm();
        GUIAccessMatrix.assertMarkerTitle(marker, markerNewTitle);

        GUIAccessMatrix.clickMatrixCellCheckbox(marker.getRightCode(), securityProfile.getCode());
        GUIAccessMatrix.clickSave();
        GUIAccessMatrix.clickErase();
        GUIAccessMatrix.assertCheckBoxValue(marker.getRightCode(), securityProfile.getCode(), true);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Права доступа". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать маркер типа "Просмотр атрибутов" marker в классе userClass</li>
     * <li>В матрице прав класса userClass добавить профиль securityProfile с ролью – "Сотрудник"</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Права доступа"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass, вкладка "Права доступа"</li>
     * <li>Нажать на иконку "Удалить" у профиля securityProfile в матрице прав,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что профиль securityProfile присутствует в матрице прав</li>
     * <li>Нажать на иконку "Удалить" у маркера marker в матрице прав,
     * затем нажать "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что маркер marker присутствует в матрице прав</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Права доступа"</li>
     * <li>Нажать на иконку "Удалить" у профиля securityProfile в матрице прав,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что профиль securityProfile отсутствует в матрице прав</li>
     * <li>Нажать на иконку "Удалить" у маркера marker в матрице прав,
     * затем нажать "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что маркер marker отсутствует в матрице прав</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionAccessRightsAccessMarker()
    {
        // Подготовка
        SecurityMarker marker = new SecurityMarkerViewAttrs(userClass).apply();

        SecurityRole employeeRole = SysRole.employee();
        SecurityProfile securityProfile = DAOSecurityProfile.create(false, null, employeeRole);
        DSLSecurityProfile.add(securityProfile);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.PERMISSIONSETTINGS);
        GUIAccessMatrix.goToAccessMatrixTab();
        GUIAccessMatrix.clickDeleteProfile(securityProfile);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertPresent(securityProfile);

        GUIAccessMatrix.clickDeleteMarker(marker.getRightCode());
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIAccessMatrix.assertMarkerPresentByTitle(marker.getTitle(), true);

        accessMarkerMatrix.addAccessMarkerPermission(ACCESS_RIGHTS, DELETE);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIAccessMatrix.clickDeleteProfile(securityProfile);
        GUIForm.confirmByYes();
        GUIAccessMatrix.assertAbsence(securityProfile);
        securityProfile.setExists(false);

        GUIAccessMatrix.clickDeleteMarker(marker.getRightCode());
        GUIForm.confirmByYes();
        GUIAccessMatrix.assertMarkerPresentByTitle(marker.getTitle(), false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Жизненный цикл". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass с жизненным циклом</li>
     * <li>Создать пользовательский статус userStatus</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Управление схемой базы данных"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что отсутствует вкладка "Жизненный цикл"</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Профили связанных жизненных циклов"</li>
     * <li>Перейти на карточку статуса userStatus</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти в раздел "Профили связанных жизненных циклов"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок со списком профилей ЖЦ</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Жизненный цикл"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что присутствует блок со списком профилей ЖЦ</li>
     * <li>Перейти на карточку статуса userStatus</li>
     * <li>Перейти на карточку класса userClass</li>
     * <li>Проверить, что присутствует вкладка "Жизненный цикл"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Профили связанных жизненных циклов"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionWorkflowAccessMarker()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        BoStatus userStatus = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(userStatus);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Присутствует вкладка \"Жизненный цикл\"",
                tester.waitDisappear(Div.CLASS_WF_TAB));

        GUIAdminNavigationTree.assertItemAbsent("processSettings:");
        GUIAdminNavigationTree.assertItemAbsent("wfProfiles:");

        GUIBoStatus.goToStatusCard(userStatus);
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUINavigational.goToWfProfiles();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIWfProfile.assertPresentWfProfilesBlock(false);

        accessMarkerMatrix.addAccessMarkerPermission(WORKFLOW, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIWfProfile.assertPresentWfProfilesBlock(true);
        GUIBoStatus.goToStatusCard(userStatus);

        GUIMetaClass.goToCard(userClass);
        Assert.assertTrue("Отсутствует вкладка \"Жизненный цикл\"",
                tester.find(Div.CLASS_WF_TAB).isDisplayed());

        GUIAdminNavigationTree.assertItemPresent("processSettings:");
        GUIAdminNavigationTree.assertItemPresent("wfProfiles:");
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Жизненный цикл". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass с жизненным циклом</li>
     * <li>В классе userClass создать 2 статуса userStatusTransitionFrom и userStatusTransitionTo</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Жизненный цикл"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на вкладку "Жизненный цикл" класса userClass</li>
     * <li>Нажать на кнопку "Добавить статус", заполнить поля на форме, затем нажать на кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый статус userStatus отсутствует</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Жизненный цикл"</li>
     * <li>Перейти на вкладку "Жизненный цикл" класса userClass</li>
     * <li>Нажать на кнопку "Добавить статус", заполнить поля на форме,
     * затем нажать на кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый статус userStatus присутствует</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionWorkflowAccessMarker()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        BoStatus userStatus = DAOBoStatus.createUserStatus(userClass);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(WORKFLOW, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);
        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        clickAddStatusAndFillForm(userStatus);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIBoStatus.assertAbsence(userStatus);

        accessMarkerMatrix.addAccessMarkerPermission(WORKFLOW, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddStatusAndFillForm(userStatus);
        GUIForm.applyForm();
        GUIBoStatus.assertPresent(userStatus);
        userStatus.setExists(true);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Жизненный цикл". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass с жизненным циклом</li>
     * <li>Создать профиль связанных жизненных циклов wfProfile</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркерам доступа "Управление схемой базы данных" и "Жизненный цикл"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на вкладку "Жизненный цикл" класса userClass</li>
     * <li>Нажать на пиктограмму "Удалить" у статуса userStatus,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что статус userStatus присутствует в списке статусов</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркерам доступа "Жизненный цикл"</li>
     * <li>Нажать на пиктограмму "Удалить" у статуса userStatus,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что статус userStatus отсутствует в списке статусов</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionWorkflowAccessMarker()
    {
        // Подготовка
        MetaClass userClass = DAOUserClass.createWithWF();
        DSLMetaClass.add(userClass);

        BoStatus userStatus = DAOBoStatus.createUserStatus(userClass);
        DSLBoStatus.add(userStatus);

        accessMarkerMatrix.addAccessMarkerPermission(DATABASE_MANAGEMENT, VIEW);
        accessMarkerMatrix.addAccessMarkerPermission(WORKFLOW, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIMetaClass.goToTab(userClass, MetaclassCardTab.LIFECYCLE);
        GUIBoStatus.clickDeleteStatus(userStatus);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIBoStatus.assertPresent(userStatus);

        accessMarkerMatrix.addAccessMarkerPermission(WORKFLOW, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIBoStatus.clickDeleteStatus(userStatus);
        GUIForm.confirmByYes();
        GUIBoStatus.assertAbsence(userStatus);
        userStatus.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Скриптовые настройки". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptInfo</li>
     * <li>Создать скриптовый модуль moduleConf</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Каталог скриптов и модулей"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка системы"
     * и разделы "Каталог скриптов и модулей", "Каталог модулей", "Каталог скриптов"</li>
     * <li>Перейти в раздел "Каталог модулей"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок список скриптовых модулей</li>
     * <li>Перейти в раздел "Каталог скриптов"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что отсутствует блок список скриптов</li>
     * <li>Перейти на карточку скрипта scriptInfo</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Перейти на карточку скриптового модуля moduleConf</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Настройки поиска"</li>
     * <li>Обновить страницу</li>
     * <li>Перейти в раздел "Каталог скриптов и модулей"</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка системы"
     * и разделы "Каталог скриптов и модулей", "Каталог модулей", "Каталог скриптов"</li>
     * <li>Перейти в раздел "Каталог модулей"</li>
     * <li>Проверить, что присутствует блок список модулей</li>
     * <li>Перейти в раздел "Каталог скриптов"</li>
     * <li>Проверить, что присутствует блок список скриптов</li>
     * <li>Перейти на карточку скрипта scriptInfo</li>
     * </ol>
     */
    @Test
    public void testViewPermissionScriptsAccessMarker()
    {
        // Подготовка
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptInfo);

        ModuleConf moduleConf = DAOModuleConf.create();
        DSLModuleConf.add(moduleConf);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScriptsAndModules();
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("scriptsandmodules:");
        GUIAdminNavigationTree.assertItemAbsent("scriptmodules:");
        GUIAdminNavigationTree.assertItemAbsent("scriptcatalog:");

        GUINavigational.goToScriptModules();
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIScriptModulesList.advlist().asserts().absence();

        GUINavigational.goToScripts();
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIScriptCatalogList.advlist().asserts().absence();

        GUIScript.goToCard(scriptInfo);
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIScriptModule.goToCard(moduleConf);
        GUIError.assertDialogError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUINavigational.goToScriptsAndModules();
        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("scriptsandmodules:");
        GUIAdminNavigationTree.expandItem("scriptsandmodules:");
        GUIAdminNavigationTree.assertItemPresent("scriptmodules:");
        GUIAdminNavigationTree.assertItemPresent("scriptcatalog:");

        GUINavigational.goToScriptModules();
        GUIScriptModulesList.advlist().asserts().presence();

        GUINavigational.goToScripts();
        GUIScriptCatalogList.advlist().asserts().presence();

        GUIScript.goToCard(scriptInfo);
    }

    /**
     * Тестирование действия "Создать" маркера доступа "Скриптовые настройки". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать пользовательский класс userClass</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Скриптовые настройки"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в раздел "Каталог модулей"</li>
     * <li>Нажать кнопку "Добавить модуль" и заполнить необходимые поля на форме добавления модуля,
     * затем нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что создаваемый скриптовый модуль moduleConf отсутствует в списке скриптовых модулей</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на создание
     * по маркеру доступа "Скриптовые настройки"</li>
     * <li>Нажать кнопку "Добавить модуль" и заполнить необходимые поля на форме добавления модуля,
     * затем нажать кнопку "Сохранить" и проверить, что форма исчезла</li>
     * <li>Проверить, что создаваемый скриптовый модуль moduleConf присутствует в списке скриптовых модулей</li>
     * </ol>
     */
    @Test
    public void testCreatePermissionScriptsAccessMarker()
    {
        // Подготовка
        ModuleConf moduleConf = DAOModuleConf.create();

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScriptModules();
        clickAddScriptModuleAndFillForm(moduleConf);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIScriptModulesList.advlist().content().asserts().rowsAbsence(moduleConf);

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, CREATE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        clickAddScriptModuleAndFillForm(moduleConf);
        GUIForm.applyForm();
        GUIScriptModulesList.advlist().content().asserts().rowsPresence(moduleConf);
        moduleConf.setExists(true);
    }

    /**
     * Тестирование действия "Редактировать" маркера доступа "Скриптовые настройки". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Скриптовые настройки"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти на карточку скриптового модуля moduleConf</li>
     * <li>Открыть форму редактирования скриптового модуля moduleConf
     * и установить тело скрипта moduleConfNewScriptBody</li>
     * <li>Нажать кнопку "Сохранить"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что тело скрипта скриптового модуля moduleConf не изменилось</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на редактирование
     * по маркеру доступа "Настройки поиска"</li>
     * <li>Перейти на карточку скриптового модуля moduleConf</li>
     * <li>Открыть форму редактирования скриптового модуля moduleConf
     * и установить тело скрипта moduleConfNewScriptBody, затем нажать кнопку "Сохранить" и проверить, что форма
     * исчезла</li>
     * <li>Проверить, что тело скрипта скриптового модуля moduleConf - moduleConfNewScriptBody</li>
     * </ol>
     */
    @Test
    public void testEditPermissionScriptsAccessMarker()
    {
        // Подготовка
        ModuleConf moduleConf = DAOModuleConf.create();
        DSLModuleConf.add(moduleConf);

        String moduleConfNewScriptBody = DAOModuleConf.createScript();

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        // Действия и проверки
        GUILogon.login(superUser);

        GUIScriptModule.goToCard(moduleConf);
        GUIScriptModule.clickEdit();
        GUIScriptModule.fillScriptBody(moduleConfNewScriptBody);
        GUIForm.clickApplyWithAssertErrorAndCancelForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIScriptModule.assertScriptBodyOnCard(moduleConf.getScriptBody());

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, EDIT);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIScriptModule.clickEdit();
        GUIScriptModule.fillScriptBody(moduleConfNewScriptBody);
        GUIForm.applyForm();
        GUIScriptModule.assertScriptBodyOnCard(moduleConfNewScriptBody);
    }

    /**
     * Тестирование действия "Удалить" маркера доступа "Скриптовые настройки". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скриптовый модуль moduleConf</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Скриптовые настройки"</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Перейти в каталог скриптовых модулей</li>
     * <li>Нажать на иконку удалить у скриптового модуля moduleConf,
     * затем нажать кнопку "Да" на форме подтверждения удаления</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>Проверить, что в списке скриптовых модулей присутствует скриптовый модуль moduleConf</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на удаление
     * по маркеру доступа "Скриптовые настройки"</li>
     * <li>Нажать на иконку удалить у скриптового модуля moduleConf,
     * затем нажать кнопку "Да" на форме подтверждения удаления и проверить, что форма исчезла</li>
     * <li>Проверить, что в списке скриптовых модулей отсутствует скриптовый модуль moduleConf</li>
     * </ol>
     */
    @Test
    public void testDeletePermissionScriptsAccessMarker()
    {
        // Подготовка
        ModuleConf moduleConf = DAOModuleConf.create();
        DSLModuleConf.add(moduleConf);

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, VIEW);
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        DSLAdminProfile.edit(adminProfile);

        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScriptModules();
        GUIScriptModulesList.advlist().content().clickPict(moduleConf, GUIScriptModulesList.DELETE_COLUMN);
        GUIForm.clickYesWithAssertError(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);
        GUIScriptModulesList.advlist().content().asserts().rowsPresence(moduleConf);

        accessMarkerMatrix.addAccessMarkerPermission(SCRIPTS, DELETE);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);

        GUIScriptModulesList.advlist().content().clickPict(moduleConf, GUIScriptModulesList.DELETE_COLUMN);
        GUIForm.confirmByYes();
        GUIScriptModulesList.advlist().content().asserts().rowsAbsence(moduleConf);
        moduleConf.setExists(false);
    }

    /**
     * Тестирование действия "Просмотр" маркера доступа "Файлы кастомизации". <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270973730 <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти в систему под суперпользователем superUser</li>
     * <li>Проверить, что в левом навигационном меню отсутствует блок "Настройка бизнес-процессов"
     * и раздел "Эскалации"</li>
     * <li>Перейти на карточку "Эскалации"</li>
     * <li>Проверить, что появилось диалоговое окно с ошибкой "У Вас нет прав на выполнение данной операции.
     * Для получения доступа необходимо обратиться к администратору."</li>
     * <li>В матрицу маркеров доступа accessMarkerMatrix добавить право на просмотр
     * по маркеру доступа "Счётчики времени"</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в левом навигационном меню присутствует блок "Настройка бизнес-процессов"
     * и раздел "Счётчики времени"</li>
     * </ol>
     */
    @Test
    public void testViewPermissionCustomizationFilesAccessMarker()
    {
        // Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToCustomizationCatalog();
        GUIForm.assertErrorMessageOnForm(ErrorMessages.CHECK_PERMISSION_ACCESS_MARKER);

        GUIAdminNavigationTree.assertItemAbsent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemAbsent("customizationCatalog:");

        accessMarkerMatrix.addAccessMarkerPermission(CUSTOMIZATION_FILES, VIEW);
        DSLAdminProfile.setAccessMarkerMatrix(adminProfile, accessMarkerMatrix);
        tester.refresh();

        GUIAdminNavigationTree.assertItemPresent(GUIAdminNavigationTree.SYSTEM_SETTINGS_ITEM_ID);
        GUIAdminNavigationTree.assertItemPresent("customizationCatalog:");
    }

    private void clickAddMetaClassAndFillForm(MetaClass metaClass, MetaClass createdMetaClass)
    {
        GUIMetaClass.callAddForm(metaClass);
        GUIMetaClass.fillAddForm(createdMetaClass);
    }

    private static void clickCopyMetaClassAndFillForm(MetaClass copiedMetaClass)
    {
        GUIMetaClass.clickCopy();
        GUIMetaClass.fillAddForm(copiedMetaClass);
    }

    private static void clickAddAttributeAndFillForm(Attribute createdAttribute)
    {
        GUIAttribute.clickAdd();
        GUIAttribute.fillBaseFieldsOnAddForm(createdAttribute);
    }

    private static void clickEditMetaClassAndFillTitle(MetaClass userCase, String userCaseNewTitle)
    {
        GUIMetaClass.openEditForm(userCase);
        GUIMetaClass.setTitle(userCaseNewTitle);
    }

    private static void clickEditAttributeAndFillTitle(Attribute textAttribute, String textAttributeNewTitle)
    {
        GUIAttribute.clickEdit(textAttribute);
        GUIAttribute.fillAttrTitle(textAttributeNewTitle);
    }

    private static void clickAddAttrGroupAndFillForm(GroupAttr attrGroup)
    {
        GUIGroupAttr.clickAdd();
        GUIGroupAttr.setTitle(attrGroup.getTitle());
        GUIGroupAttr.setCode(attrGroup.getCode());
    }

    private static void clickAddContentAndFillForm(ContentForm propertyListContent)
    {
        GUIContent.enableEditProperties();
        GUIContent.clickAdd();
        GUIContent.selectContentType(propertyListContent);
        GUIContent.fillMainFields(propertyListContent);
    }

    private static void clickEditContentAndFillTitle(ContentForm propertyListContentAddFormTab,
            String propertyListContentAddFormTabNewTitle)
    {
        GUIContent.clickEditContent(propertyListContentAddFormTab);
        GUIContent.fillTitle(propertyListContentAddFormTabNewTitle);
    }

    private static void clickEditAttrGroupAndFillTitle(GroupAttr attrGroup, String attrGroupNewTitle)
    {
        GUIGroupAttr.openEditForm(attrGroup);
        GUIGroupAttr.setTitle(attrGroupNewTitle);
    }

    private static void clickAddProfileAndFillForm(SecurityProfile securityProfile, SecurityRole employeeRole)
    {
        GUIAccessMatrix.pressButtonAddProfile();
        GUIProfileForm.setTitle(securityProfile.getTitle());
        GUIProfileForm.setCode(securityProfile.getCode());
        GUIProfileForm.setRole(true, employeeRole);
    }

    private static void clickAddMarkerAndFillForm(String markerTitle)
    {
        GUIAccessMatrix.clickAddMarker(SecurityMarker.VIEW_ATTRS);
        GUIMarkerForm.editTitle(markerTitle);
    }

    private static void clickEditProfileAndFillTitle(SecurityProfile securityProfile, String securityProfileNewTitle)
    {
        GUIAccessMatrix.callEditForm(securityProfile);
        GUIProfileForm.setTitle(securityProfileNewTitle);
    }

    private static void clickEditMarkerAndFillTitle(SecurityMarker marker, String markerNewTitle)
    {
        GUIAccessMatrix.clickEditMarker(marker.getRightCode());
        GUIMarkerForm.editTitle(markerNewTitle);
    }

    private static void clickAddStatusAndFillForm(BoStatus userStatus)
    {
        GUIBoStatus.clickAddStatus();
        GUIBoStatus.fillAddUserStatusForm(userStatus);
    }

    private static void clickAddScriptModuleAndFillForm(ModuleConf moduleConf)
    {
        GUIScriptModulesList.clickAddModule();
        GUIScriptModule.fillAddForm(moduleConf, false);
    }
}