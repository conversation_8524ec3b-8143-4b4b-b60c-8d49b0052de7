package ru.naumen.selenium.cases.operator.rights;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.accessmatrix.GUIAccessMatrix;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.ObjectType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.security.SecurityMarker;

/**
 * Вспомогательный класс для действий и проверок, используемых в {@link UnlicCustomPermissionSetTest}
 *
 * <AUTHOR>
 * @since 06.08.2024
 */
public class UnlicCustomPermissionSetAsserts extends AbstractTestCase
{
    /**
     * Получить системные атрибуты класса: {@code author}, {@code responsible}
     */
    protected static Attribute[] getSystemAttributes(MetaClass metaClass)
    {
        Attribute author = SysAttribute.author(metaClass);
        Attribute responsible = SysAttribute.responsible(metaClass);
        Attribute responsibleTeam = SysAttribute.responsibleTeam(metaClass);
        Attribute responsibleEmployee = SysAttribute.responsibleEmployee(metaClass);

        /* По какому-то непонятному стечению обстоятельств у нас в системе используется разные регистры первых букв у
         слов сотрудник и команда для класса "Запрос" и остальных*/
        // Меняем первую букву слова "сотрудник" на заглавную
        responsibleEmployee.setTitle("Ответственный (Сотрудник)");
        // Меняем первую букву слова "команда" на заглавную
        responsibleTeam.setTitle("Ответственный (Команда)");
        return new Attribute[] { author, responsible, responsibleTeam, responsibleEmployee };
    }

    /**
     * Создать пользовательские атрибуты: {@code textRTF} (код textRTFCustom), {@code boLink}, {@code backBoLinksAttr},
     * {@code aggrCustom} (код aggrCustom), {@code aggrCustom_em}, {@code aggrCustom_te}
     */
    protected static Attribute[] createUserAttributes(MetaClass metaClass)
    {
        Attribute textRTF = DAOAttribute.createTextRTF(metaClass.getFqn());
        textRTF.setCode("textRTFCustom");
        Attribute boLink = DAOAttribute.createObjectLink(metaClass, metaClass);
        Attribute backBoLinksAttr = DAOAttribute.createBackBOLinks(metaClass, boLink);
        Attribute aggrCustom = DAOAttribute.createAggregate(metaClass,
                AggregatedClasses.OU_AND_TEAM, null, null);
        aggrCustom.setCode("aggrCustom");

        Attribute aggrCustom_em = DAOAttribute.createPseudo(aggrCustom.getAggregatedEmployee(),
                aggrCustom.getAggregatedEmployeeCode());
        aggrCustom_em.setType(ObjectType.CODE);

        Attribute aggrCustom_te = DAOAttribute.createPseudo(aggrCustom.getAggregatedTeam(),
                aggrCustom.getAggregatedTeamCode());
        aggrCustom_te.setType(ObjectType.CODE);

        DSLAttribute.add(textRTF, boLink, backBoLinksAttr, aggrCustom);
        return new Attribute[] { textRTF, boLink, backBoLinksAttr, aggrCustom, aggrCustom_em, aggrCustom_te };
    }

    /**
     * Проверки разрешений на атрибуты для метакласса userClass$userCase (в лицензии определены разрешения на
     * метакласс userClass и, дополнительно, на тип userCase)
     * <ol>
     * <li>Проверить, что на карточке присутствует контент {@code contentSystemAttrs} с атрибутами {@code author,
     * responsible, responsibleTeam, responsibleEmployee}</li>
     * <li>Проверить, что на контенте {@code contentSystemAttrs} отсутствует кнопка редактирования атрибутов</li>
     * <li>Проверить, что на контенте {@code contentUserCase} присутствуют атрибуты {@code textRTF},
     * {@code boLink},{@code backBoLinksAttr}, {@code aggrCustom}, {@code aggrCustom_em}, {@code aggrCustom_te}</li>
     * <li>Проверить, что на контенте {@code contentUserCase} присутствует кнопка редактирования </li>
     * <li>Нажать кнопку редактирования и проверить на форме редактирования наличие атрибутов {@code textRTF},
     * {@code backBoLinksAttr}, {@code aggrCustom} и отсутствие других атрибутов</li>
     * <li>Закрыть форму редактирования кнопкой "Отмена"</li>
     * </ol>
     *
     * @param contentSystemAttrsUserCase контент с системными атрибутами метакласса userClass
     * @param contentUserCase контент с пользовательскими атрибутами
     * @param userAttributes набор пользовательских атрибутов
     * @param systemAttributes набор системных атрибутов
     */
    protected static void assertLicenseDefinedPermissionsForUserCase(ContentForm contentSystemAttrsUserCase,
            ContentForm contentUserCase, Attribute[] userAttributes, Attribute[] systemAttributes)
    {
        GUIContent.assertContentAttributeCaption(contentSystemAttrsUserCase, systemAttributes);
        GUIContent.assertLinkAbsense(contentSystemAttrsUserCase, GUIContent.LINK_EDIT);

        GUIContent.assertContentAttributeCaption(contentUserCase, userAttributes);
        GUIContent.assertLinkPresent(contentUserCase, GUIContent.LINK_EDIT);
        GUIContent.clickEdit(contentUserCase);
        GUIForm.assertFormAppear(GUIXpath.Div.CONTAINS_FORM);
        Set<String> presentAttrCodes = Set.of("textRTFCustom", "aggrCustom");
        Set<String> presentAttrTypes = Set.of("backBOLinks");
        GUIForm.assertAttrPresent(Arrays.stream(userAttributes)
                .filter(attr -> presentAttrCodes.contains(attr.getCode()) || presentAttrTypes.contains(attr.getType()))
                .toArray(Attribute[]::new));
        GUIForm.assertAttrAbsence(Arrays.stream(userAttributes)
                .filter(attr -> !presentAttrCodes.contains(attr.getCode()) && !presentAttrTypes.contains(
                        attr.getType()))
                .toArray(Attribute[]::new));
        GUIForm.cancelForm();
    }

    /**
     * Проверки разрешений на атрибуты для метакласса userClass$userCase2 (в лицензии определены разрешения на
     * метакласс userClass, а на тип userCase2 нет специальных разрешений)
     * <ol>
     * <li>Проверить, что на карточке отсутствует контент {@code contentSystemAttrs}</li>
     * <li>Проверить, что на карточке присутствует контент {@code notEditableContent}</li>
     * <li>Проверить, что на контенте {@code notEditableContent} отсутствует кнопка редактирования</li>
     * <li>Проверить, что на контенте {@code contentUserCase2} присутствуют атрибуты {@code textRTF}, {@code boLink},
     * {@code aggrCustom}, {@code aggrCustom_em}, {@code aggrCustom_te}, {@code backBoLinksAttr}</li>
     * <li>Проверить, что на контенте {@code contentUserCase2} присутствует кнопка редактирования </li>
     * <li>Нажать кнопку редактирования и проверить на форме редактирования наличие атрибута {@code backBoLinksAttr}
     * и отсутствие других атрибутов</li>
     * <li>Закрыть форму редактирования кнопкой "Отмена"</li>
     * </ol>
     *
     * @param contentSystemAttrsUserCase2 контент с системными атрибутами
     * @param contentUserCase2 контент с пользовательскими атрибутами
     * @param notEditableContent контент с атрибутом, не указанным в лицензии
     * @param userAttributes набор пользовательских атрибутов
     */
    protected static void assertLicenseDefinedPermissionsForUserClass(ContentForm contentSystemAttrsUserCase2,
            ContentForm contentUserCase2, ContentForm notEditableContent, Attribute[] userAttributes)
    {
        GUIContent.assertAbsence(contentSystemAttrsUserCase2);
        GUIContent.assertPresent(notEditableContent);
        GUIContent.assertLinkAbsense(notEditableContent, GUIContent.LINK_EDIT);
        GUIContent.assertContentAttributeCaption(contentUserCase2, userAttributes);
        GUIContent.assertLinkPresent(contentUserCase2, GUIContent.LINK_EDIT);
        GUIContent.clickEdit(contentUserCase2);
        GUIForm.assertFormAppear(GUIXpath.Div.CONTAINS_FORM);
        GUIForm.assertAttrPresent(Arrays.stream(userAttributes)
                .filter(attr -> attr.getType().equals("backBOLinks"))
                .toArray(Attribute[]::new));
        GUIForm.assertAttrAbsence(Arrays.stream(userAttributes)
                .filter(attr -> !attr.getType().equals("backBOLinks"))
                .toArray(Attribute[]::new));
        GUIForm.cancelForm();
    }

    /**
     * Проверка доступности переходов по статусам
     * <ol>
     * <li>Проверить, что кнопка изменения статуса присутствует на карточке</li>
     * <li>Нажать на кнопку "Изменить статус"</li>
     * <li>Проверить наличие в списке необходимых статусов</li>
     * <li>Если задан новый статус, то перевести объект в этот статус, выбрав его из списка и нажав кнопку
     * "Сохранить". Если не задан - закрыть форму кнопкой "Отмена"</li>
     * </ol>
     *
     * @param states список наименований статусов, которые должны быть доступны для выбора
     * @param newStatus статус, в который нужно перевести объект
     */
    protected static void assertTransitions(List<BoStatus> states, @Nullable BoStatus newStatus)
    {
        GUIButtonBar.assertPresent(GUIButtonBar.BTN_CHANGE_STATE);
        GUIButtonBar.changeState();
        GUISelect.assertSelect(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE,
                states.stream().map(BoStatus::getTitle).toList(), false, true, false);
        if (newStatus != null)
        {
            GUISelect.select(GUIXpath.InputComplex.NEW_STATE_PROPERTY_VALUE, newStatus.getCode());
            GUIForm.applyForm();
        }
        else
        {
            GUIForm.cancelForm();
        }
    }

    /**
     * Проверка доступности маркеров прав
     * <ol>
     * <li>Перейти на вкладку "Права доступа" карточки метакласса</li>
     * <li>Проверить доступность маркеров "Остальные переходы"  и "Добавление объекта" для метакласса</li>
     * </ol>
     *
     * @param metaClass метакласс, для которого выполнить проверку
     * @param isEditStateEnabled должен ли быть доступен маркер "Остальные переходы" (null, если не провероять)
     * @param isAddObjectEnabled должен ли быть доступен маркер "Добавление объекта" (null, если не провероять)
     * @param profileCode код проверяемого профиля
     */
    protected static void assertMarkers(MetaClass metaClass, @Nullable Boolean isEditStateEnabled,
            @Nullable Boolean isAddObjectEnabled, String profileCode)
    {
        GUIMetaClass.goToTab(metaClass, MetaclassCardTab.PERMISSIONSETTINGS);
        if (isEditStateEnabled != null)
        {
            GUIAccessMatrix.assertCheckBoxEnabled(SecurityMarker.EDIT_STATE_GROUP, profileCode, isEditStateEnabled);
        }
        if (isAddObjectEnabled != null)
        {
            GUIAccessMatrix.assertCheckBoxEnabled(AbstractBoRights.ADD.getRightCode(), profileCode, isAddObjectEnabled);
        }
    }
}
