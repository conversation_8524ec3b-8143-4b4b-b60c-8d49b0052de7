package ru.naumen.selenium.cases.admin.adminpermission;

import java.util.List;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLBreadCrumb;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.admin.DSLLeftMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLMenuItem;
import ru.naumen.selenium.casesutil.admin.DSLQuickAccessTile;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.DSLWfProfile;
import ru.naumen.selenium.casesutil.admin.GUIFolder;
import ru.naumen.selenium.casesutil.admin.GUIMenuItem;
import ru.naumen.selenium.casesutil.admin.GUINavSettings;
import ru.naumen.selenium.casesutil.admin.GUIWfProfile;
import ru.naumen.selenium.casesutil.admin.homepage.DSLHomePage;
import ru.naumen.selenium.casesutil.admin.homepage.GUINavHomePage;
import ru.naumen.selenium.casesutil.adminprofiles.DSLAdminProfile;
import ru.naumen.selenium.casesutil.advimport.DSLAdvimport;
import ru.naumen.selenium.casesutil.advimport.GUIAdvimportList;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.catalog.DSLRsRows;
import ru.naumen.selenium.casesutil.catalog.GUICatalog;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.hierarchygrid.GUIHierarchyGrid.CardObjectFocus;
import ru.naumen.selenium.casesutil.contenttemplate.DSLContentTemplate;
import ru.naumen.selenium.casesutil.contenttemplate.GUIContentTemplate;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.escalation.DSLEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.escalation.GUIEscalationLevel;
import ru.naumen.selenium.casesutil.listtemplate.DSLListTemplate;
import ru.naumen.selenium.casesutil.listtemplate.GUIListTemplate;
import ru.naumen.selenium.casesutil.metaclass.DSLActionCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIActionCondition;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationForm;
import ru.naumen.selenium.casesutil.metaclass.GUIEventAction;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.admin.Crumb;
import ru.naumen.selenium.casesutil.model.admin.DAOBreadCrumb;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.DAOHomePage;
import ru.naumen.selenium.casesutil.model.admin.DAOLeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOMenuItem;
import ru.naumen.selenium.casesutil.model.admin.DAOQuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.DAOWfProfile;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.admin.HomePage;
import ru.naumen.selenium.casesutil.model.admin.LeftMenuItem;
import ru.naumen.selenium.casesutil.model.admin.MenuItem;
import ru.naumen.selenium.casesutil.model.admin.QuickAccessTile;
import ru.naumen.selenium.casesutil.model.admin.WfProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.DAOAdminProfile;
import ru.naumen.selenium.casesutil.model.advimport.AdvimportConfig;
import ru.naumen.selenium.casesutil.model.advimport.DAOAdvimport;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAORsRow;
import ru.naumen.selenium.casesutil.model.catalogitem.RsRow;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.contenttemplate.ContentTemplate;
import ru.naumen.selenium.casesutil.model.contenttemplate.DAOContentTemplate;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.DAOEscalationSheme;
import ru.naumen.selenium.casesutil.model.escalation.EscalationLevel;
import ru.naumen.selenium.casesutil.model.escalation.EscalationScheme;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.listtemplate.DAOListTemplate;
import ru.naumen.selenium.casesutil.model.listtemplate.ListTemplate;
import ru.naumen.selenium.casesutil.model.metaclass.ActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOActionCondition;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.report.DAOReportTemplate;
import ru.naumen.selenium.casesutil.model.report.ReportTemplate;
import ru.naumen.selenium.casesutil.model.schedulertask.DAOSchedulerTask;
import ru.naumen.selenium.casesutil.model.schedulertask.SchedulerTaskScript;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.Periods;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.Strategy;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.DAOStructuredObjectsView;
import ru.naumen.selenium.casesutil.model.structuredobjectsview.StructuredObjectsView;
import ru.naumen.selenium.casesutil.model.styletemplate.DAOStyleTemplate;
import ru.naumen.selenium.casesutil.model.styletemplate.StyleTemplate;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.model.tag.DAOTag;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.casesutil.model.timer.DAOTimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition;
import ru.naumen.selenium.casesutil.model.timer.TimerDefinition.TimeMetric;
import ru.naumen.selenium.casesutil.report.DSLReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportTemplate;
import ru.naumen.selenium.casesutil.report.GUIReportTemplateList;
import ru.naumen.selenium.casesutil.role.GUISecurityRoleList;
import ru.naumen.selenium.casesutil.schedulertask.DSLSchedulerTask;
import ru.naumen.selenium.casesutil.schedulertask.GUISchedulerTaskList;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.script.GUIScript;
import ru.naumen.selenium.casesutil.script.GUIScriptModule;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.secgroup.GUISecurityGroupList;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.structuredobjectsview.DSLStructuredObjectsView;
import ru.naumen.selenium.casesutil.structuredobjectsview.GUIStructuredObjectsView;
import ru.naumen.selenium.casesutil.styletemplate.DSLStyleTemplate;
import ru.naumen.selenium.casesutil.styletemplate.GUIStyleTemplate;
import ru.naumen.selenium.casesutil.tag.DSLTag;
import ru.naumen.selenium.casesutil.tag.GUITag;
import ru.naumen.selenium.casesutil.timer.DSLTimerDefinition;
import ru.naumen.selenium.casesutil.timer.GUITimerDefinition;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.DateTimeUtils;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.UniqueRandomStringUtils;

/**
 * Тестирование видимости при работе с правами администиррования
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
 *
 * <AUTHOR>
 * @since 04.10.2024
 */
public class AdminPermissionVisibleTest extends AbstractTestCase
{
    private static SettingsSet settingsSet;
    /**
     * Суперпользователь, не имеющий прав на комплект
     */
    private static SuperUser superUser;
    private static MetaClass scCase;
    private static final String UUID = "UUID";
    private static final String DEL = "del";
    private static final String REMOVE = "remove";
    private static final String EDIT = "edit";
    private static final String DELETE = "delete";
    private static final String SWITCH_ON = "switchOn";
    private static final String DOWN = "down";
    private static final String UP = "up";
    private static final String COPY = "copy";
    private static final String SWITCH = "switch";
    private static final String SWITCH_OFF = "switchOff";

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Включить использование профилей администрирования на стенде (customAdminProfiles - true)</li>
     * <li>Включить доступность комплектов на стенде (setSetsEnabled - true)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);
        settingsSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet);

        AdminProfile adminProfile = DAOAdminProfile.createAdminProfile();
        DSLAdminProfile.add(adminProfile);

        DSLAdminProfile.addAllRightsToAdminProfile(adminProfile);

        superUser = DAOSuperUser.create();
        superUser.setAdminProfiles(List.of(adminProfile.getCode()));
        DSLSuperUser.add(superUser);

        scCase = DAOScCase.create();
        DSLMetaClass.add(scCase);
    }

    /**
     * Тестирование видимости контролов в настройке справочников и настройке бизнес процессов: группы пользователей и
     * роли, каталоги, метки, профили связанных жизненных циклов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать справочник userCatalogHidden, размеченный комплектом settingsSet</li>
     * <li>Создать справочник userCatalogVisible, не размеченный комплетом</li>
     * <li>В справочнике userCatalogHidden, создать элемент catalogItemHidden, размеченный комплектом settingsSet</li>
     * <li>В справочнике userCatalogHidden, создать элемент catalogItemVisible, не размеченный комплектом</li>
     * <li>Создать группу пользователей secGroupHidden, размеченную комплектом settingsSet</li>
     * <li>Создать группу пользователей secGroupVisible, не размеченную комплектом</li>
     * <li>Создать роль secRoleHidden, размеченную комплектом settingsSet</li>
     * <li>Создать роль secRoleVisible, не размеченную комплектом</li>
     * <li>Создать папку folderHidden в каталоге "Команда", размеченную комплектом settingsSet</li>
     * <li>Создать папку folderVisible в каталоге "Команда", не размеченную комплектом</li>
     * <li>Создать метку tagHidden, размеченную комплектом settingsSet</li>
     * <li>Создать метку tagVisible, не размеченную комплектом</li>
     * <li>Создать профиль связанных жизненных циклов wfProfileHidden, размеченный комплектом settingsSet</li>
     * <li>Создать профиль связанных жизненных циклов wfProfileVisible, не размеченный комплектом</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под суперпользователем, не имеющим прав на комплект settingsSet</li>
     * <li>Перейти на карточку каталога userCatalogHidden</li>
     * <li>Проверить, что на карточке userCatalogHidden отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке элементов userCatalogHidden для элемента catalogItemVisible присутствуют кнопки
     *  "редактировать", "копировать", "поместить в архив", "удалить"</li>
     * <li>Проверить, что в списке элементов userCatalogHidden для элемента catalogItemHidden отсутствуют кнопки
     * "редактировать", "копировать", "поместить в архив", "удалить"</li>
     * <li>Перейти на карточку каталога userCatalogVisible</li>
     * <li>Проверить, что на карточке userCatalogVisible присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Группы пользователей и роли"</li>
     * <li>Проверить, что в списке у группы secGroupVisible присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у группы secGroupHidden отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку группы secGroupVisible</li>
     * <li>Проверить, что на карточке secGroupVisible присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку группы secGroupHidden</li>
     * <li>Проверить, что на карточке secGroupHidden отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Группы пользователей и роли" на вкладку "Роли"</li>
     * <li>Проверить, что в списке у роли secRoleVisible присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у роли secRoleHidden отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку роли secRoleVisible</li>
     * <li>Проверить, что на карточке secRoleVisible присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку группы secRoleHidden</li>
     * <li>Проверить, что на карточке secRoleHidden отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Каталоги"</li>
     * <li>Проверить, что в списке у папки folderVisible присутствуют кнопки "редактировать", "копировать",
     * "поместить в архив", "удалить"</li>
     * <li>Проверить, что в списке у папки folderHidden отсутствуют кнопки "редактировать", "копировать",
     *  "поместить в архив", "удалить"</li>
     * <li>Перейти на карточку папки folderVisible</li>
     * <li>Проверить, что на карточке folderVisible присутствуют кнопки "удалить", "редактировать", "Поместить в
     * архив"</li>
     * <li>Перейти на карточку папки folderHidden</li>
     * <li>Проверить, что на карточке folderHidden отсутствуют кнопки "удалить", "редактировать", "Поместить в
     * архив" и присутствует кнопка "Копировать"</li>
     * <li>Перейти в раздел "Метки"</li>
     * <li>Проверить, что в списке у метки tagVisible присутствуют кнопки "удалить", "редактировать",
     * "включить/выключить"</li>
     * <li>Проверить, что в списке у метки tagHidden отсутствуют кнопки "удалить", "редактировать",
     * "включить/выключить"</li>
     * <li>Перейти на карточку метки tagVisible</li>
     * <li>Проверить, что на карточке tagVisible присутствуют кнопки "удалить", "редактировать",
     * "включить/выключить"</li>
     * <li>Перейти на карточку метки tagHidden</li>
     * <li>Проверить, что на карточке tagHidden отсутствуют кнопки "удалить", "редактировать", "включить/выключить"</li>
     * <li>Перейти в раздел "Профили связанных жизненных циклов"</li>
     * <li>Проверить, что в списке у профиля wfProfileVisible присутствуют кнопки "удалить", "редактировать",
     * "включить/выключить"</li>
     * <li>Проверить, что в списке у профиля wfProfileHidden отсутствуют кнопки "удалить", "редактировать",
     * "включить/выключить"</li>
     * <li>Перейти на карточку профиля wfProfileVisible</li>
     * <li>Проверить, что на карточке wfProfileVisible присутствуют кнопки "редактировать" и "включить/выключить"</li>
     * <li>Перейти на карточку профиля wfProfileHidden</li>
     * <li>Проверить, что на карточке wfProfileHidden отсутствуют кнопки "редактировать" и "включить/выключить"</li>
     * </ol>
     */
    @Test
    public void testVisibleProcessSettings()
    {
        //Подготовка
        Catalog userCatalogHidden = DAOCatalog.createUser(true, false);
        userCatalogHidden.setSettingsSet(settingsSet);
        Catalog userCatalogVisible = DAOCatalog.createUser(true, false);
        DSLCatalog.add(userCatalogHidden, userCatalogVisible);
        CatalogItem catalogItemHidden = DAOCatalogItem.createUser(userCatalogHidden);
        CatalogItem catalogItemVisible = DAOCatalogItem.createUser(userCatalogHidden);
        catalogItemHidden.setSettingsSet(settingsSet);
        DSLCatalogItem.add(catalogItemHidden, catalogItemVisible);

        SecurityGroup secGroupHidden = DAOSecurityGroup.create();
        secGroupHidden.setSettingsSet(settingsSet);
        SecurityGroup secGroupVisible = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroupHidden, secGroupVisible);

        SecurityRole secRoleHidden = DAOSecurityRole.create(null);
        secRoleHidden.setSettingsSet(settingsSet);
        SecurityRole secRoleVisible = DAOSecurityRole.create(null);
        DSLSecurityRole.add(secRoleHidden, secRoleVisible);

        Folder folderHidden = DAOFolder.create(DAOTeamCase.createClass());
        folderHidden.setSettingsSet(settingsSet);
        Folder folderVisible = DAOFolder.create(DAOTeamCase.createClass());
        DSLFolder.add(folderHidden, folderVisible);

        Tag tagHidden = DAOTag.createTag();
        tagHidden.setSettingsSet(settingsSet);
        Tag tagVisible = DAOTag.createTag();
        DSLTag.add(tagHidden, tagVisible);

        WfProfile wfProfileHidden = DAOWfProfile.create(scCase, scCase);
        wfProfileHidden.setSettingsSet(settingsSet);
        WfProfile wfProfileVisible = DAOWfProfile.create(scCase, scCase);
        DSLWfProfile.add(wfProfileHidden, wfProfileVisible);

        //Действия и проверки
        GUILogon.login(superUser);

        GUICatalog.goToCard(userCatalogHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertIconsActionExists(catalogItemVisible.getUuid(), true, EDIT, COPY,
                REMOVE, DEL);
        GUIContent.assertIconsActionExists(catalogItemHidden.getUuid(), false, EDIT, COPY,
                REMOVE, DEL);
        GUICatalog.goToCard(userCatalogVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUINavigational.goToSecGroupsAndRoles();
        GUIContent.assertIconsActionExists(secGroupVisible.getCode(), true, "secDialogEditGroup",
                "secDialogDeleteGroup");
        GUIContent.assertIconsActionExists(secGroupHidden.getCode(), false, "secDialogEditGroup",
                "secDialogDeleteGroup");
        GUISecurityGroupList.goToCard(secGroupVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUISecurityGroupList.goToCard(secGroupHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUINavigational.goToSecGroupsAndRoles();
        GUISecurityGroupList.goToRolesTab();
        GUIContent.assertIconsActionExists(secRoleVisible.getCode(), true, "secDialogEditRole",
                "secDialogDeleteRole");
        GUIContent.assertIconsActionExists(secRoleHidden.getCode(), false, "secDialogEditRole",
                "secDialogDeleteRole");
        GUISecurityRoleList.goToCard(secRoleVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUISecurityRoleList.goToCard(secRoleHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUINavigational.goToCatalogFolder();
        GUIContent.assertIconsActionExists(folderVisible.getUuid(), true, EDIT, COPY,
                REMOVE, DEL);
        GUIContent.assertIconsActionExists(folderHidden.getUuid(), false, EDIT, COPY,
                REMOVE, DEL);
        GUIFolder.goToCardWithRefresh(folderVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertArchiveButtonOnCardPresents(true);

        GUIFolder.goToCardWithRefresh(folderHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertArchiveButtonOnCardPresents(false);
        GUIContent.assertCopyButtonOnCardPresents(false);

        GUINavigational.goToTags();
        GUIContent.assertIconsActionExists(tagVisible.getCode(), true, "toggleTag", "editTag",
                "deleteTag");
        GUIContent.assertIconsActionExists(tagHidden.getCode(), false, "toggleTag", "editTag",
                "deleteTag");
        GUITag.goToTagCard(tagVisible.getCode());
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);

        GUITag.goToTagCard(tagHidden.getCode());
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);

        GUIWfProfile.goToList();
        GUIWfProfile.assertIconsActionExists(wfProfileVisible.getCode(), true, SWITCH_ON, EDIT,
                DELETE);
        GUIWfProfile.assertIconsActionExists(wfProfileHidden.getCode(), false, SWITCH_ON, EDIT,
                DELETE);
        GUIWfProfile.goToCard(wfProfileVisible);
        GUIWfProfile.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);

        GUIWfProfile.goToCard(wfProfileHidden);
        GUIWfProfile.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
    }

    /**
     * Тестирование видимости контролов в настройках связанных с эскалацией
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать счетчик времени timerHidden, размеченный комплектом settingsSet</li>
     * <li>Создать счетчик времени timerVisible, не размеченный комплектом</li>
     * <li>Создать схему эскалации schemeHidden, размеченную комплектом settingsSet</li>
     * <li>Создать схему эскалации schemeVisible, не размеченную комплектом</li>
     * <li>Создать уровень эскалации levelHidden, размеченный комплектом settingsSet</li>
     * <li>Создать уровень эскалации levelVisible, не размеченный комплектом</li>
     * <li>Создать действие, типа эскалация eventHidden со скриптом script, размеченное комплектом settingsSet</li>
     * <li>Создать действие, типа эскалация eventVisible со скриптом script, не размеченное комплектом</li>
     * <li>В действии eventHidden создать условие выполнения ДПС conditionHidden, размеченное комплектом
     * settingsSet</li>
     * <li>В действии eventHidden создать условие выполнения ДПС conditionVisible, не размеченное комплектом</li>
     * <li>В типе scCase создать атрибут типа "Целое число" integerAttr</li>
     * <li>Создать элемент справочника "Таблицы соответствий" для эскалаций catalogItemHidden для объектов типа scCase и
     * определяющего атрибута integerAttr, размеченный комплектом settingsSet</li>
     * <li>Создать элемент справочника "Таблицы соответствий" для эскалаций catalogItemVisible для объектов типа
     * scCase и
     *  определяющего атрибута integerAttr, не размеченный комплектом</li>
     * <li>В таблице соотвествий catalogItemHidden создать строку escRow1, размеченную комплектом settingsSet</li>
     * <li>В таблице соотвествий catalogItemVisible создать строку escRow2, не размеченную комплектом</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под суперпользователем не имеющим прав на комплект settingsSet</li>
     * <li>Перейти в раздел "Счетчики времени"</li>
     * <li>Проверить, что в списке у счетчика timerVisible присутствуют кнопки "редактировать", "поместить в архив",
     * "удалить"</li>
     * <li>Проверить, что в списке у счетчика timerHidden отсутствуют кнопки "редактировать", "поместить в архив",
     * "удалить"</li>
     * <li>Перейти на карточку счетчика timerVisible</li>
     * <li>Проверить, что на карточке timerVisible присутствуют кнопки "редактировать", "поместить в архив",
     * "удалить"</li>
     * <li>Перейти на карточку счетчика timerHidden</li>
     * <li>Проверить, что на карточке timerHidden отсутствуют кнопки "редактировать", "поместить в архив",
     * "удалить"</li>
     * <li>Перейти в раздел "Эскалация" на вкладку "Схемы эскалации"</li>
     * <li>Проверить, что в списке у схемы эскалации schemeVisible присутствуют кнопки "редактировать",
     * "включить/выключить", "удалить"</li>
     * <li>Проверить, что в списке у схемы эскалации schemeHidden отсутствуют кнопки "редактировать",
     * "включить/выключить", "удалить"</li>
     * <li>Перейти на карточку схемы эскалации schemeVisible</li>
     * <li>Проверить, что на карточке schemeVisible присутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку схемы эскалации schemeHidden</li>
     * <li>Проверить, что на карточке schemeHidden отсутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>В списке уровней эскалации schemeHidden проверить, что у уровня levelVisible присутствуют кнопки:
     * "редактировать", "удалить"</li>
     * <li>В списке уровней эскалации schemeHidden проверить, что у уровня levelHidden отсутствуют кнопки:
     * "редактировать", "удалить"</li>
     * <li>Перейти в раздел "Эскалация" на вкладку "Действия"</li>
     * <li>Проверить, что в списке у действия eventVisible присутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Проверить, что в списке у действия eventHidden отсутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку действия eventVisible</li>
     * <li>Проверить, что на карточке eventVisible присутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку действия eventHidden</li>
     * <li>Проверить, что на карточке eventHidden отсутствуют кнопки "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>В списке условий выполнения действия eventHidden проверить, что у условия conditionVisible присутствуют
     * кнопки:
     * "редактировать", "удалить"</li>
     * <li>В списке условий выполнения действия eventHidden проверить, что у условия conditionHidden отсутствуют кнопки:
     * "редактировать", "удалить"</li>
     * <li>Перейти на карточку условия conditionVisible</li>
     * <li>Проверить, что на карточке conditionVisible присутствуют кнопки "редактировать", "удалить"</li>
     * <li>Перейти на карточку условия conditionHidden</li>
     * <li>Проверить, что на карточке conditionHidden отсутствуют кнопки "редактировать", "удалить"</li>
     * <li>Перейти в раздел "Эскалация" на вкладку "Таблицы соответствий"</li>
     * <li>Проверить, что в списке у таблицы соответствий catalogItemVisible присутствуют кнопки "редактировать",
     * "копировать", "поместить в архив", "удалить"</li>
     * <li>Проверить, что в списке у таблицы соответствий catalogItemHidden отсутствуют кнопки "редактировать",
     * "копировать", "поместить в архив", "удалить"</li>
     * <li>Перейти на карточку таблицы соответствий catalogItemVisible</li>
     * <li>Проверить, что на карточке catalogItemVisible присутствуют кнопки "редактировать", "копировать",
     * "поместить в архив", "удалить"</li>
     * <li>Проверить, что в списке строк таблиц соответствий у строки escRow2 присутствуют кнопки "редактировать",
     * "удалить"</li>
     * <li>Перейти на карточку таблицы соответствий catalogItemHidden</li>
     * <li>Проверить, что на карточке catalogItemHidden отсутствуют кнопки "редактировать", "поместить в архив",
     * "удалить", кнопка "копировать" присутствует</li>
     * <li>Проверить, что в списке строк таблиц соответствий у строки escRow1 отсутствуют кнопки "редактировать",
     * "удалить"</li>
     * </ol>
     */
    @Test
    public void testVisibleEscalationRelation()
    {
        //Подготовка
        TimerDefinition timerHidden = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.ASTRO);
        timerHidden.setSettingsSet(settingsSet);
        TimerDefinition timerVisible = DAOTimerDefinition.createStatusTimerForSc(TimeMetric.ASTRO);
        DSLTimerDefinition.add(timerHidden, timerVisible);

        EscalationScheme schemeHidden = DAOEscalationSheme.create(timerHidden, true, scCase);
        schemeHidden.setSettingsSet(settingsSet);
        EscalationScheme schemeVisible = DAOEscalationSheme.create(timerHidden, true, scCase);
        DSLEscalation.add(schemeHidden, schemeVisible);

        EscalationLevel levelHidden =
                DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "50 SECOND", false);
        levelHidden.setLevel("0");
        levelHidden.setSettingsSet(settingsSet);
        EscalationLevel levelVisible =
                DAOEscalationLevel.create(DAOEscalationLevel.CONDITION_TIME, "50 SECOND", false);
        levelVisible.setLevel("1");
        DSLEscalation.addLevel(schemeHidden.getCode(), levelHidden);
        DSLEscalation.addLevel(schemeHidden.getCode(), levelVisible);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        EventAction eventHidden = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true,
                scCase);
        eventHidden.setSettingsSet(settingsSet);
        EventAction eventVisible = DAOEventAction.createEventScript(EventType.escalation, script.getCode(), true,
                scCase);
        DSLEventAction.add(eventHidden, eventVisible);

        ActionCondition conditionHidden = DAOActionCondition.create(eventHidden, script.getCode());
        conditionHidden.setSettingsSet(settingsSet);
        ActionCondition conditionVisible = DAOActionCondition.create(eventHidden, script.getCode());
        DSLActionCondition.add(conditionHidden);
        DSLActionCondition.add(conditionVisible);

        Attribute integerAttr = DAOAttribute.createInteger(scCase.getFqn());
        DSLAttribute.add(integerAttr);
        String attrValue = UniqueRandomStringUtils.serialNumber(4);
        integerAttr.setValue(attrValue);

        CatalogItem catalogItemHidden = DAOCatalogItem.createEscalationRule(scCase, integerAttr.getCode());
        catalogItemHidden.setSettingsSet(settingsSet);
        CatalogItem catalogItemVisible = DAOCatalogItem.createEscalationRule(scCase, integerAttr.getCode());
        DSLCatalogItem.add(catalogItemHidden, catalogItemVisible);

        RsRow escRow1 = DAORsRow.create(catalogItemHidden, GUIEscalation.ESCALATION_TARGET_DATA,
                schemeHidden.getCode(), integerAttr.getCode(), attrValue);
        escRow1.setSettingsSet(settingsSet);
        RsRow escRow2 = DAORsRow.create(catalogItemVisible, GUIEscalation.ESCALATION_TARGET_DATA,
                schemeHidden.getCode(), integerAttr.getCode(), attrValue);
        DSLRsRows.addRowToRSItem(escRow1, escRow2);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToTimers();
        GUIContent.assertIconsActionExists(timerVisible.getCode(), true, EDIT, REMOVE,
                DELETE);
        GUIContent.assertIconsActionExists(timerHidden.getCode(), false, EDIT, REMOVE,
                DELETE);
        GUITimerDefinition.goToCardWithRefresh(timerVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertArchiveButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUITimerDefinition.goToCardWithRefresh(timerHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertArchiveButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIEscalation.goToSchemesWithRefresh();
        GUIContent.assertIconsActionExists(schemeVisible.getCode(), true, SWITCH, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(schemeHidden.getCode(), false, SWITCH, EDIT,
                DELETE);

        GUIEscalation.goToSchemeWithRefresh(schemeVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIEscalation.goToSchemeWithRefresh(schemeHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIEscalationLevel.assertIconsActionExists(levelVisible.getLevel(), true, EDIT, DELETE);
        GUIEscalationLevel.assertIconsActionExists(levelHidden.getLevel(), false, EDIT, DELETE);

        GUIEscalation.goToActions();
        GUIContent.assertIconsActionExists(eventVisible.getUuid(), true, "toggleProvider",
                "editEventAction", "deleteEventAction");
        GUIContent.assertIconsActionExists(eventHidden.getUuid(), false, "toggleProvider",
                "editEventAction", "deleteEventAction");

        GUIEventAction.goToCardWithRefresh(eventVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIEventAction.goToCardWithRefresh(eventHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIContent.assertIconsActionExists(conditionVisible.getCode(), true, EDIT, DELETE);
        GUIContent.assertIconsActionExists(conditionHidden.getCode(), false, EDIT, DELETE);

        GUIActionCondition.goToCard(conditionVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIEventAction.goToCardWithRefresh(eventHidden);
        GUIActionCondition.goToCard(conditionHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIEscalation.goToRuleSettings();
        GUIContent.assertIconsActionExists(catalogItemVisible.getUuid(), true, EDIT, COPY,
                REMOVE, DEL);
        GUIContent.assertIconsActionExists(catalogItemHidden.getUuid(), false, EDIT, COPY,
                REMOVE, DEL);

        GUIEscalation.goToRuleSettingCard(catalogItemVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertCopyButtonOnCardPresents(true);
        GUIContent.assertArchiveButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIContent.assertIconsActionExists(escRow2.getId(), true, EDIT, DELETE);

        GUIEscalation.goToRuleSettingCard(catalogItemHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertCopyButtonOnCardPresents(false);
        GUIContent.assertArchiveButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIContent.assertIconsActionExists(escRow1.getId(), false, EDIT, DELETE);
    }

    /**
     * Тестирование видимости контролов в настройках системы ДПС и навигации
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательское ДПС типа "Скрипт" eventActionHidden, размеченное комплектом settingsSet</li>
     * <li>У пользовательского ДПС eventActionHidden, создать параметр paramHidden, размеченный комплектом
     * settingsSet</li>
     * <li>У пользовательского ДПС eventActionHidden, создать параметр paramVisible, не размеченный комплектом</li>
     * <li>Создать пользовательское ДПС типа "Скрипт" eventActionVisible, не размеченное комплектом</li>
     * <li>Создать элемент верхнего меню topMenuItemHidden, размеченный комплектом settingsSet</li>
     * <li>Создать элемент верхнего меню topMenuItemVisible, не размеченный комплектом</li>
     * <li>Создать элемент левого меню leftMenuItemHidden, размеченный комплектом settingsSet</li>
     * <li>Создать элемент левого меню leftMenuItemVisible, не размеченный комплектом</li>
     * <li>Создать плитку быстрого доступа quickAccessHidden, размеченную комплектом settingsSet</li>
     * <li>Создать плитку быстрого доступа quickAccessVisible, не размеченную комплектом</li>
     * <li>Создать хлебную крошку crumbHidden, размеченную комплектом settingsSet</li>
     * <li>Создать хлебную крошку crumbVisible, не размеченную комплектом</li>
     * <li>Создать домашнюю страницу homePageHidden, размеченную комплектом settingsSet</li>
     * <li>Создать домашнюю страницу homePageVisible, не размеченную комплектом</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под суперпользователем, не имеющим доступ к комплекту settingsSet</li>
     * <li>Перейти в раздел "Действия по событиям"</li>
     * <li>Проверить, что в списке у действия eventActionVisible присутствуют кнопки: "редактировать",
     * "включить/выключить", "удалить"</li>
     * <li>Проверить, что в списке у действия eventActionHidden отсутствуют кнопки: "редактировать",
     * "включить/выключить", "удалить"</li>
     * <li>Перейти на карточку действия по событию eventActionVisible</li>
     * <li>Проверить, что на карточке eventActionVisible присутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку действия по событию eventActionHidden</li>
     * <li>Проверить, что на карточке eventActionHidden отсутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Проверить, что в списке параметров на карточке eventActionHidden для параметра paramVisible присутствуют
     * кнопки:
     * "редактировать", "удалить"</li>
     * <li>Проверить, что в списке параметров на карточке eventActionHidden для параметра paramHidden отсутствуют
     * кнопки: "редактировать", "удалить"</li>
     * <li>Перейти в раздел "Интерфейс и навигация" на вкладку "Навигация"</li>
     * <li>Проверить, что в списке "Верхнее меню" у элемента меню topMenuItemVisible присутствуют кнопки: "переместить",
     * "включить/выключить", "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Верхнее меню" у элемента меню topMenuItemHidden отсутствуют кнопки: "переместить",
     * "включить/выключить", "редактировать", "удалить"</li>
     * <li>Перейти на карточку элемента меню topMenuItemVisible</li>
     * <li>Проверить, что на карточке topMenuItemVisible присутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку элемента меню topMenuItemHidden</li>
     * <li>Проверить, что на карточке topMenuItemHidden отсутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти в раздел "Интерфейс и навигация" на вкладку "Навигация"</li>
     * <li>Проверить, что в списке "Левое меню" у элемента меню leftMenuItemVisible присутствуют кнопки: "переместить",
     * "включить/выключить", "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Левое меню" у элемента меню leftMenuItemHidden отсутствуют кнопки: "переместить",
     * "включить/выключить", "редактировать", "удалить"</li>
     * <li>Перейти на карточку элемента меню leftMenuItemVisible</li>
     * <li>Проверить, что на карточке leftMenuItemVisible присутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти на карточку элемента меню leftMenuItemHidden</li>
     * <li>Проверить, что на карточке leftMenuItemHidden отсутствуют кнопки: "редактировать", "включить/выключить",
     * "удалить"</li>
     * <li>Перейти в раздел "Интерфейс и навигация" на вкладку "Навигация"</li>
     * <li>Проверить, что в списке "Панель быстрого доступа" у плитки quickAccessVisible присутствуют кнопки:
     * "переместить", "включить/выключить", "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Панель быстрого доступа" у плитки quickAccessHidden отсутствуют кнопки:
     *  "переместить", "включить/выключить", "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Навигационная цепочка ("Хлебные крошки")" у хлебной крошки crumbVisible
     * присутствуют  кнопки: "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Навигационная цепочка ("Хлебные крошки")" у хлебной крошки crumbHidden
     * присутствуют  кнопки: "редактировать", "удалить"</li>
     * <li>Перейти на карточку хлебной крошки crumbVisible</li>
     * <li>Проверить, что на карточке crumbVisible присутствуют кнопки: "редактировать", "удалить"</li>
     * <li>Перейти на карточку хлебной крошки crumbHidden</li>
     * <li>Проверить, что на карточке crumbHidden отсутствуют кнопки: "редактировать", "удалить"</li>
     * <li>Перейти в раздел "Интерфейс и навигация" на вкладку "Навигация"</li>
     * <li>Проверить, что в списке "Домашняя страница" для домашней страницы homePageVisible присутствуют кнопки:
     * "переместить", "редактировать", "удалить"</li>
     * <li>Проверить, что в списке "Домашняя страница" для домашней страницы homePageHidden отсутствуют кнопки:
     * "переместить", "редактировать", "удалить"</li>
     * <li>Перейти на карточку домашней страницы homePageVisible</li>
     * <li>Проверить, что на карточке homePageVisible присутствуют кнопки: "редактировать", "удалить"</li>
     * <li>Перейти на карточку домашней страницы homePageHidden</li>
     * <li>Проверить, что на карточке homePageHidden отсутствуют кнопки: "редактировать", "удалить"</li>
     * </ol>
     */
    @Test
    public void testVisibleSystemSettingsEventActionAndNavigation()
    {
        //Подготовка
        ScriptInfo script = DSLScriptInfo.createScriptInfo("return true");
        EventAction eventActionHidden = DAOEventAction.createEventScript(EventType.userEvent, script, true, scCase);
        eventActionHidden.setSettingsSet(settingsSet);
        EventAction eventActionVisible = DAOEventAction.createEventScript(EventType.userEvent, script, true, scCase);
        DSLEventAction.add(eventActionHidden, eventActionVisible);

        FormParameter paramHidden = DAOFormParameter.createString();
        paramHidden.setEventAction(eventActionHidden.getUuid());
        paramHidden.setSettingsSet(settingsSet);
        FormParameter paramVisible = DAOFormParameter.createString();
        paramVisible.setEventAction(eventActionHidden.getUuid());
        DSLFormParameter.save(paramHidden, paramVisible);

        MenuItem topMenuItemHidden = DAOMenuItem.createChapter(false);
        topMenuItemHidden.setSettingsSet(settingsSet);
        MenuItem topMenuItemVisible = DAOMenuItem.createChapter(false);
        DSLMenuItem.add(topMenuItemHidden, topMenuItemVisible);

        LeftMenuItem leftMenuItemHidden = DAOLeftMenuItem.createChapter(false, null);
        leftMenuItemHidden.setSettingsSet(settingsSet);
        LeftMenuItem leftMenuItemVisible = DAOLeftMenuItem.createChapter(false, null);
        DSLLeftMenuItem.add(leftMenuItemHidden, leftMenuItemVisible);

        QuickAccessTile quickAccessHidden = DAOQuickAccessTile.createQuickAccessTile(leftMenuItemHidden, true);
        quickAccessHidden.setSettingsSet(settingsSet);
        QuickAccessTile quickAccessVisible = DAOQuickAccessTile.createQuickAccessTile(leftMenuItemVisible, true);
        DSLQuickAccessTile.add(quickAccessHidden, quickAccessVisible);

        MetaClass scCase2 = DAOScCase.create();
        Attribute attribute = DAOAttribute.createObjectLink(scCase.getFqn(), scCase, null);
        Attribute attribute2 = DAOAttribute.createObjectLink(scCase2.getFqn(), scCase2, null);
        DSLMetainfo.add(scCase2, attribute, attribute2);
        Crumb crumbHidden = DAOBreadCrumb.create(scCase, attribute);
        crumbHidden.setSettingsSet(settingsSet);
        Crumb crumbVisible = DAOBreadCrumb.create(scCase2, attribute2);
        DSLBreadCrumb.add(crumbHidden, crumbVisible);

        HomePage homePageHidden = DAOHomePage.createCustomLink("customLinkText");
        homePageHidden.setSettingsSet(settingsSet);
        HomePage homePageVisible = DAOHomePage.createCustomLink("customLinkText");
        DSLHomePage.add(homePageHidden, homePageVisible);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEventActions();
        GUIContent.assertIconsActionExists(eventActionVisible.getUuid(), true, "toggleProvider",
                "editEventAction", "deleteEventAction");
        GUIContent.assertIconsActionExists(eventActionHidden.getUuid(), false, "toggleProvider",
                "editEventAction", "deleteEventAction");
        GUIEventAction.goToCardWithRefresh(eventActionVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIEventAction.goToCardWithRefresh(eventActionHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIContent.assertIconsActionExistsByCode(paramVisible.getCode(), true, UP, EDIT, DELETE);
        GUIContent.assertIconsActionExistsByCode(paramHidden.getCode(), false, DOWN, EDIT, DELETE);

        GUINavigational.goToNavigationSettings();
        GUIContent.assertIconsActionExists(topMenuItemVisible.getCode(), true, UP, SWITCH_ON, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(topMenuItemHidden.getCode(), false, DOWN, SWITCH_ON, EDIT,
                DELETE);

        GUIMenuItem.goToTopMenuItemCard(topMenuItemVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUIMenuItem.goToTopMenuItemCard(topMenuItemHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUINavigational.goToNavigationSettings();
        GUIContent.assertIconsActionExists(leftMenuItemVisible.getCode(), true, UP, SWITCH_ON, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(leftMenuItemHidden.getCode(), false, DOWN, SWITCH_ON, EDIT,
                DELETE);

        GUIMenuItem.goToLeftMenuItemCard(leftMenuItemVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertSwitchButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUINavigational.goToNavigationSettings();
        GUIMenuItem.goToLeftMenuItemCard(leftMenuItemHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertSwitchButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUINavigational.goToNavigationSettings();
        GUIContent.assertIconsActionExists(quickAccessVisible.getCode(), true, UP, SWITCH_OFF, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(quickAccessHidden.getCode(), false, DOWN, SWITCH_OFF, EDIT,
                DELETE);

        GUIContent.assertIconsActionExists(crumbVisible.getCode(), true, EDIT, DELETE);
        GUIContent.assertIconsActionExists(crumbHidden.getCode(), false, EDIT, DELETE);

        GUINavSettings.goToCrumb(crumbVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUINavigational.goToNavigationSettings();
        GUINavSettings.goToCrumb(crumbHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUINavigational.goToNavigationSettings();
        GUIContent.assertIconsActionExists(homePageVisible.getUuid(), true, UP, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(homePageHidden.getUuid(), false, DOWN, EDIT,
                DELETE);

        GUINavHomePage.goToHomePageCard(homePageVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUINavHomePage.goToHomePageCard(homePageHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);
    }

    /**
     * Тестирование видимости контролов в настройках системы скриптов и скриптовых модулей
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать скрипт scriptHidden помеченный комплектом settingsSet и скрипт scriptVisible</li>
     * <li>Создать скриптовый модуль moduleHidden помеченный комплектом moduleVisible и модуль moduleVisible</li>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником superUser</li>
     * <li>Перейти в "Каталог скриптов"</li>
     * <li>Проверить, что у scriptVisible отображается иконка редактирования, а у scriptHidden нет</li>
     * <li>Перейти на карточку scriptHidden и проверить, что кнопки "Редактировать" и "Удалить" скрыты</li>
     * <li>Перейти на карточку scriptVisible и проверить, что кнопки "Редактировать" и "Удалить" отображаются</li>
     * <li>Перейти в "Каталог модулей"</li>
     * <li>Проверить, что у moduleVisible отображается иконки редактирования и удаления, а у moduleHidden нет</li>
     * <li>Перейти на карточку moduleHidden и проверить, что кнопки "Редактировать" и "Удалить" скрыты</li>
     * <li>Перейти на карточку moduleVisible и проверить, что кнопки "Редактировать" и "Удалить" отображаются</li>
     * </ol>
     */
    @Test
    public void testVisibleSystemSettingsScripts()
    {
        //Подготовка
        ScriptInfo scriptHidden = DAOScriptInfo.createNewScriptInfo();
        scriptHidden.setSettingsSet(settingsSet);
        ScriptInfo scriptVisible = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(scriptHidden, scriptVisible);

        ModuleConf moduleHidden = DAOModuleConf.create();
        moduleHidden.setSettingsSet(settingsSet);
        ModuleConf moduleVisible = DAOModuleConf.create();
        DSLModuleConf.add(moduleHidden, moduleVisible);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToScripts();
        GUIContent.assertIconsActionExists(scriptVisible.getCode(), true, "editScript");
        GUIContent.assertIconsActionExists(scriptHidden.getCode(), false, "editScript");

        GUIScript.goToCard(scriptHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIScript.goToCard(scriptVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);

        GUINavigational.goToScriptModules();
        GUIContent.assertIconsActionExists(moduleVisible.getCode(), true, "editModule", "deleteModule");
        GUIContent.assertIconsActionExists(moduleHidden.getCode(), false, "editModule", "deleteModule");

        GUIScriptModule.goToCard(moduleHidden);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertDeleteButtonOnCardPresents(false);

        GUIScriptModule.goToCard(moduleVisible);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertDeleteButtonOnCardPresents(true);
    }

    /**
     * Тестирование видимости контролов в настройке шаблонов
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать шаблон списка listTemplate, разметив его комплектом settingsSet</li>
     * <li>Создать шаблон стиля styleTemplate, разметив его комплектом settingsSet</li>
     * <li>Создать структуру testStr</li>
     * <li>Создать контент hierarchyGrid типа "Иерархическое дерево" используя структуру testStr</li>
     * <li>Создать шаблон контента gridTemplate на основе контента hierarchyGrid, разметив его комплектом
     * settingsSet</li>
     * <li>Создать шаблон отчета reportTemplate, разметив его комплектом settingsSet</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником superUser не имеющим прав на комплект settingsSet</li>
     * <li>Перейти в раздел "Шаблоны списков"</li>
     * <li>Проверить, что в списке у шаблона listTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку шаблона listTemplate</li>
     * <li>Проверить, что на карточке шаблона listTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Шаблоны стилей"</li>
     * <li>Проверить, что в списке у шаблона styleTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку шаблона styleTemplate</li>
     * <li>Проверить, что на карточке шаблона styleTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Шаблоны контентов"</li>
     * <li>Проверить, что в списке у шаблона gridTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку шаблона gridTemplate</li>
     * <li>Проверить, что на карточке шаблона gridTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Шаблоны отчетов и печатных форм"</li>
     * <li>Проверить, что в списке у шаблона reportTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку шаблона reportTemplate</li>
     * <li>Проверить, что на карточке шаблона reportTemplate отсутствуют кнопки "удалить" и "редактировать"</li>
     * </ol>
     */
    @Test
    public void testTemplatesControls()
    {
        MetaClass userClass = DAOUserClass.create();
        DSLMetainfo.add(userClass);

        ListTemplate listTemplate = DAOListTemplate.createTemplate(userClass);
        listTemplate.setSettingsSet(settingsSet);
        DSLListTemplate.add(listTemplate);

        StyleTemplate styleTemplate = DAOStyleTemplate.createTemplate(
                "<div>" + ModelUtils.createDescription() + "</div>");
        styleTemplate.setSettingsSet(settingsSet);
        DSLStyleTemplate.add(styleTemplate);

        StructuredObjectsView testStr = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(testStr);

        ContentForm hierarchyGrid = DAOContentCard.createHierarchyGrid(DAOEmployeeCase.createClass(), true,
                PositionContent.FULL, testStr, false, CardObjectFocus.OFF.name());
        DSLContent.add(hierarchyGrid);

        ContentTemplate gridTemplate = DAOContentTemplate.createTemplate(hierarchyGrid);
        gridTemplate.setSettingsSet(settingsSet);
        DSLContentTemplate.add(gridTemplate);

        ReportTemplate reportTemplate = DAOReportTemplate.createReportTemplate(DAOReportTemplate.TEMPLATE1);
        reportTemplate.setSettingsSet(settingsSet);
        DSLReportTemplate.add(reportTemplate);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToListTemplates();
        GUIListTemplate.advlist().content().asserts().rowsPresence(listTemplate);
        GUIListTemplate.advlist().content().asserts().pictAbsence(listTemplate, GUIListTemplate.EDIT_ACTION);
        GUIListTemplate.advlist().content().asserts().pictAbsence(listTemplate, GUIListTemplate.DELETE_ACTION);

        GUIListTemplate.goToCard(listTemplate);
        GUIListTemplate.assertDeleteButtonAbsence();
        GUIListTemplate.assertEditButtonAbsence();

        GUINavigational.goToStyleTemplates();
        GUIStyleTemplate.advlist().content().asserts().rowsPresence(styleTemplate);
        GUIStyleTemplate.advlist().content().asserts().pictAbsence(styleTemplate, GUIStyleTemplate.EDIT_ACTION);
        GUIStyleTemplate.advlist().content().asserts().pictAbsence(styleTemplate, GUIStyleTemplate.DELETE_ACTION);

        GUIStyleTemplate.goToCard(styleTemplate.getCode());
        GUIStyleTemplate.assertEditButtonAbsence();
        GUIStyleTemplate.assertDeleteButtonAbsence();

        GUINavigational.goToContentTemplates();
        GUIContentTemplate.advlist().content().asserts().rowsPresence(gridTemplate);
        GUIContentTemplate.advlist().content().asserts().pictAbsence(gridTemplate, GUIContentTemplate.EDIT_ACTION);
        GUIContentTemplate.advlist().content().asserts().pictAbsence(gridTemplate, GUIContentTemplate.DELETE_ACTION);

        GUIContentTemplate.goToContentTemplate(gridTemplate.getContentCode());
        GUIContentTemplate.assertEditButtonAbsence();
        GUIContentTemplate.assertDeleteButtonAbsence();

        GUINavigational.goToReportTemplates();
        GUIReportTemplateList.advlist().content().asserts().rowsPresence(reportTemplate);
        GUIReportTemplateList.advlist()
                .content()
                .asserts()
                .pictAbsence(reportTemplate, GUIReportTemplateList.EDIT_ACTION);
        GUIReportTemplateList.advlist()
                .content()
                .asserts()
                .pictAbsence(reportTemplate, GUIReportTemplateList.DELETE_ACTION);

        GUIReportTemplate.goToCard(reportTemplate);
        GUIReportTemplate.assertEditButtonAbsence();
        GUIReportTemplate.assertDeleteButtonAbsence();
    }

    /**
     * Тестирование видимости контролов в настройке упоминаний, структур, конфигураций импорта
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать упоминание fastLinkSettingSc, разметив его комплектом settingsSet</li>
     * <li>Создать упоминание fastLinkSettingSc2, не размеченное комплектом</li>
     * <li>Создать структуру testStr, разметив ее комплектом settingsSet</li>
     * <li>Создать структуру testStr2, не размеченую комплектом</li>
     * <li>Создать конфигурацию импорта advConfig, разметив ее комплектом settingsSet</li>
     * <li>Создать конфигурацию импорта advConfig2, не размеченую комплектом</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником superUser не имеющим прав на комплект settingsSet</li>
     * <li>Перейти в раздел "Упоминания объектов"</li>
     * <li>Проверить, что в списке у упоминания fastLinkSettingSc отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у упоминания fastLinkSettingSc2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку упоминания fastLinkSettingSc</li>
     * <li>Проверить, что на карточке fastLinkSettingSc отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку упоминания fastLinkSettingSc2</li>
     * <li>Проверить, что на карточке fastLinkSettingSc2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Структуры"</li>
     * <li>Проверить, что в списке у структуры testStr отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у структуры testStr2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку стурктуры testStr</li>
     * <li>Проверить, что на карточке testStr отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку стурктуры testStr2</li>
     * <li>Проверить, что на карточке testStr2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Синхронизация"</li>
     * <li>Проверить, что в списке у конфигурации advConfig отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у конфигурации advConfig2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку конфигурации advConfig</li>
     * <li>Проверить, что на карточке advConfig отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на вкладку "История" карточки advConfig</li>
     * <li>Проверить, что на вкладке "История" карточки advConfig отсутствует кнопка "редактировать"</li>
     * <li>Перейти на карточку конфигурации advConfig2</li>
     * <li>Проверить, что на карточке advConfig2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на вкладку "История" карточки advConfig2</li>
     * <li>Проверить, что на вкладке "История" карточки advConfig2 присутствует кнопка "редактировать"</li>
     * </ol>
     */
    @Test
    public void testMentionsStructuresAdvImportControls()
    {
        SharedFixture.ou();
        FastLinkSetting fastLinkSettingSc = DAOObjectMentions.createFastLinkSetting("~", UUID, scCase);
        fastLinkSettingSc.setSettingsSet(settingsSet);
        FastLinkSetting fastLinkSettingSc2 = DAOObjectMentions.createFastLinkSetting("test", UUID, scCase);
        DSLFastLinkSetting.add(fastLinkSettingSc, fastLinkSettingSc2);

        StructuredObjectsView testStr = DAOStructuredObjectsView.create();
        testStr.setSettingsSet(settingsSet);
        StructuredObjectsView testStr2 = DAOStructuredObjectsView.create();
        DSLStructuredObjectsView.add(testStr, testStr2);

        AdvimportConfig advConfig = DAOAdvimport.createConfig(FileUtils.readAll(DSLAdvimport.CONFIG_1));
        advConfig.setSettingsSet(settingsSet);
        AdvimportConfig advConfig2 = DAOAdvimport.createConfig(FileUtils.readAll(DSLAdvimport.CONFIG_1));
        DSLAdvimport.addConfig(advConfig, advConfig2);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToFastLinkSettings();
        GUIContent.assertIconsActionExists(fastLinkSettingSc.getCode(), false, EDIT,
                DELETE);
        GUIContent.assertIconsActionExists(fastLinkSettingSc2.getCode(), true, EDIT,
                DELETE);

        GUIFastLinkSetting.goToFastSettingCard(fastLinkSettingSc);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertEditButtonOnCardPresents(false);

        GUIFastLinkSetting.goToFastSettingCard(fastLinkSettingSc2);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUINavigational.goToStructuredObjectsViews();

        GUIContent.assertIconsActionExists(testStr.getCode(), false, "editStructuredObjectsView",
                "deleteStructuredObjectsView");
        GUIContent.assertIconsActionExists(testStr2.getCode(), true, "editStructuredObjectsView",
                "deleteStructuredObjectsView");

        GUIStructuredObjectsView.goToCard(testStr);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertEditButtonOnCardPresents(false);

        GUIStructuredObjectsView.goToCard(testStr2);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUINavigational.goToAdvimport();
        GUIAdvimportList.advlistConfig().content().asserts().pictAbsence(advConfig, GUIAdvimportList.EDIT_ACTION);
        GUIAdvimportList.advlistConfig().content().asserts().pictAbsence(advConfig, GUIAdvimportList.DELETE_ACTION);

        GUIAdvimportList.advlistConfig().content().asserts().pictPresence(advConfig2, GUIAdvimportList.EDIT_ACTION);
        GUIAdvimportList.advlistConfig().content().asserts().pictPresence(advConfig2, GUIAdvimportList.DELETE_ACTION);

        GUIAdvimportList.goToCardWithRefresh(advConfig);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertEditButtonOnCardPresents(false);

        GUIAdvimportList.goToHistoryTab();
        GUIContent.assertEditButtonOnCardPresents(false);

        GUIAdvimportList.goToCardWithRefresh(advConfig2);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUIAdvimportList.goToHistoryTab();
        GUIContent.assertEditButtonOnCardPresents(true);
    }

    /**
     * Тестирование видимости контролов в настройке встроенных приложений, задач планировщика и триггеров
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00999
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$252058826
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать встроенное приложение embeddedApplication, разметив его комплектом settingsSet</li>
     * <li>Создать встроенное приложение embeddedApplication2, не размеченное комплектом</li>
     * <li>Создать задачу планировщика task, разметив ее комплектом settingsSet</li>
     * <li>Создать задачу планировщика task2, не размеченую комплектом</li>
     * <li>Создать триггер trigger для задачи планировщика task, разметив его комплектом settingsSet</li>
     * <li>Создать триггер trigger2 для задачи планировщика task2, не размеченый комплектом</li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Войти под сотрудником superUser не имеющим прав на комплект settingsSet</li>
     * <li>Перейти в раздел "Приложения"</li>
     * <li>Проверить, что в списке у приложения embeddedApplication отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у приложения embeddedApplication2 присутствуют кнопки "удалить" и
     * "редактировать"</li>
     * <li>Перейти на карточку приложения embeddedApplication</li>
     * <li>Проверить, что на карточке embeddedApplication отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку приложения embeddedApplication2</li>
     * <li>Проверить, что на карточке embeddedApplication2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти в раздел "Планировщик задач"</li>
     * <li>Проверить, что в списке у задачи task отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке у задачи task2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Перейти на карточку задачи task</li>
     * <li>Проверить, что на карточке task отсутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке тригеров задачи task у триггера trigger отсутствуют кнопки "удалить" и
     * "редактировать"</li>
     * <li>Перейти на карточку задачи task2</li>
     * <li>Проверить, что на карточке task2 присутствуют кнопки "удалить" и "редактировать"</li>
     * <li>Проверить, что в списке тригеров задачи task2 у триггера trigger2 присутствуют кнопки "удалить" и
     * "редактировать"</li>
     * </ol>
     */
    @Test
    public void testAppTaskTriggersControls()
    {
        EmbeddedApplication embeddedApplication = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        embeddedApplication.setSettingsSet(settingsSet);
        EmbeddedApplication embeddedApplication2 = DAOEmbeddedApplication.createClientSideApplicationFromFile(
                DAOEmbeddedApplication.CLIENTSIDE_APPLICATION_ZIP);
        DSLEmbeddedApplication.add(embeddedApplication2, embeddedApplication);

        ScriptInfo script = DAOScriptInfo.createNewScriptInfo();
        DSLScriptInfo.addScript(script);
        SchedulerTaskScript task = DAOSchedulerTask.createScriptRule(script.getCode());
        task.setSettingsSet(settingsSet);
        SchedulerTaskScript task2 = DAOSchedulerTask.createScriptRule(script.getCode());
        DSLSchedulerTask.addTask(task, task2);

        String date = DateTimeUtils.getRandomDateTimeddMMyyyyHHmm();
        Trigger trigger = DAOSchedulerTask.createPeriodTrigger(task, date, Periods.DAILY, Strategy.FROM_START);
        trigger.setSettingsSet(settingsSet);

        Trigger trigger2 = DAOSchedulerTask.createPeriodTrigger(task2, date, Periods.DAILY, Strategy.FROM_START);
        DSLSchedulerTask.addTrigger(trigger, trigger2);

        //Действия и проверки
        GUILogon.login(superUser);

        GUINavigational.goToEmbeddedApplications();
        GUIContent.assertIconsActionExists(embeddedApplication.getCode(), false, "editEmbeddedApp",
                "deleteEmbeddedApp");
        GUIContent.assertIconsActionExists(embeddedApplication2.getCode(), true, "editEmbeddedApp",
                "deleteEmbeddedApp");
        GUIEmbeddedApplicationForm.goToCard(embeddedApplication);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertEditButtonOnCardPresents(false);

        GUIEmbeddedApplicationForm.goToCard(embeddedApplication2);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertEditButtonOnCardPresents(true);

        GUINavigational.goToScheduler();
        GUIContent.assertIconsActionExistsForScriptTask(task.getCode(), false, "editSchTask", "deleteSchTask");
        GUIContent.assertIconsActionExistsForScriptTask(task2.getCode(), true, "editSchTask", "deleteSchTask");
        GUISchedulerTaskList.goToTaskCardWithRefresh(task);
        GUIContent.assertDeleteButtonOnCardPresents(false);
        GUIContent.assertEditButtonOnCardPresents(false);
        GUIContent.assertIconsActionExists(trigger.getId(), false, EDIT, DELETE);

        GUISchedulerTaskList.goToTaskCardWithRefresh(task2);
        GUIContent.assertDeleteButtonOnCardPresents(true);
        GUIContent.assertEditButtonOnCardPresents(true);
        GUIContent.assertIconsActionExists(trigger2.getId(), true, EDIT, DELETE);
    }
}
