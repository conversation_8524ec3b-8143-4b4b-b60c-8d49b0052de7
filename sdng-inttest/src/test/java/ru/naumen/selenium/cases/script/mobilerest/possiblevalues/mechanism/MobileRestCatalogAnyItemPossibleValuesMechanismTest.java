package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.LIST;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.FormType.PARAMETRIZED_USER_ACTION;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.element;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.elements;

import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.Iterables;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.forms.MobileFormExecutionContextBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.mobile.MobileCard;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.GroovyUtils;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибутов типа "Элемент произвольного справочника" на
 * формах через мобильное API.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestCatalogAnyItemPossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userCase;
    private static FormParameter catalogAnyItemParam;
    private static Bo userBo;
    private static EventAction userEvent;
    private static Map<String, String> expectedMap;
    private static MobileAuthentication licAuth;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать объекты userBo подтипа userCase</li>
     * <li>Создать действие по событию userEvent типа "[Пользовательское событие]" типа "Скрипт":
     * <pre>
     *     -------------------------------------------------------------------------------
     *       logger.info('success')
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Создать в ПДПС userEvent параметр логического типа booleanParam</li>
     * <li>Создать в ПДПС userEvent параметр типа "Элемент произвольного справочника" catalogAnyItemParam со скриптом:
     * <pre>
     *     -------------------------------------------------------------------------------
     *       return form.%s ? [:] : ["$код": "$значение", "$код2": "$значение2"]
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        ScriptInfo eventActionScriptInfo = DAOScriptInfo.createNewScriptInfo("logger.info('success')");
        DSLScriptInfo.addScript(eventActionScriptInfo);

        userEvent = DAOEventAction.createEventScript(EventType.userEvent, eventActionScriptInfo, true, userCase);
        DSLEventAction.add(userEvent);

        expectedMap = Map.of(
                ModelUtils.createCode(), ModelUtils.createTitle(),
                ModelUtils.createCode(), ModelUtils.createTitle()
        );

        FormParameter booleanParam = DAOFormParameter.createBool();
        String valueString = GroovyUtils.mapToString(expectedMap);
        String script = String.format("return form.%s ? [:] : %s", booleanParam.getCode(), valueString);
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);

        catalogAnyItemParam = DAOFormParameter.createCatalogAnyItem(scriptInfo);
        DSLFormParameter.saveOnEventAction(userEvent, booleanParam, catalogAnyItemParam);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения списка возможных значений параметра типа "Элемент произвольного справочника".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Параметр" = catalogAnyItemParam,</li>
     *     <li>"Тип формы" = форма заполнения параметров пользовательского ДПС</li>
     *     <li>"Код формы" = идентификатор действия userEvent,</li>
     *     <li>"Объект" = userBo</li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить что в ответе вернулся список объектов, в котором есть элементы с названиями "$код" и "$код2" и
     * соответствующими значениями</li>
     * </ul>Создать в МК карточку userCard для типа userCase, в меню которой вывести ДПС userEvent</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Тип формы" = форма заполнения параметров пользовательского ДПС</li>
     *     <li>"Код формы" = идентификатор действия userEvent,</li>
     *     <li>"Объект" = userBo,</li>
     *     <li>"Карточка" = userCard</li>
     * <li>Проверить, что в ответе атрибут catalogAnyItemParam имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут catalogAnyItemParam имеет тип выбора - список</li>
     * </ol>
     */
    @Test
    public void testCatalogAnyItem()
    {
        //Действия и проверки:
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogAnyItemParam, Map.of())
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo);
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogAnyItemParam, version)
                    .assertValues(
                            elements(expectedMap)
                    );
        }

        MobileCard userCard = DAOMobile.createMobileCard(userCase);
        userCard.setObjectActions(userEvent);
        DSLMobile.add(userCard);

        Map<String, Object> context = MobileFormExecutionContextBuilder.newContext()
                .setObject(userBo)
                .setCard(userCard)
                .build();
        ValidatableResponse formResponse = DSLMobileForms.getForm(userEvent, context, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(catalogAnyItemParam)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(userEvent, context, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(catalogAnyItemParam)
                .hasEditPresentationSelectType(LIST);
    }

    /**
     * Тестирование поиска среди списка возможных значений параметра типа "Элемент произвольного справочника".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Параметр" = catalogAnyItemParam,</li>
     *     <li>"Тип формы" = форма заполнения параметров пользовательского ДПС</li>
     *     <li>"Код формы" = идентификатор действия userEvent,</li>
     *     <li>"Объект" = userBo,</li>
     *     <li>"Строка поиска" = "$код"</li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить что в ответе вернулся список объектов, в котором есть только элемент с названием "$код" и
     * соответствующими значениями</li>
     * </ol>
     */
    @Test
    public void testCatalogAnyItemWhenPerformsSearch()
    {
        Map.Entry<String, String> elementPair = expectedMap.entrySet().iterator().next();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogAnyItemParam, Map.of())
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo)
                .setSearchString(elementPair.getValue());
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogAnyItemParam, version)
                    .assertValues(
                            element(elementPair.getKey(), elementPair.getValue())
                    );
        }
    }

    /**
     * Тестирование получения возможных значений с использованием пагинации для параметра типа "Элемент произвольного
     * справочника".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Параметр" = catalogAnyItemParam,</li>
     *     <li>"Тип формы" = форма заполнения параметров пользовательского ДПС</li>
     *     <li>"Код формы" = идентификатор действия userEvent,</li>
     *     <li>"Объект" = userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1</li>
     *     <li>"Версия мобильного API" = v13.1 или v15</li>
     * </ul></li>
     * <li>Проверить что в ответе вернулся список объектов, в котором есть только элемент с названием "$код2" и
     * соответствующими значениями</li>
     * </ol>
     */
    @Test
    public void testCatalogAnyItemWhenOffsetMoreThenZero()
    {
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogAnyItemParam, Map.of())
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo)
                .setOffset(1);
        Map.Entry<String, String> elementPair = Iterables.get(expectedMap.entrySet(), 1);

        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogAnyItemParam, version)
                    .assertValues(
                            element(elementPair.getKey(), elementPair.getValue())
                    );
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * параметра типа "Элемент произвольного справочника". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Параметр" = catalogAnyItemParam,</li>
     *     <li>"Тип формы" = форма заполнения параметров пользовательского ДПС</li>
     *     <li>"Код формы" = идентификатор действия userEvent,</li>
     *     <li>"Объект" = userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Строка поиска" = "$код"</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testCatalogAnyItemWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map.Entry<String, String> searchPair = expectedMap.entrySet().iterator().next();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogAnyItemParam, Map.of())
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo)
                .setOffset(1)
                .setSearchString(searchPair.getValue());
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogAnyItemParam).isEmpty();
        }
    }
}
