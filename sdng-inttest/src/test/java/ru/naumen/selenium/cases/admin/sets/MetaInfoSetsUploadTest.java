package ru.naumen.selenium.cases.admin.sets;

import static ru.naumen.selenium.casesutil.GUIXpath.Div.POST_ACTIONS;
import static ru.naumen.selenium.casesutil.GUIXpath.Div.PRE_ACTIONS;

import java.io.File;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLMetainfoTransfer;
import ru.naumen.selenium.casesutil.admin.GUIAdmin;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.embeddedapplication.DSLEmbeddedApplication;
import ru.naumen.selenium.casesutil.listtemplate.DSLListTemplate;
import ru.naumen.selenium.casesutil.listtemplate.GUIListTemplate;
import ru.naumen.selenium.casesutil.metaclass.DSLBoStatus;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIBoStatus;
import ru.naumen.selenium.casesutil.metaclass.GUIEmbeddedApplicationList;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.metaclass.GUIStatusAction;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.GUIMobileAddForm;
import ru.naumen.selenium.casesutil.mobile.GUIMobileList;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentTab;
import ru.naumen.selenium.casesutil.model.content.DAOTool;
import ru.naumen.selenium.casesutil.model.content.Tool.AppliedToType;
import ru.naumen.selenium.casesutil.model.content.Tool.PresentationType;
import ru.naumen.selenium.casesutil.model.content.UserTool;
import ru.naumen.selenium.casesutil.model.fastlinks.DAOObjectMentions;
import ru.naumen.selenium.casesutil.model.fastlinks.DSLFastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.FastLinkSetting;
import ru.naumen.selenium.casesutil.model.fastlinks.GUIFastLinkSetting;
import ru.naumen.selenium.casesutil.model.listtemplate.DAOListTemplate;
import ru.naumen.selenium.casesutil.model.listtemplate.ListTemplate;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOBoStatus;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EmbeddedApplication;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.StatusAction;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileList;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.sets.DAOSettingsSet;
import ru.naumen.selenium.casesutil.model.sets.SettingsSet;
import ru.naumen.selenium.casesutil.scripts.DSLConfiguration;
import ru.naumen.selenium.casesutil.sets.DSLSettingsSet;
import ru.naumen.selenium.casesutil.sets.GUISettingsSet;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование загрузки метаинформации с комплектами
 *
 * <AUTHOR>
 * @since 28.05.2025
 */
class MetaInfoSetsUploadTest extends AbstractTestCaseJ5
{
    private static SettingsSet settingsSet;
    private static final String BUTTON_NOT_EXISTS = "Кнопка не найдена";
    private static MetaClass ouClass;

    /**
     * <b>Общая подготовка</b>
     * <ol>
     * <br>
     * <li>Включить комплекты на стенде скриптом beanFactory.getBean('settingsSetStorageServiceConfiguration')
     * .setSettingSetsEnabled(true)</li>
     * <li>Добавить на стенд комплект set</li>
     * </ol>
     */
    @BeforeAll
    static void prepareFixture()
    {
        DSLConfiguration.setCustomAdminProfilesEnable(true, false);
        DSLConfiguration.setSettingSetsEnabled(true, false);
        settingsSet = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet);
        ouClass = DAOOuCase.createClass();
    }

    /**
     * Тестирование загрузки метаинформации с классом, размеченным комплектом, которого нет на стенде
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * <ol>
     * <p>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * </p>
     * <li>Создать класс UserClass (без жизненного цикла)</li>
     * <li>Выбрать для класса комплект = setForDelete</li>
     * <b>Действия:</b>
     * <li>Войти в ИА под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектом setForDelete (на форме выгрузки метаинформации параметр «Комплекты»)
     * </li>
     * <li>Удалить комплект setForDelete и класс UserClass</li>
     * <li>Загрузить метаинформацию, выгруженную на шаге 2</li>
     * <b>Проверки:</b>
     * <li>На стенде появился комплект setForDelete</li>
     * <li>На стенде появился класс UserClass</li>
     * <li>Класс UserClass связан с комплектом setForDelete</li>
     * </ol>
     */
    @Test
    void testMetaImportWithClassAndMissingSet()
    {
        //Подготовка
        MetaClass userClass = DAOUserClass.create();
        userClass.setSettingsSet(settingsSet);
        DSLMetaClass.add(userClass);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLSettingsSet.delete(settingsSet);
        DSLMetaClass.delete(userClass);
        userClass.setExists(true);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUIMetaClass.goToCard(userClass.getFqn());
        DSLMetaClass.assertPresent(userClass);
        GUISettingsSet.assertSettingsSetOnCards(settingsSet.getTitle());
    }

    /**
     * Тестирование загрузки метаинформации с формой и списком мобильного приложения, которые размечены комплектом
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00695
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00714
     * <ol>
     * <p>Подготовка:</p>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     *     <li>Залить на стенд лицензию с модулем мобильного приложения</li>
     *     <li>Для мобильного приложения создать список объектов:
     *         <ul>
     *             <li>Название/код = listCase</li>
     *             <li>Класс = Отдел</li>
     *             <li>Комплект = set</li>
     *         </ul>
     *     </li>
     *     <li>Для мобильного приложения создать форму добавления объектов:
     *         <ul>
     *             <li>Название/код = formCase</li>
     *             <li>Класс = Отдел</li>
     *             <li>Комплект = set</li>
     *         </ul>
     *     </li>
     * <b>Действия и проверки.</b>
     * <li>Войти в ИА под naumen</li>
     * <li>Выгрузить метаинформацию с комплектом set</li>
     * <li>Удалить список listCase (Настройка системы - Мобильное приложение - вкладка Списки объектов) и форму formCase
     *     (вкладка Формы добавления объектов)</li>
     * <li>Загрузить метаинформацию, выгруженную в ш. 2</li>
     * <li>Проверить, что на стенд с метаинформацией загрузился список listCase и форма formCase</li>
     * <ol>
     */
    @Test
    void testImportsSetMarkedComponents()
    {
        //Подготовка
        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
        MobileList mobileList = DAOMobile.createMobileList(ouClass);
        mobileList.setSettingsSet(settingsSet);
        DSLMobile.add(mobileList);

        MobileAddForm mobileAddForm = DAOMobile.createMobileAddForm(ouClass);
        mobileAddForm.setSettingsSet(settingsSet);
        DSLMobile.add(mobileAddForm);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLMobile.delete(mobileList, mobileAddForm);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUINavigational.goToMobileSettings();
        GUIMobileList.assertMobileListPresents(mobileList);

        GUIMobileAddForm.clickAddFormsTab();
        GUIMobileAddForm.assertPresence(mobileAddForm);
    }

    /**
     * Тестирование загрузки метаинформации с кнопками действий, которые размечены комплектом
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00123
     * <p>Подготовка:</p>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Отдел на панель действий карточки объекта добавить кнопку:
     *       <ul>
     *           <li>Название = buttonCardCase</li>
     *           <li>Комплект = set</li>
     *       </ul>
     *   </li>
     *   <li>В класс Отдел на панель действий контента Системные атрибуты на карточке объекта добавить кнопку:
     *       <ul>
     *           <li>Название = buttonContentCase</li>
     *           <li>Комплект = set</li>
     *       </ul>
     *   </li>
     *   <li>В класс Отдел на панель действий вкладки Отдел на карточке объекта добавить кнопку:
     *       <ul>
     *           <li>Название = buttonTabCase</li>
     *           <li>Комплект = set</li>
     *       </ul>
     *   </li>
     * <b>Действия и проверки.</b>
     * <ol>
     *     <li>Войти в ИА под naumen</li>
     *     <li>Выгрузить метаинформацию с комплектом set</li>
     *     <li>На карточке объекта класса Отдел удалить кнопки: buttonCardCase, buttonTabCase, buttonContentCase</li>
     *     <li>Загрузить метаинформацию, выгруженную в ш. 2</li>
     *     <li>Проверить, что на стенд с метаинформацией на карточку Отдела загрузились кнопки: buttonContentCase,
     *     buttonCardCase, buttonTabCase</li>
     * </ol>
     */
    @Test
    void testActionButtonsMarkupInMetaInfo()
    {
        String buttonTabCase = "buttonTabCase";
        //Подготовка
        ContentTab tab = DAOContentTab.createTab(ouClass.getFqn());
        GroupAttr groupAttr = DAOGroupAttr.create(ouClass);
        DSLGroupAttr.add(groupAttr);
        DSLContent.addTab(tab);

        ContentForm content = DAOContentCard.createPropertyList(ouClass, groupAttr);
        DSLContent.add(content);

        ContentForm windowContent = DSLContent.getWindowContent(ouClass);
        UserTool buttonCardCase = DAOTool.createUserTool(AppliedToType.CURRENT_OBJECT,
                PresentationType.DEFAULT_TEXT_ONLY, null, settingsSet);
        UserTool buttonContentCase = DAOTool.createUserTool(AppliedToType.CURRENT_OBJECT,
                PresentationType.DEFAULT_TEXT_ONLY, null, settingsSet);
        DSLContent.addUserTool(windowContent, buttonCardCase);
        DSLContent.addUserTool(content, buttonContentCase);
        GUIAdvListEditableToolPanel buttonContentCaseToolPanel = content.advlist().editableToolPanel();
        GUIAdvListEditableToolPanel buttonTabCaseToolPanel = tab.advlist().editableToolPanel();

        GUILogon.asSuper();
        GUIMetaClass.goToTab(ouClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.goToTab(tab);
        buttonTabCaseToolPanel.clickEditToolPanel();
        buttonTabCaseToolPanel.setUseSystemSettings(false);
        buttonTabCaseToolPanel.rightClickTool(GUIAdvListXpath.EDITABLE_TOOL_TEMPLATE);
        buttonTabCaseToolPanel.clickAddContextMenuOption();
        buttonTabCaseToolPanel.setSettingsSetOnForm(settingsSet);
        buttonTabCaseToolPanel.setTitleOnForm(buttonTabCase);
        GUIForm.applyLastModalForm();
        GUIForm.applyModalForm();

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        GUIContent.goToTab(DSLContent.getFirstTab(ouClass.getFqn()));
        GUIMetaClass.goToTab(ouClass, MetaclassCardTab.OBJECTCARD);
        GUIContent.visibility(buttonContentCaseToolPanel.getContentId());
        deleteToolFromPanel(buttonContentCaseToolPanel, buttonContentCase.getTitle());
        deleteToolFromPanel(windowContent.advlist().editableToolPanel(), buttonCardCase.getTitle());
        GUIContent.goToTab(tab);
        deleteToolFromPanel(buttonTabCaseToolPanel, buttonTabCase);

        GUIAdmin.goToCard();
        GUIAdmin.loadMetainfo(exportedMetainfo.getAbsolutePath());

        GUIMetaClass.goToTab(ouClass, MetaclassCardTab.OBJECTCARD);
        GUITester.assertPresent(content.advlist().toolPanel().asserts().getContentXpath() + GUIXpath.Div.TEXT_CONTAINS,
                BUTTON_NOT_EXISTS, buttonContentCase.getTitle());
        GUITester.assertPresent(
                windowContent.advlist().toolPanel().asserts().getContentXpath() + GUIXpath.Div.TEXT_CONTAINS,
                BUTTON_NOT_EXISTS, buttonCardCase.getTitle());

        GUIContent.goToTab(tab);
        GUITester.assertPresent(tab.advlist().toolPanel().asserts().getContentXpath() + GUIXpath.Div.TEXT_CONTAINS,
                BUTTON_NOT_EXISTS, buttonTabCase);
        GUIMetaClass.goToTab(ouClass, MetaclassCardTab.OBJECTCARD);

    }

    /**
     * Тестирование загрузки метаинформации с действиями при выходе из статуса, которые размечены разными комплектами
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00346
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать комплект set2</li>
     * <li>В класс Запрос, статусу Зарегистрирован, добавить два действия на выход из статуса:
     *   <ul>
     *     <li>Название = test1</li>
     *     <li>Скрипт = //</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * </li>
     * <li>Добавить второе действие на выход из статуса:
     *   <ul>
     *     <li>Название = test2</li>
     *     <li>Скрипт = //</li>
     *     <li>Комплект = set2</li>
     *   </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектами set и set2</li>
     * <li>В классе Запрос, в настройках жизненного цикла, у статуса Зарегистрирован удалить действия на выход из
     * статуса test1 и test2</li>
     * <li>Загрузить ранее выгруженную метаинформацию (пункт 2)</li>
     * <li>Проверить, что на стенд с метаинформацией в статус Зарегистрирован класса Запрос добавились действия на
     * выход из статуса test1 и test2</li>
     * </ol>
     */
    @Test
    void loadMetaInfoWithSetActions()
    {
        //Подготовка
        SettingsSet settingsSet2 = DAOSettingsSet.createSettingsSet();
        DSLSettingsSet.add(settingsSet2);

        MetaClass scCase = SharedFixture.scCase();
        BoStatus status = DAOBoStatus.createRegistered(scCase.getFqn());

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("//");
        StatusAction action1 = DSLBoStatus.addPostAction(status, scriptInfo, settingsSet);
        StatusAction action2 = DSLBoStatus.addPostAction(status, scriptInfo, settingsSet2);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(List.of(settingsSet, settingsSet2));
        DSLBoStatus.deleteScriptActionOrCondition(action1);
        DSLBoStatus.deleteScriptActionOrCondition(action2);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUIBoStatus.goToStatusCard(status);
        GUIStatusAction.assertActionsConditionsList(POST_ACTIONS, false, action2, action1);
    }

    /**
     * Тестирование загрузки метаинформации с условиями на вход/выход из статуса, размеченными комплектами
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00345
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Запрос, статусу Зарегистрирован, добавить условие на вход в статус:
     *   <ul>
     *     <li>Название = test1</li>
     *     <li>Скрипт = //</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * </li>
     * <li>Добавить условие на выход из статуса:
     *   <ul>
     *     <li>Название = test2</li>
     *     <li>Скрипт = //</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектом set</li>
     * <li>В классе Запрос, в настройках жизненного цикла, у статуса Зарегистрирован удалить условия на вход и выход
     * из статуса test1 и test2</li>
     * <li>Загрузить ранее выгруженную метаинформацию (пункт 2)</li>
     * <li>Проверить, что на стенд с метаинформацией в статус Зарегистрирован класса Запрос добавились условия на
     * вход и выход из статуса test1 и test2</li>
     * </ol>
     */
    @Test
    void loadMetaInfoWithStatusEntryExitConditions()
    {
        //Подготовка
        MetaClass scCase = SharedFixture.scCase();
        BoStatus status = DAOBoStatus.createRegistered(scCase.getFqn());

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo("//");
        StatusAction action1 = DSLBoStatus.addPreAction(status, scriptInfo, settingsSet);
        StatusAction action2 = DSLBoStatus.addPostAction(status, scriptInfo, settingsSet);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLBoStatus.deleteScriptActionOrCondition(action1);
        DSLBoStatus.deleteScriptActionOrCondition(action2);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUIBoStatus.goToStatusCard(status);
        GUIStatusAction.assertActionsConditionsList(POST_ACTIONS, true, action2);
        GUIStatusAction.assertActionsConditionsList(PRE_ACTIONS, true, action1);
    }

    /**
     * Тестирование загрузки метаинформации с встроенным приложением, размеченным комплектом.
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00682
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить приложение:
     *   <ul>
     *     <li>Название/код = vpTest</li>
     *     <li>Адрес приложения = e1.ru</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектом set</li>
     * <li>Удалить приложение vpTest</li>
     * <li>Загрузить ранее выгруженную метаинформацию (пункт 2)</li>
     * <li>Проверить, что на стенд загрузилось приложение vpTest</li>
     * </ol>
     */
    @Test
    void loadMetaInfoWithEmbeddedApp()
    {
        //Подготовка
        EmbeddedApplication application = DAOEmbeddedApplication.createExternalApplication("e1.ru", settingsSet);
        DSLEmbeddedApplication.add(application);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLEmbeddedApplication.delete(application);
        application.setExists(true);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUINavigational.goToEmbeddedApplications();
        GUIEmbeddedApplicationList.assertPresenceEmbeddedApplication(application);
    }

    /**
     * Тестирование загрузки метаинформации с упоминанием, размеченным комплектом
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00751
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить упоминание:
     *   <ul>
     *     <li>Название/код = test</li>
     *     <li>Объекты = Отдел</li>
     *     <li>Префикс для упоминания объекта = @</li>
     *     <li>Атрибут для формирования ссылки = Уникальный идентификатор</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * </li>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектом set</li>
     * <li>Удалить упоминание test</li>
     * <li>Загрузить ранее выгруженную метаинформацию (пункт 2)</li>
     * <li>Проверить, что на стенд с метаинформацией загрузилось упоминание test</li>
     * </ol>
     */
    @Test
    void loadMetaInfoWithMention()
    {
        //Подготовка
        FastLinkSetting fastLinkSettingEmp = DAOObjectMentions.createFastLinkSetting("@", "UUID", ouClass);
        fastLinkSettingEmp.setSettingsSet(settingsSet);
        DSLFastLinkSetting.add(fastLinkSettingEmp);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLFastLinkSetting.delete(fastLinkSettingEmp);
        fastLinkSettingEmp.setExists(true);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUINavigational.goToFastLinkSettings();
        GUIFastLinkSetting.assertPresentFastLinkInList(fastLinkSettingEmp);
    }

    /**
     * Тестирование загрузки метаинформации с шаблоном списка, размеченным комплектом
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$264339089
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00253
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00762
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Добавить шаблон списка:</li>
     *   <ul>
     *     <li>Название/код = test</li>
     *     <li>Класс объектов списка = Отдел</li>
     *     <li>Комплект = set</li>
     *   </ul>
     * <br>
     * <b>Действия и проверки</b>
     * <li>Авторизоваться под пользователем naumen</li>
     * <li>Выгрузить метаинформацию с комплектом set</li>
     * <li>Удалить шаблон списка test</li>
     * <li>Загрузить ранее выгруженную метаинформацию (пункт 2)</li>
     * <li>Проверить, что на стенд с метаинформацией загрузился шаблон списка test</li>
     * </ol>
     */
    @Test
    void loadMetaInfoWithListTemplate()
    {
        //Подготовка
        ListTemplate template = DAOListTemplate.createTemplate(ouClass);
        template.setSettingsSet(settingsSet);
        DSLListTemplate.add(template);

        //Действия и проверки
        File exportedMetainfo = DSLMetainfoTransfer.exportMetainfo(Collections.singletonList(settingsSet));
        DSLListTemplate.delete(template);
        template.setExists(true);
        DSLMetainfoTransfer.importMetainfo(exportedMetainfo);

        GUILogon.asSuper();
        GUINavigational.goToListTemplates();
        GUIListTemplate.advlist().content().asserts().rowsPresence(template);
    }

    /**
     * Удалить кнопку с панели инструментов
     * @param panel панель откуда удаляются кнопки
     * @param toolTitle название кнопок
     */
    private static void deleteToolFromPanel(GUIAdvListEditableToolPanel panel, String toolTitle)
    {
        panel.clickEditToolPanel();
        panel.rightClickToolByTitle(toolTitle);
        panel.clickDeleteContextMenuOption();
        GUIForm.applyModalForm();
    }
}
