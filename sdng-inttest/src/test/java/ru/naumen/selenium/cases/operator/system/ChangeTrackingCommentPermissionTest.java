package ru.naumen.selenium.cases.operator.system;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIStatusPanel;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.comment.DSLComment;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOCommentClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.SecurityMarker;
import ru.naumen.selenium.security.SecurityMarkerViewCommentAttrs;

/**
 * Тестирование прав на просмотр уведомлений для действия по событию "Отслеживание изменений" комментария
 *
 * <AUTHOR>
 * @since 23.05.2025
 */
public class ChangeTrackingCommentPermissionTest extends AbstractTestCase
{
    private static MetaClass userClass;
    private static Bo userBo, employee;
    private static Attribute allowedAttr, allowedAttrInSysGroup, allowedAttrInMoreGroup, forbiddenAttr;
    private static SecurityProfile secProfile;
    private static ContentForm commentList;

    private static final String superUserName = "Суперпользователь";
    private static final String addCommentPattern = "%s добавил(а) комментарий: %s.";
    private static final String editCommentPattern = "%s изменил(а) комментарий: %s.";

    /**
     * <b>Общая подготовка</b>
     * <br>
     * <ol>
     * <li>Создать пользовательский класс userClass и тип userCase</li>
     * <li>В классе "Комментарий" создать строковые атрибуты: allowedAttr, allowedAttrInSysGroup,
     * allowedAttrInMoreGroup, forbiddenAttr</li>
     * <li>В классе "Комментарий" создать группу атрибутов attrGroup и добавить в нее атрибуты allowedAttr,
     * forbiddenAttr</li>
     * <li>Добавить атрибут allowedAttrInMoreGroup в группу атрибутов "Подробнее"</li>
     * <li>Добавить атрибут allowedAttrInSysGroup в системную группу атрибутов</li>
     * <li>На карточку объекта класса userClass вывести контент commentList типа «Список комментариев»</li>
     * <li>Создать объект userBo класса userClass</li>
     * <li>Создать отдел ou</li>
     * <li>В отделе ou создать сотрудника employee</li>
     * <li>Создать группу пользователей securityGroup, добавить в неё сотрудника employee</li>
     * <li>Создать профиль secProfile для группы securityGroup, роль Сотрудник</li>
     * <li>Выдать профилю secProfile все права</li>
     * <li>Создать маркер прав на просмотр атрибутов комментариев markerForbiddenAttr и поместить в него атрибут
     * forbiddenAttr</li>
     * <li>Забрать у профиля secProfile права на маркер markerForbiddenAttr</li>
     * <li>Создать действие по событию eventActionAddComment:<br>
     *      объекты — userClass;<br>
     *      событие — Добавление комментария;<br>
     *      действие — Отслеживание изменений;<br>
     *      кому — employee</li>
     * <li>Создать действие по событию eventActionEditComment:<br>
     *       объекты — userClass;<br>
     *       событие — Редактирование комментария;<br>
     *       действие — Отслеживание изменений;<br>
     *       кому — employee</li>
     * <li>Включить отслеживание изменений объектов</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        // Общая подготовка
        userClass = DAOUserClass.createWithWFAndResp();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        MetaClass commentClass = DAOCommentClass.create();

        allowedAttr = DAOAttribute.createString(commentClass);
        allowedAttrInSysGroup = DAOAttribute.createString(commentClass);
        allowedAttrInMoreGroup = DAOAttribute.createString(commentClass);
        forbiddenAttr = DAOAttribute.createString(commentClass);
        DSLAttribute.add(allowedAttr, allowedAttrInSysGroup, allowedAttrInMoreGroup, forbiddenAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(commentClass.getFqn());
        DSLGroupAttr.add(attrGroup, forbiddenAttr, allowedAttr);

        DSLGroupAttr.addToGroup(DAOGroupAttr.createMore(), allowedAttrInMoreGroup);

        GroupAttr systemAttrGroup = DAOGroupAttr.createSystem(commentClass);
        DSLGroupAttr.addToGroup(systemAttrGroup, allowedAttrInSysGroup);

        commentList = DAOContentCard.createCommentList(userClass);
        commentList.setDetailedAttributeGroup(attrGroup);
        DSLContent.add(commentList);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Bo ou = SharedFixture.ou();
        employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, false, true);
        DAOEmployee.setTitle(employee, ModelUtils.createTitle());
        DSLBo.add(employee);

        SecurityGroup securityGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(securityGroup);
        DSLSecurityGroup.addUsers(securityGroup, employee);

        secProfile = DAOSecurityProfile.create(true, securityGroup, SysRole.employee());
        DSLSecurityProfile.add(secProfile);
        DSLSecurityProfile.grantAllPermissionsForCase(secProfile, userClass);

        SecurityMarker markerForbiddenAttr =
                new SecurityMarkerViewCommentAttrs(userClass).addAttributes(forbiddenAttr).apply();
        DSLSecurityProfile.removeRights(userClass, secProfile, markerForbiddenAttr);

        EventAction eventActionAddComment = DAOEventAction.createChangeTracking(userClass, EventType.addComment,
                employee);
        EventAction eventActionEditComment = DAOEventAction.createChangeTracking(userClass, EventType.editComment,
                employee);
        DSLEventAction.add(eventActionEditComment, eventActionAddComment);

        DSLAdmin.setObjectChangeTrackingEnabled(true);
    }

    /**
     * Тестирование проверки прав на показ уведомлений при отслеживании изменений по событиям добавления и
     * редактирования комментария<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00962 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$296842986 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li><b>{@link #prepareFixture() Общая подготовка}</b></li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>{@link #assertAllowedComment(boolean) Выполнить проверку} на появление уведомлений для обычных
     * комментария</li>
     * <li>{@link #assertAllowedComment(boolean) Выполнить проверку} на появление уведомлений для приватных
     * комментариев</li>
     * <li>Забрать у пользователя employee право на просмотр приватных комментариев</li>
     * <li>{@link #assertAllowedComment(boolean) Выполнить проверку} на отсутствие уведомлений для приватных
     * комментариев</li>
     * <li>Забрать у пользователя employee право на просмотр обычных комментариев</li>
     * <li>{@link #assertAllowedComment(boolean) Выполнить проверку} на отсутствие уведомлений для обычных
     * комментариев</li>
     * </ol>
     */
    @Test
    public void testViewPermissionsForCommentChangeTracking()
    {
        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        assertAllowedComment(false);
        assertAllowedComment(true);

        DSLSecurityProfile.removeRights(userClass, secProfile, AbstractBoRights.VIEW_PRIVATE_COMMENT);
        assertForbiddenComment(true);

        DSLSecurityProfile.removeRights(userClass, secProfile, AbstractBoRights.VIEW_COMMENT);
        assertForbiddenComment(false);
    }

    /**
     * Тестирование проверки прав на показ автора комментария при отслеживании изменений по событиям добавления и
     * редактирования комментария<br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00962 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$296842986 <br>
     * <ol>
     * <b>Подготовка</b>
     * <li><b>{@link #prepareFixture() Общая подготовка}</b></li>
     * <b>Выполнение действий и проверки</b>
     * <li>Войти в систему под сотрудником employee</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <li>От имени этого сотрудника добавить комментарий</li>
     * <li>Проверить, что появилось уведомление с именем сотрудника: "employee добавил(а) комментарий:"</li>
     * <li>Забрать у пользователя employee право на просмотр автора комментария</li>
     * <li>От имени этого сотрудника изменить комментарий</li>
     * <li>Проверить, что появилось уведомление с псевдонимом сторудника: "Сотрудник изменил(а) комментарий:"</li>
     * </ol>
     */
    @Test
    public void testViewAuthorPermissionsForCommentChangeTracking()
    {
        // Выполнение действий и проверки
        GUILogon.login(employee);
        GUIBo.goToCard(userBo);

        String commentText = ModelUtils.createText(5);
        String commentUuid = GUIComment.add(commentList, commentText, false);
        GUIStatusPanel.assertStatusPanelAppear();
        GUIStatusPanel.assertStatusMessage(String.format(addCommentPattern, employee.getTitle(), commentText));
        GUIStatusPanel.clickMessageAction();

        DSLSecurityProfile.removeRights(userClass, secProfile, AbstractBoRights.VIEW_AUTHOR);
        GUIComment.clickEdit(commentUuid);
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        String changedCommentText = ModelUtils.createText(10);
        GUIRichText.sendKeys(GUIRichText.TEXT, changedCommentText);
        GUIForm.applyForm();
        GUIStatusPanel.assertStatusPanelAppear();
        String alias = "Сотрудник";
        GUIStatusPanel.assertStatusMessage(String.format(editCommentPattern, alias, commentText + changedCommentText));
    }

    /**
     * Проверка на появление уведомлений для доступного к просмотру комментария
     * <li>{@link #assertAddComment(boolean, boolean) Выполнить проверку}, что уведомление появляется для
     * добавления комментария</li>
     * <li>{@link #assertEditComment(String, boolean) Выполнить проверку}, что уведомление появляется для
     * редактирования текста комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление появляется для
     * редактирования доступного атрибута (allowedAttr) комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление появляется при
     * одновременном редактировании доступного (allowedAttr) и недоступного (forbiddenAttr) атрибутов комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление не появляется при
     * одновременном редактировании доступных, но не видимых пользователю атрибутов (allowedAttrInMoreGroup,
     * allowedAttrInSysGroup) и недоступного, но находящегося в группе видимых атрибутов (forbiddenAttr)
     * комментария</li>
     *
     * @param isPrivate true, если требуется выполнить проверки для приватного комментария
     */
    private void assertAllowedComment(boolean isPrivate)
    {
        String commentUuid = assertAddComment(true, isPrivate);
        assertEditComment(commentUuid, true);
        assertEditAttrs(commentUuid, true, allowedAttr);
        assertEditAttrs(commentUuid, true, allowedAttr, forbiddenAttr);
        assertEditAttrs(commentUuid, false, forbiddenAttr, allowedAttrInMoreGroup, allowedAttrInSysGroup);
    }

    /**
     * Проверка на отсутствие уведомлений для комментария, недоступного к просмотру
     * <li>{@link #assertAddComment(boolean, boolean) Выполнить проверку}, что уведомление не появляется для
     * добавления комментария</li>
     * <li>{@link #assertEditComment(String, boolean) Выполнить проверку}, что уведомление не появляется для
     * редактирования текста комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление не появляется для
     * редактирования доступного атрибута (allowedAttr) комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление не появляется при
     * одновременном редактировании доступного (allowedAttr) и недоступного (forbiddenAttr) атрибутов комментария</li>
     * <li>{@link #assertEditAttrs(String, boolean, Attribute...) Выполнить проверку}, что уведомление не появляется при
     * одновременном редактировании доступных, но не видимых пользователю атрибутов (allowedAttrInMoreGroup,
     * allowedAttrInSysGroup) и недоступного, но находящегося в группе видимых атрибутов (forbiddenAttr)
     * комментария</li>
     *
     * @param isPrivate true, если требуется выполнить проверки для приватного комментария
     */
    private void assertForbiddenComment(boolean isPrivate)
    {
        String commentUuid = assertAddComment(false, isPrivate);
        assertEditComment(commentUuid, false);
        assertEditAttrs(commentUuid, false, allowedAttr);
        assertEditAttrs(commentUuid, false, allowedAttr, forbiddenAttr);
        assertEditAttrs(commentUuid, false, forbiddenAttr, allowedAttrInMoreGroup, allowedAttrInSysGroup);
    }

    /**
     * Проверка появления уведомления при добавлении комментария
     * <li>Добавить к объекту userBo комментарий от имени суперпользователя</li>
     * <li>Проверить, появилось ли сообщение «authorName добавил(а) комментерий: commentText»  на
     * панели состояния </li>
     *
     * @param hasAppear true, если ожидается, что должно отобразиться уведомление
     * @param isPrivate признак приватности комментария
     * @return uuid добавленного комментария
     */
    private String assertAddComment(boolean hasAppear, boolean isPrivate)
    {
        String commentText = ModelUtils.createText(5);
        String commentUuid = DSLComment.add(userBo.getUuid(), commentText, SharedFixture.employee().getUuid(),
                isPrivate);
        if (hasAppear)
        {
            GUIStatusPanel.assertStatusPanelAppear();
            GUIStatusPanel.assertStatusMessage(
                    String.format(addCommentPattern, superUserName, commentText));
            GUIStatusPanel.clickMessageAction();
        }
        else
        {
            GUIStatusPanel.assertStatusPanelDisappear();
        }
        return commentUuid;
    }

    /**
     * Проверка появления уведомления при редактировании текста комментария
     * <ol>
     * <li>От имени суперпользователя изменить текст комментария</li>
     * <li>Проверить, появилось ли сообщение «authorName изменил(а) комментерий: editingCommentText»  на
     * панели состояния </li>
     * </ol>
     *
     * @param commentUuid комментария
     * @param hasAppear true, если ожидается, что должно отобразиться уведомление
     */
    private void assertEditComment(String commentUuid, boolean hasAppear)
    {
        String editingCommentText = ModelUtils.createText(5);
        DSLComment.edit(commentUuid, editingCommentText);
        if (hasAppear)
        {
            GUIStatusPanel.assertStatusPanelAppear();
            GUIStatusPanel.assertStatusMessage(String.format(editCommentPattern, superUserName, editingCommentText));
            GUIStatusPanel.clickMessageAction();
        }
        else
        {
            GUIStatusPanel.assertStatusPanelDisappear();
        }
    }

    /**
     * Проверка появления уведомления при редактировании атрибутов комментария
     * <ol>
     * <li>От имени суперпользователя изменить атрибуты комментария</li>
     * <li>Проверить, появилось ли уведомление, что комментарий изменен</li>
     * </ol>
     *
     * @param hasAppear true, если ожидается, что должно отобразиться уведомление
     */
    private void assertEditAttrs(String commentUuid, boolean hasAppear, Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            attr.setValue(ModelUtils.createText(5));
        }
        DSLComment.editAttributes(commentUuid, attrs);
        if (hasAppear)
        {
            GUIStatusPanel.assertStatusPanelAppear();
            GUIStatusPanel.clickMessageAction();
        }
        else
        {
            GUIStatusPanel.assertStatusPanelDisappear();
        }
    }
}
