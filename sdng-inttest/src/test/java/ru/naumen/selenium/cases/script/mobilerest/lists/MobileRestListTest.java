package ru.naumen.selenium.cases.script.mobilerest.lists;

import static ru.naumen.selenium.casesutil.mobile.rest.lists.validators.ListActionValidators.filtration;
import static ru.naumen.selenium.casesutil.mobile.rest.lists.validators.ListActionValidators.sort;

import java.util.List;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.lists.DSLMobileLists;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockAnd;
import ru.naumen.selenium.casesutil.model.content.advlist.FilterBlockOr;
import ru.naumen.selenium.casesutil.model.content.advlist.ListFilter;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.mobile.MobileList;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Тестирование получения списков через мобильное API
 *
 * <AUTHOR>
 * @since 12.08.2019
 */
public class MobileRestListTest extends AbstractTestCase
{
    private static MetaClass userClass;
    private static MetaClass userCase;
    private static MobileAuthentication auth;

    /**
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass и его тип userCase</li>
     * <li>Создать объект userObject типа userCase</li>
     * <li>Установить лицензию мобильного приложения</li>
     * <li>Создать ключ доступа для лицензированного пользователя</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createInSelf();
        userCase = DAOMetaClass.createCase(userClass);
        DSLMetaClass.add(userClass, userCase);

        Bo userObject = DAOUserBo.create(userCase);
        DSLBo.add(userObject);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        auth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование корректного получения списка и его настроек.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00816
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$237444467
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать атрибут objectAttr в классе userClass типа "Ссылка на БО" на тип сотрудника</li>
     * <li>Создать атрибут relatedAttr в классе userClass типа "Атрибут связанного объекта",
     * атрибут связи - objectAttr (класс объектов в атрибуте - Сотрудник),
     * атрибут связанного класса - Родитель (parent, класс объектов в атрибуте - Отдел)</li>
     * <li>Создать список list для типа userCase, в который вывести атрибуты objectAttr и relatedAttr</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить содержание списка list через мобильное API</li>
     * <li>Проверяем, что в полученном списке 1 объект</li>
     * <li>Получить настройки списка list через мобильное API</li>
     * <li>Проверить, что список действий содержит действия:
     * <ul>
     *     <li>"Фильтрация";</li>
     *     <li>"Сортировка": по атрибуту Название</li>
     * </ul></li>
     * </ol>
     */
    @Test
    public void testWithList()
    {
        Attribute objectAttr = DAOAttribute.createObjectLink(userClass, SharedFixture.employeeCase(), null);
        DSLAttribute.add(objectAttr);

        Attribute parentAttr = SysAttribute.parent(DAOEmployeeCase.createClass());
        Attribute relatedAttr =
                DAOAttribute.createAttributeOfRelatedObject(userClass.getFqn(), parentAttr, "1", null, objectAttr);
        DSLAttribute.add(relatedAttr);

        MobileList list = DAOMobile.createMobileList(userClass);
        DSLMobile.add(list);
        DSLMobile.addAttributes(list, objectAttr, relatedAttr);

        ValidatableResponse listResponse = DSLMobileLists.getList(list, auth);

        listResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileLists.assertTotalObjectsCount(listResponse, 1);
        ValidatableResponse settingsResponse = DSLMobileLists.getListSettings(list, auth);
        DSLMobileLists.assertListSettings(settingsResponse, List.of(
                filtration(),
                sort().attributes(relatedAttr, objectAttr)
        ));
    }

    /**
     * Тестирование корректного получения фильтрованного списка.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00816
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00852
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00665
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$89355104
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать атрибут strAttr в классе userClass типа "Cтрока"</li>
     * <li>Создать 2 объекта типа userCase и заполнить у них атрибут strAttr значениями:
     * <pre>
     *     userObject1 - значение атрибута strAttr - "Иванов Иван Иванович"
     *     userObject2 - значение атрибута strAttr - "Карытко Павел Сегреевич"
     * </pre></li>
     * <li>Создать список list для userClass, настроить фильтрацию объектов в списке:
     * <pre>
     *     атрибут strAttr - "содержит" - "Иванов Иван Иванович"
     * </pre></li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить содержание списка list через мобильное API</li>
     * <li>Проверить, что в полученном списке 1 объект</li>
     * </ol>
     */
    @Test
    public void testListWithFilter()
    {
        Attribute stringAttr = DAOAttribute.createString(userClass);
        DSLAttribute.add(stringAttr);

        String filteredValue = "Иванов Иван Иванович";
        Bo userObject1 = DAOUserBo.create(userCase);
        Bo userObject2 = DAOUserBo.create(userCase);
        DSLBo.add(userObject1, userObject2);

        stringAttr.setValue(filteredValue);
        DSLBo.editAttributeValue(userObject1, stringAttr);

        stringAttr.setValue("Карытко Павел Сегреевич");
        DSLBo.editAttributeValue(userObject2, stringAttr);

        MobileList list = DAOMobile.createMobileList(userClass);
        DSLMobile.add(list);

        ListFilter listFilter = new ListFilter(new FilterBlockAnd(
                new FilterBlockOr(stringAttr, FilterCondition.CONTAINS, false, filteredValue)));
        DSLMobile.setListFiltration(list, listFilter);

        ValidatableResponse listResponse = DSLMobileLists.getList(list, auth);

        listResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileLists.assertTotalObjectsCount(listResponse, 1);

    }

    /**
     * Тестирование корректного получения списка, когда применяется пользовательская фильтрация, в цепочке которой есть
     * Родитель (parent) и уровень вложенности больше нуля.
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$83325250
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00607
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский класс userClass, который вложен сам в себя, и его тип userCase</li>
     * <li>Создать атрибут objectAttr в классе userClass типа "Ссылка на БО" на тип Сотрудника</li>
     * <li>Создать атрибут relatedAttr в классе userClass типа "Атрибут связанного объекта",
     * атрибут связи - objectAttr (класс объектов в атрибуте - Сотрудник),
     * атрибут связанного класса - parent (класс объектов в атрибуте - Отдел)</li>
     * <li>Создать список list для типа userCase, в который вывести атрибуты objectAttr и relatedAttr</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить содержание списка list с пользовательской фильтрацией</li>
     * <li>Проверить, что список вернулся без ошибок</li>
     * </ol>
     */
    @Test
    public void testFiltrationWithParentChain()
    {
        Attribute objectAttr = DAOAttribute.createObjectLink(userClass, SharedFixture.employeeCase(), null);
        DSLAttribute.add(objectAttr);

        Attribute parentAttr = SysAttribute.parent(DAOEmployeeCase.createClass());
        Attribute relatedAttr = DAOAttribute.createAttributeOfRelatedObject(userClass.getFqn(), parentAttr, "1", null,
                objectAttr);
        DSLAttribute.add(relatedAttr);

        MobileList list = DAOMobile.createMobileList(userClass);
        DSLMobile.add(list);
        DSLMobile.addAttributes(list, objectAttr, relatedAttr);

        String filterBy = ModelUtils.createText(10);
        ValidatableResponse listResponse = DSLMobileLists.getListWithFiltration(list, filterBy, auth);

        listResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileLists.assertTotalObjectsCount(listResponse, 0);
    }

    /**
     * Тестирование корректной фильтрации объектов списка по подстроке в атрибуте "Целое число"
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00389
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00524
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$179395951
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать атрибут integerAttr типа "Целое число"</li>
     * <li>Создать уникальную последовательность цифр filteredValuePart из 5-и подряд идущих одинаковых цифр</li>
     * <li>Создать объект типа userCase и заполнить ему атрибут integerAttr значением filteredValuePart</li>
     * <li>Создать объект типа userCase и заполнить ему атрибут integerAttr значением, в котором последовательность
     * filteredValuePart будет идти в начале значения</li>
     * <li>Создать объект типа userCase и заполнить ему атрибут integerAttr значением, в котором последовательность
     * filteredValuePart будет идти в конце значения</li>
     * <li>Создать объект типа userCase и заполнить ему атрибут integerAttr значением, в котором последовательность
     * filteredValuePart будет идти в середине значения</li>
     * <li>Создать объект типа userCase и заполнить ему атрибут integerAttr значением, в котором не будет
     * последовательности filteredValuePart</li>
     * <b>Действия:</b>
     * <li>Отправить запрос на получение списка объектов в МК, указав подстроку filteredValuePart, по которой будет
     * производиться фильтрация</li>
     * <b>Проверки:</b>
     * <li>Проверяем, что в полученном списке 2 объекта (со значением атрибута filteredValuePart и filteredValuePart,
     * идущем в начале значения)</li>
     * </ol>
     */
    @Test
    public void testFilterObjectInListBySubstringInIntegerAttribute()
    {
        //Подготовка:

        //Генерируем подстроку по принципу:
        //1. Генерируем случайное число от 2 до 8 (например, 6)
        //2. Обращаем его в строку и повторяем восемь раз, формируя строку фильтрации (6 -> "66666666")
        //3. Добавляем к изначальному числу +-1 и повторяем шаг 2, формируя строку, отличную от фильтрующейся
        //(6 -> 7 -> "77777777")
        int number = RandomUtils.nextInt(7) + 2;
        String filteredValuePart = String.valueOf(number).repeat(5);
        String randomValuePart = String.valueOf(RandomUtils.nextBoolean() ? number + 1 : number - 1).repeat(5);

        Attribute integerAttr = DAOAttribute.createInteger(userClass);
        DSLAttribute.add(integerAttr);

        Bo bo1 = DAOUserBo.create(userCase, integerAttr, filteredValuePart);
        Bo bo2 = DAOUserBo.create(userCase, integerAttr, filteredValuePart + randomValuePart);
        Bo bo3 = DAOUserBo.create(userCase, integerAttr, randomValuePart + filteredValuePart);
        Bo bo4 = DAOUserBo.create(userCase, integerAttr,
                randomValuePart + filteredValuePart + randomValuePart);
        Bo bo5 = DAOUserBo.create(userCase, integerAttr, randomValuePart);
        DSLBo.add(bo1, bo2, bo3, bo4, bo5);

        MobileList list = DAOMobile.createMobileList(userClass);
        DSLMobile.add(list);
        DSLMobile.addAttributes(list, integerAttr);

        //Действия:
        ValidatableResponse listResponse = DSLMobileLists.getListWithFiltration(list, filteredValuePart, auth);

        //Проверки:
        DSLMobileLists.assertTotalObjectsCount(listResponse, 2);
    }
}
