package ru.naumen.selenium.cases.admin.process;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.DSLCustomForm;
import ru.naumen.selenium.casesutil.content.GUICommentList;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.GUIFileList;
import ru.naumen.selenium.casesutil.content.advlist.MassOperation;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.file.GUIFileOperator;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOCustomForm;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.script.DAOModuleConf;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ModuleConf;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityRole;
import ru.naumen.selenium.casesutil.model.secgroup.SysRole;
import ru.naumen.selenium.casesutil.rights.matrix.AbstractBoRights;
import ru.naumen.selenium.casesutil.rights.matrix.EmployeeActionsRights;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.DSLLog;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityProfile;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityRole;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.security.rights.IRight;

/**
 * Тесты на действия с ролями
 * <AUTHOR>
 * @since 22.05.2025
 */
class SecurityRole4Test extends AbstractTestCaseJ5
{
    private static Bo employee;
    private static Attribute stringAttr;
    private static MetaClass employeeClass;
    private static GroupAttr systemGroupAttr;
    private static SecurityProfile userProfile;
    private static SecurityProfile employeeProfile;
    private static final String SCRIPT_CODE = """
            String getFormCode()
            {
                    String a = "";
                    if (api.actionContext.isCard())
                    {
                        a += "карточка объекта ";
                    }
                    if (api.actionContext.isStandaloneEditForm())
                    {
                        a += "форма редактирования ";
                    }
                    if (api.actionContext.isContentEditForm())
                    {
                        a += "форма редактирования (модальная) ";
                    }
                    if (api.actionContext.isQuickEditForm())
                    {
                        a += "быстрая форма редактирования ";
                    }
                    if (api.actionContext.isListInlineEditForm())
                    {
                        a += "инлайн редактирование ";
                    }
                    if (api.actionContext.isChangeCaseForm())
                    {
                        a += "форма смены типа ";
                    }
                    if (api.actionContext.isCommentAddForm())
                    {
                        a += "форма добавления комментария ";
                    }
                    if (api.actionContext.isCommentEditForm())
                    {
                        a += "форма редактирования комментария ";
                    }
                    if (api.actionContext.isFileAddForm())
                    {
                        a += "форма добавления файла ";
                    }
                    if (api.actionContext.isFileEditForm())
                    {
                        a += "форма редактирования файла ";
                    }
                    if (api.actionContext.isChangeParentForm())
                    {
                        a += "форма смены родителя ";
                    }
                    if (api.actionContext.isUserEventActionForm())
                    {
                        a += "форма ПДПС ";
                    }
                    return a;
            }
            """;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>В класс Сотрудник добавить атрибут stringAttr типа Строка, добавить его в группу атрибутов Системные
     * атрибуты</li>
     * <li>Добавить модуль testModule с текстом:<br>
     * String getFormCode()
     * {
     *         String a = "";
     *         if (api.actionContext.isCard())
     *         {
     *             a += "карточка объекта ";
     *         }
     *         if (api.actionContext.isStandaloneEditForm())
     *         {
     *             a += "форма редактирования ";
     *         }
     *         if (api.actionContext.isContentEditForm())
     *         {
     *             a += "форма редактирования (модальная) ";
     *         }
     *         if (api.actionContext.isQuickEditForm())
     *         {
     *             a += "быстрая форма редактирования ";
     *         }
     *         if (api.actionContext.isListInlineEditForm())
     *         {
     *             a += "инлайн редактирование ";
     *         }
     *         if (api.actionContext.isChangeCaseForm())
     *         {
     *             a += "форма смены типа ";
     *         }
     *         if (api.actionContext.isCommentAddForm())
     *         {
     *             a += "форма добавления комментария ";
     *         }
     *         if (api.actionContext.isCommentEditForm())
     *         {
     *             a += "форма редактирования комментария ";
     *         }
     *         if (api.actionContext.isFileAddForm())
     *         {
     *             a += "форма добавления файла ";
     *         }
     *         if (api.actionContext.isFileEditForm())
     *         {
     *             a += "форма редактирования файла ";
     *         }
     *         if (api.actionContext.isChangeParentForm())
     *         {
     *             a += "форма смены родителя ";
     *         }
     *         if (api.actionContext.isUserEventActionForm())
     *         {
     *             a += "форма ПДПС ";
     *         }
     *         return a;
     * }</li>
     * <li>Создать пользовательскую роль userRole для класса Сотрудник со скриптом определения права доступа
     * пользователя к объекту:<br>
     * logger.error("formCode = " + modules.%s.getFormCode());
     * return true</li>
     * <li>В классе Сотрудник в правах доступа добавить два профиля (оба для лицензированных пользователей):<br>
     * - Профиль userProfile с ролью userRole. Выдать этому профилю права только на перемещение объекта.<br>
     * - Профиль Сотрудник с ролью Сотрудник. Выдать этому профилю права на всё, кроме перемещения объекта.</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        // Подготовка
        employeeClass = DAOEmployeeCase.createClass();
        stringAttr = DAOAttribute.createString(employeeClass);
        DSLAttribute.add(stringAttr);
        systemGroupAttr = DAOGroupAttr.createSystem(employeeClass);
        DSLGroupAttr.addToGroup(systemGroupAttr, stringAttr);

        ModuleConf module = DAOModuleConf.createWithBody(SCRIPT_CODE);
        DSLModuleConf.add(module);

        ScriptInfo accessScriptInfo = DAOScriptInfo.createNewScriptInfo(
                """
                        logger.error("formCode = " + modules.%s.getFormCode());
                        return true""",
                module.getCode());
        DSLScriptInfo.addScript(accessScriptInfo);

        SecurityRole userRole = DAOSecurityRole.create(employeeClass, accessScriptInfo, null);
        DSLSecurityRole.add(userRole);

        employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        SecurityGroup userGroup = DAOSecurityGroup.create();
        DSLSecurityGroup.add(userGroup);
        DSLSecurityGroup.addUsers(userGroup, employee);

        userProfile = DAOSecurityProfile.create(true, userGroup, userRole);
        employeeProfile = DAOSecurityProfile.create(true, userGroup, SysRole.employee());
        DSLSecurityProfile.add(userProfile, employeeProfile);
        DSLSecurityProfile.setRights(employeeClass, userProfile, AbstractBoRights.MOVE);
        DSLSecurityProfile.setRights(employeeClass, employeeProfile, allEmployeeRightsWithoutMoveObject());
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для карточки объекта, формы
     * редактирования, формы перемещения.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = карточка объекта"</li>
     * <li>Открыть форму редактирования по кнопке Редактировать</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма редактирования"</li>
     * <li>Нажать Отмена</li>
     * <li>Открыть форму перемещения объекта по кнопке Переместить</li>
     * <li>Выбрать отдел в котором находится сотрудник, нажать Переместить</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма смены родителя"</li>
     * </ol>
     */
    @Test
    void testContextDefinitionForObjectCardAndEditFormAndMovingFormWithRelativeRole()
    {
        // Действия и проверки
        GUILogon.login(employee);
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = карточка объекта");

        GUIButtonBar.edit();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма редактирования");
        GUIForm.clickCancel();

        GUIButtonBar.move();
        BoTree tree = new BoTree(GUIXpath.Id.DESTINATION_PROPERTY_VALUE, false);
        tree.setElementInSelectTree(SharedFixture.root(), SharedFixture.ou());
        GUIForm.applyForm();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма смены родителя");
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для формы быстрого редактирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Сотрудник добавить форму быстрого добавления и редактирования caseFastForm (Группа атрибутов =
     * Системные атрибуты, Для типов = класс Сотрудник)</li>
     * <li>На карточке класса Сотрудник в панели действий контента "Системные атрибуты" изменить настройки кнопки
     * Редактировать: Использовать форму быстрого редактирования = true, Форма быстрого редактирования =
     * caseFastForm</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>Нажать на Редактировать в контенте Системные атрибуты</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма редактирования (модальная)"
     * </li>
     * </ol>
     */
    @Test
    void testContextDefinitionForFastEditFormWithRelativeRole()
    {
        // Подготовка
        CustomForm caseFastForm = DAOCustomForm.createQuickActionForm(systemGroupAttr, SharedFixture.employeeCase());
        DSLCustomForm.add(caseFastForm);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeClass, MetaclassCardTab.OBJECTCARD);

        ContentForm content = DSLContent.getContentByTitle(employeeClass, "Системные атрибуты",
                MetaclassCardTab.OBJECTCARD);

        GUIContent.clickEditToolPanel(content);
        content.advlist().editableToolPanel()
                .setUseSystemSettings(false)
                .rightClickTool(GUIButtonBar.BTN_EDIT)
                .clickAddContextMenuOption()
                .rightClickTool(GUIButtonBar.BTN_EDIT)
                .clickEditContextMenuOption()
                .setUseQuickEditForm(true)
                .selectQuickEditForm(caseFastForm)
                .clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();
        Cleaner.afterTest(true, () ->
                DSLContent.resetContentSettings(employeeClass, MetaclassCardTab.OBJECTCARD));

        // Действия и проверки
        GUILogon.login(employee);
        GUIContent.clickEdit(content);
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма редактирования (модальная)");
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для формы добавления и
     * редактирования комментария.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Сотрудник на карточку объекта добавить контент Комментарии (comments) типа Список комментариев,
     * параметры заполнены по умолчанию.</li>
     * <li>В классе Сотрудник поменять права у профилей:<br>
     * - Профилю userProfile выдать права только на добавление и редактирование комментариев<br>
     * - Профилю Сотрудник выдать права на всё, кроме добавления и редактирования комментариев.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>Открыть форму добавления комментария из контента Комментарии</li>
     * <li>Заполнить поле Текст произвольной строкой, нажать Сохранить</li>
     * <li>Обновить страницу.</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма добавления комментария"</li>
     * <li>Открыть форму редактирования добавленного комментария на ш. 3.</li>
     * <li>В поле Текст добавить ещё одну произвольную строку, нажать Сохранить</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма редактирования
     * комментария"</li>
     * </ol>
     */
    @Test
    void testContextDefinitionForAddAndEditCommentFormWithRelativeRole()
    {
        // Подготовка
        ContentForm commentList = DAOContentCard.createCommentList(employeeClass);
        DSLContent.add(commentList);

        changeUserProfileRights(AbstractBoRights.ADD_COMMENT, AbstractBoRights.EDIT_COMMENT);
        changeEmployeeProfileRights(
                allEmployeeRightsWithout(AbstractBoRights.ADD_COMMENT, AbstractBoRights.EDIT_COMMENT));

        // Действия и проверки
        GUILogon.login(employee);
        GUICommentList.clickAddLink(commentList);
        GUIForm.assertFormAppear(Div.FORM_CONTAINS);
        GUIRichText.sendKeys(GUIRichText.TEXT, ModelUtils.createText(10));
        GUIForm.applyForm();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма добавления комментария");

        String commentUuid = GUIComment.getCommentUUIDs(commentList).iterator().next();
        GUIComment.clickEdit(commentUuid);
        GUIForm.assertFormAppear(Div.FORM_CONTAINS);
        GUIRichText.sendKeys(GUIRichText.TEXT, ModelUtils.createText(10));
        GUIForm.applyForm();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма редактирования комментария");
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для формы добавления файла.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Сотрудник на карточку объекта добавить контент Файлы (files) типа Список файлов, Представление =
     * Список.</li>
     * <li>В классе Сотрудник поменять права у профилей:<br>
     * - Профилю userProfile выдать права только на добавление файлов<br>
     * - Профилю Сотрудник выдать права на всё, кроме добавления файлов.</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>Открыть форму добавления файла из контента Файлы</li>
     * <li>Загрузить произвольный файл, нажать Сохранить</li>
     * <li>Обновить страницу.</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма добавления файла"</li>
     * <li>Открыть форму редактирования добавленного файла на ш. 3.</li>
     * <li>В поле Описание написать произвольный текст, нажать Сохранить</li>
     * <li>Обновить страницу</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма редактирования файла"</li>
     * </ol>
     */
    @Test
    void testContextDefinitionForAddFileFormWithRelativeRole()
    {
        // Подготовка
        ContentForm fileList = DAOContentCard.createFileAdvList(employeeClass.getFqn(), systemGroupAttr);
        DSLContent.add(fileList);

        changeUserProfileRights(AbstractBoRights.ADD_FILE);
        changeEmployeeProfileRights(allEmployeeRightsWithout(AbstractBoRights.ADD_FILE));

        // Действия и проверки
        GUILogon.login(employee);
        GUIFileList.clickAddFileTool(fileList);
        GUIFileOperator.uploadFile(Div.FORM_CONTAINS + Div.EDITABLE_PROPERTY_LIST_CONTAINS,
                DSLFile.BIG_IMG_FOR_UPLOAD);
        GUIForm.applyForm();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма добавления файла");

        fileList.advlist().mass().selectAll();
        fileList.advlist().mass().clickOperation(MassOperation.EDIT);
        tester.sendKeys(Any.ANY_VALUE, ModelUtils.createText(10), SystemAttrEnum.REQUEST_DESCRIPTION.getCode());
        GUIForm.applyForm();
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма редактирования файла");
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для формы быстрого редактирования,
     * инлайн редактирования.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В класс Сотрудник на карточку объекта добавить контент Сотрудники (employees) типа Список объектов, Группа
     * атрибутов = Системные атрибуты, Представление = Сложный список, Класс объектов = Сотрудник</li>
     * <li>В классе Сотрудник поменять права у профилей:<br>
     * - Профилю userProfile выдать права только на редактирование атрибутов<br>
     * - Профилю Сотрудник выдать права на всё, кроме редактирования атрибутов</li>
     * <li>В классе Сотрудник атрибуту stringAttr добавить свойство Редактируемый в списках = true</li>
     * <li>В класс Сотрудник добавить<br>
     * - форму быстрого добавления и редактирования (fastForm), Группа атрибутов = Системные атрибуты</li>
     * <li>Отредактировать панель массовых операций созданного на ш. 1 контента:<br>
     * - кнопке Редактировать" добавить свойство использовать форму быстрого редактирования =  true, Форма быстрого
     * редактирования = fastForm</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>В списке Сотрудники выбрать чекбокс текущего сотрудника</li>
     * <li>Нажать редактировать в появившейся панели массовых операций</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = быстрая форма редактирования"</li>
     * <li>Нажать Отмена</li>
     * <li>Навести курсор на значение атрибута stringAttr текущего сотрудника в списке Сотрудники, нажать на
     * появившуюся кнопку Редактировать</li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = инлайн редактирование"</li>
     * </ol>
     */
    @Test
    void testContextDefinitionForFastEditFormAndInlineEditingWithRelativeRole()
    {
        // Подготовка
        ContentForm objectAdvList = DAOContentCard.createObjectAdvList(employeeClass, systemGroupAttr, employeeClass);
        DSLContent.add(objectAdvList);

        changeUserProfileRights(AbstractBoRights.EDIT_REST_ATTRIBUTES);
        changeEmployeeProfileRights(allEmployeeRightsWithout(AbstractBoRights.EDIT_REST_ATTRIBUTES));

        stringAttr.setEditableInLists(true);
        DSLAttribute.edit(stringAttr);
        Cleaner.afterTest(true, () ->
        {
            stringAttr.setEditableInLists(false);
            DSLAttribute.edit(stringAttr);
        });

        CustomForm caseFastForm = DAOCustomForm.createQuickActionForm(systemGroupAttr, employeeClass);
        DSLCustomForm.add(caseFastForm);

        GUILogon.asSuper();
        GUIMetaClass.goToTab(employeeClass, MetaclassCardTab.OBJECTCARD);

        GUIContent.clickEditToolPanel(objectAdvList);
        objectAdvList.advlist().editableMassToolPanel()
                .setUseSystemSettings(false)
                .rightClickTool(GUIButtonBar.BTN_EDIT)
                .clickEditContextMenuOption()
                .setUseQuickEditForm(true)
                .selectQuickEditForm(caseFastForm)
                .clickApplyOnDialogByTitle("Редактирование элемента");
        GUIForm.applyModalForm();

        // Действия и проверки
        GUILogon.login(employee);
        objectAdvList.advlist().mass().selectElements(employee);

        objectAdvList.advlist().mass().clickOperation(MassOperation.EDIT);
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = быстрая форма редактирования");
        GUIForm.clickCancel();

        objectAdvList.advlist().editCell().activate(employee, stringAttr);
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = инлайн редактирование");
    }

    /**
     * Тестирование определения контекста в скрипте вычислимой относительной роли для формы пользовательского
     * действия по событию.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts/
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$211561049
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>В классе Сотрудник поменять права у профилей:<br>
     * - Профилю userProfile выдать права только на пользовательские события<br>
     * - Профилю Сотрудник выдать права на всё, кроме пользовательских событий</li>
     * <li>Добавить и включить действие по событию testDPS: Объекты = Сотрудник, Событие  = Пользовательское событие,
     * Действие = Скрипт, Скрипт = //</li>
     * <li>На карточке объекта класса Сотрудник на панель действий добавить кнопку testDPS с действием testDPS</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Войти под лицензированным пользователем</li>
     * <li>Нажать testDPS</li>
     * <li>Обновить страницу </li>
     * <li>Проверить, что в логах появилась запись "ERROR script.Script - formCode = форма ПДПС"</li>
     * </ol>
     */
    @Test
    void testContextDefinitionForUserEventActionFormWithRelativeRole()
    {
        // Подготовка
        changeUserProfileRights(AbstractBoRights.USER_EVENTS);
        changeEmployeeProfileRights(allEmployeeRightsWithout(AbstractBoRights.USER_EVENTS));

        ScriptInfo script = DSLScriptInfo.createScriptInfo("//");
        EventAction eventAction = DAOEventAction.createEventScript(EventType.userEvent, script, true, employeeClass);
        DSLEventAction.add(eventAction);

        String buttonTitle = "testDPS";
        ContentForm ouCard = DSLContent.getWindowContent(employeeClass);
        DSLContent.addUserTool(ouCard, buttonTitle, 1, 1, eventAction);
        Cleaner.afterTest(true,
                () -> DSLContent.resetContentSettings(employeeClass, MetaclassCardTab.OBJECTCARD));

        // Действия и проверки
        GUILogon.login(employee);
        GUIButtonBar.clickButtonByCode(GUIButtonBar.BTN_FIRE_USER_EVENT, buttonTitle);
        DSLLog.assertPresentMessageInLog("ERROR script.Script - formCode = форма ПДПС");

    }

    /**
     * Получить список всех прав для класса "Сотрудник"
     */
    private static List<IRight> allEmployeeRights()
    {
        List<IRight> rightsList = new ArrayList<>();
        rightsList.addAll(Arrays.asList(AbstractBoRights.allDefaultRight()));
        rightsList.addAll(
                Arrays.asList(EmployeeActionsRights.BLOCKING_CTRL, EmployeeActionsRights.CHANGE_INTEGRATION_CTRL,
                        EmployeeActionsRights.CHANGE_PASSWD));

        return rightsList;
    }

    /**
     * Получить список всех прав для класса "Сотрудник", кроме переданных
     * @param abstractBoRights набор прав, которые нужно исключить
     */
    private static IRight[] allEmployeeRightsWithout(AbstractBoRights... abstractBoRights)
    {
        List<IRight> rightsList = allEmployeeRights();
        for (AbstractBoRights abstractBoRight : abstractBoRights)
        {
            rightsList.removeIf(right -> right.equals(abstractBoRight));
        }

        return rightsList.toArray(new IRight[0]);
    }

    /**
     * Получить список всех прав для класса "Сотрудник", кроме "Перемещение объекта"
     */
    private static IRight[] allEmployeeRightsWithoutMoveObject()
    {
        return allEmployeeRightsWithout(AbstractBoRights.MOVE);
    }

    /**
     * Изменить набор прав в профиле {@link #userProfile} для класса {@link #employeeClass} на переданный
     * @param rights набор прав
     */
    private void changeUserProfileRights(IRight... rights)
    {
        DSLSecurityProfile.removeRights(employeeClass, userProfile, AbstractBoRights.MOVE);
        DSLSecurityProfile.setRights(employeeClass, userProfile, rights);

        Cleaner.afterTest(true, () ->
        {
            DSLSecurityProfile.removeRights(employeeClass, userProfile, rights);
            DSLSecurityProfile.setRights(employeeClass, userProfile, AbstractBoRights.MOVE);
        });
    }

    /**
     * Изменить набор прав в {@link #employeeProfile} для класса {@link #employeeClass} на переданный
     * @param rights набор прав
     */
    private void changeEmployeeProfileRights(IRight... rights)
    {
        DSLSecurityProfile.removeRights(employeeClass, employeeProfile, allEmployeeRightsWithoutMoveObject());
        DSLSecurityProfile.setRights(employeeClass, employeeProfile, rights);

        Cleaner.afterTest(true, () ->
        {
            DSLSecurityProfile.removeRights(employeeClass, employeeProfile, rights);
            DSLSecurityProfile.setRights(employeeClass, employeeProfile, allEmployeeRightsWithoutMoveObject());
        });
    }
}
