package ru.naumen.selenium.cases.script.mobilerest.possiblevalues;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BackLinkType.WITH_FOLDER;

import java.util.Collections;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на получение возможных значений атрибутов типа "Обратная ссылка" на формах.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class MobileRestBackLinksPossibleValuesTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static Folder userFolder;
    private static MobileAuthentication licAuth;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать папку userFolder в классе userClass</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userFolder = DAOFolder.create(userClass);
        DSLFolder.add(userFolder);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование сброса кэша возможных значений при повторном открытии формы для атрибута типа "Обратная ссылка"
     * с представлением "Список выбора с папками", когда у атрибута есть скрипт фильтрации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объекты userBo и userBo2 типа userCase и добавить объект userBo в папку userFolder</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, со ссылкой на объекты типа
     * userClass</li>
     * <li>Создать атрибут backLinksAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, с
     * представлением "Список выбора с папками" и скриптом фильтрации:
     * <pre>
     * -------------------------------------------------------------------------------
     *     if (!subject) return []
     *     return ['$userBo', '$userBo2']
     * -------------------------------------------------------------------------------
     *     Где:
     *     1) $userBo - объект userBo;
     *     2) $userBo - объект userBo2.
     * </pre></li>
     * <li>Создать в МК форму addForm, на которую вывести атрибуты backLinksAttr</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для backLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответе вернулись папка userFolder и объект userBo2</li>
     * <li>Удалить объект userBo из папки userFolder</li>
     * <li>Получить форму добавления объекта addForm</li>
     * <li>Получить возможные значения для backLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответе вернулись объекты userBo, userBo2</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFoldersAndFiltrationScript()
    {
        Attribute folderAttr = SysAttribute.folders(userClass);
        folderAttr.setItemValue(userFolder);

        Bo userBo = DAOUserBo.create(userCase, folderAttr);
        Bo userBo2 = DAOUserBo.create(userCase);
        DAOBo.appendTitlePrefixes(userBo);
        DSLBo.add(userBo, userBo2);
        DSLSearch.updateIndex(userBo, userBo2);

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(
                "(!subject) ? [] : ['%s', '%s']", userBo.getUuid(), userBo2.getUuid());
        DSLScriptInfo.addScript(scriptInfo);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userClass);
        Attribute backLinksAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr);
        backLinksAttr.setEditPresentation(WITH_FOLDER);
        DAOAttribute.changeToEditFilter(backLinksAttr, scriptInfo);
        DSLAttribute.add(boLinksAttr, backLinksAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, backLinksAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksAttr, V15)
                .assertValues(
                        folderElement(userFolder),
                        treeElement(userBo2)
                );

        folderAttr.setValue(null);
        DSLBo.editAttributeValue(userBo, folderAttr);

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, Collections.emptyMap(), licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksAttr, V15)
                .assertValues(
                        treeElement(userBo),
                        treeElement(userBo2)
                );
    }

    /**
     * Тестирование отсутствия возможных значений для атрибута типа "Обратная ссылка", когда
     * ограничения по доступным типам установлены в "Ни один".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00099
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00715
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308333508
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, со ссылкой на объекты типа
     * userClass</li>
     * <li>Создать атрибут backLinksAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, и имеющий
     * ограничение по доступным типам - "Ни один"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут backLinksAttr</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для backLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответ вернулся пустой список</li>
     * <li>Выполнить поиск по возможным значениям для backLinksAttr, передав тип userCase, форму добавления addForm,
     * и поисковую строку c названием объекта userBo</li>
     * <li>Проверить, что в ответ вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testBackLinksWhenPermittedTypesIsNoOne()
    {
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLSearch.updateIndex(userBo);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userClass);
        Attribute backLinksAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr);
        DSLAttribute.add(boLinksAttr, backLinksAttr);
        DSLAttribute.editPermittedLinks(backLinksAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, backLinksAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksAttr).isEmpty();

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksAttr).isEmpty();
    }

    /**
     * Тестирование отсутствия возможных значений для атрибута типа "Обратная ссылка" с представлением "Список
     * выбора с папками", когда ограничения по доступным типам установлены в "Ни один".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00099
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00715
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308333508
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, со ссылкой на объекты типа
     * userClass</li>
     * <li>Создать атрибут backLinksFoldersAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, с
     * представлением "Список выбора с папками", и имеющий ограничение по доступным типам - "Ни один"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут backLinksFoldersAttr</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для backLinksFoldersAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответ вернулось пустое дерево</li>
     * <li>Выполнить поиск по возможным значениям для backLinksFoldersAttr, передав тип userCase, форму добавления
     * addForm, и поисковую строку c названием объекта userBo</li>
     * <li>Проверить, что в ответ вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFoldersWhenPermittedTypesIsNoOne()
    {
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLSearch.updateIndex(userBo);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userClass);
        Attribute backLinksFoldersAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr);
        backLinksFoldersAttr.setEditPresentation(BOLinksType.WITH_FOLDER);
        DSLAttribute.add(boLinksAttr, backLinksFoldersAttr);
        DSLAttribute.editPermittedLinks(backLinksFoldersAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, backLinksFoldersAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksFoldersAttr, V13_2).assertValues(tree());

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksFoldersAttr, V13_2).assertValues(treeSearch());
    }
}
