package ru.naumen.selenium.cases.script.mobilerest.possiblevalues;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.FormType.PARAMETRIZED_USER_ACTION;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.aggregatedTreeElement;

import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.bo.DSLTeam;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute.AggregatedClasses;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOTeam;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOOuCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.TxType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на получение возможных значений атрибутов типа Агрегирующий на формах.
 *
 * <AUTHOR>
 * @since 15.11.2024
 */
public class MobileRestAggregatePossibleValuesTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Удаляем все объекты классов Отдел и Сотрудник (тестируемая особенность не проявляется в случае наличия у
     * атрибута скрипта фильтрации)</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        // для обеспечения независимости теста от остальных тестов,
        // тестируемая логика не срабатывает при наличии скрипта фильтрации, который мог бы быть альтернативным решением
        DSLBo.deleteAllObjectsWithoutShared(DAOOuCase.createClass());
        DSLBo.deleteAllObjectsWithoutShared(DAOTeamCase.createClass());
        SharedFixture.ou();
        SharedFixture.clientOu();
        SharedFixture.team();

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);
    }

    /**
     * Тестирование недоступности для выбора и отделов, и сотрудников в полученных возможных значениях атрибута типа
     * Агрегирующий, когда у составной части Отдел не доступен для выбора ни один из типов.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270532611
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать в типе userCase атрибут типа Агрегирующий aggregateAttr с представлением "Поле выбора"</li>
     * <li>Изменить для составной части Отдел атрибута aggregateAttr допустимые типы объектов на "Ни один"</li>
     * <li>Создать отделы ou, ou2</li>
     * <li>Создать в отделе ou сотрудников employee, employee2</li>
     * <li>Создать в отделе ou2 сотрудников employee3</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут aggregateAttr</li>
     * <li>Создать ключ доступа для сотрудника employee</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из: не доступных для
     * выбора отделов ou, ou2 (и других элементов)</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = ou,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>aggregateAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из не доступных для
     * выбора сотрудников employee, employee2</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(cause = "Для Oracle DB отличается сортировка элементов из-за настроек collation",
            ignoreDb = { DbType.ORACLE })
    public void testAggregateWhenOusAreNotAvailableForSelection()
    {
        // Подготовка:
        Attribute aggregateAttr = DAOAttribute.createAggregate(userCase, AggregatedClasses.OU_AND_TEAM, null, null);
        DSLAttribute.add(aggregateAttr);

        Attribute aggregateOuAttr = aggregateAttr.cloneModel();
        aggregateOuAttr.setCode(aggregateOuAttr.getCode() + "_ou");
        DSLAttribute.editPermittedLinks(aggregateOuAttr);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        Bo ou2 = DAOOu.create(SharedFixture.ouCase());
        DAOBo.appendTitlePrefixes(ou, ou2);
        DSLBo.add(ou, ou2);
        DSLSearch.updateIndex(ou, ou2);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DAOBo.appendTitlePrefixes(team);
        DSLBo.add(team);
        DSLSearch.updateIndex(team);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        Bo employee3 = DAOEmployee.create(SharedFixture.employeeCase(), ou2, true);
        DAOBo.appendTitlePrefixes(employee, employee2, employee3);
        DSLBo.add(employee, employee2, employee3);
        DSLSearch.updateIndex(employee, employee2, employee3);
        DSLTeam.addEmployees(team, employee);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, aggregateAttr);

        MobileAuthentication auth = DSLMobileAuth.authAs(employee);

        // Действия и проверки:
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(aggregateAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm);
        // получаем дерево для старой версии: должен вернуться пустой элемент
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, auth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);

        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateAttr, V15)
                .assertValues(
                        aggregatedTreeElement(ou).leaf(false).selectable(false),
                        aggregatedTreeElement(ou2).leaf(false).selectable(false),
                        aggregatedTreeElement(SharedFixture.clientOu()).leaf(false).selectable(false),
                        aggregatedTreeElement(SharedFixture.ou()).leaf(false).selectable(false),
                        aggregatedTreeElement(team).leaf(false),
                        aggregatedTreeElement(SharedFixture.team()).leaf(false)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateAttr, objectTemplate)
                .setForm(addForm)
                .setParent(ou);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, auth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateAttr, V15)
                .assertValues(
                        aggregatedTreeElement(ou, employee).selectable(false),
                        aggregatedTreeElement(ou, employee2).selectable(false)
                );
    }

    /**
     * Тестирование доступности выбора отделов и сотрудников в полученных возможных значениях атрибута типа
     * Агрегирующий на форме заполнения параметров пользовательского ДПС.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$270532611
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать действие по событию <b>userEvent</b> типа "[Пользовательское событие]" с действием "Скрипт" в
     * подтипе <b>userCase</b>:
     * <pre>
     *     -------------------------------------------------------------------------------
     *      logger.info('success')
     *     -------------------------------------------------------------------------------
     * </pre></li>
     * <li>Создать в ДПС userEvent параметр типа Агрегирующий aggregateParam с представлением "Поле выбора"</li>
     * <li>Создать объект <b>userBo</b> типа <b>userCase</b></li>
     * <li>Создать отделы ou, ou2</li>
     * <li>Создать в отделе ou сотрудников employee, employee2</li>
     * <li>Создать в отделе ou2 сотрудников employee3</li>
     * <li>Создать ключ доступа для сотрудника employee</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateParam,</li>
     *     <li>"Форма" = userEvent,</li>
     *     <li>"Объект" = userBo</li>
     *     <li>"Параметры": <ul>
     *         <li>aggregateParam = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на родительском уровне состоящее из доступных для
     * выбора отделов ou, ou2 (и других элементов)</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = aggregateParam,</li>
     *     <li>"Форма" = userEvent,</li>
     *     <li>"Родитель" = ou,</li>
     *     <li>"Объект" = userBo</li>
     *     <li>"Параметры": <ul>
     *         <li>aggregateParam = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось дерево объектов, на дочернем уровне состоящее из доступных для выбора
     * сотрудников employee, employee2</li>
     * </ol>
     */
    @Test
    @IgnoreConfig(cause = "Для Oracle DB отличается сортировка элементов из-за настроек collation",
            ignoreDb = { DbType.ORACLE })
    public void testAggregateOnUserActionParamsForm()
    {
        // Подготовка:
        ScriptInfo script = DSLScriptInfo.createScriptInfo("logger.info('success')");
        EventAction userEvent = DAOEventAction.createEventScript(EventType.userEvent, script.getCode(), true,
                TxType.Sync, userCase);
        DSLEventAction.add(userEvent);

        FormParameter aggregateParam = DAOFormParameter.createAggregate(AggregatedClasses.OU_AND_TEAM, null, null);
        DSLFormParameter.saveOnEventAction(userEvent, aggregateParam);

        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        Bo team = DAOTeam.create(SharedFixture.teamCase());
        DAOBo.appendTitlePrefixes(team);
        DSLBo.add(team);
        DSLSearch.updateIndex(team);

        Bo ou = DAOOu.create(SharedFixture.ouCase());
        Bo ou2 = DAOOu.create(SharedFixture.ouCase());
        DAOBo.appendTitlePrefixes(ou, ou2);
        DSLBo.add(ou, ou2);
        DSLSearch.updateIndex(ou, ou2);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        Bo employee2 = DAOEmployee.create(SharedFixture.employeeCase(), ou, true);
        Bo employee3 = DAOEmployee.create(SharedFixture.employeeCase(), ou2, true);
        DAOBo.appendTitlePrefixes(employee, employee2, employee3);
        DSLBo.add(employee, employee2, employee3);
        DSLSearch.updateIndex(employee, employee2, employee3);
        DSLTeam.addEmployees(team, employee);

        MobileAuthentication auth = DSLMobileAuth.authAs(employee);

        // Действия и проверки:
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setAttribute(aggregateParam, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(aggregateParam, objectTemplate)
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo);
        // получаем дерево для старой версии: должен вернуться пустой элемент
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, auth, V15);
        pvResponse.statusCode(HttpStatus.SC_OK);

        DSLMobilePossibleValues.assertForAttribute(pvResponse, aggregateParam, V15)
                .assertValues(
                        aggregatedTreeElement(ou).leaf(false),
                        aggregatedTreeElement(ou2).leaf(false),
                        aggregatedTreeElement(SharedFixture.clientOu()).leaf(false),
                        aggregatedTreeElement(SharedFixture.ou()).leaf(false),
                        aggregatedTreeElement(team).leaf(false),
                        aggregatedTreeElement(SharedFixture.team()).leaf(false)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(aggregateParam, objectTemplate)
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo)
                .setParent(ou);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, auth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, aggregateParam, V15)
                .assertValues(
                        aggregatedTreeElement(ou, employee),
                        aggregatedTreeElement(ou, employee2)
                );
    }
}
