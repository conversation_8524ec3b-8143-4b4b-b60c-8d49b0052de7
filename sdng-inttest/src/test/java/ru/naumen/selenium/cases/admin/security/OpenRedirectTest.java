package ru.naumen.selenium.cases.admin.security;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.anyUrl;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static java.net.HttpURLConnection.HTTP_OK;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import com.github.tomakehurst.wiremock.WireMockServer;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;
import ru.naumen.selenium.core.AbstractTestCase;

/**
 * Тестирование устранения уязвимости open redirect
 *
 * <AUTHOR>
 * @since 5.03.2025
 */
public class OpenRedirectTest extends AbstractTestCase
{
    /**
     * Фиктивный http-сервер, на который происходят попытки редиректа
     */
    private static WireMockServer dummyServer;

    /**
     * Идентификатор ресурса на {@link #dummyServer фиктивном сервере}
     */
    private static String dummyUrl;

    /**
     * <ol>
     * <b>Общая подготовка</b>
     * <li>Создать и запустить локальный тестовый http-сервер, возвращающий на любой запрос статус 200</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        dummyServer = new WireMockServer(wireMockConfig().dynamicPort());
        dummyServer.start();

        dummyServer.stubFor(get(anyUrl())
                .willReturn(aResponse()
                        .withStatus(HTTP_OK)
                        .withBody("Open redirect page")));

        dummyUrl = "http://localhost:%d".formatted(dummyServer.port());
    }

    /**
     * Выполняет действия после запуска всех тестов в классе:
     * <ol>
     * <li>Останавливает тестовый http-сервер</li>
     * </ol>
     */
    @AfterClass
    public static void cleanup()
    {
        dummyServer.stop();
    }

    /**
     * Тестирование устранения уязвимости open redirect при GET запросе /sd  с параметром face, содержащем
     * ссылку для редиректа <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00120 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308082972
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Общая подготовка</li>
     * <li>Зайти под суперпользователем</li>
     * <b>Действия:</b>
     * <li>Выполнить GET-запрос к /sd/ с параметром face, ведущем на фиктивный сервер</li>
     * <pre>
     *     Пример: http://localhost:8080/sd/?face=http%3a%2f%2fvk.com
     * </pre>
     * <b>Проверки:</b>
     * <li>Проверить, что редирект на фиктивный сервер не произошел</li>
     * </ol>
     */
    @Test
    public void testFaceValidating()
    {
        GUILogon.asSuper();

        String standUrl = StandTypeHolder.getCurrentType().getCurrentStandUrl();
        String encodedUrl = URLEncoder.encode(dummyUrl, StandardCharsets.UTF_8);

        String requestUrl = standUrl + "?face=" + encodedUrl;

        tester.getWebDriver().get(requestUrl);

        Assert.assertTrue("Обнаружена уязвимость open redirect", GUITester.getCurrentUrl().startsWith(standUrl));
    }
}