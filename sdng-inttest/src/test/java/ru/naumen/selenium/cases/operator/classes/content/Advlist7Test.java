package ru.naumen.selenium.cases.operator.classes.content;

import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEmployeeCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOMetaClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOScCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOTeamCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование адвлистов
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00225
 * <AUTHOR>
 * @since 18 дек. 2024
 */
public class Advlist7Test extends AbstractTestCase
{
    /**
     * Тестирование сортировки по атрибуту типа "Атрибут связанного объекта", который ссылается на класс, вложенный
     * сам в себя
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00100
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00770
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00110
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$290715027
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательский класс вложенный сам в себя userClass</li>
     * <li>Создать атрибут sbo типа "Ссылка на БО" в классе Запрос, сслыющийся на userClass</li>
     * <li>Создать атрибут relatedAttr в классе Запрос типа "Атрибут связанного объекта":
     *  <ol>
     *      <li>Атрибут связи - sbo</li>
     *      <li>Атрибут связанного объекта - Родитель</li>
     *      <li>Показывать значения атрибута - родителя верхнего типа</li>
     *  </ol>
     * </li>
     * <li>Создать атрибут nbo в классе Запрос типа "Набор ссылок на БО", ссылающийся на класс Команда</li>
     * <li>В Заросе создать группу атрибутов attrGroup и добавить в неё атрибут relatedAttr</li>
     * <li>Создать атрибут backBO в классе Команда типа "Обратная ссылка", прямая ссылка - nbo</li>
     * <li>В классе Сотрудник на вкладке "Карточка объекта" добавить контент "Список связанных объектов"
     * relatedObjectList:
     * <ol>
     *     <li>атрибут - backBO</li>
     *     <li>класс объектов - Запрос</li>
     *     <li>группа атрибутов - attrGroup</li>
     *     <li>представление - сложный список</li>
     * </ol>
     * </li>
     * <br>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИО под сотрудником</li>
     * <li>На карточке сотрудника в списке relatedObjectList нажать на название колонку атрибута relatedAttr</li>
     * <li>Проверить, что на странице нет ошибок</li>
     * </ol>
     */
    @Test
    public void testAdvlistSortedOnAttributeOfRelatedObject()
    {
        //Подготовка
        MetaClass scClass = DAOScCase.createClass();
        MetaClass teamClass = DAOTeamCase.createClass();
        MetaClass emplClass = DAOEmployeeCase.createClass();
        MetaClass userClass = DAOUserClass.createInSelf();
        DSLMetaClass.add(userClass);

        Attribute sbo = DAOAttribute.createObjectLink(scClass, userClass);

        Attribute relatedAttr = DAOAttribute.createAttributeOfRelatedObject(scClass, sbo,
                SysAttribute.parent(userClass));
        relatedAttr.setRelatedObjectHierarchyLevel("-1");

        Attribute nbo = DAOAttribute.createBoLinks(scClass, teamClass);
        DSLAttribute.add(sbo, nbo, relatedAttr);

        GroupAttr attrGroup = DAOGroupAttr.create(scClass);
        DSLGroupAttr.add(attrGroup, relatedAttr);

        Attribute backBO = DAOAttribute.createBackBOLinks(teamClass, nbo);
        DSLAttribute.add(backBO);

        ContentForm relatedObjectList = DAOContentCard.createRelatedObjectList(emplClass.getFqn(),
                "<EMAIL>@" + backBO.getCode(), attrGroup);
        relatedObjectList.setPresentation(PresentationContent.ADVLIST.get());
        DSLContent.add(relatedObjectList);

        //Выполнение действий и проверок
        GUILogon.asTester();
        relatedObjectList.advlist().content().clickColumnName(relatedAttr.getTitle());
        GUIError.assertErrorAbsence();
    }

    /**
     * Тестирование отображения объектов, вложенных самих в себя в списке связанных объектов, если в классе объекта есть
     * Атрибут связанного объекта, где атрибутом связи является Обратная ссылка на сам вложенный класс, а атрибутом
     * связанного объекта является обратная ссылка на класс, где выведен список связанных объектов <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00110 <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$306916404
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать класс userClass</li>
     * <li>Создать класс nestedClass</li>
     * <li>Создать тип userCase класса userClass</li>
     * <li>Создать тип nestedCase класса nestedClass</li>
     * <li>Добавить в класс userClass атрибут boLinksAttr типа "Набор ссылок на БО" для объектов типа nestedClass</li>
     * <li>Добавить в класс nestedClass атрибут nestedObjects типа "Набор ссылок на БО" для объектов типа
     * nestedClass</li>
     * <li>Добавить в класс nestedClass атрибут backLink1 типа "Обратная ссылка" для атрибута nestedObjects</li>
     * <li> nestedClass атрибут backLink2 типа "Обратная ссылка" для атрибута boLinksAttr</li>
     * <li>Добавить в класс nestedClass атрибут relatedObjectAttr1 типа "Атрибут связанного объекта" с атрибутом
     * связи backLink1, с атрибутом объекта backLink2</li>
     * <li>Добавить в класс nestedClass атрибут relatedObjectAttr2 типа "Атрибут связанного объекта" с атрибутом
     * связи backLink1, с атрибутом объекта backLink2 (необходимо для гарантированного воспроизведения дефекта)</li>
     * <li>На карточку класса userClass вывести контент relatedObjList типа "Список связанных объектов" по атрибуту
     * boLinksAttr</li>
     * <li>Создать объект userCaseBo типа userCase</li>
     * <li>Создать объект nestedCaseBo типа nestedCase</li>
     * <li>Связать userCaseBo и nestedCaseBo через атрибут backLink2</li>
     * <b>Действия</b>
     * <li>Зайти под суперпользователем</li>
     * <li>Перейти на карточку объекта userCaseBo</li>
     * <b>Проверки</b>
     * <li>Проверить, что в списке relatedObjList присутствует объект nestedCaseBo</li>
     * </ol>
     */
    @Test
    public void testAdvListContainsLinkedObjects()
    {
        // Подготовка
        MetaClass userClass = SharedFixture.userClass();
        MetaClass nestedClass = DAOUserClass.create();
        DSLMetaClass.add(nestedClass);
        MetaClass userCase = DAOMetaClass.createCase(userClass);
        MetaClass nestedCase = DAOMetaClass.createCase(nestedClass);
        DSLMetaClass.add(userCase, nestedCase);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userClass, nestedClass);
        Attribute nestedObjects = DAOAttribute.createBoLinks(nestedClass, nestedClass);
        Attribute backLink1 = DAOAttribute.createBackBOLinks(nestedClass, nestedObjects);
        Attribute backlink2 = DAOAttribute.createBackBOLinks(nestedClass, boLinksAttr);
        Attribute relatedObjectAttr1 = DAOAttribute.createAttributeOfRelatedObject(nestedClass, backLink1, backlink2);
        Attribute relatedObjectAttr2 = DAOAttribute.createAttributeOfRelatedObject(nestedClass, backLink1, backlink2);
        DSLAttribute.add(boLinksAttr, nestedObjects, backLink1, backlink2, relatedObjectAttr1, relatedObjectAttr2);

        String chain = userClass.getFqn() + "@" + boLinksAttr.getCode();
        ContentForm relatedObjList = DAOContentCard.createRelatedObjectList(userClass.getFqn(), chain, nestedCase);
        DSLContent.add(relatedObjList);

        Bo userCaseBo = DAOUserBo.create(userCase);
        Bo nestedCaseBo = DAOUserBo.create(nestedCase);
        DSLBo.add(userCaseBo, nestedCaseBo);

        backlink2.setValue(Json.listToString(userCaseBo.getUuid()));
        DSLBo.editAttributeValue(nestedCaseBo, backlink2);
        // Действия
        GUILogon.asSuper();
        GUIBo.goToCard(userCaseBo);
        // Проверки
        relatedObjList.advlist().content().asserts().rowsPresence(nestedCaseBo);
    }
}