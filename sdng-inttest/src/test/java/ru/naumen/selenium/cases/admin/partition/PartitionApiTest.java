package ru.naumen.selenium.cases.admin.partition;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLAdminLog;
import ru.naumen.selenium.casesutil.admin.LogEntry;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.metaclass.SystemClass;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тестирование апи методов миграции в секционированную таблицу.
 *
 * <AUTHOR>
 * @since 30.08.2024
 */
@IgnoreConfig(cause = "NSDPRD-31296", ignoreDb = { DbType.ORACLE, DbType.MSSQL })
public class PartitionApiTest extends AbstractTestCase
{
    private static final String PARTITION_CATEGORIES = "partitioning";
    private static final String ASSERT_ERROR_MSG = "Описание записи в логе технолога не совпало с ожидаемым";
    private static final String SCRIPT_PATTERN = "api.partition.%s(%s)";
    private static final String SERVICE_CALL = StringUtils.wrap(SystemClass.SERVICECALL.getCode(), '\'');

    /**
     * Тестирование того, что пользователь без прав vendor/admin не может выполнить методы миграции.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01003
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01004
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$253096531
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать нового пользователя employee.</li>
     * <li>Сгенерировать для пользователя employee accessKey.</li>
     * <b>Выполнение действий и проверок:</b>
     * <li>Выполнить api.partition.getMigrationStatus с сгенерированным accessKey.</li>
     * <li>Проверить, что при выполнении апи метод упала ошибка с текстом "Access is denied".</li>
     * </ol>
     */
    @Test
    public void testUserCanNotExecuteMigrationApiMethod()
    {
        //Подготовка
        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), false, true);
        DSLBo.add(employee);

        ScriptRunner scriptRunner = new ScriptRunner(String.format(SCRIPT_PATTERN, "getMigrationStatus", SERVICE_CALL));
        scriptRunner.setAccessKey(DSLApplication.createAccessKey(employee.getLogin()));

        // Действие и проверка
        scriptRunner.assertScriptError("Access is denied");
    }

    /**
     * Тестирование того, что нельзя секционировать таблицу метакласса "Запрос" по пользовательскому атрибуту типа
     * "Логический",
     * только по системному атрибуту "removed".
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01003
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req01004
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$253096531
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать пользовательский атрибут типа "Логический" в классе "Запрос".</li>
     * <b>Выполнение действий и проверок:</b>
     * <li>Выполнить api.partition.createPartitionsByBoolean по созданному атрибуту.</li>
     * <li>Проверить, что при выполнении апи метода упала ошибка с текстом
     * "Секционирование по атрибуту с кодом '<код атрибута>' для класса 'serviceCall' запрещено."</li>
     * <li>Выполнить api.partition.createPartitionsByBoolean по "removed".</li>
     * <li>Проверить, что в логе админа появилась запись "Для класса 'serviceCall' успешно созданы секции:
     * tbl_serviceCall_removed_true_part, tbl_serviceCall_removed_false_part.".</li>
     * </ol>
     */
    @Test
    public void testCreatePartitionOnlyForRemovedAttribute()
    {
        //Подготовка
        Cleaner.afterTest(true, PartitionApiTest::finishMigrationForServiceCall);

        Attribute attr = DAOAttribute.createBool(SystemClass.SERVICECALL.getCode());
        DSLAttribute.add(attr);

        // Действие и проверка 1
        ScriptRunner.assertError(String.format(SCRIPT_PATTERN, "createPartitionsByBoolean",
                        SERVICE_CALL + String.format(", '%s'", attr.getCode())),
                String.format("Секционирование по атрибуту с кодом '%s' для класса 'serviceCall' запрещено.",
                        attr.getCode()));

        // Действие 2
        ScriptRunner.executeScript(SCRIPT_PATTERN, "createPartitionsByBoolean", SERVICE_CALL + ", 'removed'");

        // Проверка 2
        checkAdminLogMsg("Для класса 'serviceCall' успешно созданы секции: "
                         + "tbl_servicecall_removed_true_part, tbl_servicecall_removed_false_part.");
    }

    private static void checkAdminLogMsg(String msg)
    {
        LogEntry adminLogUuid = DSLAdminLog.getLastByCategoryCode(PARTITION_CATEGORIES);
        Assert.assertEquals(ASSERT_ERROR_MSG, msg, adminLogUuid.getDescription());
    }

    private static void finishMigrationForServiceCall()
    {
        ScriptRunner.executeScript(String.format(SCRIPT_PATTERN, "finishMigration", SERVICE_CALL));
    }
}