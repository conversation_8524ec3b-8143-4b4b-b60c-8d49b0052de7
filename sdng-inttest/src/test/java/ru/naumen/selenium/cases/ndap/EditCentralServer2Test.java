package ru.naumen.selenium.cases.ndap;

import static ru.naumen.selenium.casesutil.NdapConstants.*;

import org.junit.After;
import org.junit.Assert;
import org.junit.Test;

import ru.naumen.selenium.casesutil.NdapConstants;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.ndap.DAOGeneralParameter;
import ru.naumen.selenium.casesutil.model.ndap.DAOLocalServer;
import ru.naumen.selenium.casesutil.model.ndap.DAONDAPServerCase;
import ru.naumen.selenium.casesutil.model.ndap.connection.DAOConnection;
import ru.naumen.selenium.casesutil.model.ndap.metric.DAOMetric;
import ru.naumen.selenium.casesutil.model.ndap.trigger.DAOTrigger;
import ru.naumen.selenium.casesutil.ndap.DSLNDAPServer;
import ru.naumen.selenium.core.AbstractNDAPTestCase;
import ru.naumen.selenium.core.exception.ErrorInGroovyScriptException;
import ru.naumen.selenium.init.SharedFixture;

/**
 * В данном классе проверяется корректный текст ошибки при недоступности центрального сервера
 *
 * <AUTHOR>
 * @since 03.07.2025
 */
public class EditCentralServer2Test extends AbstractNDAPTestCase
{
    private static final MetaClass ndapServerClass = DAONDAPServerCase.createClass();
    private static final Bo centralServer = SharedFixture.centralServer();
    private static final String LOCALHOST = "localhost";
    private static final String INCORRECT_PORT = "0101";

    @After
    public void afterEach()
    {
        DSLNDAPServer.prepareCentralServer();
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не запущен и пытаемся добавить объект типа Подключение
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00649
     * http://http://sd-jira.naumen.ru/browse/NSDWRK-14084
     * <ol>
     * <b>Подготовка:</b>
     * <li>Отредактировать центральный сервер мониторинга (centralServer),
     *     указать некорректные атрибуты: Адрес (например 100) и HTTP-порт (например 100)</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Для каждого типа подключения CLI, HTTP, ICMP, IMAP4, JDBC, JMX, POP3, WMI, SSH, VSphere, LDAP:</li>
     * <li>Создать БО подключения</li>
     * <li>Проверить что появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга.</li>
     * </ol>
     */
    @Test
    public void testErrorAddingConnections()
    {
        //Подготовка:
        setIncorrectBaseUrl();
        Exception exception;

        //Действия и проверки:
        Bo cliConnection = DAOConnection.createCLI();
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(cliConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo httpConnection = DAOConnection.createHTTP(URL_EXAMPLE, Boolean.FALSE.toString(), null, null, null);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(httpConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo icmpConnection = DAOConnection.createICMP(IP_EXAMPLE);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(icmpConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo imap4Connection = DAOConnection.createIMAP4(IP_EXAMPLE, PORT_EXAMPLE, USER_EXAMPLE, PASSWORD_EXAMPLE, false,
                10_000);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(imap4Connection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo jdbcConnection = DAOConnection.createJDBC(CONNECTION_STRING_EXAMPLE, USER_EXAMPLE, PASSWORD_EXAMPLE);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(jdbcConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo jmxConnection = DAOConnection.createJMX(IP_EXAMPLE, PORT_EXAMPLE, USER_EXAMPLE, PASSWORD_EXAMPLE);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(jmxConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo pop3Connection = DAOConnection.createPOP3(IP_EXAMPLE, PORT_EXAMPLE, USER_EXAMPLE, PASSWORD_EXAMPLE, false,
                10_000);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(pop3Connection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo wmiConnection = DAOConnection.createWMI(IP_EXAMPLE, PORT_EXAMPLE, USER_EXAMPLE, DOMAIN_EXAMPLE,
                PASSWORD_EXAMPLE);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(wmiConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo sshConnection = DAOConnection.createSSH(IP_EXAMPLE, PORT_EXAMPLE, USER_EXAMPLE, PASSWORD_EXAMPLE);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(sshConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo vSphereConnection = DAOConnection.createVSphere(USER_EXAMPLE, PASSWORD_EXAMPLE, URL_EXAMPLE, URL_EXAMPLE2);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(vSphereConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo ldapConnection = DAOConnection.createLDAP(URL_EXAMPLE, "SIMPLE", USER_EXAMPLE, PASSWORD_EXAMPLE, "TLS",
                10_000);
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(ldapConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));

        Bo snmpConnection = DAOConnection.snmpBuilder()
                .setAddress(IP_EXAMPLE)
                .setPort(PORT_EXAMPLE)
                .setCommunity(READ_COMMUNITY_EXAMPLE)
                .setUserName(USER_EXAMPLE)
                .setPassword(PASSWORD_EXAMPLE)
                .setProtocol(PROTOCOL_EXAMPLE)
                .setVersion(SNMP_VERSION_EXAMPLE)
                .build();
        exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(snmpConnection));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не запущен и пытаемся добавить объект типа Параметр
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00651
     * http://http://sd-jira.naumen.ru/browse/NSDWRK-13950
     * <ol>
     * <b>Подготовка:</b>
     * <li>Отредактировать центральный сервер мониторинга (centralServer),
     *     указать некорректные атрибуты: Адрес (например 100) и HTTP-порт (например 100)</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать БО типа Обобщенный параметр</li>
     * <li>Проверить что появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга.</li>
     * </ol>
     */
    @Test
    public void testErrorAddingGeneralParameter()
    {
        //Подготовка:
        setIncorrectBaseUrl();

        //Действия и проверки:
        Bo parameter = DAOGeneralParameter.create("return 1", NdapConstants.SELF_CONNECTION_TYPE);
        Exception exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(parameter));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не доступен и пытаемся добавить Локальный сервер
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00648
     * http://sd-jira.naumen.ru/browse/NSDWRK-13330
     * <ol>
     * <b>Подготовка:</b>
     * <li>Отредактировать центральный сервер мониторинга, указав некорректные address и httpPort</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать БО типа Локальный сервер</li>
     * <li>Проверить что появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга.</li>
     * </ol>
     */
    @Test
    public void testErrorAddingLocalServer()
    {
        //Подготовка
        setIncorrectBaseUrl();

        //Действия и проверки:
        Bo localServer = DAOLocalServer.create(IP_EXAMPLE, PORT_EXAMPLE);
        Exception exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(localServer));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не доступен и пытаемся добавить объект типа Активная
     * метрика
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00654
     * http://sd-jira.naumen.ru/browse/NSDWRK-14012
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать объект подключение CLI (cliConnection)</li>
     * <li>Создать объект типа "Обобщённый параметр" с типом подключения 'Подключение CLI' (parameter)</li>
     * <li>Отредактировать центральный сервер мониторинга (centralServer),
     *     указать некорректные атрибуты: Адрес (например 100) и HTTP-порт (например 100)</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать объект типа Активная метрика с подключением cliConnection и параметром parameter</li>
     * <li>Появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга</li>
     * </ol>
     */
    @Test
    public void testErrorAddingActiveMetric()
    {
        //Подготовка:
        //Создать подключение CLI (cliConnection):
        Bo cliConnection = DAOConnection.createCLI();
        DSLBo.add(cliConnection);

        //Создать параметр parameter
        Bo parameter = DAOGeneralParameter.create(NdapConstants.SCRIPT_EXAMPLE, NdapConstants.CLI_CONNECTION_TYPE);
        DSLBo.add(parameter);

        //Указать неверные адрес и порт
        setIncorrectBaseUrl();

        //@formatter:off
        Bo metric = DAOMetric.metricBuilder()
                .setConnection(cliConnection)
                .setParameter(parameter)
                .setServer(centralServer)
                .setEnabled(true)
                .setPollPeriod(NdapConstants.POLL_PERIOD)
                .build(true);
        //@formatter:on

        //Действия и проверки:
        Exception exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(metric));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не запущен и пытаемся добавить объект типа Триггер
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00653
     * http://sd-jira.naumen.ru/browse/NSDWRK-14094
     * <ol>
     * <b>Подготовка:</b>
     * <li>Отредактировать центральный сервер мониторинга (centralServer),
     * указать некорректные атрибуты: Адрес (например 100) и HTTP-порт (например 100)</li>
     * <li>Создать элемент справочника Уровни критичности тревог (severity)</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать БО типа Триггер с Критичностью severity</li>
     * <li>Появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга.</li>
     * </ol>
     */
    @Test
    public void testErrorAddingTrigger()
    {
        //Подготовка
        setIncorrectBaseUrl();
        CatalogItem severity = DAOCatalogItem.createSeverity(6);
        DSLCatalogItem.add(severity);

        //Действия и проверки:
        //@formatter:off
        Bo trigger = DAOTrigger.builder()
                .enabled(true)
                .severity(severity)
                .alertRule(NdapConstants.TRIGGER_SCRIPT_EXAMPLE)
                .build();
        //@formatter:on
        Exception exception = Assert.assertThrows(ErrorInGroovyScriptException.class, () -> DSLBo.add(trigger));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    /**
     * Тестирование появления ошибки когда Центральный сервер не доступен и пытаемся отредактировать объект NDAP
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00648
     * http://sd-jira.naumen.ru/browse/NSDPRD-6228
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать подключение типа ICMP (icmpConnection)</li>
     * <li>Отредактировать центральный сервер мониторинга (centralServer),
     * указать некорректные атрибуты: Адрес (например 100) и HTTP-порт (например 100)</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Создать БО типа Триггер с Критичностью severity</li>
     * <li>Появилось сообщение об ошибке:
     *     Произошла ошибка при подключении к центральному серверу мониторинга.</li>
     * </ol>
     */
    @Test
    public void testErrorEditingNDAPObjects()
    {
        //Подготовка
        Bo icmpConnection = DAOConnection.createICMP(NdapConstants.LOCALHOST);
        DSLBo.add(icmpConnection);
        setIncorrectBaseUrl();

        //Действия и проверки:
        Attribute address = SysAttribute.address(ndapServerClass);
        address.setValue(URL_EXAMPLE);

        Exception exception = Assert.assertThrows(ErrorInGroovyScriptException.class,
                () -> DSLBo.editAttributeValue(icmpConnection, address));
        Assert.assertTrue(exception.getMessage().startsWith(ErrorMessages.ERROR_CONNECT_CENTRAL_SERVER));
    }

    private void setIncorrectBaseUrl()
    {
        Attribute incorrectBaseUrl = SysAttribute.baseUrl(ndapServerClass);
        incorrectBaseUrl.setValue(String.format(DSLNDAPServer.BASE_URL_TEMPLATE, LOCALHOST, INCORRECT_PORT));
        DSLBo.editAttributeValue(centralServer, incorrectBaseUrl);
    }
}
