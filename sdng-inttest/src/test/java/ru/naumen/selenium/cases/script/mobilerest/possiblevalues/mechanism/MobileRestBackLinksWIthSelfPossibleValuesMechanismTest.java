package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.LIST;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType.LIST_EDIT;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType.LIST_WITH_FOLDER;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType.WITH_FOLDER;

import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибута типа "Обратная ссылка" на формах через мобильное API,
 * для метакласса вложенного в себя.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestBackLinksWIthSelfPossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static Attribute backLinksAttr, backLinksFoldersAttr, backLinksListAttr, backLinksListFoldersAttr;
    private static Folder userFolder;
    private static Bo userBo, userBo2, userBo3;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Общая часть подготовки</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать папку userFolder в классе userClass</li>
     * <li>Создать объекты userBo, userBo2, userBo3 типа userCase и добавить объект userBo, userBo2 в папку
     * userFolder</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, со ссылкой на объекты типа
     * userCase</li>
     * <li>Создать атрибут backLinksAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, со ссылкой на
     * объекты типа userCase, с представлением "Дерево выбора"</li>
     * <li>Создать атрибут backLinksFoldersAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, со
     * ссылкой на объекты типа userCase, с представлением "Дерево выбора с папками"</li>
     * <li>Создать атрибут backLinksListAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, со
     * ссылкой на объекты типа userCase, с представлением "Список выбора"</li>
     * <li>Создать атрибут backLinksListFoldersAttr типа "Обратная ссылка" в типе userCase на атрибут boLinksAttr, со
     * ссылкой на объекты типа userCase, с представлением "Список выбора с папками"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибуты backLinksAttr, backLinksFoldersAttr</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.createInSelf();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userFolder = DAOFolder.create(userClass);
        DSLFolder.add(userFolder);

        Attribute folderAttr = SysAttribute.folders(userClass);
        folderAttr.setItemValue(userFolder);

        userBo = DAOUserBo.create(userCase, folderAttr);
        userBo2 = DAOUserBo.createWithParent(userCase, userBo);
        userBo2.setUserAttribute(folderAttr);
        userBo3 = DAOUserBo.create(userCase);
        DAOBo.appendTitlePrefixes(userBo, userBo2, userBo3);
        DSLBo.add(userBo, userBo2, userBo3);
        DSLSearch.updateIndex(userBo, userBo2, userBo3);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userCase);
        backLinksAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr, userCase);
        backLinksFoldersAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr, userCase);
        backLinksFoldersAttr.setEditPresentation(WITH_FOLDER);
        backLinksListAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr, userCase);
        backLinksListAttr.setEditPresentation(LIST_EDIT);
        backLinksListFoldersAttr = DAOAttribute.createBackBOLinks(userCase, boLinksAttr, userCase);
        backLinksListFoldersAttr.setEditPresentation(LIST_WITH_FOLDER);
        DSLAttribute.add(boLinksAttr, backLinksAttr, backLinksFoldersAttr, backLinksListAttr, backLinksListFoldersAttr);

        addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm,
                backLinksAttr, backLinksFoldersAttr, backLinksListAttr, backLinksListFoldersAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения списка возможных значений атрибута типа "Обратная ссылка" с представлением "Дерево
     * выбора", для метакласса вложенного в себя. До версии 13.2 мобильного API должен возвращаться список, после -
     * дерево.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = backLinksAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий userBo, userBo2, userBo3</li>
     * <li>Получить возможные значения для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, состоящее на головном уровне из объектов userBo, userBo2,
     * userBo3</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут backLinksAttr имеет тип выбора - null</li>
     * <li>Получить форму для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут backLinksAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testBackLinks()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksAttr)
                .assertValues(
                        element(userBo),
                        element(userBo2),
                        element(userBo3)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(backLinksAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksAttr)
                .assertValues(
                        treeElement(userBo).leaf(false),
                        treeElement(userBo2).leaf(false),
                        treeElement(userBo3).leaf(false)
                );

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);
        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(backLinksAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование получения списка возможных значений атрибута типа "Обратная ссылка" с представлением "Дерево
     * выбора с папками", для метакласса вложенного в себя. До версии 13.2 мобильного API должен возвращаться список
     * без папок, после - дерево с папками.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo, userBo2, userBo3</li>
     * <li>Получить возможные значения для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из папки userFolder и
     * объектов userBo, userBo2, userBo3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = userFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на дочернем уровне состоящее из объектов userBo,
     * userBo2</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут backLinksAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFolders()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksFoldersAttr)
                .assertValues(
                        element(userBo),
                        element(userBo2),
                        element(userBo3)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksFoldersAttr)
                .assertValues(
                        folderElement(userFolder),
                        treeElement(userBo).leaf(false),
                        treeElement(userBo2).leaf(false),
                        treeElement(userBo3).leaf(false)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setParent(userFolder);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, backLinksFoldersAttr)
                .assertValues(
                        treeElement(userBo).leaf(false),
                        treeElement(userBo2).leaf(false)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(backLinksFoldersAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование получения списка возможных значений атрибута типа "Обратная ссылка" с представлением "Список
     * выбора", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из userBo, userBo2, userBo3</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут backLinksAttr имеет тип выбора - список</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentation()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListAttr, objectTemplate)
                .setForm(addForm);
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListAttr)
                    .assertValues(
                            element(userBo),
                            element(userBo2),
                            element(userBo3)
                    );
        }

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(backLinksListAttr)
                .hasEditPresentationSelectType(LIST);
    }

    /**
     * Тестирование получения списка возможных значений атрибута типа "Обратная ссылка" с представлением "Список
     * выбора с папками", для метакласса вложенного в себя. До версии 13.2 мобильного API должен возвращаться список
     * без папок, после - дерево с папками.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo, userBo2, userBo3</li>
     * <li>Получить возможные значения для версии 13.2 c аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из папки userFolder и
     * объекта userBo3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Родитель" = userFolder,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на дочернем уровне состоящее из объектов userBo,
     * userBo2</li>
     * <li>Получить форму:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут backLinksListFoldersAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentationAndFolders()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListFoldersAttr,
                        objectTemplate)
                .setForm(addForm);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListFoldersAttr)
                .assertValues(
                        element(userBo),
                        element(userBo2),
                        element(userBo3)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksListFoldersAttr)
                .assertValues(
                        folderElement(userFolder),
                        treeElement(userBo3)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksListFoldersAttr,
                        objectTemplate)
                .setForm(addForm)
                .setParent(userFolder);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);
        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, backLinksListFoldersAttr)
                .assertValues(
                        treeElement(userBo),
                        treeElement(userBo2)
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_2);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(backLinksListFoldersAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Обратная ссылка" с представлением "Дерево выбора",
     * для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список, состоящий из объекта userBo2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из объекта userBo2</li>
     * </ol>
     */
    @Test
    public void testBackLinksWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksAttr)
                .assertValues(
                        element(userBo2)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksAttr)
                .assertValues(
                        treeSearch(
                                treeElement(userBo2).leaf(false)
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Обратная ссылка" с представлением "Дерево выбора с
     * папками", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список, состоящий из объекта userBo2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из объекта userBo2, вложенного в папку userFolder и
     * представленного отдельно</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFoldersWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksFoldersAttr)
                .assertValues(
                        element(userBo2)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksFoldersAttr)
                .assertValues(
                        treeSearch(
                                folderElement(userFolder).children(
                                        treeElement(userBo2).leaf(false)
                                ),
                                treeElement(userBo2).leaf(false)
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Обратная ссылка" с представлением "Список выбора",
     * для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список, состоящий из объекта userBo2</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentationWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListAttr)
                    .assertValues(
                            element(userBo2)
                    );
        }
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Обратная ссылка" с представлением "Список выбора с
     * папками", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список, состоящий из объекта userBo2</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo2,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево, состоящее из объекта userBo2, вложенного в папку userFolder</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentationAndFoldersWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListFoldersAttr,
                        objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo2.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListFoldersAttr)
                .assertValues(
                        element(userBo2)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksListFoldersAttr)
                .assertValues(
                        treeSearch(
                                folderElement(userFolder).children(
                                        treeElement(userBo2)
                                )
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование получения списка возможных значений с использованием пагинации для атрибута типа "Обратная
     * ссылка" с представлением "Дерево выбора", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo2, userBo3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>userClass = 1,</li>
     *     </ul></li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из userBo2, userBo3</li>
     * </ol>
     */
    @Test
    public void testBackLinksWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksAttr)
                .assertValues(
                        element(userBo2),
                        element(userBo3)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksAttr, objectTemplate)
                .setForm(addForm)
                .addPosition(userClass, 1);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksAttr)
                .assertValues(
                        treeElement(userBo2).leaf(false),
                        treeElement(userBo3).leaf(false)
                );
    }

    /**
     * Тестирование получения списка возможных значений с использованием пагинации для атрибута типа "Обратная
     * ссылка" с представлением "Дерево выбора с папками", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo2, userBo3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>"Папки" = 1,</li>
     *         <li>userClass = 1,</li>
     *     </ul></li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из userBo2, userBo3</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFoldersWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksFoldersAttr)
                .assertValues(
                        element(userBo2),
                        element(userBo3)
                );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(backLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .addFolderPosition(1)
                .addPosition(userClass, 1);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksFoldersAttr)
                .assertValues(
                        treeElement(userBo2).leaf(false),
                        treeElement(userBo3).leaf(false)
                );
    }

    /**
     * Тестирование получения списка возможных значений с использованием пагинации для атрибута типа "Обратная
     * ссылка" с представлением "Список выбора", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из userBo2, userBo3</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentationWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1);
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListAttr)
                    .assertValues(
                            element(userBo2),
                            element(userBo3)
                    );
        }
    }

    /**
     * Тестирование получения списка возможных значений с использованием пагинации для атрибута типа "Обратная
     * ссылка" с представлением "Список выбора с папками", для метакласса вложенного в себя.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из userBo2, userBo3</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>"Папки" = 1,</li>
     *     </ul></li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListFoldersAttr = null,</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулось дерево объектов, на родительском уровне состоящее из userBo3</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithListPresentationAndFoldersWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams =
                MobilePossibleValuesParams.create(backLinksListFoldersAttr, objectTemplate)
                        .setForm(addForm)
                        .setOffset(1);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListFoldersAttr)
                .assertValues(
                        element(userBo2),
                        element(userBo3)
                );

        MobilePossibleValuesParams pvParams2 =
                MobilePossibleValuesParams.create(backLinksListFoldersAttr, objectTemplate)
                        .setForm(addForm)
                        .addFolderPosition(1);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, backLinksListFoldersAttr)
                .assertValues(
                        treeElement(userBo3)
                );
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Обратная ссылка" с представлением "Список выбора", для метакласса вложенного в себя. Для поиска
     * пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksListAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>backLinksListAttr = null</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.1 или v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testBackLinksWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(backLinksListAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(backLinksListAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userBo.getTitle());
        for (MobileVersion version : List.of(V13_1, V13_2))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, backLinksListAttr).isEmpty();
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Обратная ссылка" для представлений, которые отображаются деревом, для метакласса вложенного в
     * себя. Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$120730342
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr или backLinksFoldersAttr или backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>"Атрибут (обратная ссылка)" = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся пустой список</li>
     * <li>Получить возможные значения:<ul>
     *     <li>"Атрибут" = backLinksAttr или backLinksFoldersAttr или backLinksListFoldersAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название userBo,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>"Атрибут (обратная ссылка)" = null</li>
     *     </ul></li>
     *     <li>"Версия мобильного API" = v13.2</li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testBackLinksWithFoldersWhenPerformsSearchAndOffsetMoreThenZero()
    {
        for (Attribute attribute : List.of(backLinksAttr, backLinksFoldersAttr, backLinksListFoldersAttr))
        {
            Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                    .setMetaClass(userCase)
                    .setAttribute(attribute, null)
                    .build();
            MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(attribute, objectTemplate)
                    .setForm(addForm)
                    .setOffset(1)
                    .setSearchString(userBo.getTitle());
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, attribute).isEmpty();

            ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

            pvResponse2.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse2, attribute).assertValues(tree());
        }
    }
}
