package ru.naumen.selenium.cases.script.http;

import org.junit.Before;
import org.junit.Test;

import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.rest.DSLRest;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на api.http.getBodyPublishers().ofFormData().
 *
 * <AUTHOR>
 * @since 07.11.2024
 */
public class HttpApiFormDataPublisherTest extends AbstractTestCase
{
    private String accessKey;
    private MetaClass userClass;
    private MetaClass userCase;
    private Bo userBo;

    /**
     * <ol>
     * <b>Общая подготовка.</b>
     * <li>Получение accessKey</li>
     * <li>Создание userClass</li>
     * <li>Создание userCase</li>
     * <li>Создание userBo</li>
     * </ol>
     */
    @Before
    public void prepareFixture()
    {
        accessKey = DSLApplication.createAccessKey(SharedFixture.employee().getLogin());
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
    }

    /**
     * Тестирование прикрепления файла к объекту через REST-метод add-file
     * при помощи FormDataPublisher.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$232596358
     * <br>
     * <ol>
     * <b>{@link #prepareFixture() Общая подготовка}</b>
     * <li>Создать пользовательский класс userClass и унаследованный от него тип userCase</li>
     * <li>Создать объект userBo типа userCase</li>
     * <br>
     * <b>Выполнение действий</b>
     * <li>Прикрепить с помощью REST-метода add-file файл к объекту userBo : services/rest/add-file/${userBoUuid}
     * ?accessKey=${accessKey}</li>
     * <br>
     * <b>Проверка</b>
     * <li>Проверить, что у объекта userBo прикреплён файл</li>
     * </ol>
     */
    @Test
    public void testChangedAttributesAfterRESTaddFile()
    {
        String uri = DSLRest.getAddFileUri(userBo, accessKey);

        // Выполнение действий
        ScriptRunner.executeScript(
                """
                            import java.net.http.HttpRequest;
                            import java.net.http.HttpResponse.BodyHandlers;
                        
                            def dummyBytes = [0, 1] as byte[]
                            def data = api.http.getBodyPublishers().ofFormData().addStream('file', 'fileName', () -> new ByteArrayInputStream(dummyBytes));
                        
                            def request = HttpRequest.newBuilder()
                                .uri(URI.create('%s'))
                                .header("Content-Type", data.contentType())
                                .POST(data)
                                .build();
                        
                            def client = api.http.getClient();
                        
                            client.send(request, BodyHandlers.ofString());
                        """, uri);

        // Проверки
        DSLFile.assertFiles(userBo, true);
    }
}