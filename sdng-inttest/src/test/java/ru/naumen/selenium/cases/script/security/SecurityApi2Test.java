package ru.naumen.selenium.cases.script.security;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.containsString;
import static ru.naumen.selenium.casesutil.user.GUILogon.LOGIN_ERROR_BY_USER;

import java.util.List;

import org.apache.http.HttpStatus;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLSuperUser;
import ru.naumen.selenium.casesutil.admin.GUIConsole;
import ru.naumen.selenium.casesutil.admin.SystemGroup;
import ru.naumen.selenium.casesutil.admin.interfaze.GUIInterface;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.eventaction.DSLFeatureConfiguration;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.secgroup.DAOSecurityGroup;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.casesutil.model.superuser.DAOSuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser;
import ru.naumen.selenium.casesutil.model.superuser.SuperUser.SystemSuperUserProfile;
import ru.naumen.selenium.casesutil.personalsettings.GUIPersonalSettings;
import ru.naumen.selenium.casesutil.scripts.DSLApplication;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.secgroup.DSLSecurityGroup;
import ru.naumen.selenium.casesutil.security.DAOSecurityPolicy;
import ru.naumen.selenium.casesutil.security.DSLSecurityPolicy;
import ru.naumen.selenium.casesutil.security.SecurityPolicy;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на скриптовое API (API для работы с правами из скриптов)
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
 *
 * <AUTHOR>
 * @since 10.02.2021
 */
public class SecurityApi2Test extends AbstractTestCase
{
    private final static String CHANGE_SUPER_USER_CREDENTIALS =
            "api.security.changeSuperUserCredentials('%s', '%s', '%s')";
    private final static String CHANGE_SUPERUSER_PASSWORD_SCRIPT = "api.security.changeSuperUserPassword('%s', '%s')";

    private static SecurityGroup secGroup1;
    private static SecurityGroup secGroup2;
    private static SecurityGroup secGroup3;
    private static SecurityGroup secGroup4;

    /**
     * <b>Общая часть подготовки</b>
     * <li>Создать в системе группы пользователей: secGroup1, secGroup2, secGroup3, secGroup4</li>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        secGroup1 = DAOSecurityGroup.create();
        secGroup2 = DAOSecurityGroup.create();
        secGroup3 = DAOSecurityGroup.create();
        secGroup4 = DAOSecurityGroup.create();
        DSLSecurityGroup.add(secGroup1, secGroup2, secGroup3, secGroup4);
    }

    /**
     * Тестирование получения всех групп пользователей на стенде через api-метод "api.security.getGroups()"
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$158207150
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт получения всех групп пользователей:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.getGroups()
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>Поверить, что в ответе сервера хранятся группы пользователей secGroup1, secGroup2, secGroup3 и
     * secGroup4</li>
     */
    @Test
    public void testGetGroups()
    {
        //Подготовка
        List<SecurityGroup> expectedGroups = List.of(secGroup1, secGroup2, secGroup3, secGroup4);

        //Действия и проверки
        String actualGroups = ScriptRunner.executeScript("api.security.getGroups()");

        for (SecurityGroup secGroup : expectedGroups)
        {
            String title = secGroup.getTitle();
            String code = secGroup.getCode();

            Assert.assertThat(String.format(ErrorMessages.SEC_GROUP_NOT_EXIST, title, code),
                    actualGroups, containsString(String.format("[code=%s, title=%s]", code, title)));
        }
    }

    /**
     * Тестирование получения всех групп пользователей на стенде через api-метод "api.security.getGroups()", с учетом
     * включенной системной группы "sys_employeesForGatewayIntegration"
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$158207150
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Включить интеграцию с внешним шлюзом</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт получения всех групп пользователей:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.getGroups()
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>Поверить, что в ответе сервера хранятся группы пользователей secGroup1, secGroup2, secGroup3, secGroup4
     * и системная группа "sys_employeesForGatewayIntegration"</li>
     */
    @Test
    public void testGetGroupsWithSystemGroup()
    {
        //Подготовка
        DSLFeatureConfiguration.enableGatewayIntegration();
        List<String> expectedGroups = List.of(secGroup1.getCode(), secGroup2.getCode(), secGroup3.getCode(),
                secGroup4.getCode(), SystemGroup.GATEWAY_INTEGRATION_GROUP.getCode());

        //Действия и проверки
        String actualGroups = ScriptRunner.executeScript("api.security.getGroups()");

        for (String secGroupCode : expectedGroups)
        {
            Assert.assertThat(
                    String.format("Группа пользователей с кодом %s - не найдена в ответе от сервера.", secGroupCode),
                    actualGroups, containsString(String.format("code=%s", secGroupCode)));
        }
    }

    /**
     * Тестирование разблокировки суперпользователя на стенде через api-метод "api.security.unlockSuperUser(login)"
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$219655846
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать суперпользователя superUser</li>
     * <li>Настроить и включить политику безопасности:
     *      <pre>
     *      - "Количество неудачных попыток входа в систему без временной блокировки учетной записи" = "1",
     *      - "Время блокировки учетной записи между неудачными попытками входа в систему (сек)" = "0",
     *      - "Максимальное количество неудачных попыток входа в систему" = "2".
     *      </pre>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>На форме входа в приложение пытаемся авторизоваться под:
     *      <pre>
     *      - Логин: логин суперпользователя superUser
     *      - Пароль: логин суперпользователя superUser
     *      </pre>
     * </li>
     * <li>Проверяем, что авторизация не прошла и появилось сообщение об ошибке:
     *     "Невозможно войти в систему. Неправильные имя пользователя и/или пароль. Попробуйте повторить ввод."</li>
     * <li>Пытаемся снова авторизоваться под:
     *      <pre>
     *      - Логин: логин суперпользователя superUser
     *      - Пароль: логин суперпользователя superUser
     *      </pre>
     * </li>
     * <li>Выполнить скрипт разблокировки суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.unlockSuperUser(login)
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>Авторизуемся под суперпользователем superUser</li>
     */
    @Test
    public void testUnlockSuperUser()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);

        SecurityPolicy policy = DAOSecurityPolicy.create();
        policy.setEnabled(true);
        policy.setMaxFailedTriesBeforeSuspension(1);
        policy.setSuspensionTime(0);
        policy.setMaxFailedTries(2);
        DSLSecurityPolicy.edit(policy);

        //Действия и проверки
        final String login = superUser.getLogin();
        GUILogon.tryLoginAssertError(login, login, LOGIN_ERROR_BY_USER);
        GUILogon.tryLoginAssertError(login, login, LOGIN_ERROR_BY_USER);

        ScriptRunner.executeScript("api.security.unlockSuperUser('%s')".formatted(login));
        GUILogon.login(superUser);
    }

    /**
     * Тестирование метода проверки наличия у текущего суперпользователя системных профилей
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Действия и проверки:</b>
     * <li>Авторизуемся под пользователем naumen</li>
     * <li>Переходим в консоль</li>
     * <li>Выполняем скрипт <i>api.security.hasAdminProfile(profile)</i> для профилей vendor и crudAdmin</li>
     * <li>Проверяем, что результат выполнения true</li>
     * <li>Выполняем скрипт <i>api.security.hasAdminProfile('admin')</i></li>
     * <li>Проверяем, что результат выполнения false</li>
     * <br>
     * <li>Авторизуемся под пользователем system</li>
     * <li>Переходим в консоль</li>
     * <li>Выполняем скрипт <i>api.security.hasAdminProfile(profile)</i> для профилей admin и crudAdmin</li>
     * <li>Проверяем, что результат выполнения true</li>
     * <li>Выполняем скрипт <i>api.security.hasAdminProfile('vendor')</i></li>
     * <li>Проверяем, что результат выполнения false</li>
     */
    @Test
    public void testSuperHasAdminProfile()
    {
        //Действия и проверки
        GUILogon.asSuper();
        GUIConsole.goToConsole();
        String script = "api.security.hasAdminProfile('%s')";
        Assert.assertTrue(Boolean.parseBoolean(
                GUIConsole.runGroovyScriptInText(script.formatted(SystemSuperUserProfile.VENDOR.getCode()))));
        Assert.assertTrue(Boolean.parseBoolean(GUIConsole.runGroovyScriptInText(
                script.formatted(SystemSuperUserProfile.CRUD_ADMIN.getCode()))));
        Assert.assertFalse(Boolean.parseBoolean(GUIConsole.runGroovyScriptInText(
                script.formatted(SystemSuperUserProfile.ADMIN.getCode()))));

        GUILogon.asSystem();
        GUIConsole.goToConsole();

        Assert.assertTrue(Boolean.parseBoolean(GUIConsole.runGroovyScriptInText(
                script.formatted(SystemSuperUserProfile.ADMIN.getCode()))));
        Assert.assertTrue(Boolean.parseBoolean(GUIConsole.runGroovyScriptInText(
                script.formatted(SystemSuperUserProfile.CRUD_ADMIN.getCode()))));
        Assert.assertFalse(Boolean.parseBoolean(
                GUIConsole.runGroovyScriptInText(script.formatted(SystemSuperUserProfile.VENDOR.getCode()))));
    }

    /**
     * Тестирование метода смены логина суперпользователя
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать суперпользователя superUser</li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт смены логина суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('login', 'newLogin', 'newPassword')
     *      --------------------------------------------------
     *      </pre>
     *      где login - текущий логин суперпользователя, newLogin - новый логин пользователя
     * </li>
     * <li>На форме входа в приложение пытаемся авторизоваться под:
     *      <pre>
     *      - Логин: новый логин суперпользователя superUser
     *      - Пароль: пароль суперпользователя superUser
     *      </pre>
     * </li>
     * <li>Проверяем, что авторизация прошла и мы авторизованы под суперпользователем с новым логином</li>
     */
    @Test
    public void testChangeSuperUserLogin()
    {
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        //Действия и проверки
        final String newLogin = ModelUtils.createLogin();
        ScriptRunner.executeScript(
                (CHANGE_SUPER_USER_CREDENTIALS.formatted(superUser.getLogin(), newLogin, superUser.getPassword())));

        GUILogon.login(newLogin, superUser.getPassword());
        GUILogon.assertAuthorizedLogin(newLogin);
        superUser.setLogin(newLogin);
    }

    /**
     * Тестирование метода смены пароля суперпользователя
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>Создать суперпользователя superUser</li>
     * <li>Настроить и включить политику безопасности:
     *      <pre>
     *      - "Максимальное количество символов в пароле" = "4".
     *      </pre>
     * </li>
     * <br>
     * <b>Действия и проверки:</b>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('login', '111')
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>Проверить, что возникла ошибка "Пароль не удовлетворяет политике безопасности"</li>
     * <br>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('login', 'newPassword')
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>На форме входа в приложение пытаемся авторизоваться под:
     *      <pre>
     *      - Логин: логин суперпользователя superUser
     *      - Пароль: новый пароль суперпользователя superUser
     *      </pre>
     * </li>
     * <li>Проверяем, что авторизация прошла и мы авторизованы под суперпользователем с новым паролем</li>
     */
    @Test
    public void testChangeSuperUserPassword()
    {
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);

        SecurityPolicy policy = DAOSecurityPolicy.create();
        policy.setEnabled(true);
        policy.setMinPasswordLength(4);
        DSLSecurityPolicy.edit(policy);

        //Действия и проверки
        String login = superUser.getLogin();
        final ScriptRunner script = new ScriptRunner(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted(login, "111"));
        script.assertScriptError(
                "Пароль не удовлетворяет политике безопасности: количество символов в пароле должно быть не менее 4.");

        final String password = ModelUtils.createPassword();
        final ScriptRunner script2 =
                new ScriptRunner(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted(login, password));
        script2.runScript();

        GUILogon.login(login, password);
    }

    /**
     * Тестирование разрыва сессии после смены пароля суперпользователю
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать суперпользователя superUser</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИА под superUser</li>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('{login}', '{password}')
     *      --------------------------------------------------
     *      где:
     *      login - логин superUser
     *      password - новый пароль
     *      </pre>
     * </li>
     * <li>Кликнуть по логотипу системы</li>
     * <li>Проверить, что появилось окно вопроса и нажать "Авторизоваться"</li>
     * <li>Проверить, что появилось сообщение об ошибке на странице ввода логина: Учетная запись была
     * изменена. Для входа в систему введите обновленные логин и пароль.</li>
     */
    @Test
    public void testLoginPageErrorAfterChangePassword()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        GUILogon.login(superUser);

        //Выполнение действий и проверок
        String password = ModelUtils.createPassword();
        ScriptRunner.executeScript(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted(superUser.getLogin(), password));

        tester.click(GUIInterface.LOGO_SYSTEM);
        GUIForm.assertQuestionAppear("Сообщение с текстом 'Операция не может быть выполнена' не появилось.");
        GUIForm.clickNo();
        GUIError.assertContainsInErrorMessage(GUILogon.ID_ERROR_MESSAGE, ErrorMessages.ERROR_CHANGE_SUPERUSER);
    }

    /**
     * Тестирование не возможности сменить пароль, если новый пароль не отличается от старого или равен логину при
     * включенной политике безопасности
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать суперпользователя superUser</li>
     * <li>Включить политику безопасности и настроить:
     * <ul>
     *     <li>Запретить использовать логин в качестве нового пароля = true</li>
     *     <li>Запретить использовать текущий пароль в качестве нового пароля = true</li>
     * </ul></li>
     * <b>Выполнение действий и проверок</b>
     * <li></li>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('{login}', '{login}')
     *      --------------------------------------------------
     *      где:
     *      login - логин superUser
     *      </pre>
     * </li>
     * <li>Проверить, что скрипт не выполнился и появилась ошибка: Пароль не удовлетворяет политике безопасности:
     * пароль не должен совпадать с логином.</li>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('{login}', '{oldPassword}')
     *      --------------------------------------------------
     *      где:
     *      login - логин superUser
     *      oldPassword - старый пароль superUser
     *      </pre>
     * </li>
     * <li>Проверить, что скрипт не выполнился и появилась ошибка: Пароль не удовлетворяет политике безопасности:
     * пароль не должен совпадать с текущим паролем.</li>
     */
    @Test
    public void testErrorChangePasswordForSuperUserWithSecurityPolicy()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);

        SecurityPolicy policy = DAOSecurityPolicy.create();
        policy.setEnabled(true);
        policy.setRestrictLoginAsPassword(true);
        policy.setRestrictCurrentPasswordAsNew(true);
        DSLSecurityPolicy.edit(policy);

        //Выполнение действий и проверок
        String login = superUser.getLogin();
        String password = superUser.getPassword();
        final ScriptRunner script =
                new ScriptRunner("api.security.changeSuperUserPassword('%s', '%s')".formatted(login, login));
        script.assertScriptError(ErrorMessages.PASSWORD_EQUALS_CURRENT_LOGIN);

        final ScriptRunner script2 =
                new ScriptRunner("api.security.changeSuperUserPassword('%s', '%s')".formatted(login, password));
        script2.assertScriptError(ErrorMessages.PASSWORD_EQUALS_CURRENT_PASSWORD);
    }

    /**
     * Тестирование невозможности сменить пароль для naumen под другим суперпользователем с CRUD правами
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИА под System</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('naumen', '{password}')
     *      --------------------------------------------------
     *      где:
     *      password - новый пароль
     *      </pre>
     * </li>
     * <li>Проверить, что скрипт не выполнился и появилась ошибка:
     * Редактирование технолога запрещено</li>
     */
    @Test
    public void testErrorChangeVendorsPassword()
    {
        //Выполнение действий и проверок
        GUILogon.asSystem();
        GUIConsole.goToConsole();
        String password = ModelUtils.createPassword();
        GUIConsole.runScriptInTextWithoutChecks(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted("naumen", password));
        GUIError.assertContainsInErrorDialog("Редактирование технолога запрещено");
    }

    /**
     * Тестирование невозможности сменить пароль для под суперпользователем без CRUD правами
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать суперпользователя superUser</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Зайти в ИА под superUser</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт смены пароля суперпользователя:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('superUser', 'test')
     *      --------------------------------------------------
     *      </pre>
     * </li>
     * <li>Проверить, что скрипт не выполнился и появилась ошибка:
     * Редактирование/создание/удаление объектов класса Технолог из скриптов запрещено</li>
     */
    @Test
    public void testErrorChangePasswordWithoutCrudProfile()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);

        //Выполнение действий и проверок
        GUILogon.login(superUser);
        GUIConsole.goToConsole();
        GUIConsole.runScriptInTextWithoutChecks(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted("system", "test"));
        GUIError.assertContainsInErrorDialog(
                "Редактирование/создание/удаление объектов класса Технолог из скриптов запрещено");
    }

    /**
     * Тестирование невозможности сменить логин суперпользователю, если новый логин такой же как у пользователя
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать суперпользователя superUser</li>
     * <li>Создать пользователя employee</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Выполнить скрипт смены пароля логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('$superUserLogin', '$newLogin', '$newPassword')
     *      --------------------------------------------------
     *      где:
     *      1) $superUserLogin - логин superUser
     *      2) $newLogin - логин, который используется employee
     *      3) $newPassword - новый пароль
     *      </pre>
     * </li>
     * <li>Проверить, что скрипт не выполнился и появилась ошибка:
     * Суперпользователь не может быть добавлен. Сотрудник с таким логином уже существует {employee.login}</li>
     */
    @Test
    public void testErrorChangeLoginForSuperUserEqualsEmployeeLogin()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), SharedFixture.ou(), true);
        DSLBo.add(employee);

        //Выполнение действий и проверок
        final ScriptRunner script =
                new ScriptRunner(CHANGE_SUPER_USER_CREDENTIALS.formatted(superUser.getLogin(), employee.getLogin(),
                        employee.getPassword()));
        script.assertScriptError(
                "Суперпользователь не может быть добавлен. Сотрудник с таким логином уже существует: '%s'".formatted(
                        employee.getTitle()));
    }

    /**
     * Тестирование выполнение REST запроса после смены логина суперпользователя с параметром
     * ru.naumen.security.editLogin.saveAccessKey.enabled=true
     * <br>
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать суперпользователя superUser</li>
     * <li>Получить ключ доступа accessKey для superUser</li>
     * <b>Выполнение действий и проверок</b>
     * <li>Выполнить скрипт включения сохранения accessKey:
     * <pre>
     *      --------------------------------------------------
     *      beanFactory.getBean('configurationProperties').setSaveAccessKey(true)
     *      --------------------------------------------------
     * </pre>
     * <li>Выполнить скрипт смены логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('{superUserLogin}', 'newLogin', 'newPassword')
     *      --------------------------------------------------
     *      где:
     *      superUserLogin - логин superUser
     *      newLogin - новый логин
     *      </pre>
     * </li>
     * <li>Выполнить REST запрос services/rest/get/%s?accessKey</li>
     * <li>Проверить, что запрос выполнился с кодом 200</li>
     */
    @Test
    public void testRestFotSuperUserAfterChangePassword()
    {
        //Подготовка
        String saveAccessKeyScript = "beanFactory.getBean('configurationProperties').setSaveAccessKey(%s)";
        Cleaner.afterTest(() -> ScriptRunner.executeScript(saveAccessKeyScript.formatted(false)));
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        String login = superUser.getLogin();
        String accessKey = ScriptRunner.executeScript("api.auth.getAccessKey('%s').uuid".formatted(login));
        //Выполнение действий и проверок
        ScriptRunner.executeScript(saveAccessKeyScript.formatted(true));
        String newLogin = ModelUtils.createLogin();
        ScriptRunner.executeScript(CHANGE_SUPER_USER_CREDENTIALS.formatted(login, newLogin, superUser.getPassword()));

        String link = Config.get().getWebAddress() + "services/rest/get/%s?accessKey=%s";
        given()
                .expect()
                .statusCode(HttpStatus.SC_OK)
                .when()
                .get(String.format(link, SharedFixture.ou().getUuid(), accessKey));
    }

    /**
     * Тестирование сообщения об ошибке при попытке сменить логин суперпользователя API методом, если в метод
     * передан несуществующий логин
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Действия</b>
     * <li>Выполнить скрипт смены логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('$errorLogin', '$newLogin', '$newPassword')
     *      --------------------------------------------------
     *      где:
     *      1) $errorLogin - ошибочный логин суперпользователя, который хотим изменить
     *      2) $newLogin - новый логин
     *      3) $newPassword - новый пароль
     *      </pre>
     * </li>
     * <b>Проверка</b>
     * <li>Скрипт не выполнился и появилась ошибка: Объект не найден: uuid=errorLogin$newLogin</li>
     * </ol>
     */
    @Test
    public void testErrorChangeLoginForSuperUserWithErrorLogin()
    {
        //Действия
        final ScriptRunner script =
                new ScriptRunner(CHANGE_SUPER_USER_CREDENTIALS.formatted("errorLogin", "newLogin", "error"));

        //Проверка
        script.assertScriptError("Объект не найден: uuid=superUser$errorLogin");
    }

    /**
     * Тестирование невозможности изменить логин суперпользователя с помощью API метода, если новый логин принадлежит
     * другому суперпользователю
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательского суперпользователя superUser</li>
     * <b>Действия</b>
     * <li>Выполнить скрипт смены логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('$firstLogin', '$secondLogin', '$newPassword')
     *      --------------------------------------------------
     *      где:
     *      1) $firstLogin - логин суперпользователя, который хотим изменить
     *      2) $secondLogin - новый логин, который уже занят другим суперпользователем
     *      3) $newPassword - новый пароль
     *      </pre>
     * </li>
     * <b>Проверка</b>
     * <li>Скрипт не выполнился и появилась ошибка: Суперпользователь не может быть изменён. Суперпользователь с
     * таким логином уже существует.</li>
     * </ol>
     */
    @Test
    public void testErrorChangeLoginForSuperUserEqualsAnotherSuperUserLogin()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        SuperUser naumen = DAOSuperUser.createNaumen();

        //Действия
        final ScriptRunner script =
                new ScriptRunner(CHANGE_SUPER_USER_CREDENTIALS.formatted(naumen.getLogin(), superUser.getLogin(),
                        superUser.getPassword()));

        //Проверка
        script.assertScriptError(
                "Суперпользователь не может быть изменён. Суперпользователь с таким логином уже существует.");
    }

    /**
     * Тестирование невозможности изменить логин суперпользователя с помощью API метода с правами пользовательского
     * суперпользователя (без CRUD)
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>Создать пользовательского суперпользователя superUser</li>
     * <b>Действия</b>
     * <li>Выполнить скрипт смены логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('$login', '$newLogin', '$newPassword')
     *      --------------------------------------------------
     *      где:
     *      1) $login - логин суперпользователя, который хотим изменить
     *      2) $newLogin - новый логин
     *      3) $newPassword - новый пароль
     *      </pre>
     * </li>
     * <b>Проверка</b>
     * <li>Скрипт не выполнился и появилась ошибка:  Редактирование/создание/удаление объектов класса Технолог из
     * скриптов запрещено</li>
     * </ol>
     */
    @Test
    public void testErrorChangeLoginForSuperUserWithoutCrudRights()
    {
        //Подготовка
        SuperUser superUser = DAOSuperUser.create();
        DSLSuperUser.add(superUser);
        SuperUser system = DAOSuperUser.createSystem();

        //Действия
        String accessKey = DSLApplication.createAccessKey(superUser.getLogin());
        final ScriptRunner script =
                new ScriptRunner(CHANGE_SUPER_USER_CREDENTIALS.formatted(system.getLogin(), "systemNewLogin",
                        system.getPassword()));
        script.setAccessKey(accessKey);

        //Проверка
        script.assertScriptError(
                "Редактирование/создание/удаление объектов класса Технолог из скриптов запрещено");
    }

    /**
     * Тестирования сохранения сессии суперпользователя при смене пароля с помощью API метода самому себе
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Действия:</b>
     * <li>Войти в ИА под System</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт смены пароля:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserPassword('$login', '$password')
     *      --------------------------------------------------
     *      где:
     *      1) $login - логин суперпользователя, которому хотим изменить пароль
     *      2) $password - новый пароль
     *      </pre>
     * </li>
     * <li>Перейти на главную страницу ИА, кликнув на логотип</li>
     * <b>Проверки:</b>
     * <li>Ошибок нет</li>
     * <li>Пользователь перешел на главную страницу ИА</li>
     * </ol>
     */
    @Test
    public void testSessionSaveAfterSelfChangePasswordForSuperUser()
    {
        //Подготовка
        SuperUser system = DAOSuperUser.createSystem();
        Cleaner.afterTest(() -> DSLSuperUser.edit(system, true));

        //Действия
        GUILogon.asSystem();
        GUIConsole.goToConsole();
        String password = ModelUtils.createPassword();

        GUIConsole.runGroovyScriptInText(CHANGE_SUPERUSER_PASSWORD_SCRIPT.formatted(system.getUuid(), password));
        GUINavigational.goToPersonalSettings();

        //Проверки
        GUIError.assertErrorAbsence();
        GUIPersonalSettings.assertThatCard();
    }

    /**
     * Тестирование завершения сессии суперпользователя при смене логина с помощью API метода самому себе
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$235924007
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts
     * <br>
     * <ol>
     * <b>Действия и проверки</b>
     * <li>Войти в ИА под System</li>
     * <li>Перейти в консоль</li>
     * <li>Выполнить скрипт смены логина:
     *      <pre>
     *      --------------------------------------------------
     *      api.security.changeSuperUserCredentials('$login', '$newLogin', '$newPassword')
     *      --------------------------------------------------
     *      где:
     *      1) $login - логин суперпользователя, который хотим изменить
     *      2) $newLogin - новый логин
     *      3) $newPassword - новый пароль
     *      </pre>
     * </li>
     * <li>Проверить, что появилось окно предупрежения о завершении сессии</li>
     * <li>Нажать "Авторизоваться"</li>
     * <li>Проверить, что на форме авторизации отображается ошибка "Учетная запись была изменена. Для входа в систему
     * введите обновленные логин и пароль."</li>
     * </ol>
     */
    @Test
    public void testSessionEndAfterSelfChangeLoginForSuperUser()
    {
        //Подготовка
        SuperUser system = DAOSuperUser.createSystem();
        String newLogin = ModelUtils.createLogin();

        Cleaner.afterTest(() -> ScriptRunner.executeScript(
                CHANGE_SUPER_USER_CREDENTIALS.formatted(newLogin, system.getLogin(), system.getPassword())));

        //Действия и проверки
        GUILogon.asSystem();
        GUIConsole.goToConsole();
        GUIConsole.runGroovyScriptInText(
                CHANGE_SUPER_USER_CREDENTIALS.formatted(system.getLogin(), newLogin, system.getPassword()));
        tester.click(GUIInterface.LOGO_SYSTEM);

        GUIForm.assertQuestionAppear("Сообщение о завершении сессии не появилось.");
        GUIForm.clickNo();
        GUIError.assertContainsInErrorMessage(GUILogon.ID_ERROR_MESSAGE, ErrorMessages.ERROR_CHANGE_SUPERUSER);
    }
}
