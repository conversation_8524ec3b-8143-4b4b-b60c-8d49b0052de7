package ru.naumen.selenium.cases.script.mobilerest.possiblevalues;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V12;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.FormType.PARAMETRIZED_USER_ACTION;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.elements;

import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.customforms.DSLFormParameter;
import ru.naumen.selenium.casesutil.metaclass.DSLEventAction;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOEventAction;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction.EventType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.params.DAOFormParameter;
import ru.naumen.selenium.casesutil.model.params.FormParameter;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.casesutil.scripts.GroovyUtils;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на получение возможных значений атрибутов типа "Набор элементов произвольного справочника" на формах.
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
public class MobileRestCatalogAnyItemSetPossibleValuesMechanismTest extends AbstractTestCase
{
    private static Bo userBo;
    private static EventAction userEvent;
    private static MobileAuthentication licAuth;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать объекты userBo подтипа userCase</li>
     * <li>Создать действие по событию userEvent типа "[Пользовательское событие]" типа "Скрипт":
     * <pre>
     *     -------------------------------------------------------------------------------
     *      logger.info('success')
     *     -------------------------------------------------------------------------------
     * </pre>
     * </li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);

        ScriptInfo eventActionScriptInfo = DAOScriptInfo.createNewScriptInfo("logger.info('success')");
        DSLScriptInfo.addScript(eventActionScriptInfo);

        userEvent = DAOEventAction.createEventScript(EventType.userEvent, eventActionScriptInfo, true, userCase);
        DSLEventAction.add(userEvent);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование отображения возможных значений атрибута типа "Набор элементов произвольного справочника".
     * когда у атрибута есть скрипт фильтрации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00953
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00957
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$152300406
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать в ПДПС userEvent параметр логического типа booleanParam</li>
     * <li>Создать в ПДПС userEvent параметр типа "Набор элементов произвольного справочника" catalogAnyItemSetParam
     * со скриптом:
     * <pre>
     *     return form.%s ? [:] : ["$код": "$значение", "$код2": "$значение2"]
     * </pre>
     * и со скриптом фильтрации, который возвращает только первое значение:
     * <pre>
     *     return !form ? [:] : ["$код": "$значение"]
     * </pre>
     * </li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для catalogAnyItemSetParam, передав ПДПС userEvent</li>
     * <li>Проверить, что в ответе вернулся ключ "$код" с соответствующим значением</li>
     * </ol>
     */
    @Test
    public void testCatalogAnyItemSetWithFiltrationScript()
    {
        Map<String, String> originalMap = Map.of(
                ModelUtils.createCode(), ModelUtils.createTitle(),
                ModelUtils.createCode(), ModelUtils.createTitle()
        );
        Map<String, String> expectedMap = Map.ofEntries(originalMap.entrySet().iterator().next());

        FormParameter booleanParam = DAOFormParameter.createBool();
        ScriptInfo scriptInfo = createCatalogAnyItemScript(booleanParam, originalMap);
        ScriptInfo filtrationScriptInfo = createFiltrationScript(expectedMap);
        FormParameter catalogAnyItemSetParam = DAOFormParameter.createCatalogAnyItemSet(scriptInfo);
        DAOAttribute.changeToEditFilter(catalogAnyItemSetParam, filtrationScriptInfo);
        DSLFormParameter.saveOnEventAction(userEvent, booleanParam, catalogAnyItemSetParam);

        //Действия и проверки:
        Map<String, Object> objectTemplate = MobileObjectPropertiesBuilder.from(userBo).build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogAnyItemSetParam, objectTemplate)
                .setFormType(PARAMETRIZED_USER_ACTION)
                .setFormCode(userEvent.getUserEventUuid())
                .setObject(userBo);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V12);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogAnyItemSetParam)
                .assertValues(
                        elements(expectedMap)
                );
    }

    /**
     * Создать скрипт вычисления элементов справочника, который вернёт переданные значения в качестве результата:
     * <pre>
     *     return form.%s ? [:] : $values
     * </pre>
     *
     * @param values значения, которые должны вернуться в качестве результата скрипта
     */
    private static ScriptInfo createCatalogAnyItemScript(Attribute booleanParam, Map<String, String> values) // NOPMD
    {
        String valueString = GroovyUtils.mapToString(values);
        String script = String.format("return form.%s ? [:] : %s", booleanParam.getCode(), valueString);
        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(script);
        DSLScriptInfo.addScript(scriptInfo);
        return scriptInfo;
    }

    /**
     * Создать скрипт фильтрации значений для параметра типа "Элемент произвольного справочника", который вернёт
     * переданные значения в качестве результата:
     * <pre>
     *     return !form ? [] : $filtrationValues
     * </pre>
     *
     * @param filtrationValues значения, которые должны вернуться в качестве результата скрипта
     */
    private static ScriptInfo createFiltrationScript(Map<String, String> filtrationValues)
    {
        String valueString = GroovyUtils.mapToString(filtrationValues);
        String filtrationScript = String.format("return !form ? [] : %s", valueString);
        ScriptInfo filtrationScriptInfo = DAOScriptInfo.createNewScriptInfo(filtrationScript);
        DSLScriptInfo.addScript(filtrationScriptInfo);
        return filtrationScriptInfo;
    }
}
