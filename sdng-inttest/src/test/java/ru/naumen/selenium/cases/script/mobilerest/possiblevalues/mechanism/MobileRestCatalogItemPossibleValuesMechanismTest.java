package ru.naumen.selenium.cases.script.mobilerest.possiblevalues.mechanism;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_1;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.LIST;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.EditPresentationSelectType.TREE;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.*;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.CatalogItemType.EDIT_TREE;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на базовый механизм получения возможных значений атрибутов типа "Элемент справочника" на формах через
 * мобильное API.
 *
 * <AUTHOR>
 * @since 04.03.2024
 */
public class MobileRestCatalogItemPossibleValuesMechanismTest extends AbstractTestCase
{
    private static MetaClass userCase;
    private static Catalog catalog;
    private static Attribute catalogItemAttr, catalogItemTreeAttr;
    private static CatalogItem catalogFolder, catalogFolder2;
    private static CatalogItem catalogItem, catalogItem2, catalogItem3, catalogItem4;
    private static CatalogItem[] catalogItems;
    private static MobileAddForm addForm;
    private static MobileAuthentication licAuth;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать справочник catalog и папку catalogFolder в нём</li>
     * <li>Создать в справочнике catalog:
     * <ul>
     *     <li>папку catalogFolder,</li>
     *     <li>элементы catalogItem и catalogItem2,</li>
     *     <li>папку catalogFolder2 и элемент catalogItem3 внутри папки catalogFolder,</li>
     *     <li>элемент catalogItem4 внутри папки catalogFolder2</li>
     * </ul></li>
     * <li>Создать в справочнике catalog дополнительно ещё 20 элементов catalogItems для тестирования пагинации</li>
     * <li>Создать атрибут catalogItemAttr типа "Элемент справочника" в типе userCase, ссылающийся на справочник
     * catalog</li>
     * <li>Создать атрибут catalogItemTreeAttr типа "Элемент справочника" в типе userCase с представлением
     * "Дерево выбора", ссылающийся на справочник catalog</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут catalogItemAttr</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        MetaClass userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        catalog = DAOCatalog.createUser(true, true);
        DSLCatalog.add(catalog);

        catalogFolder = DAOCatalogItem.createUserFolder(catalog);
        catalogItem = DAOCatalogItem.createUser(catalog);
        catalogItem2 = DAOCatalogItem.createUser(catalog);
        DAOCatalogItem.appendCodePrefixes(0, 26, catalogFolder, catalogItem, catalogItem2);
        DSLCatalogItem.add(catalogFolder, catalogItem, catalogItem2);

        catalogFolder2 = DAOCatalogItem.createUserFolder(catalog, catalogFolder);
        DAOCatalogItem.appendCodePrefixes(3, 26, catalogFolder2);
        DSLCatalogItem.add(catalogFolder2);

        catalogItem3 = DAOCatalogItem.createUser(catalog, catalogFolder);
        catalogItem4 = DAOCatalogItem.createUser(catalog, catalogFolder2);
        DAOCatalogItem.appendCodePrefixes(4, 26, catalogItem3, catalogItem4);
        DSLCatalogItem.add(catalogItem3, catalogItem4);

        int expectedCount = 20;
        catalogItems = new CatalogItem[expectedCount];
        for (int i = 0; i < expectedCount; ++i)
        {
            catalogItems[i] = DAOCatalogItem.createUser(catalog);
        }
        DAOCatalogItem.appendCodePrefixes(6, 26, catalogItems);
        DSLCatalogItem.add(catalogItems);

        DSLSearch.updateIndex(catalogFolder, catalogFolder2, catalogItem, catalogItem2, catalogItem3, catalogItem4);
        DSLSearch.updateIndex(catalogItems);

        catalogItemAttr = DAOAttribute.createCatalogItem(userCase, catalog, null);
        catalogItemTreeAttr = DAOAttribute.createCatalogItem(userCase, catalog, null);
        catalogItemTreeAttr.setEditPresentation(EDIT_TREE);
        DSLAttribute.add(catalogItemAttr, catalogItemTreeAttr);

        addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, catalogItemAttr);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование получения возможных значений атрибута типа "Элемент справочника" с представлением "Список
     * элементов справочника". Папки возвращаться не должны. До версии 15 должны возвращаться все возможные
     * значения атрибута, в силу того что на клиентах ранее не поддерживалась пагинация.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107215033
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версий v13.1:<ul>
     *     <li>"Атрибут" = catalogItemAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий только из catalogItem, catalogItem2,
     * catalogItem3, catalogItem4 и всех объектов catalogItems</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий из catalogItem, catalogItem2,
     * catalogItem3, catalogItem4 и первых 16 объектов из catalogItems</li>
     * <li>Получить форму для версии 13.1:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут catalogItemAttr имеет тип выбора - null</li>
     * <li>Получить форму для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе атрибут catalogItemAttr имеет тип выбора - список</li>
     * </ol>
     */
    @Test
    public void testCatalogItem()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemAttr)
                .assertValues(
                        list(
                                element(catalogItem),
                                element(catalogItem2),
                                element(catalogItem3),
                                element(catalogItem4)
                        ).with(
                                elements(catalogItems)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemAttr, V15)
                .assertValues(
                        list(
                                element(catalogItem),
                                element(catalogItem2),
                                element(catalogItem3),
                                element(catalogItem4)
                        ).with(
                                elements(Arrays.copyOf(catalogItems, 16))
                        )
                );

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V13_1);

        formResponse.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse, false)
                .assertForAttribute(catalogItemAttr)
                .hasEditPresentationSelectType(null);

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(catalogItemAttr)
                .hasEditPresentationSelectType(LIST);
    }

    /**
     * Тестирование получения возможных значений атрибута типа "Элемент справочника" с представлением "Дерево выбора".
     * До версии 15 мобильного API должен возвращаться список, после - дерево.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107215033
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = catalogItemTreeAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemTreeAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из catalogItem, catalogItem2, catalogItem3,
     * catalogItem4 и всех объектов catalogItems</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, который состоит из catalogFolder, catalogItem,
     * catalogItem2 и первых 17 объектов из catalogItems, у не доступной для выбора папки catalogFolder есть дочерние
     * элементы, уровень дерева возвращён не полностью = true</li>
     * <li>Выполнить повторное получение возможных значений, дополнительно передав родителя - catalogFolder</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, который состоит из catalogFolder2 и
     * catalogItem3</li>
     * <li>Выполнить повторное получение возможных значений, дополнительно передав родителя - catalogFolder2</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, который состоит из catalogItem4, у не доступной
     * для выбора папки catalogFolder2 есть дочерние элементы</li>
     * <li>Получить форму для версии 15:<ul>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemTreeAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе атрибут catalogItemTreeAttr имеет тип выбора - дерево</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWithTreePresentation()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemTreeAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemTreeAttr)
                .assertValues(
                        list(
                                element(catalogItem),
                                element(catalogItem2),
                                element(catalogItem3),
                                element(catalogItem4)
                        ).with(
                                elements(catalogItems)
                        )
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemTreeAttr, V15).assertValues(
                tree(
                        treeElement(catalogFolder).leaf(false).selectable(false),
                        treeElement(catalogItem),
                        treeElement(catalogItem2)
                ).with(
                        treeElements(Arrays.copyOf(catalogItems, 17))
                ).hasMore(true)
        );

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .setParent(catalogFolder);
        ValidatableResponse pvResponse3 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

        pvResponse3.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse3, catalogItemTreeAttr, V15).assertValues(
                treeElement(catalogFolder2).leaf(false).selectable(false),
                treeElement(catalogItem3)
        );

        MobilePossibleValuesParams pvParams3 = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .setParent(catalogFolder2);
        ValidatableResponse pvResponse4 = DSLMobilePossibleValues.getPossibleValues(pvParams3, licAuth, V15);

        pvResponse4.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse4, catalogItemTreeAttr, V15).assertValues(
                treeElement(catalogItem4)
        );

        ValidatableResponse formResponse2 = DSLMobileForms.getForm(addForm, objectTemplate, licAuth, V15);

        formResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobileForms.assertForForm(formResponse2, false)
                .assertForAttribute(catalogItemTreeAttr)
                .hasEditPresentationSelectType(TREE);
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Элемент справочника".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107215033
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версий 13.1 и 15:<ul>
     *     <li>"Атрибут" = catalogItemAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название catalogItem,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся список объектов, состоящий только из catalogItem</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(catalogItem.getTitle());
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemAttr, version)
                    .assertValues(
                            element(catalogItem)
                    );
        }
    }

    /**
     * Тестирование поиска среди возможных значений атрибута типа "Элемент справочника" с представлением
     * "Дерево выбора".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = catalogItemTreeAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Строка поиска" = название catalogItem,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemTreeAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из catalogItem</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся уровень дерева объектов, состоящей из catalogItem</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWithTreePresentationWhenPerformsSearch()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemTreeAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(catalogItem.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemTreeAttr)
                .assertValues(
                        element(catalogItem)
                );

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemTreeAttr, V15)
                .assertValues(
                        treeSearch(
                                treeElement(catalogItem)
                        ).foundAmount(1)
                );
    }

    /**
     * Тестирование не возможности получения возможных значений с использованием пагинации для атрибута типа "Элемент
     * справочника". До версии 15 отсутствует пагинация значений.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107215033
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = catalogItemAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из последних 14 объектов catalogItems</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);

        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);
        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);
        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemAttr, V15)
                .assertValues(
                        list(
                                elements(Arrays.copyOfRange(catalogItems, 6, 20))
                        )
                );
    }

    /**
     * Тестирование не возможности получения возможных значений с использованием пагинации для атрибута типа "Элемент
     * справочника" с представлением "Дерево выбора". До версии 15 значения возвращаются списком без пагинации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = catalogItemTreeAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 10,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemTreeAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15:<ul>
     *     <li>"Атрибут" = catalogItemsTreeAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиги относительно начала списка для каждого из классов":<ul>
     *         <li>catalog = 10,</li>
     *     </ul></li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemsTreeAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся список объектов, состоящий из последних 13 объектов catalogItems</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWithTreePresentationWhenOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemTreeAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams
                .create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(10);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemTreeAttr).isEmpty();

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .addPosition(catalog, 10);
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemTreeAttr, V15)
                .assertValues(
                        treeElements(Arrays.copyOfRange(catalogItems, 7, 20))
                );
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Элемент справочника". Для поиска пагинация не применяется никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$107215033
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версий 13.1 и 15:<ul>
     *     <li>"Атрибут" = catalogItemAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Строка поиска" = название catalogItem,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemAttr = null,</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответах вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testCatalogItemWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(catalogItem.getTitle());
        for (MobileVersion version : List.of(V13_1, V15))
        {
            ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, version);
            pvResponse.statusCode(HttpStatus.SC_OK);
            DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemAttr).isEmpty();
        }
    }

    /**
     * Тестирование не возможности поиска среди возможных значений, одновременно с использованием пагинации, для
     * атрибута типа "Элемент справочника" с представлением "Дерево выбора". Для поиска пагинация не применяется
     * никогда.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для версии 13.1:<ul>
     *     <li>"Атрибут" = catalogItemTreeAttr,</li>
     *     <li>"Форма" = addForm,</li>
     *     <li>"Сдвиг относительно начала списка" = 1,</li>
     *     <li>"Строка поиска" = название userCase,</li>
     *     <li>"Объект": <ul>
     *         <li>"Тип объекта" = userCase,</li>
     *         <li>catalogItemTreeAttr = null</li>
     *     </ul></li>
     * </ul></li>
     * <li>Проверить, что в ответе вернулся пустой список</li>
     * <li>Получить возможные значения для версии 15 с аналогичными запросом</li>
     * <li>Проверить, что в ответе вернулся пустой уровень дерева объектов</li>
     * </ol>
     */
    @Test
    public void testCaseListWithTreePresentationWhenPerformsSearchAndOffsetMoreThenZero()
    {
        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(catalogItemTreeAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(catalogItemTreeAttr, objectTemplate)
                .setForm(addForm)
                .setOffset(1)
                .setSearchString(userCase.getTitle());
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_1);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, catalogItemTreeAttr).isEmpty();

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, catalogItemTreeAttr, V15).assertValues(treeSearch());
    }
}
