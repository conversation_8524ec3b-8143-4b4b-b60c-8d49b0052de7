package ru.naumen.selenium.cases.operator.dynamicfield;

import org.junit.BeforeClass;
import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.GUIBo;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.IntegerType;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.StringType;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.attr.SystemAttrEnum;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAODynamicField;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;

/**
 * Тестирование API динамических полей.
 * <AUTHOR>
 * @since Feb 20, 2024
 */
@IgnoreConfig(cause = "NSDPRD-30827", ignoreDb = { DbType.ORACLE })
public class DynamicFieldsApiTest extends AbstractTestCase
{
    private static MetaClass userCase;
    private static Attribute jsonAttr;
    private static Bo dynIntegerAttrBo;
    private static Bo dynStringAttrBo;
    private static Attribute dynIntegerAttr;
    private static Attribute dynStringAttr;
    private static ContentForm objectList;

    /**
     * Общая подготовка
     * <br>
     * <ol>
     * <li>Загрузить лицензию с поддержкой динамических полей</li>
     * <li>Создать пользовательский класс userClass (Жизненный цикл — да, Назначение ответственного — да) и
     * унаследованный от него тип userCase</li>
     * <li>В классе userClass создать атрибут jsonAttr типа «Значения динамических полей»</li>
     * <li>Создать шаблоны динамических полей:
     *   <ul>
     *     <li>dynStringAttr типа «Строка» с представлением для отображения «Строка»</li>
     *     <li>dynIntegerAttr типа «Целое число» с представлением для отображения «Целое число»</li>
     *   </ul>
     * </li>
     * <li>В классе userClass создать группу атрибутов attrGroup и добавить в нее атрибуты «Название» и jsonAttr</li>
     * <li>На карточку объекта класса userClass вывести список объектов objectList (Класс объектов — userClass,
     * Представление — Сложный список)</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);
        MetaClass userClass = DAOUserClass.createWithWFAndResp();
        userCase = DAOUserCase.create(userClass);
        jsonAttr = DAOAttribute.createDynamic(userClass);
        DSLMetainfo.add(userClass, userCase, jsonAttr);

        dynStringAttrBo = DAODynamicField.create(StringType.CODE, StringType.VIEW, StringType.EDIT);
        dynIntegerAttrBo = DAODynamicField.create(IntegerType.CODE, IntegerType.VIEW, IntegerType.EDIT);
        DSLBo.add(dynStringAttrBo, dynIntegerAttrBo);
        dynStringAttr = DAODynamicField.createDynamicAttribute(jsonAttr, dynStringAttrBo);
        dynIntegerAttr = DAODynamicField.createDynamicAttribute(jsonAttr, dynIntegerAttrBo);

        GroupAttr attrGroup = DAOGroupAttr.create(userClass);
        DSLGroupAttr.add(attrGroup, SysAttribute.title(userClass), jsonAttr);
        objectList = DAOContentCard.createObjectAdvList(userClass.getFqn(), attrGroup, userClass);
        DSLContent.add(objectList);
    }

    /**
     * Тестирование метода слияния значений динамических полей с условием замены по idHolder
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Заполнить динамические поля в объекте userBo с помощью скрипта:
     * <pre>
     *   utils.edit('userBo', [
     *     'jsonAttr': [
     *       'dynIntegerAttr': [
     *         [value: 44, idHolder: 'id1'],
     *         [value: 55, idHolder: 'id2']
     *       ],
     *       'dynStringAttr': [
     *         [value: 'first', idHolder: 'id1'],
     *         [value: 'second', idHolder: 'id2']
     *       ]
     *     ]
     *   ]);
     * </pre></li>
     * <br>
     * <b>Действия</b>
     * <li>Выполнить скрипт:
     * <pre>
     *   def subject = utils.get('userBo');
     *   def result = api.dynfield.mergeDynamicFieldValues(subject.jsonAttr, [
     *     'dynIntegerAttr': [
     *       [value: 99, idHolder: 'id1']
     *     ],
     *     'dynStringAttr': [
     *       [value: 'other second', idHolder: 'id2'],
     *       [value: 'third', idHolder: 'id3']
     *     ]
     *   ], { v1, v2 -> v1.idHolder != null && v1.idHolder.equals(v2.idHolder) });
     *   utils.edit(subject, ['jsonAttr': result]);
     * </pre></li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке objectList для объекта userBo dynIntegerAttr = 99, 55</li>
     * <li>В списке objectList для объекта userBo dynStringAttr = first, other second, third</li>
     * </ol>
     */
    @Test
    public void testMergeDynamicFieldsByIdHolder()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        ScriptRunner.executeScript(
                """
                        utils.edit('%s', [
                          '%s': [
                            '%s': [
                              [value: 44, idHolder: 'id1'],
                              [value: 55, idHolder: 'id2']
                            ],
                            '%s': [
                              [value: 'first', idHolder: 'id1'],
                              [value: 'second', idHolder: 'id2']
                            ]
                          ]
                        ]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        // Действия
        ScriptRunner.executeScript(
                """
                        def subject = utils.get('%s');
                        def result = api.dynfield.mergeDynamicFieldValues(subject.%s, [
                          '%s': [
                            [value: 99, idHolder: 'id1']
                          ],
                          '%s': [
                            [value: 'other second', idHolder: 'id2'],
                            [value: 'third', idHolder: 'id3']
                          ]
                        ], { v1, v2 -> v1.idHolder != null && v1.idHolder.equals(v2.idHolder) });
                        utils.edit(subject, ['%2$s': result]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        // Проверки
        objectList.advlist().content().asserts().attrValue(userBo, dynIntegerAttr, "99, 55");
        objectList.advlist().content().asserts().attrValue(userBo, dynStringAttr, "first, other second, third");
    }

    /**
     * Тестирование метода слияния значений динамических полей без условий
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Заполнить динамические поля в объекте userBo с помощью скрипта:
     * <pre>
     *   utils.edit('userBo', [
     *     'jsonAttr': [
     *       'dynIntegerAttr': [
     *         [value: 44],
     *         [value: 55]
     *       ],
     *       'dynStringAttr': [
     *         [value: 'first'],
     *         [value: 'second']
     *       ]
     *     ]
     *   ]);
     * </pre></li>
     * <br>
     * <b>Действия</b>
     * <li>Выполнить скрипт:
     * <pre>
     *   def subject = utils.get('userBo');
     *   def result = api.dynfield.mergeDynamicFieldValues(subject.jsonAttr, [
     *     'dynIntegerAttr': [
     *       [value: 99]
     *     ],
     *     'dynStringAttr': [
     *       [value: 'other second'],
     *       [value: 'third']
     *     ]
     *   ]);
     *   utils.edit(subject, ['jsonAttr': result]);
     * </pre></li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке objectList для объекта userBo dynIntegerAttr = 44, 55, 99</li>
     * <li>В списке objectList для объекта userBo dynStringAttr = first, second, other second, third</li>
     * </ol>
     */
    @Test
    public void testMergeDynamicFieldsWithoutCondition()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        ScriptRunner.executeScript(
                """
                        utils.edit('%s', [
                          '%s': [
                            '%s': [
                              [value: 44],
                              [value: 55]
                            ],
                            '%s': [
                              [value: 'first'],
                              [value: 'second']
                            ]
                          ]
                        ]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        // Действия
        ScriptRunner.executeScript(
                """
                        def subject = utils.get('%s');
                        def result = api.dynfield.mergeDynamicFieldValues(subject.%s, [
                          '%s': [
                            [value: 99]
                          ],
                          '%s': [
                            [value: 'other second'],
                            [value: 'third']
                          ]
                        ]);
                        utils.edit(subject, ['%2$s': result]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        // Проверки
        objectList.advlist().content().asserts().attrValue(userBo, dynIntegerAttr, "44, 55, 99");
        objectList.advlist().content().asserts().attrValue(userBo, dynStringAttr, "first, second, other second, third");
    }

    /**
     * Тестирование метода удаления значений динамических полей с условием удаления по idHolder
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Заполнить динамические поля в объекте userBo с помощью скрипта:
     * <pre>
     *   utils.edit('userBo', [
     *     'jsonAttr': [
     *       'dynIntegerAttr': [
     *         [value: 44, idHolder: 'id1'],
     *         [value: 55, idHolder: 'id2']
     *       ],
     *       'dynStringAttr': [
     *         [value: 'first', idHolder: 'id1'],
     *         [value: 'second', idHolder: 'id2']
     *       ]
     *     ]
     *   ]);
     * </pre></li>
     * <br>
     * <b>Действия</b>
     * <li>Выполнить скрипт:
     * <pre>
     *   def subject = utils.get('userBo');
     *   def result = api.dynfield.removeDynamicFieldValues(subject.jsonAttr, ['dynIntegerAttr'],
     *       { v -> v.idHolder != null && v.idHolder.equals('id1') });
     *   utils.edit(subject, ['jsonAttr': result]);
     * </pre></li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке objectList для объекта userBo dynIntegerAttr = 55</li>
     * <li>В списке objectList для объекта userBo dynStringAttr = first, second</li>
     * </ol>
     */
    @Test
    public void testRemoveDynamicFieldsByIdHolder()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        ScriptRunner.executeScript(
                """
                        utils.edit('%s', [
                          '%s': [
                            '%s': [
                              [value: 44, idHolder: 'id1'],
                              [value: 55, idHolder: 'id2']
                            ],
                            '%s': [
                              [value: 'first', idHolder: 'id1'],
                              [value: 'second', idHolder: 'id2']
                            ]
                          ]
                        ]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        // Действия
        ScriptRunner.executeScript(
                """
                        def subject = utils.get('%s');
                        def result = api.dynfield.removeDynamicFieldValues(subject.%s, ['%s'],
                            { v -> v.idHolder != null && v.idHolder.equals('id1') });
                        utils.edit(subject, ['%2$s': result]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        // Проверки
        objectList.advlist().content().asserts().attrValue(userBo, dynIntegerAttr, "55");
        objectList.advlist().content().asserts().attrValue(userBo, dynStringAttr, "first, second");
    }

    /**
     * Тестирование метода удаления значений динамических полей без условий
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Заполнить динамические поля в объекте userBo с помощью скрипта:
     * <pre>
     *   utils.edit('userBo', [
     *     'jsonAttr': [
     *       'dynIntegerAttr': [
     *         [value: 44, idHolder: 'id1'],
     *         [value: 55, idHolder: 'id2']
     *       ],
     *       'dynStringAttr': [
     *         [value: 'first', idHolder: 'id1'],
     *         [value: 'second', idHolder: 'id2']
     *       ]
     *     ]
     *   ]);
     * </pre></li>
     * <br>
     * <b>Действия</b>
     * <li>Выполнить скрипт:
     * <pre>
     *   def subject = utils.get('userBo');
     *   def result = api.dynfield.removeDynamicFieldValues(subject.jsonAttr, ['dynIntegerAttr']);
     *   utils.edit(subject, ['jsonAttr': result]);
     * </pre></li>
     * <li>Войти в систему под сотрудником</li>
     * <li>Перейти на карточку объекта userBo</li>
     * <br>
     * <b>Проверки</b>
     * <li>В списке objectList не отображается динамическое поле dynIntegerAttr</li>
     * <li>В списке objectList для объекта userBo dynStringAttr = first, second</li>
     * </ol>
     */
    @Test
    public void testRemoveDynamicFieldsWithoutConditions()
    {
        // Подготовка
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        ScriptRunner.executeScript(
                """
                        utils.edit('%s', [
                          '%s': [
                            '%s': [
                              [value: 44, idHolder: 'id1'],
                              [value: 55, idHolder: 'id2']
                            ],
                            '%s': [
                              [value: 'first', idHolder: 'id1'],
                              [value: 'second', idHolder: 'id2']
                            ]
                          ]
                        ]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid(),
                dynStringAttrBo.getUuid());

        // Действия
        ScriptRunner.executeScript(
                """
                        def subject = utils.get('%s');
                        def result = api.dynfield.removeDynamicFieldValues(subject.%s, ['%s']);
                        utils.edit(subject, ['%2$s': result]);
                        """, userBo.getUuid(), jsonAttr.getCode(), dynIntegerAttrBo.getUuid());

        GUILogon.asTester();
        GUIBo.goToCard(userBo);

        // Проверки
        objectList.advlist().content().asserts().columnNames(false, true,
                SystemAttrEnum.TITLE.getTitle(), dynStringAttr.getTitle());
        objectList.advlist().content().asserts().attrValue(userBo, dynStringAttr, "first, second");
    }
}
