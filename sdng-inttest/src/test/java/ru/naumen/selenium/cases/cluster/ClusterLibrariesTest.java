package ru.naumen.selenium.cases.cluster;

import static ru.naumen.selenium.cases.cluster.ClusterTestUtils.checkTest;

import java.nio.file.Paths;

import org.junit.Test;

import ru.naumen.selenium.casesutil.admin.library.DSLLibrary;
import ru.naumen.selenium.casesutil.script.DSLModuleConf;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;

/**
 * Тестирование синхронизации кластера при загрузке библиотек
 * <AUTHOR>
 * @since 11.12.2024
 */
public class ClusterLibrariesTest extends AbstractClusterSynchronizationTestCase
{
    private static final String MODULE_CODE = "librariesScriptTestModule";

    /**
     * Тестирование корректности синхронизации кластера при одновременном удалении
     * и загрузке новой версии той же самой библиотеки.
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/additionalLibraries
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$290513719
     * <ol>
     *     <b>Подготовка</b>
     *     <li>Загрузить библиотеку library-script-module-test.jar</li>
     * </ol>
     * <ol>
     *     <b>Действия и проверки</b>
     *     <li>Выполнить скрипт в консоли:
     *     <pre>api.libraries.delete('library-script-module-test.jar')</pre>
     *     </li>
     *     <li>Загрузить библиотеку library-script-module-test.jar </li>
     *     <li>Перейти на бэк-ноду и провести на ней синхронизацию</li>
     *     <li>Проверить, что модуль librariesScriptTestModule присутствует в системе</li>
     * </ol>
     */
    @Test
    public void testSynchronizationUploadSameLibrary()
    {
        //Подготовка
        DSLLibrary.uploadLibrary(DSLLibrary.TEST_JAR_WITH_MODULE_LIBRARY_PATH);
        ClusterTestUtils.synchronizeCluster();
        //Действия и проверки
        final String libName = Paths.get(DSLLibrary.TEST_JAR_WITH_MODULE_LIBRARY_PATH).getFileName().toString();
        ScriptRunner.executeScript("api.libraries.delete('%s')", libName);
        DSLLibrary.uploadLibrary(DSLLibrary.TEST_JAR_WITH_MODULE_LIBRARY_PATH);
        checkTest(standType -> DSLModuleConf.assertPresent(MODULE_CODE));
    }
}