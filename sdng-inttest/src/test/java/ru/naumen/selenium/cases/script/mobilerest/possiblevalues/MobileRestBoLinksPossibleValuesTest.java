package ru.naumen.selenium.cases.script.mobilerest.possiblevalues;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V13_2;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.V15;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.folderElement;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.tree;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.treeElement;
import static ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators.PossibleValuesValidators.treeSearch;
import static ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BOLinksType.WITH_FOLDER;

import java.util.Collections;
import java.util.Map;

import org.apache.http.HttpStatus;
import org.junit.BeforeClass;
import org.junit.Test;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.admin.DSLFolder;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.bo.DSLSearch;
import ru.naumen.selenium.casesutil.metainfo.DSLMetainfo;
import ru.naumen.selenium.casesutil.mobile.DSLMobile;
import ru.naumen.selenium.casesutil.mobile.rest.auth.DSLMobileAuth;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.DSLMobilePossibleValues;
import ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.MobilePossibleValuesParams;
import ru.naumen.selenium.casesutil.model.admin.DAOFolder;
import ru.naumen.selenium.casesutil.model.admin.Folder;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.SysAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAOBo;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.DAOMobile;
import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;
import ru.naumen.selenium.casesutil.model.mobile.MobileAuthentication;
import ru.naumen.selenium.casesutil.model.script.DAOScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.script.DSLScriptInfo;
import ru.naumen.selenium.core.AbstractTestCase;
import ru.naumen.selenium.init.SharedFixture;

/**
 * Тесты на получение возможных значений атрибутов типа "Набор ссылок на БО" на формах.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class MobileRestBoLinksPossibleValuesTest extends AbstractTestCase
{
    private static MetaClass userClass, userCase;
    private static Folder userFolder;
    private static MobileAuthentication licAuth;

    /**
     * <b>Подготовка</b>
     * <ol>
     * <li>Создать пользовательский класс userClass и его подтип userCase</li>
     * <li>Создать папку userFolder в классе userClass</li>
     * <li>Загрузить файл лицензии с модулем мобильного приложения</li>
     * <li>Создать ключ доступа</li>
     * </ol>
     */
    @BeforeClass
    public static void prepareFixture()
    {
        userClass = DAOUserClass.create();
        userCase = DAOUserCase.create(userClass);
        DSLMetainfo.add(userClass, userCase);

        userFolder = DAOFolder.create(userClass);
        DSLFolder.add(userFolder);

        DSLAdmin.installLicense(DSLAdmin.MOBILE_LICENSE_PATH);

        licAuth = DSLMobileAuth.authAs(SharedFixture.employee());
    }

    /**
     * Тестирование сброса кэша возможных значений при повторном открытии формы для атрибута типа "Набор ссылок на БО"
     * с представлением "Список выбора с папками", когда у атрибута есть скрипт фильтрации.
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00637
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$262494576
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$128882491
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объекты userBo и userBo2 типа userCase и добавить объект userBo в папку userFolder</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, со ссылкой на объекты типа userCase,
     * с представлением "Список выбора с папками" и скриптом фильтрации:
     * <pre>
     * -------------------------------------------------------------------------------
     *     if (!subject) return []
     *     return ['$userBo', '$userBo2']
     * -------------------------------------------------------------------------------
     *     Где:
     *     1) $userBo - объект userBo;
     *     2) $userBo - объект userBo2.
     * </pre></li>
     * <li>Создать в МК форму addForm, на которую вывести атрибуты boLinksAttr</li>
     * <br>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для boLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответе вернулись папка userFolder и объект userBo2</li>
     * <li>Удалить объект userBo из папки userFolder</li>
     * <li>Получить форму добавления объекта addForm</li>
     * <li>Получить возможные значения для boLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответе вернулись объекты userBo, userBo2</li>
     * </ol>
     */
    @Test
    public void testBoLinksWithFoldersAndFiltrationScript()
    {
        Attribute folderAttr = SysAttribute.folders(userClass);
        folderAttr.setItemValue(userFolder);
        Bo userBo = DAOUserBo.create(userCase, folderAttr);
        Bo userBo2 = DAOUserBo.create(userCase);
        DAOBo.appendTitlePrefixes(userBo);
        DSLBo.add(userBo, userBo2);
        DSLSearch.updateIndex(userBo, userBo2);

        ScriptInfo scriptInfo = DAOScriptInfo.createNewScriptInfo(
                "(!subject) ? [] : ['%s', '%s']", userBo.getUuid(), userBo2.getUuid());
        DSLScriptInfo.addScript(scriptInfo);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userCase);
        boLinksAttr.setEditPresentation(WITH_FOLDER);
        DAOAttribute.changeToEditFilter(boLinksAttr, scriptInfo);
        DSLAttribute.add(boLinksAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, boLinksAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(boLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(boLinksAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, boLinksAttr, V15)
                .assertValues(
                        folderElement(userFolder),
                        treeElement(userBo2)
                );

        folderAttr.setValue(null);
        DSLBo.editAttributeValue(userBo, folderAttr);

        ValidatableResponse formResponse = DSLMobileForms.getForm(addForm, Collections.emptyMap(), licAuth, V15);

        formResponse.statusCode(HttpStatus.SC_OK);

        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V15);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, boLinksAttr, V15)
                .assertValues(
                        treeElement(userBo),
                        treeElement(userBo2)
                );
    }

    /**
     * Тестирование отсутствия возможных значений для атрибута типа "Набор ссылок на БО", когда
     * ограничения по доступным типам установлены в "Ни один".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00097
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00715
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308333508
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать атрибут boLinksAttr типа "Набор ссылок на БО" в типе userCase, ссылающийся на объекты класса
     * userClass, и имеющий ограничение по доступным типам - "Ни один"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут boLinksAttr</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для boLinksAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответ вернулся пустой список</li>
     * <li>Выполнить поиск по возможным значениям для boLinksAttr, передав тип userCase, форму добавления addForm,
     * и поисковую строку c названием объекта userBo</li>
     * <li>Проверить, что в ответ вернулся пустой список</li>
     * </ol>
     */
    @Test
    public void testBoLinksWhenPermittedTypesIsNoOne()
    {
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLSearch.updateIndex(userBo);

        Attribute boLinksAttr = DAOAttribute.createBoLinks(userCase, userClass);
        DSLAttribute.add(boLinksAttr);
        DSLAttribute.editPermittedLinks(boLinksAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, boLinksAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(boLinksAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(boLinksAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, boLinksAttr).isEmpty();

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(boLinksAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, boLinksAttr).isEmpty();
    }

    /**
     * Тестирование отсутствия возможных значений для атрибута типа "Набор ссылок на БО" с представлением "Список
     * выбора с папками", когда ограничения по доступным типам установлены в "Ни один".
     * <br>
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00097
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00715
     * https://naupp.naumen.ru/sd/operator/#uuid:smrmTask$308333508
     * <br>
     * <ol>
     * <b>Подготовка:</b>
     * <li>{@link #prepareFixture() Общая часть подготовки}</li>
     * <li>Создать объект userBo типа userCase</li>
     * <li>Создать атрибут boLinksFoldersAttr типа "Набор ссылок на БО" в типе userCase, ссылающийся на объекты класса
     * userClass, с представлением "Список выбора с папками", и имеющий ограничение по доступным типам - "Ни один"</li>
     * <li>Создать в МК форму addForm, на которую вывести атрибут boLinksFoldersAttr</li>
     * <b>Выполнение действий и проверки:</b>
     * <li>Получить возможные значения для boLinksFoldersAttr, передав тип userCase и форму добавления addForm</li>
     * <li>Проверить, что в ответ вернулось пустое дерево</li>
     * <li>Выполнить поиск по возможным значениям для boLinksFoldersAttr, передав тип userCase, форму добавления
     * addForm, и поисковую строку c названием объекта userBo</li>
     * <li>Проверить, что в ответ вернулось пустое дерево</li>
     * </ol>
     */
    @Test
    public void testBoLinksWithFoldersWhenPermittedTypesIsNoOne()
    {
        Bo userBo = DAOUserBo.create(userCase);
        DSLBo.add(userBo);
        DSLSearch.updateIndex(userBo);

        Attribute boLinksFoldersAttr = DAOAttribute.createBoLinks(userCase, userClass);
        boLinksFoldersAttr.setEditPresentation(WITH_FOLDER);
        DSLAttribute.add(boLinksFoldersAttr);
        DSLAttribute.editPermittedLinks(boLinksFoldersAttr);

        MobileAddForm addForm = DAOMobile.createMobileAddForm(userClass);
        addForm.setCases(userCase);
        DSLMobile.add(addForm);
        DSLMobile.addAttributes(addForm, boLinksFoldersAttr);

        Map<String, Object> objectTemplate = new MobileObjectPropertiesBuilder()
                .setMetaClass(userCase)
                .setAttribute(boLinksFoldersAttr, null)
                .build();
        MobilePossibleValuesParams pvParams = MobilePossibleValuesParams.create(boLinksFoldersAttr, objectTemplate)
                .setForm(addForm);
        ValidatableResponse pvResponse = DSLMobilePossibleValues.getPossibleValues(pvParams, licAuth, V13_2);

        pvResponse.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse, boLinksFoldersAttr, V13_2).assertValues(tree());

        MobilePossibleValuesParams pvParams2 = MobilePossibleValuesParams.create(boLinksFoldersAttr, objectTemplate)
                .setForm(addForm)
                .setSearchString(userBo.getTitle());
        ValidatableResponse pvResponse2 = DSLMobilePossibleValues.getPossibleValues(pvParams2, licAuth, V13_2);

        pvResponse2.statusCode(HttpStatus.SC_OK);
        DSLMobilePossibleValues.assertForAttribute(pvResponse2, boLinksFoldersAttr, V13_2).assertValues(treeSearch());
    }
}
