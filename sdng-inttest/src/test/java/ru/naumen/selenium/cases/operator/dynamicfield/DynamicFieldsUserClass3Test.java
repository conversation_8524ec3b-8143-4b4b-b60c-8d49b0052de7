package ru.naumen.selenium.cases.operator.dynamicfield;

import java.io.File;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.admin.DSLAdmin;
import ru.naumen.selenium.casesutil.attr.DSLAttribute;
import ru.naumen.selenium.casesutil.attr.DSLGroupAttr;
import ru.naumen.selenium.casesutil.bo.DSLBo;
import ru.naumen.selenium.casesutil.catalog.DSLCatalog;
import ru.naumen.selenium.casesutil.catalog.DSLCatalogItem;
import ru.naumen.selenium.casesutil.content.DSLContent;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListFastFilterForm;
import ru.naumen.selenium.casesutil.dynamicfield.DSLDynamicFieldConfiguration;
import ru.naumen.selenium.casesutil.file.DSLFile;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.attr.DAOGroupAttr;
import ru.naumen.selenium.casesutil.model.attr.GroupAttr;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.bo.DAODynamicField;
import ru.naumen.selenium.casesutil.model.bo.DAOEmployee;
import ru.naumen.selenium.casesutil.model.bo.DAOOu;
import ru.naumen.selenium.casesutil.model.bo.DAOUserBo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.catalogitem.DAOCatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentCard;
import ru.naumen.selenium.casesutil.model.file.DAOSdFile;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.metaclass.DAORootClass;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserCase;
import ru.naumen.selenium.casesutil.model.metaclass.DAOUserClass;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.user.GUILogon;
import ru.naumen.selenium.core.AbstractTestCaseJ5;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.config.DbType;
import ru.naumen.selenium.core.rules.ConfigRule.IgnoreConfig;
import ru.naumen.selenium.init.SharedFixture;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.Json;

/**
 * Тестирование динамических полей в интерфейсе оператора с настройкой шаблона на пользовательских классах.
 * <AUTHOR>
 * @since 20.11.2024
 */
@IgnoreConfig(cause = "NSDPRD-32357", ignoreDb = { DbType.ORACLE })
class DynamicFieldsUserClass3Test extends AbstractTestCaseJ5
{
    private static ContentForm userList;
    private static Bo user1, user2, attrsGroupBo, doubleBo, boLinksBo, objectLinkBo, stringBo, timeIntervalBo,
            randomBo, textBo, fileBo, integerBo;
    private static Attribute dynAttrs;
    private static final String SCRIPT_PATTERN = "utils.edit('%s', [dynAttrs: ['%s': ['%s': [value: '%s']]]])";

    /**
     * Общая подготовка
     * <ol>
     * <br>
     * <li>Загрузить на стенд лицензионный файл с модулем Динамические поля (dynamicField)</li>
     * <li>Создать пользовательский класс "Шаблон динамического атрибута" (код attrs) и вложенными в него типами:</li>
     *  <ul>
     *      <li>"Произвольный тип" (код randAttr)</li>
     *      <li>"Строка" (код attrString)</li>
     *      <li>"Временной интервал" (код attrDtInterval)</li>
     *      <li>"Текст" (код attrText)</li>
     *      <li>"Набор ссылок на БО: Отдел" (код attrMultOu)</li>
     *      <li>"Ссылка на БО: Сотрудник" (код attrBoEm)</li>
     *      <li>"Файл" (код attrFile)</li>
     *  </ul>
     * <li>Создать пользовательский класс "Группа атрибутов" (код attrGroups) с типом "Группа атрибутов" (код
     * attrGroups)</li>
     * <li>Создать пользовательский справочник "Представление" (код visor) с элементами: Целое число (attrInteger),
     * Вещественное число (attrDouble)</li>
     * <li>В класс Шаблон динамического атрибута (attrs) добавить атрибуты:</li>
     *   <ul>
     *      <li>Название = Очередность</li>
     *      <li>Код = sequenceAttr</li>
     *      <li>Тип = Целое число</li>
     *      <br>
     *      <li>Название = Видимый в списках</li>
     *      <li>Код = visible</li>
     *      <li>Тип = Логический</li>
     *      <li>Значение по умолчанию = да</li>
     *      <br>
     *      <li>Название = Группы атрибутов</li>
     *      <li>Код = attrGroups</li>
     *      <li>Тип = Набор ссылок на БО</li>
     *      <li>Класс = Группа атрибутов</li>
     *      <br>
     *      <li>Название = Представление</li>
     *      <li>Код = visor</li>
     *      <li>Тип = Элемент справочника</li>
     *      <li>Класс = Представление</li>
     *   </ul>
     * </li>
     * <li>В класс Группа атрибутов (attrGroups) добавить атрибуты:</li>
     *   <ul>
     *     <li>Название = Видимый в списках</li>
     *     <li>Код = visible</li>
     *     <li>Тип = Логический</li>
     *     <li>Значение по умолчанию = да</li>
     *     <br>
     *     <li>Название = Атрибуты</li>
     *     <li>Код = attrs</li>
     *     <li>Тип = Обратная ссылка</li>
     *     <li>Класс = Шаблон динамического атрибута/Группы атрибутов</li>
     *   </ul>
     * <li>В классе Шаблон динамического атрибута добавить в группу атрибутов Системные атрибуты созданные в ш. 5
     * атрибуты: Очередность, Видимый в списках, Группы атрибутов, Представление</li>
     * <li>В классе Группа атрибутов добавить в группу атрибутов Системные атрибуты созданные в ш. 6 атрибуты:
     * Видимая в списках, Атрибуты</li>
     * <li>Загрузить на стенд файл конфигурации динамических атрибутов dynamic-fields.xml, прикреплённый к задаче</li>
     * <li>Выполнить скрипт загрузки конфигурации из файла, который прикрепили в ш. 9, где file$XXX - UUID этого файла
     * <br>beanFactory.getBean('dynamicFieldConfigurationLoader').reloadFromFile('file$XXX')</li>
     * <li>Создать объекты класса Шаблон динамического атрибута, название объекта совпадает с названием типа объекта,
     * который создаём: Произвольный тип, Временной интервал, Набор ссылок на БО: Отдел, Ссылка на БО: Сотрудник,
     * Строка, Текст, Файл
     * <br>И ещё два объекта, у которых тип не совпадает с названием, заполнив атрибуты:</li>
     *   <ul>
     *      <li>Название = Целое число</li>
     *      <li>Тип объекта = Строка</li>
     *      <li>Представление(атрибут) = Целое число(элемент справочника)</li>
     *      <br>
     *      <li>Название = Вещественное число</li>
     *      <li>Тип объекта = Строка</li>
     *      <li>Представление(атрибут) = Вещественное число(элемент справочника)</li>
     *   </ul>
     * <li>Создать объект Группа атрибутов типа Группа атрибутов, заполнить атрибут "Атрибуты" = все созданные
     * объекты в ш. 11</li>
     * <li>Создать класс userClass с типом userCase</li>
     * <li>В класс userClass добавить атрибут dynAttrs, тип = Значения динамических полей и вывести его в группу
     * атрибутов Системные атрибуты</li>
     * <li>Вывести на карточку Компании, вкладка Атрибуты компании контент типа Список объектов: Название/код =
     * userList, Класс = userClass, Группа = Системные атрибуты</li>
     * <li>Создать два объекта типа userCase: user1 и user2</li>
     * </ol>
     */
    @BeforeAll
    public static void prepareFixture()
    {
        DSLAdmin.installLicense(DSLAdmin.DYNAMIC_FIELD_LICENSE);

        MetaClass tempDinAttrClass = DAOUserClass.createWithCode("attrs");
        tempDinAttrClass.setTitle("Шаблон динамического атрибута");

        MetaClass randomCase = DAOUserCase.create(tempDinAttrClass, "randAttr", "Произвольный тип");
        MetaClass stringCase = DAOUserCase.create(tempDinAttrClass, "attrString", "Строка");
        MetaClass dtIntervalCase = DAOUserCase.create(tempDinAttrClass, "attrDtInterval", "Временной интервал");
        MetaClass textCase = DAOUserCase.create(tempDinAttrClass, "attrText", "Текст");
        MetaClass multOuCase = DAOUserCase.create(tempDinAttrClass, "attrMultOu", "Набор ссылок на БО: Отдел");
        MetaClass boEmCase = DAOUserCase.create(tempDinAttrClass, "attrBoEm", "Ссылка на БО: Сотрудник");
        MetaClass fileCase = DAOUserCase.create(tempDinAttrClass, "attrFile", "Файл");
        DSLMetaClass.add(tempDinAttrClass, randomCase, stringCase, dtIntervalCase, textCase, multOuCase, boEmCase,
                fileCase);

        MetaClass groupsClass = DAOUserClass.createWithCode("attrGroups");
        groupsClass.setTitle("Группа атрибутов");
        MetaClass groupsCase = DAOUserCase.create(groupsClass, "attrGroups", "Группа атрибутов");
        DSLMetaClass.add(groupsClass, groupsCase);

        Catalog catalog = DAOCatalog.createUser(false, false); // Может нужны другие параметры
        catalog.setTitle("Представление");
        catalog.setCode("visor");
        DSLCatalog.add(catalog);

        CatalogItem integerItem = DAOCatalogItem.createUser(catalog);
        integerItem.setTitle("Целое число");
        integerItem.setCode("attrInteger");
        CatalogItem doubleItem = DAOCatalogItem.createUser(catalog);
        doubleItem.setTitle("Вещественное число");
        doubleItem.setCode("attrDouble");
        DSLCatalogItem.add(integerItem, doubleItem);

        Attribute sequenceAttr = DAOAttribute.createInteger(tempDinAttrClass);
        sequenceAttr.setTitle("Очередность");
        sequenceAttr.setCode("sequenceAttr");

        Attribute visibleAttr = DAOAttribute.createBool(tempDinAttrClass);
        visibleAttr.setTitle("Видимый в списках");
        visibleAttr.setCode("visible");
        visibleAttr.setDefaultValue("true");

        Attribute attrGroups = DAOAttribute.createBoLinks(tempDinAttrClass, groupsClass);
        attrGroups.setTitle("Группы атрибутов");
        attrGroups.setCode("attrGroups");

        Attribute visorAttr = DAOAttribute.createCatalogItem(tempDinAttrClass, catalog,
                null);  // Попробовать поменять нулл
        visorAttr.setTitle("Представление");
        visorAttr.setCode("visor");

        DSLAttribute.add(sequenceAttr, visibleAttr, attrGroups, visorAttr);

        Attribute visibleAttr2 = DAOAttribute.createBool(groupsClass);
        visibleAttr2.setTitle("Видимая в списках");
        visibleAttr2.setCode("visible");
        visibleAttr2.setDefaultValue("true");

        Attribute attrsAttr = DAOAttribute.createBackBOLinks(groupsClass, attrGroups);
        attrsAttr.setTitle("Атрибуты");
        attrsAttr.setCode("attrs");

        DSLAttribute.add(visibleAttr2, attrsAttr);

        GroupAttr tempDirSysGroup = DAOGroupAttr.createSystem(tempDinAttrClass);
        DSLGroupAttr.edit(tempDirSysGroup, new Attribute[] { sequenceAttr, visibleAttr, attrGroups, visorAttr },
                new Attribute[] {});

        GroupAttr groupsSysGroup = DAOGroupAttr.createSystem(groupsClass);
        DSLGroupAttr.edit(groupsSysGroup, new Attribute[] { visibleAttr2, attrsAttr }, new Attribute[] {});

        String configXml = FileUtils.readAll(DSLFile.DYN_FIELDS);
        DSLDynamicFieldConfiguration.reload(configXml);
        Cleaner.afterAllTest(true, DSLDynamicFieldConfiguration::reload);

        randomBo = DAOUserBo.create(randomCase);
        randomBo.setTitle("Произвольный тип");

        stringBo = DAOUserBo.create(stringCase);
        stringBo.setTitle("Строка");

        timeIntervalBo = DAOUserBo.create(dtIntervalCase);
        timeIntervalBo.setTitle("Временной интервал");

        textBo = DAOUserBo.create(textCase);
        textBo.setTitle("Текст");

        boLinksBo = DAOUserBo.create(multOuCase);
        boLinksBo.setTitle("Набор ссылок на БО: Отдел");

        objectLinkBo = DAOUserBo.create(boEmCase);
        objectLinkBo.setTitle("Ссылка на БО: Сотрудник");

        fileBo = DAOUserBo.create(fileCase);
        fileBo.setTitle("Файл");

        DSLBo.add(randomBo, stringBo, timeIntervalBo, textBo, boLinksBo, objectLinkBo, fileBo);

        integerBo = DAOUserBo.create(stringCase);
        integerBo.setTitle("Целое число");

        doubleBo = DAOUserBo.create(stringCase);
        doubleBo.setTitle("Вещественное число");

        DSLBo.add(integerBo, doubleBo);

        visorAttr.setValue(integerItem.getUuid());
        DSLBo.editAttributeValue(integerBo, visorAttr);

        visorAttr.setValue(doubleItem.getUuid());
        DSLBo.editAttributeValue(doubleBo, visorAttr);

        attrsGroupBo = DAOUserBo.create(groupsCase);
        attrsGroupBo.setTitle("Группа атрибутов");
        DSLBo.add(attrsGroupBo);

        attrsAttr.setValue(
                Json.listToString(randomBo.getUuid(), stringBo.getUuid(), timeIntervalBo.getUuid(), textBo.getUuid(),
                        boLinksBo.getUuid(), objectLinkBo.getUuid(), fileBo.getUuid(), integerBo.getUuid(),
                        doubleBo.getUuid()));
        DSLBo.editAttributeValue(attrsGroupBo, attrsAttr);

        MetaClass userClass = DAOUserClass.create();
        MetaClass userCase = DAOUserCase.create(userClass);
        DSLMetaClass.add(userClass, userCase);

        dynAttrs = DAOAttribute.createDynamic(userClass);
        dynAttrs.setCode("dynAttrs");
        dynAttrs.setTitle("dynAttrs");
        DSLAttribute.add(dynAttrs);

        GroupAttr userSysGroup = DAOGroupAttr.createSystem(userClass);
        DSLGroupAttr.edit(userSysGroup, new Attribute[] { dynAttrs }, new Attribute[] {});

        userList = DAOContentCard.createObjectAdvList(DAORootClass.create(), userSysGroup, userClass);
        userList.setTitle("userList");
        userList.setCode("userList");
        DSLContent.add(userList);

        user1 = DAOUserBo.create(userCase);
        user1.setTitle("user1");
        user2 = DAOUserBo.create(userCase);
        user2.setTitle("user2");
        DSLBo.add(user1, user2);
    }

    /**
     * Тестирование фильтрации и сортировки динамического атрибута типа Вещественное число
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Вещественное число': [value: '11.1']]
     *   ]
     * ])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Вещественное число': [value: '8']]
     *   ]
     * ])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList открыть форму быстрой фильтрации атрибута Вещественное число</li>
     * <li>Отфильтровать по критерию "не равно 11.1"</li>
     * <li>Проверить, что в списке один объект user2.</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "больше 10"</li>
     * <li>Проверить, что в списке один объект user1</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не пусто"</li>
     * <li>Проверить, что в списке два объекта user1 и user2.</li>
     * <li>Нажать на название столбца Вещественное число</li>
     * <li>Проверить, что список отсортирован в порядке user2, user1</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfDoubleDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), doubleBo.getUuid(), "11.1");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), doubleBo.getUuid(), "8");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute doubleDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, doubleBo);

        userList.advlist().content().clickFastFilterButton(doubleDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("11.1");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), true, true);

        userList.advlist().content().clickFastFilterButton(doubleDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.GREATER);
        GUIAdvListFastFilterForm.setConditionString("10");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1), true, true);

        userList.advlist().content().clickFastFilterButton(doubleDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_EMPTY);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        userList.advlist().content().clickColumnName(doubleDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2, user1), true, true);
    }

    /**
     * Тестирование фильтрации и сортировки динамических атрибутов типов: Набор ссылок на БО, Ссылка на БО
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Создать объекты ou1 и ou2 класса Отдел и объект empl класса Сотрудник в отделе ou1.</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Ссылка на БО: Сотрудник': [value: 'UUID сотрудника empl'],
     *     'UUID объекта Набор ссылок на БО: Отдел': [[value: 'UUID отдела ou1'], [value: 'UUID отдела ou2']]
     *   ]
     * ]])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Ссылка на БО: Сотрудник': [value: 'UUID сотрудника empl'],
     *     'UUID объекта Набор ссылок на БО: Отдел': [value: 'UUID отдела ou1']]
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList отфильтровать атрибут Ссылка на БО: Сотрудник по критерию "содержит empl"</li>
     * <li>Проверить, что в списке два объекта user1 и user2</li>
     * <li>Отфильтровать атрибут по Набор ссылок на БО по критерию "не содержит ou2"</li>
     * <li>Проверить, что в списке один объект user2</li>
     * <li>Сбросить фильтрацию атрибута Набор ссылок на БО и Ссылка на БО</li>
     * <li>Дважды нажать на название столбца Ссылка на БО</li>
     * <li>Проверить, что объекты отображены в списке в порядке user2, user1</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfObjectLinkAndBoLinksDynamicAttribute()
    {
        // Подготовка
        Bo ou1 = DAOOu.create(SharedFixture.ouCase());
        Bo ou2 = DAOOu.create(SharedFixture.ouCase());
        DSLBo.add(ou1, ou2);

        Bo employee = DAOEmployee.create(SharedFixture.employeeCase(), ou1, true, true);
        DSLBo.add(employee);

        ScriptRunner.executeScript(
                "utils.edit('%s', [dynAttrs: ['%s': ['%s': [value: '%s'], '%s': [[value: '%s'], [value: '%s']]]]])",
                user1.getUuid(), attrsGroupBo.getUuid(), objectLinkBo.getUuid(), employee.getUuid(),
                boLinksBo.getUuid(), ou1.getUuid(), ou2.getUuid());

        ScriptRunner.executeScript("utils.edit('%s', [dynAttrs: ['%s': ['%s': [value: '%s'], '%s': [value: '%s']]]])",
                user2.getUuid(), attrsGroupBo.getUuid(), objectLinkBo.getUuid(), employee.getUuid(),
                boLinksBo.getUuid(), ou1.getUuid());

        // Убираем объекты из динамического атрибута, чтобы по ним смог отработать клинер без ошибок
        final String CLEANER_SCRIPT_PATTERN = "utils.edit('%s', [dynAttrs: ['%s': ['%s': [value: %s], '%s': [value: "
                                              + "%s]]]])";
        Cleaner.afterTest(true, () ->
        {
            ScriptRunner.executeScript(CLEANER_SCRIPT_PATTERN,
                    user1.getUuid(), attrsGroupBo.getUuid(), objectLinkBo.getUuid(), "null", boLinksBo.getUuid(),
                    "null");

            ScriptRunner.executeScript(CLEANER_SCRIPT_PATTERN,
                    user2.getUuid(), attrsGroupBo.getUuid(), objectLinkBo.getUuid(), "null", boLinksBo.getUuid(),
                    "null");
        });

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute objectLinkDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, objectLinkBo);

        userList.advlist().content().clickFastFilterButton(objectLinkDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.CONTAINS);
        GUIAdvListFastFilterForm.setConditionSelect(ou1, employee);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        Attribute boLinksDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, boLinksBo);

        userList.advlist().content().clickFastFilterButton(boLinksDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_CONTAINS);
        GUIAdvListFastFilterForm.setConditionSelect(ou2);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), false, true);

        userList.advlist().content().clickFastFilterButton(objectLinkDynAttr);
        GUIAdvListFastFilterForm.clickResetConditionFilterIcon();
        GUIAdvListFastFilterForm.apply();

        userList.advlist().content().clickFastFilterButton(boLinksDynAttr);
        GUIAdvListFastFilterForm.clickResetConditionFilterIcon();
        GUIAdvListFastFilterForm.apply();

        userList.advlist().content().clickColumnName(objectLinkBo.getTitle());
        userList.advlist().content().clickColumnName(objectLinkBo.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2, user1), true, true);
    }

    /**
     * Тестирование фильтрации и сортировки динамического атрибута типа Строка
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Строка': [value: 'HI']
     *   ]
     * ]])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Строка': [value: 'hello'],
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList отфильтровать атрибут Строка по критерию "содержит hello"</li>
     * <li>Проверить, что в списке один объект user2</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не содержит (и не пусто) hello"</li>
     * <li>Проверить, что в списке один объект user1</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не пусто"</li>
     * <li>Проверить, что в списке два объекта user1 и user2</li>
     * <li>Нажать на название столбца Строка</li>
     * <li>Проверить, что объекты отображены в списке в порядке user2, user1</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfStringDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), stringBo.getUuid(), "HI");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), stringBo.getUuid(),
                "hello");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute stringDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, stringBo);

        userList.advlist().content().clickFastFilterButton(stringDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("hello");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), false, true);

        userList.advlist().content().clickFastFilterButton(stringDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("hello");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1), false, true);

        userList.advlist().content().clickFastFilterButton(stringDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_EMPTY);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        userList.advlist().content().clickColumnName(stringDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2, user1), true, true);
    }

    /**
     * Тестирование фильтрации и сортировки динамического атрибута типа Временной интервал
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Временной интервал': [value: '20000']
     *   ]
     * ]])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Временной интервал': [value: '2354364'],
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList отфильтровать атрибут Временной интервал по критерию "больше 30 секунд"</li>
     * <li>Проверить, что в списке один объект user2</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "равен 20 секунд"</li>
     * <li>Проверить, что в списке один объект user1</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не пусто"</li>
     * <li>Проверить, что в списке два объекта user1 и user2</li>
     * <li>Дважды нажать на название столбца Временной интервал</li>
     * <li>Проверить, что объекты отображены в списке в порядке user2, user1</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfTimeIntervalDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), timeIntervalBo.getUuid(),
                "20000");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), timeIntervalBo.getUuid(),
                "2354364");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute timeIntervalDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, timeIntervalBo);

        userList.advlist().content().clickFastFilterButton(timeIntervalDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.GREATER);
        GUIAdvListFastFilterForm.setConditionString("30");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), true, true);

        userList.advlist().content().clickFastFilterButton(timeIntervalDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.EQUAL);
        GUIAdvListFastFilterForm.setConditionString("20");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1), true, true);

        userList.advlist().content().clickFastFilterButton(timeIntervalDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_EMPTY);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        userList.advlist().content().clickColumnName(timeIntervalDynAttr.getTitle());
        userList.advlist().content().clickColumnName(timeIntervalDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2, user1), true, true);
    }

    /**
     * Тестирование отсутствия фильтрации и сортировки динамического атрибута типа Произвольный тип
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Произвольный тип': [value: 'HI']
     *   ]
     * ]])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Произвольный тип': [value: 'hello']
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList нажать на название столбца Произвольный тип.</li>
     * <li>Проверить, что порядок объектов в списке user1, user2 (не изменился)</li>
     * <li>Навести курсор на название столбца Произвольный тип.</li>
     * <li>Проверить, что у названия справа нет кнопки-иконки Фильтрация</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfRandomDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), randomBo.getUuid(), "HI");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), randomBo.getUuid(),
                "hello");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute randomDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, randomBo);

        userList.advlist().content().clickColumnName(randomDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), true, true);
        userList.advlist().content().asserts().fastFilterIconPresent(randomDynAttr, false);
    }

    /**
     * Тестирование фильтрации и сортировки динамического атрибута типа Текст
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Текст': [value: 'HI'],
     *   ]
     * ]])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Текст': [value: 'hello'],
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList отфильтровать атрибут Текст по критерию "содержит hello"</li>
     * <li>Проверить, что в списке один объект user2</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не содержит (и не пусто) hello"</li>
     * <li>Проверить, что в списке один объект user1</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не пусто"</li>
     * <li>Проверить, что в списке два объекта user1 и user2</li>
     * <li>Нажать на название столбца Текст</li>
     * <li>Проверить, что объекты отображены в списке в порядке user1, user2 (не изменился)</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfTextDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), textBo.getUuid(), "HI");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), textBo.getUuid(), "hello");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute textDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, textBo);

        userList.advlist().content().clickFastFilterButton(textDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("hello");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), false, true);

        userList.advlist().content().clickFastFilterButton(textDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("hello");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1), false, true);

        userList.advlist().content().clickFastFilterButton(textDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_EMPTY);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        userList.advlist().content().clickColumnName(textDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), true, true);
    }

    /**
     * Тестирование отсутствия фильтрации и сортировки динамического атрибута типа Файл
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Загрузить на стенд произвольный файл fileCase.</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Файл': [value: 'UUID файла fileCase']
     *   ]
     * ]])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList нажать на название столбца Файл.</li>
     * <li>Проверить, что порядок объектов в списке user1, user2 (не изменился)</li>
     * <li>Навести курсор на название столбца Файл.</li>
     * <li>Проверить, что у названия справа нет кнопки-иконки Фильтрация</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfFileDynamicAttribute()
    {
        // Подготовка
        File file = FileUtils.createFileWithSizeBytes(ModelUtils.createCode() + ".txt", 500);
        SdFile sdFile = DAOSdFile.create(file.getAbsolutePath(), SharedFixture.root());
        DSLFile.addFile(sdFile);

        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), fileBo.getUuid(),
                sdFile.getUuid());

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute fileDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, fileBo);

        userList.advlist().content().clickColumnName(fileDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), true, true);
        userList.advlist().content().asserts().fastFilterIconPresent(fileDynAttr, false);
    }

    /**
     * Тестирование фильтрации и сортировки динамического атрибута типа Целое число
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00998
     * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00976
     * https://naupp.naumen.ru/sd/operator/?anchor=#uuid:smrmTask$238328264
     * <br>
     * <ol>
     * <b>Подготовка</b>
     * <li>{@link #prepareFixture() Общая подготовка}</li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user1
     * <pre>
     * utils.edit('UUID объекта user1', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Целое число': [value: '11']]
     *   ]
     * ])
     * </pre>
     * </li>
     * <li>Выполнить скрипт заполнения атрибутов dynAttrs у объекта user2
     * <pre>
     * utils.edit('UUID объекта user2', [dynAttrs: [
     *   'UUID объекта Группа атрибутов': [
     *     'UUID объекта Целое число': [value: '8']]
     *   ]
     * ])
     * </pre>
     * </li>
     * <b>Выполнение действий и проверок</b>
     * <li>Войти в ИО под naumen</li>
     * <li>В списке userList открыть форму быстрой фильтрации атрибута Целое число</li>
     * <li>Отфильтровать по критерию "не равно 11.1"</li>
     * <li>Проверить, что в списке один объект user2.</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "больше 10"</li>
     * <li>Проверить, что в списке один объект user1</li>
     * <li>Сбросить фильтрацию, отфильтровать по критерию "не пусто"</li>
     * <li>Проверить, что в списке два объекта user1 и user2.</li>
     * <li>Нажать на название столбца Целое число</li>
     * <li>Проверить, что список отсортирован в порядке user2, user1</li>
     * </ol>
     */
    @Test
    void testFilteringAndSortingOfIntegerDynamicAttribute()
    {
        // Подготовка
        ScriptRunner.executeScript(SCRIPT_PATTERN, user1.getUuid(), attrsGroupBo.getUuid(), integerBo.getUuid(), "11");
        ScriptRunner.executeScript(SCRIPT_PATTERN, user2.getUuid(), attrsGroupBo.getUuid(), integerBo.getUuid(), "8");

        // Действия и проверки
        GUILogon.asSuper();
        GUINavigational.goToOperatorUI();

        // Шаг сброса представления - нужен для корректного отображения динамического атрибута после прохождения
        // других тестов
        userList.advlist().prs().selectByTitle(null);

        Attribute integerDynAttr = DAODynamicField.createDynamicAttribute(dynAttrs, integerBo);

        userList.advlist().content().clickFastFilterButton(integerDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_CONTAINS);
        GUIAdvListFastFilterForm.setConditionString("11");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2), true, true);

        userList.advlist().content().clickFastFilterButton(integerDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.GREATER);
        GUIAdvListFastFilterForm.setConditionString("10");
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1), true, true);

        userList.advlist().content().clickFastFilterButton(integerDynAttr);
        GUIAdvListFastFilterForm.selectCondition(FilterCondition.NOT_EMPTY);
        GUIAdvListFastFilterForm.apply();
        userList.advlist().content().asserts().rows(Lists.newArrayList(user1, user2), false, true);

        userList.advlist().content().clickColumnName(integerDynAttr.getTitle());
        userList.advlist().content().asserts().rows(Lists.newArrayList(user2, user1), true, true);
    }
}