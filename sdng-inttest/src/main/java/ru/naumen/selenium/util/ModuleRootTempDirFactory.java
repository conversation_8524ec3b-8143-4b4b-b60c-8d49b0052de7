package ru.naumen.selenium.util;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.junit.jupiter.api.extension.AnnotatedElementContext;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.io.TempDirFactory;

/**
 * Фабрика переопределяющая место сохранения папки темп у JUnit 5 аннотации @TempDir
 * <AUTHOR>
 * @since 23.05.25
 */
public class ModuleRootTempDirFactory implements TempDirFactory
{
    @Override
    public Path createTempDirectory(AnnotatedElementContext elementContext, ExtensionContext extensionContext)
    {
        // Определяем корень модуля (рабочую директорию сборки)
        Path moduleRoot = Paths.get(System.getProperty("user.dir"));
        // Создаём папку `temp` в корне модуля (если её нет)
        Path tempDir = moduleRoot.resolve("temp");
        try
        {
            Files.createDirectories(tempDir);
        }
        catch (IOException e)
        {
            throw new RuntimeException("Не получилось создать папку temp", e);
        }
        return tempDir;
    }
}