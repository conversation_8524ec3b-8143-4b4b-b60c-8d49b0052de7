package ru.naumen.selenium.casesutil.model.mobile.actions;

import ru.naumen.selenium.casesutil.model.mobile.MobileAddForm;

public class MobileContentListAction extends AbstractMobileObjectAction
{
    private boolean geoRequired;

    public MobileContentListAction(String code, String caption)
    {
        super(code, MobileActionType.SYSTEM, caption);
    }

    public void setAddForm(MobileAddForm addForm)
    {
        setFormCode(addForm.getCode());
    }

    public void setGeoRequired(boolean geoRequired)
    {
        this.geoRequired = geoRequired;
    }

    public boolean isGeoRequired()
    {
        return geoRequired;
    }
}
