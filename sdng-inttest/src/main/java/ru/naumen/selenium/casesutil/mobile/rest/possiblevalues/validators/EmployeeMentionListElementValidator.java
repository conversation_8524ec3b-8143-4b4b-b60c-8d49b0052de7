package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import io.restassured.response.ValidatableResponse;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор возможных значений псевдоатрибута @mention объектов системного типа "Сотрудник"
 */
public class EmployeeMentionListElementValidator extends ListPossibleValueValidator
{
    /**
     * Логин сотрудника.
     */
    @Nullable
    private String login;
    /**
     * Электронная почта сотрудника
     */
    @Nullable
    private String email;

    public EmployeeMentionListElementValidator(String code, String title)
    {
        super(code, title);
    }

    /**
     * Устанавливает логин пользователя для проверки. Устанавливать, если пользователь, выполняющий запрос имеет право
     * на просмотр системного атрибута "Логин" класса "Сотрудник".
     */
    public EmployeeMentionListElementValidator login(@Nullable String login)
    {
        this.login = login;
        return this;
    }

    /**
     * Устанавливает почту пользователя для проверки. Устанавливать, если пользователь, выполняющий запрос имеет
     * право на просмотр системного атрибута "Адрес электронной почты" класса "Сотрудник".
     */
    public EmployeeMentionListElementValidator email(@Nullable String mail)
    {
        this.email = mail;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);
        response.body(absolutePath + "?.subtitle", is(getSubtitle()));
    }

    /**
     * Возвращает ожидаемый подзаголовок в ответе
     */
    private String getSubtitle()
    {
        return Stream.of(login, email)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(" | "));
    }
}
