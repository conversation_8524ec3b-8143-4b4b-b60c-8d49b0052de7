package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Базовая реализация валидатора для возможного значения атрибута в дереве.
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
public abstract class BaseTreePossibleValueValidator<S extends BaseTreePossibleValueValidator<?>> implements PossibleValuesValidator
{
    private final String title;
    private boolean isLeaf = true;
    private boolean isSelectable = true;
    private BaseTreePossibleValueValidator<?>[] children;

    BaseTreePossibleValueValidator(@Nullable String title)
    {
        this.title = title;
    }

    /**
     * Позволяет валидировать флаг отсутствия дочерних значений
     *
     * @param leaf флаг отсутствия дочерних значений
     */
    @SuppressWarnings("unchecked")
    public S leaf(boolean leaf)
    {
        this.isLeaf = leaf;
        return (S)this;
    }

    /**
     * Позволяет валидировать флаг отсутствия дочерних значений
     *
     * @param selectable флаг отсутствия дочерних значений
     */
    @SuppressWarnings("unchecked")
    public S selectable(boolean selectable)
    {
        this.isSelectable = selectable;
        return (S)this;
    }

    /**
     * Позволяет валидировать дочерние возможные значения
     *
     * @param children валидаторы для дочерних возможных значений
     */
    @SuppressWarnings("unchecked")
    public S children(BaseTreePossibleValueValidator<?>... children)
    {
        this.isLeaf = false;
        this.children = children;
        return (S)this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        if (title != null)
        {
            response.body(absolutePath + "?.title", is(title));
        }

        response.body(absolutePath + "?.isLeaf", is(isLeaf));
        response.body(absolutePath + "?.isSelectable", is(isSelectable));

        if (children != null)
        {
            for (int i = 0; i < children.length; i++)
            {
                children[i].validate(response, absolutePath + ".children[" + i + "]", version);
            }

            response.body(absolutePath + ".children.size()", is(children.length));
        }
    }
}
