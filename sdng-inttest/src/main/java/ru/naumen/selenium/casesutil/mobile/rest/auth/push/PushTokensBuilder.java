package ru.naumen.selenium.casesutil.mobile.rest.auth.push;

import java.util.EnumMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Builder, использующийся для создания {@link PushTokens}
 */
public final class PushTokensBuilder
{
    private static final PushTokens EMPTY_PUSH_TOKENS = new PushTokens(Map.of());

    private final Map<PushProvider, String> pushTokens;

    PushTokensBuilder(Map<String, String> pushTokens)
    {
        this.pushTokens = pushTokens.entrySet().stream()
                .filter(entry -> PushProvider.CODES.contains(entry.getKey()))
                .collect(Collectors.toMap(entry -> PushProvider.of(entry.getKey()), Entry::getValue));
    }

    PushTokensBuilder()
    {
        this.pushTokens = new EnumMap<>(PushProvider.class);
    }

    /**
     * Добавить push-токен FCM
     */
    public PushTokensBuilder addFcm(String fcmPushToken)
    {
        this.pushTokens.put(PushProvider.FCM, fcmPushToken);
        return this;
    }

    /**
     * Добавить push-токен RuStore
     */
    public PushTokensBuilder addRuStore(String rustorePushToken)
    {
        this.pushTokens.put(PushProvider.RUSTORE, rustorePushToken);
        return this;
    }

    /**
     * Добавить push-токен HMS
     */
    public PushTokensBuilder addHms(String hmsPushToken)
    {
        this.pushTokens.put(PushProvider.HMS, hmsPushToken);
        return this;
    }

    /**
     * Создать набор push-токенов
     */
    public PushTokens build()
    {
        if (pushTokens.isEmpty() || areAllTokensNull())
        {
            return EMPTY_PUSH_TOKENS;
        }

        return new PushTokens(new EnumMap<>(pushTokens));
    }

    private boolean areAllTokensNull()
    {
        return pushTokens.values().stream().allMatch(Objects::isNull);
    }
}
