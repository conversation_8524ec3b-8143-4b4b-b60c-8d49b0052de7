package ru.naumen.selenium.casesutil.content.advlist;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.CONTENT_HEADER_CELL;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.StringUtils;

/**
 * Класс определяет адвлист в интерфейсе
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvlist extends CoreTester
{
    private String contentId;

    private String contentXpath;
    protected GUIAdvListUtil advlist;

    /**
     * Конструктор
     * @param contentIdOrXpath id элемента, который является advList-ом, либо его xpath (может иметь символы
     *                         форматирования)
     * @param args параметры для форматированного contentIdOrXpath, могут отсутствовать
     */
    public GUIAdvlist(String contentIdOrXpath)
    {
        if (StringUtils.isEmpty(contentIdOrXpath) || StringUtils.checkStringPattern(contentIdOrXpath, "\\(*/.*"))
        {
            this.contentXpath = contentIdOrXpath;
        }
        else
        {
            this.contentId = contentIdOrXpath;
            this.contentXpath = String.format(GUIXpath.Div.ID_PATTERN, contentId);
        }

        advlist = new GUIAdvListUtil(getContentIdOrXpath());
    }

    /**
     * Получить путь (xpath) до адвлиста
     * @return путь (xpath) до адвлиста
     */
    public String getContentXpath()
    {
        return contentXpath;
    }

    /**
     * Получить путь (xpath) до заголовка advlist'а для указанного атрибута
     * @param attr модель атрибута столбца таблицы advlist'а
     * @return путь (xpath) до заголовка advlist'а
     */
    public String getHeaderXpath(Attribute attr)
    {
        return getContentXpath()
               + String.format(CONTENT_HEADER_CELL, String.format("%s@%s", attr.getParentFqn(), attr.getCode()));
    }

    /**
     * Получить id адвлиста
     * @return id адвлиста
     */
    public String getContentId()
    {
        if (contentId == null)
        {
            throw new ErrorInCodeException("ID адвлиста не указан.");
        }
        return contentId;
    }

    protected String getContentIdOrXpath()
    {
        return contentId == null ? contentXpath : contentId;
    }

    /**
     * Получить uuid адвлиста
     * @return uuid адвлиста
     */
    protected String getContentUuid()
    {
        String pattern = "gwt-debug-\\w*\\.(.*)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(contentId == null ? "" : contentId);
        if (m.find())
        {
            return m.group(1);
        }
        else
        {
            throw new ErrorInCodeException(String.format("Не удалось получить UUID адвлиста из ID=%s", contentId));
        }
    }

}
