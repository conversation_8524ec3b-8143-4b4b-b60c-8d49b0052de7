package ru.naumen.selenium.casesutil.content.advlist.asserts;

import java.util.List;

import org.junit.Assert;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListDefaultPrsForm;
import ru.naumen.selenium.casesutil.content.advlist.PageSize;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.content.ContentForm;

/**
 * Утилитарные методы для проверок основного содержимого формы настройки представления по умолчанию адвлиста
 * <AUTHOR>
 * @since 11 апреля 2016 г.
 *
 */
public class AGUIAdvListDefaultPrsForm extends GUIAdvListDefaultPrsForm
{
    private static final String WARNING_TEXT = "//div[@id='gwt-debug-warning']";
    private static final String FORM_BLOCKS = GUIXpath.Div.CONTENT_CONTAINER + "/div";

    private static final List<String> PAGE_COUNT_ITEMS = Lists.newArrayList("20", "50", "100");
    private static final String[] FORM_BLOCKS_ORDER = new String[] {
            "gwt-debug-warning", "gwt-debug-copyFromTemplate", "gwt-debug-defaultParams",
            "gwt-debug-filter", "gwt-debug-sort", "gwt-debug-attributes", "gwt-debug-widthColumnsDefault" };

    /**
     * Проверить начальное состояние формы
     * @param content модель контента, для которого настраивают представление по умолчанию
     */
    public void assertForm(ContentForm content)
    {
        GUIForm.assertDialogCaption(String.format("Настройка вида по умолчанию для контента %s", content.getTitle()));
        GUITester
                .assertTextPresent(
                        WARNING_TEXT,
                        "Настроенный вид по умолчанию будет отображаться в режиме оператора, если вид не выбран (в "
                        + "поле выбора указано значение '[Выберите вид]')");
        List<String> ids = GUITester.getAttributeList(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + FORM_BLOCKS, "id");

        Assert.assertEquals("Количество блоков на форме настройки представления по умолчанию не совпадает с ожидаемым",
                FORM_BLOCKS_ORDER.length, ids.size());
        for (int i = 0; i < FORM_BLOCKS_ORDER.length; i++)
        {
            Assert.assertEquals(
                    String.format("Неверный порядок блока №%d на форме настройки представления по умолчанию", i + 1),
                    FORM_BLOCKS_ORDER[i], ids.get(i));
        }
        GUISelect.assertCountElements(PAGE_COUNT_INPUT, 3);
        GUISelect.assertSelect(PAGE_COUNT_INPUT, PAGE_COUNT_ITEMS, true, true, false);
        assertPageSize(PageSize.L20);
        ATTRIBUTES.assertInherit(true);
    }

    /**
     * Проверить значение 'Количество объектов на странице'
     * @param linesPerPage - ожидаемое количество
     */
    public void assertPageSize(PageSize linesPerPage)
    {
        GUITester.assertValueWithMsg(PAGE_COUNT_INPUT, linesPerPage.get(),
                "Неверное значение 'Количество объектов на странице'");
    }

}