package ru.naumen.selenium.casesutil.model.mobile.actions.object;

import java.util.List;

/**
 * Хранилище верхнего и нижнего блоков с действиями
 *
 * <AUTHOR>
 * @since 19.08.2022
 */
public class MobileContentObjectActions
{
    /** Верхний блок действий в контенте */
    private final List<MobileContentActionsRow> topActions;
    /** Нижний блок действий в контенте */
    private final List<MobileContentActionsRow> bottomActions;

    public MobileContentObjectActions(List<MobileContentActionsRow> topActions,
            List<MobileContentActionsRow> bottomActions)
    {
        this.topActions = topActions;
        this.bottomActions = bottomActions;
    }

    public List<MobileContentActionsRow> getTopActions()
    {
        return topActions;
    }

    public List<MobileContentActionsRow> getBottomActions()
    {
        return bottomActions;
    }
}