package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.CoreMatchers.notNullValue;
import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.FROM_V15;

import io.restassured.response.ValidatableResponse;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор для возможного значения атрибута в списке.
 *
 * <AUTHOR>
 * @since 23.03.2022
 */
@SuppressWarnings("LombokGetterMayBeUsed")
public class ListPossibleValueValidator implements PossibleValuesValidator
{
    private final String uuid;
    private final String title;

    ListPossibleValueValidator(@Nullable String uuid, @Nullable String title)
    {
        this.uuid = uuid;
        this.title = title;
    }

    /**
     * Возвращает ожидаемый код проверяемого элемента
     */
    public String getUuid()
    {
        return uuid;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        response.body(absolutePath, notNullValue());

        if (uuid != null)
        {
            if (FROM_V15.contains(version))
            {
                response.body(absolutePath + "?.uuid", is(uuid));
            }
            else
            {
                response.body(absolutePath + "?.code", is(uuid));
            }
        }
        if (title != null)
        {
            response.body(absolutePath + "?.title", is(title));
        }
    }
}
