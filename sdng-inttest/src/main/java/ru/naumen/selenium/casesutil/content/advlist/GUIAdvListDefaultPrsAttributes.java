package ru.naumen.selenium.casesutil.content.advlist;

import java.util.List;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant;

/**
 * Тесты на настройку списка атрибутов формы настройки представления по умолчанию списка объектов
 * https://projects.naumen.ru/ServiceDesk/Releases/4.0/Requirements/Req00659
 *
 * <AUTHOR>
 * @since 10 марта 2016 г.
 */

public class GUIAdvListDefaultPrsAttributes extends GUITester
{
    private static final String X_INHERIT = "//input[@id='gwt-debug-inherit-input']";
    private static final String X_ADD_ATTR_BTN = "//div[@id='gwt-debug-addDefaultPrsAttr']";
    private static final String X_INPUT_ATTR = "//div[@id='gwt-debug-attribute-value']//input";
    private static final String X_INPUT_PRS = "//div[@id='gwt-debug-prs-value']//input";
    private static final String X_ATTR_TABLE = "//div[@id='gwt-debug-attributes']//table";
    private static final String X_ATTR_HEADERS = X_ATTR_TABLE + "//th";
    private static final String X_ATTR_TABLE_ROW = X_ATTR_TABLE + "//tr[contains(@__did, '@%s')]";
    private static final String X_TITLES_COLUMN = X_ATTR_TABLE + "//tr/td[3]";
    private static final String X_ATTR_TITLE_COLUMN = X_ATTR_TABLE_ROW + "/td[3]";
    private static final String X_ATTR_CODE_COLUMN = X_ATTR_TABLE_ROW + "/td[4]";
    private static final String X_ATTR_PRS_COLUMN = X_ATTR_TABLE_ROW + "/td[5]";
    private static final String X_EDIT_ATTR_ICON = X_ATTR_TABLE_ROW + GUIXpath.Span.EDIT_ICON; // fontIcon
    private static final String X_HIDE_ATTR_ICON = X_ATTR_TABLE_ROW + GUIXpath.Span.DEL_ICON; // fontIcon
    private static final String X_ATTR_SELECT = "//div[@id='gwt-debug-attribute-value']//input";
    private static final String X_CANCEL_ATTR_FORM = "//div[@id='gwt-debug-addAdvlistAttribute']" + GUIXpath.Div.CANCEL;

    /**
     * Добавить атрибут в список.
     *
     * @param attribute - модель добавляемого атрибута
     */
    public void add(AdvListAttribute attribute)
    {
        tester.click(X_ADD_ATTR_BTN);
        GUISelect.selectById(X_INPUT_ATTR, attribute.getCode());
        GUISelect.selectById(X_INPUT_PRS, attribute.getPresentation());
        GUIForm.applyForm();
    }

    /**
     * Добавить атрибуты в список
     *
     * @param attributes - список моделей атрибутов которые нужно добавить
     */
    public void add(List<AdvListAttribute> attributes)
    {
        for (AdvListAttribute attribute : attributes)
        {
            add(attribute);
        }
    }

    /**
     * Проверить, что присутствует кнопка "Добавить атрибут" 
     */
    public void assertAddAttrButtonPresent(boolean exists)
    {
        GUITester.assertExists(X_ADD_ATTR_BTN, exists, "Кнопка Добавить атрибут отсутствует ");
    }

    /**
     * Проверить список атрибутов, которые можно добавить в таблицу
     * (должна быть открыта форма добавления атрибута)
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param attrs набор моделей ожидаемых атрибутов (без элемента [не указано])
     */
    public void assertAttributeSelect(boolean order, boolean match, Attribute... attrs)
    {
        List<String> expected = ModelUtils.getTitles(attrs);
        GUISelect.assertSelectWithoutEmpty(X_ATTR_SELECT, expected, order, match, 1, 1);
    }

    /**
     * Проверить, выставлен ли чекбокс Наследовать настройку из связанной с контентом группы атрибутов
     */
    public void assertInherit(boolean inherit)
    {
        GUITester.assertCheckboxState(X_INHERIT, inherit);

        GUITester.assertExists(X_ATTR_TABLE, !inherit,
                "Значение атрибута наследовать настройку из связанной группы атрибутов не совпадает с заданным");
    }

    /**
     * Проверить порядок расположения атрибутов на карточке списка 
     *
     * @param attributes - атрибуты в ожидаемом порядке
     */
    public void assertOrder(Attribute... attributes)
    {
        List<String> expected = ModelUtils.getTitles(attributes);
        GUITester.assertFindElements(X_TITLES_COLUMN, expected, true, true);
    }

    /**
     * Проверить присутствие атрибута в списке 
     *
     * @param attribute -атрибут
     * @param present присутствует/отсутствует (true/false)
     */
    public void assertPresent(AdvListAttribute attribute, boolean present)
    {
        String xpath = String.format(X_ATTR_TITLE_COLUMN, attribute.getCode());
        GUITester.assertExists(xpath, present, present ? "Атрибут не найден" : "Атрибут присутствует");
    }

    /**
     * Проверить список атрибутов на форме
     *
     * @param attributes - ожидаемые атрибуты
     */
    public void assertPresent(List<AdvListAttribute> attributes)
    {
        GUITester.assertCountElements(X_TITLES_COLUMN, attributes.size());
        for (AdvListAttribute attribute : attributes)
        {
            GUITester.assertTextPresent(X_ATTR_TITLE_COLUMN, attribute.getTitle(), attribute.getCode());
            GUITester.assertTextPresent(X_ATTR_CODE_COLUMN, attribute.getCode(), attribute.getCode());
            GUITester.assertTextPresent(X_ATTR_PRS_COLUMN, AttributeConstant.getPrsTitle(attribute.getPresentation()),
                    attribute.getCode());
        }
    }

    /**
     * Проверить названия колонок в таблице
     */
    public void assertTable()
    {
        GUITester.assertFindElements(X_ATTR_HEADERS, Lists.newArrayList("", "", "Атрибут", "Код атрибута",
                "Представление по умолчанию для атрибута в контенте", "", ""), true, true);
    }

    /**
     * Нажать кнопку "Добавить атрибут" 
     */
    public void clickAddAttrButton()
    {
        tester.click(X_ADD_ATTR_BTN);
    }

    /**
     * Нажать кнопку Отмена на форме добавления атрибута
     */
    public void clickCancelAddAttributeForm()
    {
        tester.click(X_CANCEL_ATTR_FORM);
    }

    /**
     * Кликнуть по иконке редактирования атрибута в списке 
     * @param attribute - модель атрибута
     */
    public void clickEditIcon(AdvListAttribute attribute)
    {
        tester.click(X_EDIT_ATTR_ICON, attribute.getCode());
    }

    /**
     * Кликнуть по иконке скрытия атрибута в списке 
     */
    public void clickHideIcon(AdvListAttribute attribute)
    {
        tester.click(X_HIDE_ATTR_ICON, attribute.getCode());
    }

    /**
     * Щелкнуть по чекбоксу "Наследовать настройку из связанной группы атрибутов"
     */
    public void clickInherit()
    {
        tester.click(X_INHERIT);
    }

    /**
     * Нажать кнопку перемещения вниз в списке атрибутов
     * @param attribute перемещаемый атрибут
     */
    public void moveDown(AdvListAttribute attribute)
    {
        tester.click(GUIXpath.SpecificComplex.MOVE_DOWN, attribute.getCode());
    }

    /**
     * Нажать кнопку перемещения вверх в списке атрибутов
     * @param attribute перемещаемый атрибут
     */
    public void moveUp(AdvListAttribute attribute)
    {
        tester.click(GUIXpath.SpecificComplex.MOVE_UP, attribute.getCode());
    }
}
