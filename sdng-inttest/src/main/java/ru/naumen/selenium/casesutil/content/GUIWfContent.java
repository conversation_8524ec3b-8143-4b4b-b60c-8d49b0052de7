package ru.naumen.selenium.casesutil.content;

import static ru.naumen.selenium.casesutil.GUIXpath.Div.ID_PATTERN;
import static ru.naumen.selenium.casesutil.metaclass.GUIWfDiagram.SVG_STATUS;
import static ru.naumen.selenium.casesutil.metaclass.GUIWfDiagram.SVG_STATUS_TEXT;
import static ru.naumen.selenium.casesutil.metaclass.GUIWfDiagram.SVG_TRANSITION;

import java.util.List;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.base.Preconditions;

import java.util.ArrayList;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;

/**
 * Методы для работы с контентом типа "Диаграмма жизненного цикла"
 * <AUTHOR>
 * @since 16.01.2014
 */
public class GUIWfContent extends CoreTester
{
    /**
     * Проверить, что в контенте "Диаграмма жизненного цикла" на диаграмме отсутствуют статусы
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param statuses набор моделей проверяемых статусов
     */
    public static void assertStatusAbsence(ContentForm content, BoStatus... statuses)
    {
        for (BoStatus status : statuses)
        {
            Assert.assertTrue("На диаграмме присутствует статус: " + status.getTitle(),
                    tester.waitDisappear(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode()));
            Assert.assertTrue("У статуса на диаграмме присутствует текст: " + status.getTitle(),
                    tester.waitDisappear(ID_PATTERN + SVG_STATUS_TEXT, content.getXpathId(), status.getCode()));
        }
    }

    /**
     * Проверить, что в контенте "Диаграмма жизненного цикла" на диаграмме присутствуют статусы
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param statuses набор моделей проверяемых статусов
     */
    public static void assertStatusPresence(ContentForm content, BoStatus... statuses)
    {
        for (BoStatus status : statuses)
        {
            Assert.assertTrue("На диаграмме отсутствует статус: " + status.getTitle(),
                    tester.waitAppear(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode()));
            Assert.assertTrue("У статуса на диаграмме отсутствует текст: " + status.getTitle(),
                    tester.waitAppear(ID_PATTERN + SVG_STATUS_TEXT, content.getXpathId(), status.getCode()));
        }
    }

    /**
     * Проверить, что в контенте "Диаграмма жизненного цикла" на диаграмме у статусов корректные названия
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param statuses набор моделей проверяемых статусов
     */
    public static void assertStatusText(ContentForm content, BoStatus... statuses)
    {
        for (BoStatus status : statuses)
        {
            GUITester.assertTextPresentWithMsg(ID_PATTERN + SVG_STATUS_TEXT, status.getTitle(),
                    "У статуса на диаграмме не корректное название.", content.getXpathId(), status.getCode());
        }
    }

    /**
     * Проверить, что на диаграмме "Диаграмма жизненного цикла" отсутствуют переходы между статусами
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param statuses набор пар моделей статусов, между которыми должен отсутствовать переход
     */
    public static void assertTransitionsAbsence(ContentForm content, BoStatus... statuses)
    {
        Preconditions.checkArgument(statuses.length % 2 == 0, "Количество статусов должно быть четно.");
        for (int i = 0; i < statuses.length; i += 2)
        {
            String msg = "Присутствует переход между статусами: [%s - %s]";
            Assert.assertTrue(String.format(msg, statuses[i].getTitle(), statuses[i + 1].getTitle()),
                    tester.waitDisappear(ID_PATTERN + SVG_TRANSITION, content.getXpathId(), statuses[i].getCode(),
                            statuses[i + 1].getCode()));
        }
    }

    /**
     * Проверить, что в контенте "Диаграмма жизненного цикла" на диаграмме присутствуют переходы между статусами
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param statuses набор пар моделей статусов, между которыми должен присутствовать переход
     */
    public static void assertTransitionsPresence(ContentForm content, BoStatus... statuses)
    {
        Preconditions.checkArgument(statuses.length % 2 == 0, "Количество статусов должно быть четно.");
        for (int i = 0; i < statuses.length; i += 2)
        {
            String msg = "Отсутствует переход между статусами: [%s - %s]";
            Assert.assertTrue(String.format(msg, statuses[i].getTitle(), statuses[i + 1].getTitle()),
                    tester.waitAppear(ID_PATTERN + SVG_TRANSITION, content.getXpathId(), statuses[i].getCode(),
                            statuses[i + 1].getCode()));
        }
    }

    /**
     * Кликнуть по статусу на диаграмме (клик в центр блока со статусом)
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param status модель статуса
     */
    public static void clickStatus(ContentForm content, BoStatus status)
    {
        tester.clickOnInvisibleElement(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode());
    }

    /**
     * Кликнуть по статусу на диаграмме (клик в левый верхний угол блока)
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param status модель статуса
     */
    public static void clickStatusBlock(ContentForm content, BoStatus status)
    {
        tester.clickTopLeftCorner(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode());
    }

    /**
     * Получить цвет блока статуса на диаграмме в контенте "Диаграмма жизненного цикла"
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param status модель статуса
     * @return атрибут "fill" веб элемента, которому соотвествует блок статуса
     */
    public static String getStatusColor(ContentForm content, BoStatus status)
    {
        WebElement block = tester.findDisplayed(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode());
        return block.getAttribute("fill");
    }

    /**
     * Получение координат статуса на диаграмме в контенте "Диаграмма жизненного цикла"
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param status модель БО статуса
     * @return список координат x, y статуса на диаграмме (координаты x, y получаются как атрибуты x, y веб элемента
     * статуса на диаграмме)
     */
    public static List<String> getStatusCoordinates(ContentForm content, BoStatus status)
    {
        List<String> coords = new ArrayList<>();
        WebElement element = tester.findDisplayed(ID_PATTERN + SVG_STATUS, content.getXpathId(), status.getCode());
        coords.add(element.getAttribute("x"));
        coords.add(element.getAttribute("y"));
        return coords;
    }

    /**
     * Получение координат перехода между статусами в контенте "Диаграмма жизненного цикла"
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param from модель статуса, из которого осуществляется переход
     * @param to модель статуса, в который осуществляется переход
     * @return атрибут "d" веб элемента, которому соотвествует переход между статусами
     */
    public static String getTransitionCoordinates(ContentForm content, BoStatus from, BoStatus to)
    {
        WebElement transition = tester.findDisplayed(ID_PATTERN + SVG_TRANSITION, content.getXpathId(), from.getCode(),
                to.getCode());
        return transition.getAttribute("d");
    }

    /**
     * Переместить статус на диаграмме в контенте "Диаграмма жизненного цикла"
     * @param content модель контента "Диаграмма жизненного цикла"
     * @param status модель статуса
     * @param xOffset горизонтальное смещение статуса
     * @param yOffset вертикальное смещение статуса
     */
    public static void moveStatus(ContentForm content, BoStatus status, int xOffset, int yOffset)
    {
        tester.dragAndDropByTopLeftCorner(ID_PATTERN + SVG_STATUS, xOffset, yOffset, content.getXpathId(),
                status.getCode());
    }
}
