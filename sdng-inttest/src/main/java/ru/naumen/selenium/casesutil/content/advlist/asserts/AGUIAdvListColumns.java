package ru.naumen.selenium.casesutil.content.advlist.asserts;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD
import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

/**
 * Утилитарные методы для проверок, связанных с настройкой полей advList-а, через интерфейс
 * <AUTHOR>
 * @since 25.05.2015
 */
public class AGUIAdvListColumns extends GUIAdvlist
{
    public AGUIAdvListColumns(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверить список атрибутов (по коду), настроенных для отображения, на форме "Настройка полей" в ИО
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param attributes список атрибутов
     */
    public void attrs(boolean order, boolean match, Attribute... attributes)
    {
        List<String> expected = ModelUtils.getCodes(attributes);
        List<String> actual = new ArrayList<>();
        boolean isContinue = true;
        long currentTime = System.currentTimeMillis();
        while (isContinue && System.currentTimeMillis() - currentTime < WAIT_TIME * 1000)
        {
            actual = Lists.transform(GUITester.getAttributePropertyElements(COLUMNS_ATTRS, "id"), (String input) ->
            {
                return StringUtils.substringAfter(input, "@");
            });

            if (match)
            {
                actual.remove(GUISelect.EMPTY_VALUE);
                isContinue = !(order ? expected : Sets.newHashSet(expected))
                        .equals(order ? actual : Sets.newHashSet(actual));
            }
            else
            {
                isContinue = !(order ? CollectionUtils.orderContains(expected, actual) : actual.containsAll(expected));
            }
        }
        String message = "Список атрибутов на форме 'Настройка полей' не совпал с ожидаемым. Ожидался: %s, получен %s";
        Assert.assertFalse(String.format(message, expected, actual), isContinue);
    }

    /**
     * Проверить выпадающий список с атрибутами на форме "Настройка полей"
     * (должна быть открыта форма "Настройка полей")
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param attrs набор моделей ожидаемых атрибутов, если набор пуст, то проверяется, что поле задизейблено
     */
    public void attrsSelectOnDialog(boolean order, boolean match, Attribute... attrs)
    {
        if (attrs.length == 0)
        {
            Assert.assertFalse("Поле для выбора атрибута для сортировки не задизейблено.",
                    tester.findDisplayed(COLUMNS_ATTR_INPUT).isEnabled());
        }
        else
        {
            List<String> expected = ModelUtils.getTitles(attrs);
            GUISelect.assertSelectWithoutEmpty(COLUMNS_ATTR_INPUT, expected, order, match, 1, 1);
        }
    }

    /**
     * Проверить текущее представление для атрибута на форме "Настройка полей"
     * @param attribute атрибут, в котором проверяется представление
     * @param presentationTitle название представления атрибута
     */
    public void currentPresentation(Attribute attribute, String presentationTitle)
    {
        String msg = "Полученное представление для атрибута '" + attribute.getTitle() + "' не совпало с ожидаемым.";
        GUITester.assertTextPresentWithMsg(COLUMNS_ATTR_PRESENTATION, presentationTitle, msg, attribute.getCode());
    }

    /**
     * Проверить список доступных представлений для атрибута на форме "Настройка полей" (без учета порядка)
     * @param attribute модель атрибута, в котором проверяются представления
     * @param presentationTitles названия представлений атрибута
     */
    public void presentations(Attribute attribute, String... presentationTitles)
    {
        GUISelect.assertSelect(COLUMNS_ATTR_PRESENTATION, Lists.newArrayList(presentationTitles), false, true, true,
                attribute.getCode());
        GUISelect.hideSelect();
    }
}
