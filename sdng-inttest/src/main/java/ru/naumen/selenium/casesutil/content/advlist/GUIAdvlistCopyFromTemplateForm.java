package ru.naumen.selenium.casesutil.content.advlist;

import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.interfaceelement.TemplatesTree;

/**
 * Утилитарные средства для работы с формой "Копирование настроек из шаблона"
 * <AUTHOR>
 * @since 10.05.2018
 */
public class GUIAdvlistCopyFromTemplateForm extends GUIAdvlist
{
    public static final String TEMPLATE_TREE = GUIXpath.Div.VALUE_CELL_TREE + "//span[@id='%s']";
    public static final String TEMPLATE_VALUE =
            GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + "//*[@id='gwt-debug-template-value']";
    public static final String OTHERS_TEMPLATE = "othersTemplates";

    public static final String APPLY_INFO_MESSAGE = "Применение настроек из шаблона \"%s\" выполнено.\nБыли "
                                                    + "скопированы: %s.";

    private TemplatesTree tree;

    public GUIAdvlistCopyFromTemplateForm(String contentXpath)
    {
        super(contentXpath);
        tree = new TemplatesTree(GUIAdvlistCopyFromTemplateForm.TEMPLATE_VALUE);
    }

    /**
     * Подтвердить отсутствие шаблона в списке доступных для выбора
     * @param template
     */
    public void assertPresentTemplate(boolean expected, String... nodes)
    {
        tree.assertPresentElement(expected, nodes);
    }

    /**
     * Выбрать шаблон в списке на форме "Копирование настроек из шаблона" и нажать "Сохранить"
     * @param template
     */
    public void selectTemplate(String... nodes)
    {
        tree.setElementInSelectTree(nodes);
        clickApplyOnDialogByTitle("Копирование настроек из шаблона");
    }

    /**
     * Нажать "Сохранить" на диалоге с заданным заголовком
     * (метод необходимо вызывать при наличии нескольких модальных окон на экране)
     * @param dialogTitle
     */
    public void clickApplyOnDialogByTitle(String dialogTitle)
    {
        tester.click(
                GUIXpath.Any.TEXT_PATTERN + "/parent::*/parent::*/parent::*/parent::*/parent::*"
                + GUIXpath.Any.APPLY_BUTTON,
                dialogTitle);
    }
}