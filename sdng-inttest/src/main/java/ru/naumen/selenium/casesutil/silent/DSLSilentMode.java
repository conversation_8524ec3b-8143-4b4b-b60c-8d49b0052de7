package ru.naumen.selenium.casesutil.silent;

import ru.naumen.selenium.casesutil.model.RemoveOperation;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.Cleaner;

/**
 * Утилитарные методы для работы с silent mode
 *
 * <AUTHOR>
 * @since 6 апр. 2016 г.
 */
public class DSLSilentMode
{
    /**
     * Установить настройки silent mode
     *
     * @param enabled - true, активировать silent mode
     * @param suitableIps - список ip адресов, попадающих под исключение
     */
    public static void setEnabled(boolean enabled, String... suitableIps)
    {
        ScriptElement element = SESilentMode.setEnabled(enabled, suitableIps);
        new ScriptRunner(true, element).runScript();

        Cleaner.push(new RemoveOperation(SESilentMode.setEnabled(false)));
    }
}
