package ru.naumen.selenium.casesutil.content;

import java.util.Arrays;
import java.util.List;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.PropertyDialogBoxContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.MetaTree;
import ru.naumen.selenium.casesutil.interfaceelement.RelationTree;
import ru.naumen.selenium.casesutil.metaclass.DSLMetaClass.MetaclassCardTab;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PagingPosition;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PresentationContent;
import ru.naumen.selenium.casesutil.model.content.gantt.GanttContent;
import ru.naumen.selenium.util.Colors;
import ru.naumen.selenium.util.Json;

/**
 * Методы для заполнения основных полей контентов при его создании
 * <AUTHOR>
 * @since 12.04.2012
 */
public class GUIFillContent extends CoreTester
{
    /**
     * Метод для заполнения основных полей контентов в зависимости от таба
     * @param model модель контента
     */
    public static void fill(ContentForm model)
    {
        switch (MetaclassCardTab.getMetaclassCardTab(model.getTab()))
        {
            case OBJECTCARD:
                fillCard(model);
                break;
            case NEWENTRYFORM:
                fillAddForm(model);
                break;
            case EDITFORM:
                fillEditForm(model);
                break;
            default:
                break;
        }
    }

    /**
     * Изменение класса 
     * @param model модель данных типа "Контент"
     */
    public static void fillClassObjectList(ContentForm model)
    {
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.CLASS_LIST_VALUE_INPUT, model.getMetaclassCode());
    }

    /**
     * Заполнение кода, если тело кода пустое, то заполняем по умолчанию названием
     * @param model модель данных типа "Контент"
     */
    public static void fillCodeOnCardForm(ContentForm model)
    {
        if (GUIContent.isEditForm())
        {
            Assert.assertTrue("Поле кода должно быть нередактируемым",
                    tester.waitAppear(
                            GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + "//div[@id='gwt-debug-code-caption']/span"));

        }
        else if (!model.getCode().isEmpty())
        {
            tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, model.getCode());
        }
    }

    /**
     * Установить чекбокс "Скрывать корешок, если вкладка единственная"
     * @param model модель данных типа "Контент"
     */
    public static void fillHideSingleTab(ContentForm model)
    {
        tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.DIALOG_CONTENT_INPUT, model.isHideSingleTab(),
                GUITab.HIDE_SINGLE_TAB);
    }

    /**
     * Заполнение названия кода и отображение названия. Если в модели для поля "Отображать название" указано
     * значение по умолчанию(true), то ничего не делает, обязательно заполняется с дефолтными значениями
     * @param model модель данных типа "Контент"
     */
    public static void fillTitleAndCodeOnCardForm(ContentForm model)
    {
        assertInsertAutoName(model);
        fillTitleOnCardForm(model);
        fillCodeOnCardForm(model);
        fillShowCaption(model);
    }

    /**
     * Заполнение названия и отображение названия. Если в модели для поля "Отображать название" указано
     * значение по умолчанию(true), то ничего не делает
     * @param model модель данных типа "Контент"
     */
    public static void fillTitleOnCardForm(ContentForm model)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, model.getTitle());
    }

    /**
     * Выбор постраничной навигации на форме добавления/редактирования контента
     * @param value {@link PagingPosition}
     */
    public static void setPagingPosition(PagingPosition value)
    {
        tester.selectRadiobutton(GUIContent.PAGING_POSITION_INPUT, value.get());
    }

    /**
     * Установить поле "Отображать название" на форме добавления/редактирования контента.
     * (форма должна быть открыта)
     * @param isSet
     */
    public static void setShowCaption(boolean isSet)
    {
        tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT, isSet);
    }

    /**
     * Заполнить поле "Название" на форме добавления/редактирования контента
     * @param title название
     */
    public static void setTitle(String title)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, title);
    }

    /**
     * Заполнить поле "Код" на форме добавления/редактирования контента
     * @param code название
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, code);
    }

    /**
     * Проверить отсутствие поля "Профили".
     */
    static void assertAbsenceProfiles()
    {
        Assert.assertTrue("Поле \"Профили\" присутствует",
                tester.waitDisappear(GUIXpath.PropertyDialogBoxContent.PROFILES_VALUE_INPUT));
    }

    /**
     * Проверить отсутствие поля "Отображать название".
     */
    static void assertAbsenceShowCaption()
    {
        Assert.assertTrue("Поле \"Отображать название\" присутствует",
                tester.waitDisappear(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT));
    }

    /**
     * Заполнить поле "Расположение". Если в модели указано значение по умолчанию(На всю ширину), то ничего не делает
     * (форма должна быть открыта)
     * @param model модель контента
     */
    public static void fillPosition(ContentForm model)
    {
        if (!PositionContent.FULL.get().equals(model.getPosition()))
        {
            GUISelect.select(GUIXpath.PropertyDialogBoxContent.POSITION_VALUE_INPUT_ON_FORM, model.getPosition());
        }
    }

    /**
     * Заполнение поля "Отображать название". Если в модели указано значение по умолчанию(true), то ничего не делает
     * (форма должна быть открыта)
     * @param model модель данных типа "Контент"
     */
    static void fillShowCaption(ContentForm model)
    {
        boolean value = Boolean.valueOf(model.getShowTitle());
        if (!value)
        {
            tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT, value);
        }
    }

    /**
     * Заполнение полей "Отображать описание атрибутов для формы" (форма должна быть открыта)
     *
     * @param model модель данных типа "Контент"
     */
    private static void fillShowAttrDescriptionOnForm(ContentForm model)
    {
        tester.setCheckbox(PropertyDialogBoxContent.SHOW_ATTR_DESCRIPTION_FOR_ADD_FORM_VALUE_ON_FORM,
                model.isShowAttrDescriptionOnAddForm());
        tester.setCheckbox(PropertyDialogBoxContent.SHOW_ATTR_DESCRIPTION_FOR_EDIT_FORM_VALUE_ON_FORM,
                model.isShowAttrDescriptionOnEditForm());
    }

    /**
     * Проверка заполнения названия по умолчанию
     * @param model модель данных типа "Контент"
     */
    private static void assertInsertAutoName(ContentForm model)
    {
        if (GUIContent.isEditForm())
        {
            return;
        }

        tester.click(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE);
        ContentType contentType = ContentType.getContentType(model.getType());
        GUITester.assertValue(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, getAutoName(contentType));
    }

    /**
     * Метод для заполнения основных полей контентов формы добавления
     * @param model модель контента
     */
    private static void fillAddForm(ContentForm model)
    {

        switch (ContentType.getContentType(model.getType()))
        {
            case TAB_BAR:
                fillCardTabBar(model);
                break;
            case SELECT_CONTACTS:
                fillAddFormSelectContacts(model);
                break;
            case SELECT_CASE:
                fillAddFormSelectCase(model);
                break;
            case SELECT_SC_CASE:
                fillAddFormSelectScCase(model);
                break;
            case CLIENT_INFO:
                fillAddFormClientInfo(model);
                break;
            case PROPERTY_LIST:
                fillAddFormPropertyList(model);
                break;
            case RELATED_OBJECT_LIST:
                fillCardRelatedList(model);
                break;
            case REL_OBJ_PROP_LIST:
                fillCardRelObjPropList(model);
                break;
            case EDITABLE_PROPERTY_LIST:
                fillAddFormPropertyList(model);
                break;
            case MASS_PROBLEMS:
                fillAddFormMassProblems(model);
                break;
            case SELECT_PARENT:
                fillAddFormSelectParent(model);
                break;
            case SELECT_CLIENT:
                fillAddFormSelectClient(model);
                break;
            default:
                break;
        }
    }

    /**
     * Заполнение основных полей формы добавления контента "Информация о пользователе"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormClientInfo(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.EMPL_ATTR_GROUP, model.getEmployeeAttrGroupCode());
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.OU_ATTR_GROUP, model.getOuAttrGroupCode());
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TEAM_ATTRIBUTE_GROUP, model.getTeamAttrGroupCode());
        fillPosition(model);
    }

    /**
     * Заполнение основных полей формы добавления контента "Связанные запросы"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormMassProblems(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
        fillPresentationMassProblems(model);
    }

    /**
     * Заполнение основных полей формы добавления контента "Параметры объекта"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormPropertyList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillAttributeGroup(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей формы добавления контента "Выбор типа объекта"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormSelectCase(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей формы добавления контента "Выбор контрагента"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormSelectClient(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        if (Boolean.valueOf(model.getShowTitle()))
        {
            tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT, true);
        }
        else
        {
            tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT, false);
        }
        fillPosition(model);
    }

    /**
     * Заполнение основных полей формы добавления контента "Выбор контактного лица"
     * @param model  модель данных типа "Контент"
     */
    private static void fillAddFormSelectContacts(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
        if (model.containsKey(ContentForm.ADDITIONAL_CONTACTS))
        {
            List<String> list = Json.GSON.fromJson(model.getAdditionalContacts(), Json.LIST_STRING_TYPE);
            GUIMultiSelect.selectNotClean(GUIXpath.InputComplex.CONTACT_ATTRIBUTES_VALUE,
                    list.toArray(new String[list.size()]));
        }

    }

    /**
     * Заполнение основных полей формы добавления контента "Выбор родительского объекта"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormSelectParent(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        assertAbsenceProfiles();
    }

    /**
     * Заполнение основных полей формы добавления контента "Выбор типа запроса"
     * @param model модель данных типа "Контент"
     */
    private static void fillAddFormSelectScCase(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, model.getTitle());
    }

    /**
     * Заполняет группу атрибутов по модели.
     * @param model
     */
    private static void fillAttributeGroup(ContentForm model)
    {
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.ATTRIBUTE_GROUP_VALUE_INPUT, model.getAttributeGroupCode());
    }

    /**
     * Метод для заполнения основных полей контентов карточки объекта
     * @param model модель контента
     */
    private static void fillCard(ContentForm model)
    {
        switch (ContentType.getContentType(model.getType()))
        {
            case CHILD_OBJECT_LIST:
                fillCardChildObjectListOrObjectList(model);
                break;
            case OBJECT_LIST:
                fillCardChildObjectListOrObjectList(model);
                break;
            case RELATED_OBJECT_LIST:
                fillCardRelatedList(model);
                break;
            case PROPERTY_LIST:
                fillCardPropertyList(model);
                break;
            case REL_OBJ_PROP_LIST:
                fillCardRelObjPropList(model);
                break;
            case COMMENT_LIST:
                fillCardCommentList(model);
                break;
            case EVENT_LIST:
                fillCardEventList(model);
                break;
            case REPORT:
                fillCardReport(model);
                break;
            case EMBEDDED_APPLICATION:
                fillCardEmbeddedApplication(model);
                break;
            case REPORT_LIST:
                fillCardReportList(model);
                break;
            case TAB_BAR:
                fillCardTabBar(model);
                break;
            case FILE_LIST:
                fillFileList(model);
                break;
            case RELATION_SCHEME:
                fillCardRelationSheme(model);
                break;
            case NETWORK_SCHEME:
                fillCardNetworkScheme(model);
                break;
            case WF_DIAGRAM:
                fillWfDiagram(model);
                break;
            case GANTT:
                if (model instanceof GanttContent)
                {
                    GUIGanttContent.fillFields((GanttContent)model);
                }
                break;
            default:
                break;
        }
    }

    /**
     * Заполнение основных полей карточки объекта контента "Список вложенных объектов"/"Список объектов"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardChildObjectListOrObjectList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.CLASS_LIST_VALUE_INPUT, model.getMetaclassCode());
        fillSelectOnForm(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Комментарии к объекту"
     * (Заполняет поле Название и отображать название)
     * @param model модель данных типа "Контент"
     */
    private static void fillCardCommentList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillShowAttrDescriptionOnForm(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Встроенное приложение"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardEmbeddedApplication(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        if (Boolean.parseBoolean(model.getApplicationEnabled()))
        {
            GUISelect.assertElementColor(
                    GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.EMBEDDED_APPLICATION_VALUE,
                    model.getApplicationCode(), Colors.LIGHT_BLACK);
        }
        else
        {
            GUISelect.assertElementColor(
                    GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.EMBEDDED_APPLICATION_VALUE,
                    model.getApplicationCode(), Colors.REAL_GREY);
        }
        GUISelect.selectByXpath(
                GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.EMBEDDED_APPLICATION_VALUE,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, model.getApplicationCode()));
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "История изменений объекта"
     * (Заполняет поле Название и отображать название, представление и расположение)
     * @param model модель данных типа "Контент"
     */
    private static void fillCardEventList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPresentation(model);
        fillAttributeGroup(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Схема сети"
     * @param model модель данных типа "Схема сети"
     */
    private static void fillCardNetworkScheme(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        MetaTree mTree = new MetaTree(GUIContent.DISPLAYED_CASES_VALUE);
        for (String fqn : Json.stringToList(model.getCasesCode()))
        {
            mTree.setElementInMultiSelectTree(fqn);
        }
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Параметры объекта"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardPropertyList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillAttributeGroup(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Список связанных объектов"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardRelatedList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.SHOW_RELATED_WITH_NESTED_VALUE_INPUT,
                model.getShowRelatedWithNested());
        if (model.getShowRelatedWithNested())
        {
            String beforeHierarchyRecipient = String.format("%s@currentObject", model.getParentFqn());
            if (!(model.getBeforeHierarchyAttribute().equals(beforeHierarchyRecipient)
                  || model.getBeforeHierarchyAttribute().equals(ContentForm.CURRENT_OBJECT)))
            {
                beforeHierarchyRecipient += "." + model.getBeforeHierarchyAttribute();
            }
            int afterHierarchyCurrentObjectFqnPos = model.getRecepient().indexOf('@');
            String afterHierarchyCurrentObjectFqn = model.getRecepient().substring(0,
                    afterHierarchyCurrentObjectFqnPos);
            String afterHierarchyRecipient = String.format("%s@currentObject", afterHierarchyCurrentObjectFqn);
            if (!model.getRecepient().equals(afterHierarchyRecipient))
            {
                afterHierarchyRecipient += "." + model.getRecepient();
            }
            RelationTree beforeHierarchyTree = new RelationTree(
                    GUIXpath.PropertyDialogBoxContent.BEFORE_HIERARCHY_VALUE);
            beforeHierarchyTree.setElement(beforeHierarchyRecipient);

            if (model.getHierarchyAttribute() != null)
            {
                GUISelect.selectById(GUIXpath.PropertyDialogBoxContent.HIERARCHY_ATTRIBUTE_VALUE_INPUT,
                        model.getHierarchyAttribute());
            }
            RelationTree afterHierarchyTree = new RelationTree(GUIXpath.PropertyDialogBoxContent.AFTER_HIERARCHY_VALUE);
            afterHierarchyTree.setElement(afterHierarchyRecipient);

        }
        else
        {
            RelationTree tree = new RelationTree(GUIXpath.Any.ATTRIBUTE_SELECT_VALUE);
            tree.setElement(model.getRecepient());
        }
        if (tester.waitAppear(1, GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.VALUE))
        {
            tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.VALUE,
                    model.getTitleRelatedAddForm());
        }
        fillSelectOnForm(model);
    }

    private static void fillCardRelationSheme(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        MetaTree mTree = new MetaTree(GUIContent.DISPLAYED_CASES_VALUE);
        for (String fqn : Json.stringToList(model.getDisplayCases()))
        {
            mTree.setMetaClass(fqn);
        }
        GUIMultiSelect.select(GUIContent.RELATION_VALUE + "//input",
                Json.stringToList(model.getAttributesCodes()).toArray(new String[0]));
        tester.sendKeys(GUIContent.HOPS, model.getHops());
    }

    /**
     * Заполнение основных полей на форме добавления контента "Параметры связанного объекта"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardRelObjPropList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.ATTRIBUTE_VALUE_INPUT_ON_FORM, model.getRelAttribute());
        fillAttributeGroup(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Отчет"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardReport(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        GUISelect.selectByXpath(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.TEMPLATES_LIST_VALUE,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, model.getTemplateCode()));
        fillPosition(model);

    }

    /**
     * Заполнение основных полей карточки объекта контента "Список отчетов"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardReportList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        List<String> list = Json.GSON.fromJson(model.getTemplatesCode(), Json.LIST_STRING_TYPE);
        GUIMultiSelect.select(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.TEMPLATES_LIST_VALUE,
                list.toArray(new String[list.size()]));
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Панель вкладок"
     * @param model модель данных типа "Контент"
     */
    private static void fillCardTabBar(ContentForm model)
    {
        fillTitleOnCardForm(model);
        fillCodeOnCardForm(model);
        fillHideSingleTab(model);

        assertAbsenceShowCaption();
        List<String> profiles = model.getProfileCodes();
        if (!profiles.isEmpty())
        {
            GUIMultiSelect.select(GUIContent.PROFILES_VALUE_INPUT, profiles.toArray(new String[profiles.size()]));
        }
        List<String> versProfiles = model.getVersProfileCodes();
        if (!versProfiles.isEmpty())
        {
            GUIMultiSelect.select(GUIContent.VERS_PROFILES_VALUE_INPUT,
                    versProfiles.toArray(new String[versProfiles.size()]));
        }
        fillPosition(model);
    }

    /**
     * Метод для заполнения основных полей контентов формы редактирования
     * @param model модель контента
     */
    private static void fillEditForm(ContentForm model)
    {
        switch (ContentType.getContentType(model.getType()))
        {
            case PROPERTY_LIST:
                fillAddFormPropertyList(model);
                break;
            case EDITABLE_PROPERTY_LIST:
                fillAddFormPropertyList(model);
                break;
            case REL_OBJ_PROP_LIST:
                fillCardRelObjPropList(model);
                break;
            default:
                break;
        }
    }

    /**
     * Заполнение основных полей карточки объекта контента "Список файлов"
     * @param model модель данных типа "Контент"
     */
    private static void fillFileList(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
        GUIContent.assertPresentionList(Arrays.asList("Иконки", "Список"));
        GUISelect.select(GUIXpath.InputComplex.PRESENTATION_LIST_VALUE, model.getPresentation());
    }

    /**
     * Заполнить поле "Представление". Если в модели указано значение по умолчанию(Простой список), то ничего не делает
     * (форма должна быть открыта)
     * @param model модель контента
     */
    private static void fillPresentation(ContentForm model)
    {
        if (!PresentationContent.DEFAULT.get().equals(model.getPresentation()))
        {
            GUISelect.select(GUIXpath.InputComplex.PRESENTATION_LIST_VALUE, model.getPresentation());
        }
    }

    /**
     * Заполнить поле "Представление" на форме добавления контента "Связанные запросы". Если в модели указано
     * значение по умолчанию(Простая форма), то ничего не делает
     * (форма должна быть открыта)
     * @param model модель контента
     */
    private static void fillPresentationMassProblems(ContentForm model)
    {
        if (!model.isSimpleForm())
        {
            GUISelect.select(GUIXpath.PropertyDialogBoxContent.PRESENTATION_TYPE_VALUE_INPUT, "complex");
        }
    }

    /**
     * Заполнение селектов
     * @param model модель данных типа "Контент"
     */
    private static void fillSelectOnForm(ContentForm model)
    {
        List<String> list = Json.GSON.fromJson(model.getCasesCode(), Json.LIST_STRING_TYPE);
        GUIMultiSelect.select(GUIXpath.PropertyDialogBoxContent.CASE_LIST_VALUE_INPUT,
                list.toArray(new String[list.size()]));
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.ATTRIBUTE_GROUP_LIST_VALUE_INPUT,
                model.getAttributeGroupCode());
        fillPresentation(model);
        fillPosition(model);
    }

    /**
     * Заполнение основных полей карточки объекта контента "Диаграмма жизненного цикла"
     * (Заполняет поле Название и отображать название)
     * @param model модель данных типа "Контент"
     */
    private static void fillWfDiagram(ContentForm model)
    {
        fillTitleAndCodeOnCardForm(model);
        fillPosition(model);
    }

    /**
     * Получить название контента "по умолчанию" по типу контента
     * @param contentType тип контента
     */
    public static String getAutoName(ContentType contentType)
    {
        switch (contentType)
        {
            case TAB_BAR:
            case PROPERTY_LIST:
            case REL_OBJ_PROP_LIST:
            case EDITABLE_PROPERTY_LIST:
            case EMBEDDED_APPLICATION:
                return "";
            case SELECT_CONTACTS:
            case SELECT_CASE:
            case SELECT_SC_CASE:
            case CLIENT_INFO:
            case MASS_PROBLEMS:
            case SELECT_PARENT:
            case SELECT_CLIENT:
            case OBJECT_LIST:
            case REPORT:
            case REPORT_LIST:
            case RELATION_SCHEME:
            case NETWORK_SCHEME:
            case WF_DIAGRAM:
            case GANTT:
                return GUITester.getValue(GUIXpath.InputComplex.TYPE_VALUE);
            case CHILD_OBJECT_LIST:
                return "Вложенные объекты";
            case RELATED_OBJECT_LIST:
                return "Связанные объекты";
            case COMMENT_LIST:
                return "Комментарии";
            case EVENT_LIST:
                return "История изменений";
            case FILE_LIST:
                return "Файлы";
            default:
                Assert.fail("Был выбран не определенный в тестирующей системе контент");
                return null;
        }
    }
}
