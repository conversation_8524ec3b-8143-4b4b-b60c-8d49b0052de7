package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Базовая реализация валидатора для возможных значений атрибута, представленных в виде дерева.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public abstract class BaseTreePossibleValuesValidator<S extends BasePossibleValuesValidator<BaseTreePossibleValueValidator<?>, S>>
        extends BasePossibleValuesValidator<BaseTreePossibleValueValidator<?>, S>
{
    private boolean hasMore;

    BaseTreePossibleValuesValidator()
    {
        super("?.values");
    }

    /**
     * Позволяет валидировать флаг того, что уровень дерева возвращён не полностью
     */
    @SuppressWarnings("unchecked")
    public S hasMore(boolean hasMore)
    {
        this.hasMore = hasMore;
        return (S)this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);
        response.body(absolutePath + ".hasMore", is(hasMore));
    }
}
