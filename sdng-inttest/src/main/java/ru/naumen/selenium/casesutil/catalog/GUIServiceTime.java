package ru.naumen.selenium.casesutil.catalog;

import java.io.File;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.StringUtils;

/**
 * утилитарные методы для работы с элементом справочника "Классы обслуживания"
 * <AUTHOR>
 * @since 15.05.2013
 *
 */
public class GUIServiceTime extends CoreTester
{
    public static final String X_PICTOGRAM_ST_PATTERN = "//tr[contains(@class, '%s')]" + GUIXpath.Span.ANY_ICON;
    static final String X_PICTOGRAM_EXCLUSION_PATTERN = "//*[@id='gwt-debug-dateTitle' and text()='%s']/../../.."
                                                        + GUIXpath.Span.ANY_ICON;
    static final String X_PERIOD_ST_PATTERN = "//tr[contains(@class,'%s')]//*[@id='gwt-debug-periodTitles']";
    static final String X_PERIOD_EXCLUSION_PATTERN = "//*[@id='gwt-debug-dateTitle' and text()='%s']/../../."
                                                     + ".//*[@id='gwt-debug-periodTitles']";
    static final String X_PERIOD_EXCLUSION_PATTERN_ONLY_DAY = "//*[@id='gwt-debug-dateTitle' and text()='%s']";
    static final String X_MINUTE_FINISH = "//div[@id='gwt-debug-minute-finish']//input";
    static final String X_HOUR_FINISH = "//div[@id='gwt-debug-hour-finish']//input";
    static final String X_MINUTE_START = "//div[@id='gwt-debug-minute-start']//input";
    static final String X_HOUR_START = "//div[@id='gwt-debug-hour-start']//input";
    /**Пиктограмма 'Удалить' на форме редактирования периодов обслуживания (указывается номер строки).*/
    public static final String X_DELETE_PERIOD_PICTOGRAM_ST = "(//*[@id='gwt-debug-container']//span[contains(@id,"
                                                              + "'gwt-debug-delPeriodImg-srvTimePeriod')])[%s]";
    public static final String X_DELETE_EXCLUSION_PICTOGRAM_ST = "(//*[@id='gwt-debug-container']//span[contains(@id,"
                                                                 + "'gwt-debug-delPeriodImg-srvTimeExcl')])[%s]";
    public static final String X_DELETE_EXCLUSION_PICTOGRAM_WITH_EXCL_UUID = "//*[@id='gwt-debug-container']//span"
                                                                             + "[contains(@id,'gwt-debug-delPeriodImg"
                                                                             + "-%s')]";
    public static final String X_INFO_MESSAGE_DEFAULT_ST = "//*[@id='gwt-debug-attention']/span[2]";
    public static final String UPLOAD_EXCLUSIONS_FILE_BUTTON = "//*[@id='gwt-debug-icalFile-value']";
    public static final String DOWNLOAD_EXCLUSIONS_FILE_BUTTON = "//div[@id='gwt-debug-exportAdvlist']";
    public static final String STATE_VALUE = String.format(Div.ANY_VALUE, "state");

    public static final String FOLDER_UPLOAD_FILES_PATH = Config.get().getResourceDir()
                                                          + String.format("uploadFiles%s", File.separator);

    /**
     * Нажать на кнопку "Добавить исключение"
     */
    public static void clickAddExclusion()
    {
        tester.click(GUIXpath.Other.TOOL_BAR + GUIXpath.Div.ADD);
    }

    /**
     * Добавить дату исключения в класс обслуживания
     * @param exclusionDate дата исключения (например: "30.01.2012")
     */
    public static void sendExclusionDate(String exclusionDate)
    {
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.DEBUG_VALUE + "//input", exclusionDate);
        GUIForm.applyForm();
    }

    /**
     * Добавить исключение в график обслуживание. (без заполнения периода)
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclusionDate дата исключения (например: "30.01.2012")
     */
    public static void addExclusion(CatalogItem item, String exclusionDate)
    {
        GUICatalogItem.goToCard(item);
        clickAddExclusion();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        sendExclusionDate(exclusionDate);
    }

    /**
     * Заполнить исключению период обслуживания
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclutionDate дата уже существующего исключения (например: "30.01.2012")
     * @param hourStart час начала периода - значения [0,1,...,23]
     * @param minuteStart минута начала периода - значения [0,15,30,45]
     * @param hourFinish час окончания периода - значения [0,1,...,23]
     * @param minuteFinish минута окончания периода - значения [0,15,30,45]
     */
    public static void addExclusionPeriod(CatalogItem item, String exclutionDate, String hourStart, String minuteStart,
            String hourFinish, String minuteFinish)
    {
        GUICatalogItem.goToCard(item);
        GUIServiceTime.clickPictogramExclusion(exclutionDate, "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm(hourStart, minuteStart, hourFinish, minuteFinish);
        GUIForm.applyForm();
    }

    /**
     * Добавить исключение в график обслуживание с заполнением периода
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclutionDate дата исключения (например: "30.01.2012")
     * @param hourStart час начала периода - значения [0,1,...,23]
     * @param minuteStart минута начала периода - значения [0,15,30,45]
     * @param hourFinish час окончания периода - значения [0,1,...,23]
     * @param minuteFinish минута окончания периода - значения [0,15,30,45]
     */
    public static void addExclusionWithPeriod(CatalogItem item, String exclutionDate, String hourStart,
            String minuteStart, String hourFinish, String minuteFinish)
    {
        addExclusion(item, exclutionDate);
        GUIServiceTime.clickPictogramExclusion(exclutionDate, "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm(hourStart, minuteStart, hourFinish, minuteFinish);
        GUIForm.applyForm();
    }

    /**
     * Добавление периода в класс обслуживания
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param dayOfWeek день недели - значения [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
     * @param hourStart час начала периода - значения [0,1,...,23]
     * @param minuteStart минута начала периода - значения [0,15,30,45]
     * @param hourFinish час окончания периода - значения [0,1,...,23]
     * @param minuteFinish минута окончания периода - значения [0,15,30,45].
     */
    public static void addSTPeriod(CatalogItem item, String dayOfWeek, String hourStart, String minuteStart,
            String hourFinish, String minuteFinish)
    {
        GUICatalogItem.goToCard(item);
        GUIServiceTime.clickPictogramST(dayOfWeek, "add");
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        GUIServiceTime.fillPeriodOnForm(hourStart, minuteStart, hourFinish, minuteFinish);
        GUIForm.applyForm();
    }

    /**
     * Проверить, класс обслуживания находится в статусе "Черновик"
     */
    public static void assertStateDraft()
    {
        assertState("Черновик");
    }

    /**
     * Проверить, класс обслуживания находится в статусе "Активный"
     */
    public static void assertStateActive()
    {
        assertState("Активный");
    }

    /**
     * Проверить, класс обслуживания находится в статусе "Старая версия"
     */
    public static void assertStateOldVersion()
    {
        assertState("Старая версия");
    }

    /**
     * Проверить статус класса обслуживания на карточке справочника
     * @param expectedState ожидаемый статус
     */
    public static void assertState(String expectedState)
    {
        String state = tester.getText(STATE_VALUE);
        String msg = String.format("Статус класса обслуживания не совпал. Ожидается: %s, получен: %s",
                expectedState, state);
        Assert.assertEquals(msg, state, expectedState);
    }

    /**
     * Убедиться в появлении ошибки при добавлении исключение в график обслуживание. (без заполнения периода)
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclutionDate дата исключения (например: "30.01.2012")
     * @param errorMessage текст ошибки
     */
    public static void assertAddExclusionError(CatalogItem item, String exclutionDate, String errorMessage)
    {
        GUICatalogItem.goToCard(item);
        clickAddExclusion();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.sendKeys(GUIXpath.Div.FORM_CONTAINS + GUIXpath.Any.DEBUG_VALUE + "//input", exclutionDate);
        GUIForm.applyFormAssertError(errorMessage);
    }

    /**
     * Проверить периоды исключения у определенной даты в таблице "Исключения в графике обслуживания", в карточке
     * элемента справочника "Классы обслуживания"
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclutionDate дата исключения (например: "30.01.2012")
     */
    public static void assertAllDayExclusion(CatalogItem item, String exclutionDate)
    {
        GUICatalogItem.goToCard(item);
        Assert.assertTrue("В элементе справочника Классы обслуживания отсутствуют Исключения в графике обслуживания ",
                tester.waitAppear(GUIContent.X_BLOCK_TITLE, "Исключения в графике обслуживания"));
        String actual = tester.getText(X_PERIOD_EXCLUSION_PATTERN_ONLY_DAY, exclutionDate);
        String message = String.format(
                "Полученое значение периода исключения в таблице \"Исключения в графике обслуживания\" не совпало с "
                + "ожидаемым."
                + "Полученое значение \"%s\".", actual);
        Assert.assertNotNull(message, actual);
    }

    /**
     * Проверить отсутствие периода исключения на определенную дату в таблице "Исключения в графике обслуживания", в
     * карточке
     * элемента справочника "Классы обслуживания"
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclutionDate дата исключения (например: "30.01.2012")
     */
    public static void assertAllDayExclusionMissing(CatalogItem item, String exclutionDate)
    {
        GUICatalogItem.goToCard(item);
        Assert.assertTrue("В элементе справочника Классы обслуживания отсутствуют Исключения в графике обслуживания ",
                tester.waitAppear(GUIContent.X_BLOCK_TITLE, "Исключения в графике обслуживания"));
        Assert.assertTrue(tester.waitDisappear(X_PERIOD_EXCLUSION_PATTERN_ONLY_DAY, exclutionDate));
    }

    /**
     * Проверить периоды исключения у определенной даты в таблице "Исключения в графике обслуживания", в карточке
     * элемента справочника "Классы обслуживания"
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param exclusionDate дата исключения (например: "30.01.2012")
     * @param expectedPeriod ожидаемое содержимое графы в столбце "Периоды исключения". (например 3:45-5:30, 6:45-11:15,
     * если поле пусто то передавать пустую строку - "")
     */
    public static void assertExclusion(CatalogItem item, String exclusionDate, String expectedPeriod)
    {
        GUICatalogItem.goToCard(item);
        Assert.assertTrue("В элементе справочника Классы обслуживания отсутствуют Исключения в графике обслуживания ",
                tester.waitAppear(GUIContent.X_BLOCK_TITLE, "Исключения в графике обслуживания"));
        String xpath = String.format(X_PERIOD_EXCLUSION_PATTERN, exclusionDate);
        String actual = StringUtils.isEmpty(expectedPeriod) ? tester.find(xpath).getText() : tester.getText(xpath);
        String message = String
                .format("Полученое значение периода исключения в таблице \"Исключения в графике обслуживания\" не "
                        + "совпало с ожидаемым."
                        + "Полученое значение \"%s\", ожидаемое значение \"%s\".", actual, expectedPeriod);
        Assert.assertEquals(message, expectedPeriod, actual);
    }

    /**
     * Проверить присутствие в экспортируемом файле указанных исключений
     * @param exclusionsFile файл с исключениями
     * @param exclusions даты исключений
     */
    public static void assertFileContainsExclusions(File exclusionsFile, String... exclusions)
    {
        String fileContent = FileUtils.readAll(exclusionsFile.getAbsolutePath());

        for (String exclusion : exclusions)
        {
            Assert.assertTrue("В файле содержится ожидаемое исключение", fileContent.contains(exclusion));
        }
    }

    /**
     * Проверить периоды обслуживания у определенного дня в таблице "График обслуживания", в карточке элемента
     * справочника
     * "Классы обслуживания"
     * @param item модель элемента системного справочника "Классы обслуживания"
     * @param dayOfWeek день недели - значения [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
     * @param expectedPeriod ожидаемое содержимое графы в столбце "Периоды обслуживания". (например 3:45-5:30,
     *                       6:45-11:15,
     * если поле пусто то передавать пустую строку - "")
     */
    public static void assertTSPeriod(CatalogItem item, String dayOfWeek, String expectedPeriod)
    {
        GUICatalogItem.goToCard(item);
        assertTSPeriodOnCard(dayOfWeek, expectedPeriod);
    }

    /**
     * Проверить периоды обслуживания у определенного дня в таблице "График обслуживания", в карточке элемента
     * справочника
     * "Классы обслуживания" на карточке справочника
     * @param dayOfWeek день недели - значения [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
     * @param expectedPeriod ожидаемое содержимое графы в столбце "Периоды обслуживания". (например 3:45-5:30,
     *                       6:45-11:15,
     * если поле пусто то передавать пустую строку - "")
     */
    public static void assertTSPeriodOnCard(String dayOfWeek, String expectedPeriod)
    {
        Assert.assertTrue("В элементе справочника Классы обслуживания отсутствует График обслуживания",
                tester.waitAppear(GUIContent.X_BLOCK_TITLE, "График обслуживания"));
        String actual = tester.getText(X_PERIOD_ST_PATTERN, dayOfWeek);
        String message = String.format(
                "Полученое значение периода обслуживания в таблице \"График обслуживания\" не совпало с ожидаемым."
                + "Полученое значение \"%s\", ожидаемое значение \"%s\".", actual, expectedPeriod);
        Assert.assertEquals(message, expectedPeriod, actual);
    }

    /**
     * Кликнуть на пиктограмму в таблице "Исключения в графике обслуживания" в карточке элемента справочника "Классы
     * обслуживания".
     * @param exclutionDate дата, соответствующая дате исключения. (например: "30.01.2012")
     * @param action действие - значения [add, edit, delete]
     */
    public static void clickPictogramExclusion(String exclutionDate, String action)
    {
        tester.click(X_PICTOGRAM_EXCLUSION_PATTERN, exclutionDate, action);
    }

    /**
     * Кликнуть на пиктограму в таблице "График обслуживания" в карточке элемента справочника "Классы обслуживания"
     * @param dayOfWeek день недели - значения [monday, tuesday, wednesday, thursday, friday, saturday, sunday]
     * @param action действие - значения [add, edit]
     */
    public static void clickPictogramST(String dayOfWeek, String action)
    {
        tester.click(X_PICTOGRAM_ST_PATTERN, dayOfWeek, action);
    }

    /**
     * Удалить период исключения с формы по индексу строки
     * @param rowIndex индекс удаляемого периода исключения в таблице
     */
    public static void deleteExclusionPeriodOnForm(String rowIndex)
    {
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(X_DELETE_EXCLUSION_PICTOGRAM_ST, rowIndex);
    }

    /**
     * Удалить период исключения с формы по uuid-у периода исключения
     * @param srvTimeExclUuid uuid периода исключения
     */
    public static void deleteExclusionPeriodOnFormByUuid(String srvTimeExclUuid)
    {
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(X_DELETE_EXCLUSION_PICTOGRAM_WITH_EXCL_UUID, srvTimeExclUuid);
    }

    /**
     * Заполнить период исключения или обслуживания.
     * Для использования метода должно быть открыто окно заполнения периода.
     * Метод только заполняет, но не закрывает форму
     * @param hourStart час начала периода - значения [0,1,...,23]
     * @param minuteStart минута начала периода - значения [0,15,30,45]
     * @param hourFinish час окончания периода - значения [0,1,...,23]
     * @param minuteFinish минута окончания периода - значения [0,15,30,45]
     */
    public static void fillPeriodOnForm(String hourStart, String minuteStart, String hourFinish, String minuteFinish)
    {
        GUISelect.selectWithScroll(X_HOUR_START, String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, hourStart));
        GUISelect.selectWithScroll(X_MINUTE_START,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, minuteStart));
        GUISelect.selectWithScroll(X_HOUR_FINISH,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, hourFinish));
        GUISelect.selectWithScroll(X_MINUTE_FINISH,
                String.format(GUIXpath.Complex.POPUP_LIST_SELECT_PATTERN, minuteFinish));
    }

}
