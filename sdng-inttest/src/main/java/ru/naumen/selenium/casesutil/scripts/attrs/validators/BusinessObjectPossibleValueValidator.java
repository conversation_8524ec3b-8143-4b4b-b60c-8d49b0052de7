package ru.naumen.selenium.casesutil.scripts.attrs.validators;

import java.util.Map;

import org.junit.Assert;

/**
 * Валидатор возможного значения атрибута, полученного через методы скриптового API.
 * Позволяет валидировать результаты для атрибутов типов "Ссылка на БО", "Набор ссылок на БО", "Обратная ссылка",
 * "Агрегирующий", "Ответственный", "Элемент справочника", "Набор элементов справочника".
 * @see AbstractPossibleValueValidator
 *
 * <AUTHOR>
 * @since 19.05.2023
 */
@SuppressWarnings({ "java:S1845", "java:S5960" }) // код тестов
public class BusinessObjectPossibleValueValidator
        extends AbstractPossibleValueValidator<BusinessObjectPossibleValueValidator>
{
    private static final String UUID = "uuid";
    private static final String METACLASS = "metaClass";

    private final String uuid;
    private final String metaClass;

    /**
     * Создаёт валидатор для элемента дерева возможных значений атрибута
     *
     * @param uuid идентификатор
     * @param title название
     * @param metaClass FQN типа
     */
    public BusinessObjectPossibleValueValidator(String uuid, String title, String metaClass)
    {
        super(title);
        this.uuid = uuid;
        this.metaClass = metaClass;
    }

    @Override
    public void validate(Map<String, Object> actualValue, String pathPrefix)
    {
        super.validate(actualValue, pathPrefix);

        Assert.assertEquals(pathPrefix + UUID, uuid, actualValue.get(UUID));
        Assert.assertEquals(pathPrefix + METACLASS, metaClass, actualValue.get(METACLASS));
    }
}
