package ru.naumen.selenium.casesutil.mobile.contents.listcontent;

import static ru.naumen.selenium.casesutil.CoreTester.tester;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath.Div;

/**
 * Объект, предоставляющий действия, доступные при работе с формой действий в контентах "Список вложенных объектов" и
 * "Список связанных объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class GUIMobileListContentActionSettingsForm
{
    private static final String ADD_ACTION_BUTTON_XPATH = Div.PROPERTY_DIALOG_BOX + "//*[@id='gwt-debug-toolBar']"
                                                          + "//div[@id='gwt-debug-add']";
    /** Путь до кнопки "Добавить действия" на форме */
    public static final String ADD_EDIT_ACTION_FORM_XPATH = "//div[@id='gwt-debug-mobile-edit-actions-form']";

    /**
     * Переходит в список действий таблицы действий
     */
    public GUIMobileActionSettingsTable table()
    {
        return new GUIMobileActionSettingsTable(this);
    }

    /**
     * Нажимает кнопку "Добавить действие". Все введённые данные будут записаны в <code>action</code>
     *
     * @param action объект, в котором будут все данные заполненные на форме
     */
    public GUIMobileActionSettingsAddActionForm addAction(MobileListContentActionHolder action)
    {
        tester.click(ADD_ACTION_BUTTON_XPATH);
        Assert.assertTrue("Форма добавления/редактирования действия не открылась!",
                tester.waitAppear(ADD_EDIT_ACTION_FORM_XPATH));
        return new GUIMobileActionSettingsAddActionForm(this, action);
    }

    /**
     * Нажимает кнопку "Добавить действие"
     */
    public GUIMobileActionSettingsAddActionForm addAction()
    {
        return addAction(new MobileListContentActionHolder());
    }

    /**
     * Удаляет все действия, которые возможно удалить в контенте
     */
    public void clearActions()
    {
        table().allActions(GUIMobileActionSettingsAction::delete)
                .toForm()
                .apply();
    }

    /**
     * Нажимает кнопку "Сохранить" на форме
     */
    public void apply()
    {
        GUIForm.applyForm();
    }

    /**
     * Нажимает кнопку "Отменить" на форме
     */
    public void cancel()
    {
        GUIForm.cancelForm();
    }
}
