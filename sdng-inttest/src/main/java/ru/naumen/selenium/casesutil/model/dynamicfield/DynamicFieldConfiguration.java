package ru.naumen.selenium.casesutil.model.dynamicfield;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ru.naumen.selenium.casesutil.model.CleanerGroup;
import ru.naumen.selenium.casesutil.model.IForRemoveModel;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SEDynamicField;

/**
 * Конфигурация динамических полей в АТ.
 * <AUTHOR>
 * @since Apr 07, 2024
 */
public class DynamicFieldConfiguration extends CleanerGroup implements IForRemoveModel
{
    private boolean applied = false;

    private String templateClassFqn;
    private Map<String, String> attributeMapping = new HashMap<>();
    private Map<String, String> typeMapping = new HashMap<>();
    private String pathToGroups;
    private List<String> templateVisibilityConditions = new ArrayList<>();
    private List<String> groupVisibilityConditions = new ArrayList<>();

    public String getTemplateClassFqn()
    {
        return templateClassFqn;
    }

    public void setTemplateClassFqn(String templateClassFqn)
    {
        this.templateClassFqn = templateClassFqn;
    }

    public Map<String, String> getAttributeMapping()
    {
        return attributeMapping;
    }

    public void setAttributeMapping(Map<String, String> attributeMapping)
    {
        this.attributeMapping = attributeMapping;
    }

    public Map<String, String> getTypeMapping()
    {
        return typeMapping;
    }

    public void setTypeMapping(Map<String, String> typeMapping)
    {
        this.typeMapping = typeMapping;
    }

    public String getPathToGroups()
    {
        return pathToGroups;
    }

    public void setPathToGroups(String pathToGroups)
    {
        this.pathToGroups = pathToGroups;
    }

    public List<String> getTemplateVisibilityConditions()
    {
        return templateVisibilityConditions;
    }

    public void setTemplateVisibilityConditions(List<String> templateVisibilityConditions)
    {
        this.templateVisibilityConditions = templateVisibilityConditions;
    }

    public List<String> getGroupVisibilityConditions()
    {
        return groupVisibilityConditions;
    }

    public void setGroupVisibilityConditions(List<String> groupVisibilityConditions)
    {
        this.groupVisibilityConditions = groupVisibilityConditions;
    }

    @Override
    public List<ScriptElement> addToRemoveScript()
    {
        return isApplied() ? Collections.singletonList(SEDynamicField.reload()) : Collections.emptyList();
    }

    public boolean isApplied()
    {
        return applied;
    }

    public void setApplied(boolean applied)
    {
        this.applied = applied;
    }
}
