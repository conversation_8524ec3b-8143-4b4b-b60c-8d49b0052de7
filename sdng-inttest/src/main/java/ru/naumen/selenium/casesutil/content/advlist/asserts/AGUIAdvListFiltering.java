package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.content.advlist.FilterCondition;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.interfaceelement.CoreTree;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.util.Colors;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для проверок, связанных с настройкой фильтрации advList-а, через интерфейс
 *
 * <AUTHOR>
 * @since 17.04.2015
 */
public class AGUIAdvListFiltering extends GUIAdvlist
{
    public AGUIAdvListFiltering(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверить, является ли выбранный объект в поле выбора архивным
     *
     * @param node модель бизнес-объекта
     */
    public void assertSelectObjectIsArch(int andNumber, int orNumber, Bo node)
    {
        assertColorSelectObject(andNumber, orNumber, Colors.GREY, node);
    }

    /**
     * Проверить цвет выбраного элемента
     *
     * @param expected ожидаемый цвет
     * @param node модель бизнес-объекта
     */
    private void assertColorSelectObject(int andNumber, int orNumber, String expected, Bo node)
    {
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET, andNumber, orNumber);
        WebElement selected = tester
                .getElementsWithExpectError(String.format(selectXpath + GUIXpath.Any.ANY, node.getUuid())).get(0);
        String currentColor = selected.getCssValue("color");
        Assert.assertEquals("Выбранный элемент другого цвета", expected, currentColor);
    }

    /**
     * Проверить список полей, по которым возможна фильтрация
     * (должна быть раскрыта панель настройки фильтров)
     *
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     * элементы
     * @param attrs набор моделей ожидаемых атрибутов (без элемента [не указано])
     */
    public void attrs(boolean order, boolean match, Attribute... attrs)
    {
        List<String> expected = ModelUtils.getTitles(attrs);
        GUISelect.assertSelectWithoutEmpty(getContentXpath() + FILTER_ATTRS_INPUT, expected, order, match, 1, 1);
    }

    /**
     * Проверить атрибуты, по которым невозможна фильтрация
     *
     * @param attrs набор моделей атрибутов
     */
    public void attrsNotDisplayed(Attribute... attrs)
    {
        String[] codes = Stream.of(attrs).map(Attribute::getFqn).toArray(String[]::new);
        GUISelect.assertNotDisplayed(String.format(getContentXpath() + FILTER_ATTRS_INPUT, 1, 1), codes);
    }

    /**
     * Проверить, является ли объект из выпадающего списка архивным
     *
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param node - БО
     */
    public void conditionInSelectIsArch(int andNumber, int orNumber, Bo node)
    {
        String archTitle = "(арх.) ".concat(node.getTitle());
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber);
        GUISelect.assertDisplayedByTitle(selectXpath, archTitle);
        GUISelect.assertElementColor(selectXpath, node.getUuid(), Colors.GREY);
        GUISelect.hideSelect(selectXpath);
        GUISelect.selectById(selectXpath, node.getUuid());
    }

    /**
     * Проверить, является ли объект из выпадающего списка архивным
     *
     * @param andNumber номер блока "И", в который добавляется фильтр
     * @param orNumber номер блока "ИЛИ", в который добавляется фильтр
     * @param nodes набор БО - родителей до нужного узла, включая сам узел
     */
    public void conditionInTreeIsArch(int andNumber, int orNumber, Bo... nodes)
    {
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber);
        tester.click(selectXpath);
        BoTree tree = new BoTree(selectXpath, false);
        tree.openTreeToNode(ModelUtils.extractUuids(nodes));
        tree.assertText("(арх.) ".concat(nodes[nodes.length - 1].getTitle()), nodes);
        tree.assertElementColor(Colors.GREY, ModelUtils.extractUuids(nodes));
        GUISelect.hideSelect(selectXpath);
    }

    /**
     * Проверить условия на панели с настроенными фильтрами
     *
     * @param expectedPattern шаблон ожидаемого значения с одним параметром
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке
     * через запятую
     * @see ru.naumen.selenium.util.StringUtils#checkShuffleStringPattern(String, String, String, Object...)
     */
    public void conditionOnMinPanel(String expectedPattern, Object... args)
    {
        String msg = "Полученный список настроенных фильтров:%n%s%nне совпал с ожидаемым:%n%s%n";
        GUITester.assertTextContainsWithMsg(getContentXpath() + FILTER_SAVED_TITLE,
                String.format(expectedPattern, args), msg);
    }

    /**
     * Проверить список критериев, по которым возможна фильтрация
     * (должна быть раскрыта панель настройки фильтров)
     *
     * @param conditions набор ожидаемых критериев
     */
    public void conditions(FilterCondition... conditions)
    {
        List<String> expected = Arrays.stream(conditions)
                .map((input) ->
                {
                    if (input == null)
                    {
                        throw new ErrorInCodeException("Критерий фильтрации не может быть null.");
                    }
                    return input.getTitle();
                })
                .collect(Collectors.toList());

        GUISelect.assertSelectWithoutEmpty(getContentXpath() + FILTER_CONDITIONS_INPUT, expected, false, true, 1, 1);
    }

    /**
     * Проверить список критериев, по которым невозможна фильтрация
     *
     * @param conditions набор ожидаемых критериев
     */
    public void conditionsAbsence(FilterCondition... conditions)
    {
        String select = String.format(getContentXpath() + FILTER_CONDITIONS_INPUT, 1, 1);
        for (FilterCondition cond : conditions)
        {
            GUISelect.assertNotDisplayed(select, cond.getCode(), cond.getTitle());
        }
    }

    /**
     * Проверить условия на панели с настроенными фильтрами
     *
     * @param expectedPattern шаблон ожидаемого значения с одним параметром
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемой строке
     * через запятую
     * @see ru.naumen.selenium.util.StringUtils#checkShuffleStringPattern(String, String, String, Object...)
     */
    public void conditonsOnMinPanel(String expectedPattern, Object... args)
    {
        String msg = "Полученный список настроенных фильтров:%n%s%nне совпал с ожидаемым:%n%s%n";

        boolean check = false;
        String actual = null;
        long startTime = System.currentTimeMillis();
        while (!check && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000)
        {
            actual = tester.getText(getContentXpath() + FILTER_SAVED_TITLE);
            check = StringUtils.checkShuffleStringPattern(actual, expectedPattern, ", ", args);
        }
        Assert.assertTrue(String.format(msg, actual, expectedPattern), check);
    }

    /**
     * Проверить список критериев, по которым возможна фильтрация
     *
     * @param conditions набор ожидаемых критериев
     */
    public void conditonsPresence(FilterCondition... conditions)
    {
        String select = String.format(getContentXpath() + FILTER_CONDITIONS_INPUT, 1, 1);
        for (FilterCondition cond : conditions)
        {
            GUISelect.assertDisplayed(select, cond.getCode());
        }
    }

    /**
     * Проверить текущий атрибут, который выбран для фильтрации
     *
     * @param attr модель ожидаемого атрибута, может быть null, тогда проверяется значение [не указано]
     */
    public void currentAttr(Attribute attr)
    {
        GUITester.assertValueWithMsg(getContentXpath() + FILTER_ATTRS_INPUT,
                attr == null ? GUISelect.EMPTY_VALUE : attr.getTitle(),
                "Полученный текущий атрибут фильтрации не совпадает с ожидаемым.", 1, 1);
    }

    /**
     * Проверить текущий критерий, по которому возможна фильтрация
     *
     * @param condition ожидаемый критерий, может быть null, тогда проверяется значение [нет элементов]
     */
    public void currentCondition(FilterCondition condition)
    {
        GUITester.assertValueWithMsg(getContentXpath() + FILTER_CONDITIONS_INPUT,
                condition == null ? GUISelect.NO_ELEMENTS : condition.getTitle(),
                "Полученный текущий критерий фильтрации не совпадает с ожидаемым.", 1, 1);
    }

    /**
     * Проверяет значение в поле выбора даты "c:" в фильтрации advlist'а (представление = Два поля ввода)
     */
    public void fromDateField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_DATE_FROM_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет значение в поле выбора даты "c:" в фильтрации advlist'а (представление = Поле ввода)
     */
    public void fromDateTimeField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_DATE_FROM_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет значение в поле выбора времени "c:" в фильтрации advlist'а (представление = Два поля ввода)
     */
    public void fromTimeField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_TIME_FROM_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверить, является ли выбранный объект в поле выбора архивным
     *
     * @param node модель бизнес-объекта
     * @param gray должен ли текст быть серым, true - да, false - нет
     */
    public void inputIsArch(int andNumber, int orNumber, Bo node, Boolean gray)
    {
        String archTitle = "(арх.) ".concat(node.getTitle());
        inputText(andNumber, orNumber, archTitle);
        if (gray)
        {
            inputColor(andNumber, orNumber, Colors.GREY);
        }
    }

    /**
     * Проверить, соответствует ли цвет текста, отображаемого в поле ввода ожидаемому
     *
     * @param expected ожидаемое значение
     */
    private void inputColor(int andNumber, int orNumber, String expected)
    {
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber);
        GUITester.assertColor(selectXpath, expected);
    }

    /**
     * Проверить, соответствует ли текст, отображаемый в поле ввода ожидаемому
     *
     * @param expected ожидаемое значение
     */
    public void inputText(int andNumber, int orNumber, String expected)
    {
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, andNumber, orNumber);
        GUITester.assertValue(selectXpath, expected);
    }

    /**
     * Проверить отсутствие панели с настроенными фильтрами
     */
    public void minPanelAbsence()
    {
        GUITester.assertExists(getContentXpath() + FILTER_MIN_PANEL, false,
                "Панель с настроенными фильтрами присутствует.");
    }

    /**
     * Проверить присутствие панели с настроенными фильтрами
     */
    public void minPanelPresence()
    {
        GUITester.assertExists(getContentXpath() + FILTER_MIN_PANEL, true,
                "Панель с настроенными фильтрами отсутствует.");
    }

    /**
     * Проверить отсутствие панели настройки фильтров
     */
    public void panelAbsence()
    {
        GUITester.assertExists(getContentXpath() + FILTER_PANEL, false, "Панель настройки фильтров присутствует.");
    }

    /**
     * Проверить присутствие панели настройки фильтров
     */
    public void panelPresence()
    {
        GUITester.assertExists(getContentXpath() + FILTER_PANEL, true, "Панель настройки фильтров отсутствует.");
    }

    /**
     * Проверить возможные значения выпадающего списка при фильтрации (полное совпадение с учетом порядка)
     * (должна быть раскрыта панель настройки фильтров)
     *
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     * элементы
     * @param values набор названий ожидаемых значений в виде списка
     */
    public void selectValue(boolean order, boolean match, List<String> values)
    {
        GUISelect.assertSelect(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, values, order, match, false, 1, 1);
    }

    /**
     * Проверить возможные значения выпадающего списка при фильтрации (полное совпадение с учетом порядка)
     * (должна быть раскрыта панель настройки фильтров)
     *
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     * элементы
     * @param values набор названий ожидаемых значений
     */
    public void selectValue(boolean order, boolean match, String... values)
    {
        GUISelect.assertSelect(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, Lists.newArrayList(values), order, match,
                false, 1, 1);
    }

    /**
     * Проверить отсутствие значений в выпадающем списке при фильтрации
     * (должна быть раскрыта панель настройки фильтров)
     *
     * @param values набор названий значений
     */
    public void selectValueNotDisplayed(String... values)
    {
        String selectXpath = String.format(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, 1, 1);
        for (String val : values)
        {
            GUISelect.assertNotDisplayedByTitle(selectXpath, val);
        }
    }

    public void textOnMinPanel(String expected)
    {
        String selectXpath = getContentXpath() + FILTER_MIN_PANEL_CONDITION_VALUE;
        GUITester.assertTextPresent(selectXpath, expected);
    }

    /**
     * Проверяет значение в поле выбора даты "по:" в фильтрации advlist'а (представление = Два поля ввода)
     */
    public void toDateField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_DATE_TO_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет значение в поле выбора даты "по:" в фильтрации advlist'а (представление = Поле ввода)
     */
    public void toDateTimeField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_DATE_TO_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет значение в поле выбора времени "по:" в фильтрации advlist'а (представление = Два поля ввода)
     */
    public void toTimeField(String expected)
    {
        GUITester.assertValue(FILTER_WIDGET_TIME_TO_INPUT, expected, getContentXpath(), 1, 1);
    }

    /**
     * Проверяет отсутствие объекта в дереве возможных значений фильтрации.
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param bos путь к проверяемому элементу дерева
     */
    public void treeValueAbsent(int andNumber, int orNumber, Bo... bos)
    {
        BoTree boTree = new BoTree(getContentXpath() + FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        boTree.assertPresentElement(false, bos);
    }

    /**
     * Проверяет отсутствие объекта в дереве возможных значений фильтрации.
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param uuids путь к проверяемому элементу дерева
     */
    public void treeValueAbsent(int andNumber, int orNumber, String... uuids)
    {
        BoTree boTree = new BoTree(getContentXpath() + FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        boTree.assertPresentElement(false, uuids);
    }

    /**
     * Проверяет наличие объекта в дереве возможных значений фильтрации.
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param bos путь к проверяемому элементу дерева
     */
    public void treeValuePresent(int andNumber, int orNumber, Bo... bos)
    {
        BoTree boTree = new BoTree(getContentXpath() + FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        boTree.assertPresentElement(true, bos);
    }

    /**
     * Проверяет наличие объекта в дереве возможных значений фильтрации.
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param uuids путь к проверяемому элементу дерева
     */
    public void treeValuePresent(int andNumber, int orNumber, String... uuids)
    {
        BoTree boTree = new BoTree(getContentXpath() + FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        boTree.assertPresentElement(true, uuids);
    }

    /**
     * Проверяет возможность выбора элемента в дереве значений фильтрации.
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param expected true, если проверяется доступность, в противоположном случае - false
     * @param uuids путь к проверяемому элементу дерева
     */
    public void treeValueSelectable(int andNumber, int orNumber, boolean expected, String... uuids)
    {
        BoTree boTree = new BoTree(getContentXpath() + FILTER_VALUE_WIDGET, false, andNumber, orNumber);
        boTree.assertEnableElement(expected, uuids);
    }

    /**
     * Проверяет, что присутствуют "плашки" с указанными значениями под полем выбора
     */
    public void valuesPresentOnTagForm(String... values)
    {
        for (String value : values)
        {
            Assert.assertTrue("На форме отсутствует серая плашка со значением: " + value,
                    tester.waitAppear(getContentXpath() + FILTER_VALUE_WIDGET +
                                      GUIXpath.Complex.SELECT_FORMTAG_WITH_TEXT, 1, 1, value));
        }
    }

    /**
     * Проверяет, что поле ввода значения для фильтрации отсутствует
     */
    public void valueWidgetAbsent()
    {
        GUITester.assertAbsent(getContentXpath() + FILTER_VALUE_WIDGET, "Поле ввода не должно присутствовать", 1, 1);
    }

    /**
     * Проверяет, что поле ввода значения для фильтрации присутствует
     */
    public void assertValueWidgetPresent()
    {
        GUITester.assertPresent(getContentXpath() + FILTER_VALUE_WIDGET, "Поле ввода должно присутствовать", 1, 1);
    }

    /**
     * Проверяет, что поле ввода значения для фильтрации не является деревом
     */
    public void valueWidgetNotTree()
    {
        tester.click(getContentXpath() + FILTER_VALUE_WIDGET_INPUT, 1, 1);
        GUITester.assertExists(CoreTree.X_VALUE_TREE, false, "Поле ввода не должно быть дерево");
    }

    /**
     * Проверить название атрибута в выпадающем списке
     *
     * @param andNumber номер элемента фильтрации "И"
     * @param orNumber номер элемента фильтрации "ИЛИ"
     * @param title название атрибута
     */
    public void attributeTitlePresent(int andNumber, int orNumber, String title)
    {
        String input = getContentXpath() + String.format(FILTER_ATTRS_INPUT, andNumber, orNumber);
        GUISelect.assertDisplayedByTitle(input, title);
    }

    /**
     * Проверить количество блоков фильтрации на форме редактирования
     *
     * @param andCount количество блоков И
     * @param orCount количество блоков Или
     */
    public void assertFilterBlockCount(int andCount, int orCount)
    {
        Assert.assertEquals(andCount, tester.findDisplayedElements(Div.ANY_CONTAINS, "ListFilterAndElement.").size());
        Assert.assertEquals(orCount, tester.findDisplayedElements(Div.ANY_CONTAINS, "ListFilterOrElement.").size());
    }
}
