package ru.naumen.selenium.casesutil.content.advlist;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIXpath.Id;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListEditableToolPanel.ToolPanelKind;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListPagingType.PagingType;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvList;
import ru.naumen.selenium.casesutil.content.advlist.presentation.GUIAdvListPresentation;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с адвлистами
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListUtil extends CoreTester
{
    private String contentId;

    private String contentXpath;
    private GUIAdvListContent content;
    private GUIAdvListToolPanel toolPanel;
    private GUIAdvListEditableToolPanel editableToolPanel;
    private GUIAdvlistObjectActions objectActions;
    private GUIAdvListEditableToolPanel editableMassToolPanel;
    private GUIAdvListEditableToolPanel editableListObjectToolPanel;
    private GUIAdvListPaging paging, topPaging;
    private GUIAdvListColumns columns;
    private GUIAdvListMassOperation mass;
    private GUIAdvlListEditableCell editCell;
    private GUIAdvListPresentation prs;
    private GUIAdvlistCopyFromTemplateForm copyFromTemplateForm;
    private AGUIAdvList asserts;

    /**
     * Создает объект для работы со списком на отдельной странице на основе ссылки на этот список.
     * @param link ссылка на список
     * @return объект для работы со списком на отдельной странице
     */
    public static GUIAdvListUtil forStandaloneList(String link)
    {
        int encodedParamsIndex = link.indexOf("encoded_text$");
        return encodedParamsIndex >= 0
                ? new GUIAdvListUtil(Id.STANDALONE_OBJECT_LIST_CONTENT_UUID, link.substring(encodedParamsIndex))
                : new GUIAdvListUtil(Id.STANDALONE_OBJECT_LIST_CONTENT_UUID, StringUtils.EMPTY_STRING);
    }

    /**
     * Создает объект для работы со списком связанных объектов на отдельной странице на основе ссылки на этот список.
     * @param link ссылка на список
     * @return объект для работы со списком на отдельной странице
     */
    public static GUIAdvListUtil forStandaloneRelatedList(String link)
    {
        int encodedParamsIndex = link.indexOf("encoded_text$");
        return encodedParamsIndex >= 0
                ? new GUIAdvListUtil(Id.STANDALONE_REL_OBJECT_LIST_CONTENT_UUID, link.substring(encodedParamsIndex))
                : new GUIAdvListUtil(Id.STANDALONE_REL_OBJECT_LIST_CONTENT_UUID, StringUtils.EMPTY_STRING);
    }

    /**
     * Создает объект для работы со списком вложенных объектов на отдельной странице на основе ссылки на этот список.
     * @param link ссылка на список
     * @return объект для работы со списком на отдельной странице
     */
    public static GUIAdvListUtil forStandaloneChildObjectList(String link)
    {
        int encodedParamsIndex = link.indexOf("encoded_text$");
        return encodedParamsIndex >= 0
                ? new GUIAdvListUtil(Id.STANDALONE_CHILD_OBJECT_LIST_CONTENT_UUID, link.substring(encodedParamsIndex))
                : new GUIAdvListUtil(Id.STANDALONE_CHILD_OBJECT_LIST_CONTENT_UUID, StringUtils.EMPTY_STRING);
    }

    /**
     * Конструктор
     * @param contentIdOrXpath id элемента, который является advList-ом, либо его xpath (может иметь символы
     *                         форматирования)
     * @param args параметры для форматированного contentIdOrXpath, могут отсутствовать
     */
    public GUIAdvListUtil(String contentIdOrXpath, Object... args)
    {
        String xpathOrId = String.format(contentIdOrXpath, args);
        if (StringUtils.isEmpty(xpathOrId) || StringUtils.checkStringPattern(xpathOrId, "\\(*/.*"))
        {
            this.contentXpath = xpathOrId;
        }
        else
        {
            this.contentId = xpathOrId;
        }
    }

    public AGUIAdvList asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvList(getContentIdOrXpath());
        }
        return asserts;
    }

    public GUIAdvListColumns columns()
    {
        if (columns == null)
        {
            columns = new GUIAdvListColumns(getContentIdOrXpath());
        }
        return columns;
    }

    public GUIAdvListContent content()
    {
        if (content == null)
        {
            content = new GUIAdvListContent(getContentIdOrXpath());
        }
        return content;
    }

    public GUIAdvlistCopyFromTemplateForm copyFromTemplateForm()
    {
        if (copyFromTemplateForm == null)
        {
            copyFromTemplateForm = new GUIAdvlistCopyFromTemplateForm(getContentIdOrXpath());
        }
        return copyFromTemplateForm;
    }

    public GUIAdvListEditableToolPanel editableListObjectToolPanel()
    {
        if (null == editableListObjectToolPanel)
        {
            editableListObjectToolPanel = new GUIAdvListEditableToolPanel(getContentIdOrXpath(),
                    ToolPanelKind.OBJECT_ACTIONS);
        }
        return editableListObjectToolPanel;
    }

    /**
     * Панель массовых операций
     */
    public GUIAdvListEditableToolPanel editableMassToolPanel()
    {
        if (null == editableMassToolPanel)
        {
            editableMassToolPanel = new GUIAdvListEditableToolPanel(getContentIdOrXpath(),
                    ToolPanelKind.MASS_OPERATIONS);
        }
        return editableMassToolPanel;
    }

    /**
     * Панель действий
     */
    public GUIAdvListEditableToolPanel editableToolPanel()
    {
        if (editableToolPanel == null)
        {
            editableToolPanel = new GUIAdvListEditableToolPanel(getContentIdOrXpath());
        }
        return editableToolPanel;
    }

    public GUIAdvListEditableToolPanel editableToolPanelByKind(String toolPanelKind)
    {
        editableMassToolPanel = new GUIAdvListEditableToolPanel(getContentIdOrXpath(), toolPanelKind);
        return editableMassToolPanel;
    }

    public GUIAdvlListEditableCell editCell()
    {
        if (editCell == null)
        {
            editCell = new GUIAdvlListEditableCell(getContentIdOrXpath());
        }
        return editCell;
    }

    public GUIAdvListMassOperation mass()
    {
        if (mass == null)
        {
            mass = new GUIAdvListMassOperation(getContentIdOrXpath());
        }
        return mass;
    }

    /**
     * Действия в списке объектов
     */
    public GUIAdvlistObjectActions objectActions()
    {
        if (objectActions == null)
        {
            objectActions = new GUIAdvlistObjectActions(getContentIdOrXpath());
        }
        return objectActions;
    }

    public GUIAdvListPaging paging()
    {
        if (paging == null)
        {
            paging = new GUIAdvListPaging(getContentIdOrXpath(), PagingType.BOTTOM);
        }
        return paging;
    }

    public GUIAdvListPresentation prs()
    {
        if (prs == null)
        {
            prs = new GUIAdvListPresentation(getContentIdOrXpath());
        }
        return prs;
    }

    public GUIAdvListToolPanel toolPanel()
    {
        if (toolPanel == null)
        {
            toolPanel = new GUIAdvListToolPanel(getContentIdOrXpath());
        }
        return toolPanel;
    }

    public GUIAdvListPaging topPaging()
    {
        if (topPaging == null)
        {
            topPaging = new GUIAdvListPaging(getContentIdOrXpath(), PagingType.TOP);
        }
        return topPaging;
    }

    private String getContentIdOrXpath()
    {
        return contentId == null ? contentXpath : contentId;
    }
}
