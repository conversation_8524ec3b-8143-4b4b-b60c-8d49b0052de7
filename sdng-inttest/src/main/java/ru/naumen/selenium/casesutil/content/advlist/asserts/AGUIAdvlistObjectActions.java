package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.EDITABLE_TOOL_FORM_ICON_FIELD;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.X_ACTION_VALUE_INPUT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.X_COPY_FROM_PARENT_BUTTON;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.X_OBJECT_ACTIONS_BLOCK;

import java.util.Arrays;

import jakarta.annotation.Nullable;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionInvocationMethod;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlistObjectActions.ActionsMenuPosition;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.EventAction;

/**
 * Проверки для блока "Действия в списке в админке"
 * <AUTHOR>
 *
 */
public class AGUIAdvlistObjectActions
{
    private static final String X_ANY_TABLE_ROW_ACTION_TITLE_COLUMN = "//tr//*[@id='actionTitle' and contains(text(),"
                                                                      + " '%s')]";
    private static final String X_ANY_TABLE_ROW_ACTION_COLUMN = "//tr//*[@id='actionColumn' and contains(text(), "
                                                                + "'%s')]";
    private static final String X_OBJECT_TABLE_ROW_ACTION_TITLE_COLUMN = "//tr[@__gwt_row=%s]//*[@id='actionTitle']";
    private static final String X_OBJECT_TABLE_ROW_ACTION_COLUMN = "//tr[@__gwt_row=%s]//*[@id='actionColumn']";
    private static final String X_OBJECT_TABLE_ROW_ACTION_INVOCATION_COLUMN = "//tr[@__gwt_row=%s]//*[@id"
                                                                              + "='actionInvocationColumn']";
    public static final String RADIOBUTTON_INPUT = "//input[@id='gwt-debug-%s-input']";

    /**
     * Проверить, что отсутствует кнопка "Скопировать настройки из родителя"
     */
    public void assertCopyButtonAbsent()
    {
        assertCopyButtonPresent(false);
    }

    /**
     * Проверить, что присутствует кнопка "Скопировать настройки из родителя"
     */
    public void assertCopyFromParentButtonPresent()
    {
        assertCopyButtonPresent(true);
    }

    /**
     * Проверяет отсутствие строки действия с указанным названием.
     * @param itemTitle название действия (пункта меню)
     */
    public void assertMenuItemAbsent(String itemTitle)
    {
        String message = String.format("Пункт меню действий '%s' присутствует в списке.", itemTitle);
        GUITester.assertAbsent(X_ANY_TABLE_ROW_ACTION_TITLE_COLUMN, message, itemTitle);
    }

    /**
     * Проверяет отсутствие строки действия с указанным описанием.
     * @param itemDescription описание действия (пункта меню)
     */
    public void assertMenuItemAbsentByDescription(String itemDescription)
    {
        String message = String.format("Пункт меню действий с описанием '%s' присутствует в списке.", itemDescription);
        GUITester.assertAbsent(X_ANY_TABLE_ROW_ACTION_COLUMN, message, itemDescription);
    }

    /**
     * Проверить наличие действия в таблице по заданному индексу
     * @param position индекс действия в таблице
     * @param itemTitle название действия
     * @param itemAction действие, может быть null тогда проверяться не будет
     * @param itemInvocation способ вызова действий, может быть null, тогда проверится
     * {@link ActionInvocationMethod#ACTION_LIST}
     */
    public void assertMenuItemPresent(int position, String itemTitle, @Nullable String itemAction,
            @Nullable ActionInvocationMethod itemInvocation)
    {
        String invocationTitle = itemInvocation == null ? ActionInvocationMethod.ACTION_LIST.getTitle()
                : itemInvocation.getTitle();

        String titleMissing = "В строке списка под номером: " + position + " в колонке 'Название' не найден текст: "
                              + itemTitle;
        String invocationMissing = "В строке списка под номером: " + position
                                   + " в колонке 'Способ вызова действия' не найден текст: " + invocationTitle;
        GUITester.assertTextPresentWithMsg(X_OBJECT_TABLE_ROW_ACTION_TITLE_COLUMN, itemTitle, titleMissing, position);
        GUITester.assertTextPresentWithMsg(X_OBJECT_TABLE_ROW_ACTION_INVOCATION_COLUMN, invocationTitle,
                invocationMissing, position);
        if (itemAction != null)
        {
            String actionMissing = "В строке списка под номером: " + position
                                   + " в колонке 'Действие' не найден текст: " + itemTitle;
            GUITester.assertTextPresentWithMsg(X_OBJECT_TABLE_ROW_ACTION_COLUMN, itemAction, actionMissing, position);
        }
    }

    /**
     * Проверка значения расположения иконок
     */
    public void assertMenuPosition(ActionsMenuPosition position)
    {
        GUITester.assertRadioButton(X_OBJECT_ACTIONS_BLOCK + RADIOBUTTON_INPUT, true, position.toString());
    }

    /**
     * Проверка значения иконки вызова меню
     */
    public void assertObjectActionsMenuIcon(CatalogItem icon)
    {
        GUISelect.assertValue(EDITABLE_TOOL_FORM_ICON_FIELD, icon);
    }

    /**
     * Проверить что блок с действиями в списке объектов присутствует
     */
    public void assertObjectActionsBlockExists()
    {
        GUITester.assertExists(GUIAdvlistObjectActions.X_OBJECT_ACTIONS_BLOCK, true,
                "Блок действий в списке объектов отсутствует");
    }

    /**
     * Проверяет список доступных для выбора действий на форме редактирования.
     * @param mustExist true, если требуется проверить наличие, false - отсутствие
     * @param eventActions ожидаемый набор действий
     */
    public void availableActionsOnForm(boolean mustExist, EventAction... eventActions)
    {
        String[] codes = Arrays.stream(eventActions).map(ea -> ea.getUserEventUuid())
                .<String> toArray(n -> new String[n]);
        availiableActionsOnFormByCodes(mustExist, codes);
    }

    /**
     * Проверяет список доступных для выбора действий на форме редактирования.
     * @param mustExist true, если требуется проверить наличие, false - отсутствие
     * @param actions ожидаемый набор системных действий
     */
    public void availiableActionsOnFormByCodes(boolean mustExist, String... actions)
    {
        if (mustExist)
        {
            GUISelect.assertDisplayed(X_ACTION_VALUE_INPUT, actions);
        }
        else
        {
            GUISelect.assertNotDisplayed(X_ACTION_VALUE_INPUT, actions);
        }
    }

    private void assertCopyButtonPresent(boolean present)
    {
        String message = String.format("Кнопка \"Скопировать настройку из родителя\" %s",
                (present ? "отсутствует" : "присутствует"));
        GUITester.assertExists(X_COPY_FROM_PARENT_BUTTON, present, message);
    }

    /**
     * Проверить значение чекбокса "Использовать системные настройки" на форме редактирования
     * @param present ожидаемое значение
     */
    public void assertUseSystemSettings(boolean present)
    {
        GUITester.assertCheckboxState(
                GUIAdvlistObjectActions.X_OBJECT_ACTIONS_BLOCK + GUIAdvlistObjectActions.X_USE_SYSTEM_SETTINGS_CHECKBOX,
                present);
    }
}
