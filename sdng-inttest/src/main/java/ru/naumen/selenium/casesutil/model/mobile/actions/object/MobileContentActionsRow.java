package ru.naumen.selenium.casesutil.model.mobile.actions.object;

import java.util.List;

/**
 * Строка с действиями в верхнем или нижнем блоках
 *
 * <AUTHOR>
 * @since 19.08.2022
 */
public class MobileContentActionsRow
{
    /** Действия в одной строке */
    private List<MobileContentObjectAction> actions;

    public MobileContentActionsRow(List<MobileContentObjectAction> actions)
    {
        this.actions = actions;
    }

    public List<MobileContentObjectAction> getActions()
    {
        return actions;
    }

    public void setActions(List<MobileContentObjectAction> actions)
    {
        this.actions = actions;
    }
}