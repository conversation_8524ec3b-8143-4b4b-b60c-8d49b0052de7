package ru.naumen.selenium.casesutil.content;

import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;

import java.util.Locale;

import org.junit.Assert;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebElement;

import com.google.common.base.Preconditions;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.PropertyDialogBoxContent;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.interfaceelement.GUIFrame;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.core.BrowserTS.WebBrowserType;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.config.Config;

/**
 * Утилитарные методы для работы с контентом "Комментарии к объекту" в интерфейсе оператора
 * <AUTHOR>
 * @since 23.05.2012
 */
public class GUICommentList extends CoreTester
{
    public static final String ADD_COMMENT_BUTTON = "//div[contains(@id,'addComment')]";
    public static final String ADD_COMMENT_BUTTON_PATERN = GUIXpath.Div.ID_PATTERN + ADD_COMMENT_BUTTON;

    public static final String ADD_COMMENT_LINK = Div.CONTAINS_ADD_COMMENT;
    public static final String ADD_COMMENT_LINK_PATTERN = GUIXpath.Div.ID_PATTERN + ADD_COMMENT_LINK;

    public static final String ADD_COMMENT_INLINE_MIN_FORM = "//div[contains(@class,'addCommentMinForm')]";
    public static final String ADD_COMMENT_INLINE_MIN_FORM_PATTERN =
            GUIXpath.Div.ID_PATTERN + ADD_COMMENT_INLINE_MIN_FORM;

    public static final String COMMENT_ELEMENT = "//*[@id='%s']//*[@id='%s']";

    public static final String DEL_COMMENT_ICON = COMMENT_ELEMENT + GUIXpath.Span.DEL_ICON;
    public static final String EDIT_COMMENT_ICON = COMMENT_ELEMENT + GUIXpath.Span.EDIT_COMMENT_ICON;
    public static final String COMMENT_AUTHOR = COMMENT_ELEMENT + "//*[@__code='author']//span[@__code='name']";
    public static final String COMMENT_DATE = COMMENT_ELEMENT + "//span[@__code='date']";
    public static final String COMMENT_CONTENT = COMMENT_ELEMENT + "/div";

    public static final String COMMENT_PAGER = "//table[@id='commentList']//div[contains(@class, 'simplePager')]";
    public static final String COPY_COMMENT_DISABLED_PROPERTY = "//*[@id='gwt-debug-copyComment-caption']";
    public static final String X_COPY_COMMENT_CHECKBOX = "//*[@id='gwt-debug-copyComment-value']";

    public static final String X_COPY_COMMENT_CHECKBOX_INPUT = X_COPY_COMMENT_CHECKBOX + GUIForm.X_VALUE_INPUT;

    public static final String X_IS_PRIVATE_CHECKBOX = "//*[contains(@id,'gwt-debug-isPrivate') and contains(@id, "
                                                       + "'value')]";

    public static final String X_PRIVATE_CHECKBOX = "//*[contains(@id,'gwt-debug-private') and contains(@id, 'value')]";
    public static final String X_PRIVATE_CHECKBOX_INPUT = X_PRIVATE_CHECKBOX + GUIForm.X_VALUE_INPUT;
    public static final String X_PRIVATE_CHECKBOX_LABEL = X_PRIVATE_CHECKBOX + GUIForm.X_VALUE_LABEL;

    public static final String X_PRIVATE_CHECKBOXES = "(" + X_PRIVATE_CHECKBOX + "|" + X_IS_PRIVATE_CHECKBOX + ")";

    public static final String ACTIVE_MENTION = "//a[@data-alias = '%s' and contains(@href, '%s') and text() = '%s']";
    public static final String IFRAME = "//*[@id='%s']//iframe";
    public static final String NOT_ACTIVE_MENTION = "//a[@data-alias = '%s' and not(contains(@href, '%s')) and text()"
                                                    + " = '%s']";

    public static final String TOOL_PANEL_SHOW_BUTTON = "//*[@id='gwt-debug-toolPanelShowButton']";

    public static final String TOOL_PANEL_HIDE_BUTTON = "//*[@id='gwt-debug-toolPanelHideButton']";

    public static final String EXPAND_BUTTON = "//*[@id='gwt-debug-expandButton']";

    public static final String COLLAPSE_BUTTON = "//*[@id='gwt-debug-collapseButton']";

    public static final int FULL_FORM_HEIGHT = 270;

    private static final String TYPE_COMMENT = "Введите комментарий...";

    /**
     * Добавить комментарий 
     * @param commentList контент типа "Комментарии к объекту"
     * @param commentMessage текст комментария
     */
    public static void addComment(ContentForm commentList, String commentMessage)
    {
        GUICommentList.clickAddLink(commentList);
        setText(commentMessage);
        GUIForm.applyForm();
    }

    /**
     * Добавить комментарий 
     * @param commentList контент типа "Комментарии к объекту"
     * @param commentMessage текст комментария
     * @param isPrivate приватный
     * @param copy копировать
     */
    public static void addComment(ContentForm commentList, String commentMessage, boolean isPrivate, boolean copy)
    {
        GUICommentList.clickAddLink(commentList);
        setText(commentMessage);
        setPrivate(isPrivate);
        tester.setCheckbox(GUICommentList.X_COPY_COMMENT_CHECKBOX_INPUT, copy);
        GUIForm.applyForm();
    }

    /**
     * На форме добавления комментария установить текст комментария
     * @param text текст комментария
     */
    public static void setText(String text)
    {
        GUIRichText.sendKeys(GUIRichText.TEXT, text);
    }

    /**
     * На форме добавления комментария установить признак приватный
     * @param isPrivate true - приватный, иначе false
     */
    public static void setPrivate(boolean isPrivate)
    {
        tester.setCheckbox(GUICommentList.X_PRIVATE_CHECKBOX_INPUT, isPrivate);
    }

    /**
     * Проверить отсутствие упоминания в комментарии
     * @param mention - префикс упоминания
     * @param commentUuid - uuid комментария
     * @param bo - проверяемый БО
     */
    public static void assertAbsenceMentionInComment(String mention, String commentUuid, Bo bo)
    {
        GUIFrame.switchToFrame(IFRAME, commentUuid);
        Assert.assertTrue("Упоминание появилось",
                tester.waitDisappear(ACTIVE_MENTION, mention, bo.getUuid(), bo.getTitle()));
        GUIFrame.switchToTopWindow();
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" отсутствует ссылка "Добавить комментарий"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertAddLinkAbsence(ContentForm content)
    {
        String message = "В контенте типа 'Комментарии к объекту' присутствует ссылка 'Добавить комментарий'.";
        Assert.assertTrue(message, tester.waitDisappear(ADD_COMMENT_LINK_PATTERN, content.getXpathId()));
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" присутствует ссылка "Добавить комментарий"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertAddLinkPresent(ContentForm content)
    {
        String message = "В контенте типа 'Комментарии к объекту' отсутствует ссылка 'Добавить комментарий'.";
        Assert.assertTrue(message, tester.waitAppear(ADD_COMMENT_LINK_PATTERN, content.getXpathId()));
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" отсутствует инлайн форма добавления комментария
     * (Для вызова метода необходимо находится на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertAddCommentInlineMinFormAbsence(ContentForm content)
    {
        String message = "В контенте типа 'Комментарии к объекту' присутствует инлайн форма добавления комментария.";
        Assert.assertTrue(message, tester.waitDisappear(ADD_COMMENT_INLINE_MIN_FORM_PATTERN, content.getXpathId()));
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" присутствует инлайн форма добавления комментария
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertAddCommentInlineMinFormPresent(ContentForm content)
    {
        GUITester.assertValue(ADD_COMMENT_INLINE_MIN_FORM_PATTERN + "//input", TYPE_COMMENT, content.getXpathId());
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" присутствует инлайн форма добавления комментария
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertAddCommentInlineFormPresent(ContentForm content)
    {
        GUIForm.assertFormAppear(String.format(GUIXpath.Div.ID_PATTERN + GUIXpath.Div.FORM_CONTAINS,
                content.getXpathId()));
    }

    /**
     * Проверить, что упоминание появилось в комментарии и кликнуть по ссылке
     * @param mention - префикс упоминания
     * @param commentUuid - uuid комментария
     * @param bo - ожидаемый БО
     */
    public static void assertAndClickMentionComment(String mention, String commentUuid, Bo bo)
    {
        GUIFrame.switchToFrame(IFRAME, commentUuid);
        Assert.assertTrue("Упоминание не появилось",
                tester.waitAppear(ACTIVE_MENTION, mention, bo.getUuid(), bo.getTitle()));
        tester.click(ACTIVE_MENTION, mention, bo.getUuid(), bo.getTitle());
        GUIFrame.switchToTopWindow();
    }

    /**
     * Проверить соответствие автора комментария
     */
    public static void assertAuthor(ContentForm content, String commentUUID, String expectedAuthor)
    {
        String author = getAuthor(content, commentUUID);
        Assert.assertEquals(expectedAuthor, author);
    }

    /**
     * Проверить автора комментария
     * @param content модель контента, в котором находится комментарий
     * @param commentUuid uuid проверяемого комментария
     * @param expected ожидаемый автор
     */
    public static void assertCommentAuthor(final ContentForm content, final String commentUuid, final String expected)
    {
        Preconditions.checkNotNull(expected);
        String autor = GUICommentList.getAuthor(content, commentUuid);
        Assert.assertEquals("Автор комментария не соответствует ожидаемому", expected, autor);
    }

    /**
     * Проверить текст комментария
     * @param content модель контента, в котором находится комментарий
     * @param commentUuid uuid проверяемого комментария
     * @param expected ожидаемый текст
     */
    public static void assertCommentText(final ContentForm content, final String commentUuid, final String expected)
    {
        Preconditions.checkNotNull(expected);
        try
        {
            WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, input ->
            {
                String actual = "";
                if (tester.waitAppear(getCommentIframeXpath(content, commentUuid)))
                {
                    actual = getCommentText(content, commentUuid);
                }
                return expected.equals(actual);
            });
        }
        catch (TimeoutException e)
        {
            Assert.fail(String.format("Веб элемент с указанным текстом '%s' не найден.", expected));
        }
    }

    /**
     * Проверить что на форме добаления комментария отсутствует чекбокс 'Копировать'
     */
    public static void assertCopyCommentCheckboxAbsent()
    {
        String message = "На форме добавления комментария присутствует чекбос 'Копировать'.";
        Assert.assertTrue(message, tester.waitDisappear(X_COPY_COMMENT_CHECKBOX));
    }

    /**
     * Проверяет, что название параметра копирования комментария соответствует ожидаемому.
     * @param expected ожидаемое название параметра
     */
    public static void assertCopyCommentCheckboxCaption(String expected)
    {
        assertCopyCommentCheckboxPresent();
        GUITester.assertTextContainsWithMsg(GUIXpath.Any.ANY_VALUE_LABEL, expected,
                "Название параметра копирования комментария не совпало с ожидаемым.", "copyComment");
    }

    /**
     * Проверить что на форме добаления комментария присутствует чекбокс 'Копировать'
     */
    public static void assertCopyCommentCheckboxPresent()
    {
        String message = "На форме добавления комментария отсутствует чекбос 'Копировать'.";
        Assert.assertTrue(message, tester.waitAppear(X_COPY_COMMENT_CHECKBOX));
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" у всех комментариев отсутствует ссылка "Удалить"
     */
    public static void assertDeletePictureAbsence(ContentForm content)
    {
        String msg = "В контенте типа 'Комментарии к объекту' у объектов должна отсутствовать ссылка 'Удалить'.";
        for (String commentUuid : GUIComment.getCommentUUIDs(content))
        {
            Assert.assertTrue(msg,
                    tester.waitDisappear(DEL_COMMENT_ICON, content.getXpathId(), commentUuid)); // fontIcon
        }
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" у всех комментариев присутствует ссылка "Удалить"
     */
    public static void assertDeletePicturePresence(ContentForm content)
    {
        String message = "В контенте типа 'Комментарии к объекту' у объектов должна присутствовать ссылка 'Удалить'.";
        for (String commentUuid : GUIComment.getCommentUUIDs(content))
        {
            Assert.assertTrue(message,
                    tester.waitAppear(DEL_COMMENT_ICON, content.getXpathId(), commentUuid)); // fontIcon
        }
    }

    /**
     * Проверить, что на форме смены статуса запроса отсутствует неактивное свойство 'Копировать в ведомые объекты'
     */
    public static void assertDisabledCopyCommentPropertyAbsense()
    {
        Assert.assertTrue("На форме смены статуса присутствует неактивное свойство 'Копировать в ведомые объекты'",
                tester.waitDisappear(COPY_COMMENT_DISABLED_PROPERTY));
    }

    /**
     * Проверить, что на форме смены статуса запроса присутствует неактивное свойство 'Копировать в ведомые объекты'
     */
    public static void assertDisabledCopyCommentPropertyPresents()
    {
        Assert.assertTrue("На форме смены статуса отсутствует неактивное свойство 'Копировать в ведомые объекты'",
                tester.waitAppear(COPY_COMMENT_DISABLED_PROPERTY));
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" у всех комментариев отсутствует ссылка "Редактировать"
     */
    public static void assertEditPictureAbsence(ContentForm content)
    {
        String msg = "В контенте типа 'Комментарии к объекту' у объектов должна отсутствовать ссылка 'Редактировать'.";
        for (String commentUuid : GUIComment.getCommentUUIDs(content))
        {
            Assert.assertTrue(msg,
                    tester.waitDisappear(EDIT_COMMENT_ICON, content.getXpathId(), commentUuid)); // fontIcon
        }
    }

    /**
     * Проверить, что в контенте типа "Комментарии к объекту" у всех комментариев присутствует ссылка "Редактировать"
     */
    public static void assertEditPicturePresence(ContentForm content)
    {
        String message = "В контенте типа 'Комментарии к объекту' у объектов должна присутствовать ссылка "
                         + "'Редактировать'.";
        for (String commentUuid : GUIComment.getCommentUUIDs(content))
        {
            Assert.assertTrue(message,
                    tester.waitAppear(EDIT_COMMENT_ICON, content.getXpathId(), commentUuid)); // fontIcon
        }
    }

    /**
     * Проверить присутствие упоминания в комментарии
     * @param mention - префикс
     * @param commentUuid - uuid комментария
     * @param bo - БО, который должен присутствовать в комментарии
     */
    public static void assertPresentMentionInComment(String mention, String commentUuid, Bo bo)
    {
        GUIFrame.switchToFrame(IFRAME, commentUuid);
        Assert.assertTrue("Упоминание не появилось",
                tester.waitAppear(ACTIVE_MENTION, mention, bo.getUuid(), bo.getTitle()));
        GUIFrame.switchToTopWindow();
    }

    /**
     * Проверить присутствие неактивного упоминания в комментарии
     * @param mention - префикс
     * @param commentUuid - uuid комментария
     * @param bo - БО, который должен присутствовать в комментарии
     */
    public static void assertPresentNotActiveMention(String mention, String commentUuid, Bo bo)
    {
        GUIFrame.switchToFrame(IFRAME, commentUuid);
        Assert.assertTrue("Упоминание появилось",
                tester.waitAppear(NOT_ACTIVE_MENTION, mention, bo.getUuid(), bo.getTitle()));
        GUIFrame.switchToTopWindow();
    }

    /**
     * Проверить надпись, указывающюю на то, что комментарий приватный
     * @param content модель контента, в котором находится комментарий
     * @param commentUUID uuid проверяемого комментария
     * @param expected ожидаемая надпись
     */
    public static void assertPrivate(ContentForm content, String commentUUID, String expected)
    {
        String msg = "Отсутствует корректная надпись о том, что добавленный комментарий является приватным.";
        GUITester.assertTextContainsWithMsg(COMMENT_ELEMENT, expected, msg, content.getXpathId(), commentUUID);
    }

    /**
     * Проверить название чекбокса "Приватный" на формах с добавлением комментария
     * @param expected ожидаемое название
     */
    public static void assertPrivateCaptionOnForm(String expected)
    {
        String msg = "На форме отсутствует чекбокс 'Приватный' с названием: " + expected;
        GUITester.assertTextContainsWithMsg(X_PRIVATE_CHECKBOX_LABEL, expected, msg);
    }

    /**
     * Проверить, что на форме добавления/редактирования комментария отсутствует чекбокс "Приватный"
     * (должна быть открыта форма с возможностью ввести комментарий)
     */
    public static void assertPrivateCheckboxAbsence()
    {
        Assert.assertTrue("На форме присутствует чекбокс 'Приватный'", tester.waitDisappear(X_PRIVATE_CHECKBOXES));
    }

    /**
     * Проверить, что на форме добавления комментария присутствует чекбокс "Приватный"
     * (должна быть открыта форма с возможностью ввести комментарий)
     */
    public static void assertPrivateCheckboxPresent()
    {
        Assert.assertTrue("На форме отсутствует чекбокс 'Приватный'", tester.waitAppear(X_PRIVATE_CHECKBOXES));
    }

    /**
     * Проверить значение неактивного свойства 'Копировать в ведомые объекты' на форме смены статуса запроса
     * @param value false/true
     */
    public static void assertValueOfDisabledCopyCommentProperty(boolean value)
    {
        assertDisabledCopyCommentPropertyPresents();
        Assert.assertEquals("Копировать в ведомые объекты: " + (value ? "да" : "нет"),
                tester.getText(COPY_COMMENT_DISABLED_PROPERTY));
    }

    /**
     * Нажать кнопку "Добавить комментарий" на контенте "Комментарий"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарий")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void clickAddButton(ContentForm content)
    {
        tester.click(ADD_COMMENT_BUTTON_PATERN, content.getXpathId());
    }

    /**
     * Нажать ссылку "Добавить комментарий" в контенте "Комментарии"
     * (Для вызова метода необходимо находиться на карточке объекта)
     */
    public static void clickAddLink()
    {
        tester.click(ADD_COMMENT_LINK);
    }

    /**
     * Нажать ссылку "Добавить комментарий" на контенте типа "Комментарии к объекту"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void clickAddLink(ContentForm content)
    {
        tester.scrollIntoView(ADD_COMMENT_LINK_PATTERN, content.getXpathId());
        tester.click(ADD_COMMENT_LINK_PATTERN, content.getXpathId());
    }

    /**
     * Нажать на поле инлайн формы добавления комментария "Введите комментарий..." на контенте типа "Комментарии к
     * объекту"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void clickAddCommentInlineMinForm(ContentForm content)
    {
        tester.scrollIntoView(ADD_COMMENT_INLINE_MIN_FORM_PATTERN, content.getXpathId());
        tester.click(ADD_COMMENT_INLINE_MIN_FORM_PATTERN, content.getXpathId());
    }

    /**
     * Нажать изображение "Удалить" на контенте типа "Комментарии к объекту", напротив комментария 
     * указанного его uuid-ом
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUUID uuid отображаемого комментария в контенте
     */
    public static void clickDeletePicture(ContentForm content, String commentUUID)
    {
        tester.click(DEL_COMMENT_ICON, content.getXpathId(), commentUUID); //fontIcon
    }

    /**
     * Нажать изображение "Редактировать" на контенте типа "Комментарии к объекту", напротив комментария 
     * указанного его uuid-ом
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUUID uuid отображаемого комментария в контенте
     */
    public static void clickEditPicture(ContentForm content, String commentUUID)
    {
        tester.click(EDIT_COMMENT_ICON, content.getXpathId(), commentUUID); //fontIcon
    }

    /**
     * Кликнуть по коментарию ссылке
     * @param content контент комментарии к объекту
     * @param commentUuid uuid комментария
     */
    public static void clickLinkInComment(ContentForm content, String commentUuid)
    {
        //TODO for chrome
        if (WebBrowserType.CHROME.toString().equals(Config.get().getWebBrowserType().toUpperCase(Locale.ENGLISH)))
        {
            //@formatter:off
            String js = "var theFrameDocument = arguments[0].contentDocument || arguments[0].contentWindow.document;"
                      + "return theFrameDocument.getElementsByTagName('a')[0].click();";
            //@formatter:on
            WebElement iframe = tester.find(COMMENT_ELEMENT + "//iframe", content.getXpathId(), commentUuid);
            tester.runJavaScript(js, iframe);
            return;
        }

        try
        {
            WebElement iframe = tester.find(COMMENT_ELEMENT + "//iframe", content.getXpathId(), commentUuid);
            tester.getWebDriver().switchTo().frame(iframe);
            tester.click("//a");
        }
        finally
        {
            tester.getBrowserTS().getWebDriver().switchTo().defaultContent();
        }
    }

    /**
     * Кликнуть на кнопку открытия панели инструментов
     *
     * @param contentForm модель контента типа "Комментарии к объекту"
     */
    public static void clickToolPanelShowButton(ContentForm contentForm)
    {
        tester.click(TOOL_PANEL_SHOW_BUTTON, contentForm.getXpathId());
    }

    /**
     * Кликнуть на кнопку скрытия панели инструментов
     *
     * @param contentForm модель контента типа "Комментарии к объекту"
     */
    public static void clickToolPanelHideButton(ContentForm contentForm)
    {
        tester.click(TOOL_PANEL_HIDE_BUTTON, contentForm.getXpathId());
    }

    /**
     * Кликнуть на кнопку "Развернуть" на форме Inline-комментария
     *
     * @param contentForm модель контента типа "Комментарии к объекту"
     */
    public static void clickExpandButton(ContentForm contentForm)
    {
        tester.click(EXPAND_BUTTON, contentForm.getXpathId());
    }

    /**
     * Проверить наличие кнопки "Свернуть" на форме Inline-комментария
     */
    public static void assertCollapseButton()
    {
        Assert.assertTrue("На форме отсутствует кнопка 'Свернуть'", tester.waitAppear(COLLAPSE_BUTTON));
    }

    /**
     * Проверить наличие кнопки "Развернуть" на форме Inline-комментария
     */
    public static void assertExpandButton()
    {
        Assert.assertTrue("На форме отсутствует кнопка 'Развернуть'", tester.waitAppear(EXPAND_BUTTON));
    }

    /**
     * Нажать на кнопку "Свернуть" на форме Inline-комментария
     *
     * @param contentForm модель контента типа "Комментарии к объекту"
     */
    public static void clickCollapseButton(ContentForm contentForm)
    {
        tester.click(COLLAPSE_BUTTON, contentForm);
    }

    /**
     * Проверить, что форма Inline-комментария открыта в полном режиме
     */
    public static void assertAddCommentInlineFullFormPresent()
    {
        Assert.assertTrue("Полная форма добавления комментария не открылась",
                tester.find(GUIComment.X_IFRAME).getSize().getHeight() > FULL_FORM_HEIGHT);
    }

    /**
     * Проверить, что форма Inline-комментария открыта в компактном режиме
     */
    public static void assertAddCommentInlineCompactFormPresent()
    {
        Assert.assertTrue("Компактная форма добавления комментария не открылась",
                tester.find(GUIComment.X_IFRAME).getSize().getHeight() < FULL_FORM_HEIGHT);
    }

    /**
     * Удалить первый комментарий из отображаемого списка
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void deleteFirstComment(ContentForm content)
    {
        String uuid = GUIComment.getCommentUUIDs(content).iterator().next();
        clickDeletePicture(content, uuid);
        GUIForm.assertQuestionAppear("Отсутствует форма подтверждения удаления.");
        GUIForm.confirmByYes();
    }

    /**
     * Получить автора комментария в контенте типа "Комментарии к объекту", напротив комментария 
     * указанного его uuid-ом
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUUID uuid отображаемого комментария в контенте
     */
    public static String getAuthor(ContentForm content, String commentUUID)
    {
        return tester.getText(COMMENT_AUTHOR, content.getXpathId(), commentUUID);
    }

    /**
     * Получить xpath iframe'а, содержащего текст комментария
     * @param content - модель контента "Комментарии к объекту"
     * @param commentUuid - uuid комментария
     * @return xpath
     */
    public static String getCommentIframeXpath(ContentForm content, String commentUuid)
    {
        return String.format(COMMENT_ELEMENT + "//iframe", content.getXpathId(), commentUuid);
    }

    /**
     * Получить текст комментария в контенте типа "Комментарии к объекту", напротив комментария 
     * указанного его uuid-ом
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUUID uuid отображаемого комментария в контенте
     */
    public static String getCommentText(final ContentForm content, final String commentUUID)
    {
        return GUITester.getTextFromIframe(COMMENT_ELEMENT + "//iframe", content.getXpathId(), commentUUID);
    }

    /**
     * Получить дату комментария в контенте типа "Комментарии к объекту", напротив комментария 
     * указанного его uuid-ом
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param content модель контента типа "Параметры объекта"
     * @param commentUUID uuid отображаемого комментария в контенте
     */
    public static String getDateTime(ContentForm content, String commentUUID)
    {
        return tester.getText(COMMENT_DATE, content.getXpathId(), commentUUID);
    }

    /**
     * Нажать на поле инлайн формы добавления комментария "Введите комментарий..." на контенте типа "Комментарии к
     * объекту"
     * (Для вызова метода необходимо находиться на карточке объекта, содержащей контент "Комментарии к объекту")
     * @param xpathContent - xpath контента
     */
    public static void clickAddCommentInlineMinForm(String xpathContent)
    {
        String xpath = xpathContent + ADD_COMMENT_INLINE_MIN_FORM;
        tester.scrollIntoView(xpath);
        tester.click(xpath);
        if (!tester.waitAppear(EXPAND_BUTTON))
        {
            tester.click(xpath);
        }
    }

    /**
     * Отменить добавление инлайн комментария
     * @param xpathContent - xpath контента
     */
    public static void clickCancel(String xpathContent)
    {
        tester.click(xpathContent + GUIXpath.Div.CANCEL);
    }

    /**
     * Отменить добавление инлайн комментария
     * @param contentForm - контент комментариев
     */
    public static void clickCancel(ContentForm contentForm)
    {
        String format = String.format(Div.COMMENT_LIST, contentForm.getCode());
        clickCancel(format);
    }

    /**
     * Проверить цвет фона текста комментария на форме
     * @param expectedColor ожидаемый цвет
     */
    public static void assertTextBackgroundColor(String expectedColor)
    {
        GUIRichText.assertBackgroundColor(expectedColor);
    }

    /**
     * В списке комментариев проверить цвет фона комментария
     * @param content контент Комментарии
     * @param commentUUID идентификатор комментария
     * @param expectedColor ожидаемый цвет
     */
    public static void assertCommentBackgroundColor(ContentForm content, String commentUUID, String expectedColor)
    {
        GUITester.assertBackgroundColor(String.format(COMMENT_CONTENT, content.getXpathId(), commentUUID),
                expectedColor);
    }

    /**
     * На форме добавления комментария установить значение параметра "Показывать описание атрибутов на форме
     * добавления"
     * @param showAttrDescriptionOnAddForm true - показывать описание, иначе false
     */
    public static void setShowAttrDescriptionOnAddForm(boolean showAttrDescriptionOnAddForm)
    {
        tester.setCheckbox(PropertyDialogBoxContent.SHOW_ATTR_DESCRIPTION_FOR_ADD_FORM_VALUE_ON_FORM,
                showAttrDescriptionOnAddForm);
    }

    /**
     * На форме добавления комментария установить значение параметра "Показывать описание атрибутов на форме
     * добавления"
     * @param showAttrDescriptionOnEditForm true - показывать описание, иначе false
     */
    public static void setShowAttrDescriptionOnEditForm(boolean showAttrDescriptionOnEditForm)
    {
        tester.setCheckbox(PropertyDialogBoxContent.SHOW_ATTR_DESCRIPTION_FOR_EDIT_FORM_VALUE_ON_FORM,
                showAttrDescriptionOnEditForm);
    }
}
