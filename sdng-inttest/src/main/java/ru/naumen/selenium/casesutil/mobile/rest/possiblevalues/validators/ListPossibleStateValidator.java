package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.model.metaclass.BoStatus;

/**
 * Валидатор для возможного статуса в списке.
 *
 * <AUTHOR>
 * @since 29.03.2023
 */
public class ListPossibleStateValidator extends ListPossibleValueValidator
{
    private boolean hasAttributesOnForm = false;

    public ListPossibleStateValidator(BoStatus status)
    {
        super(status.getCode(), status.getTitle());
    }

    /**
     * Позволяет валидировать флаг доступности формы смены статуса для проверяемого перехода
     */
    public ListPossibleStateValidator hasAttributesOnForm(boolean hasAttributesOnForm)
    {
        this.hasAttributesOnForm = hasAttributesOnForm;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);

        response.body(absolutePath + "?.hasAttributesOnForm", is(hasAttributesOnForm));
    }
}
