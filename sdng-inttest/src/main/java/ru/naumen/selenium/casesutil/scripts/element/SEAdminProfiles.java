package ru.naumen.selenium.casesutil.scripts.element;

import java.util.List;

import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Фабрика скриптов для профилей администрирования. Используется при очистке
 * <AUTHOR>
 * @since 04.05.2024
 */
public class SEAdminProfiles
{
    /**
     * Создает скрипт удаления профилей администрирования.
     * @param codes коды профилей администрирования
     * @return скрипт удаления
     */
    public static ScriptElement deleteAdminProfile(String... codes)
    {
        ScriptModules.getSettingsSetModule();
        ScriptElement element = new ScriptElement();
        element.getParams().addAll(List.of(codes));
        element.setPathToFunctionFile("scripts/groovy/cleaner/deleteAdminProfile.groovy");
        return element;
    }
}