package ru.naumen.selenium.casesutil.content;

import java.util.List;
import java.util.Map;

import jakarta.annotation.Nullable;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIError;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.SpecificComplex;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityProfile;
import ru.naumen.selenium.casesutil.model.tag.Tag;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для работы с вкладками через интерфейс
 * <AUTHOR>
 * @since 02.10.2013
 */
public class GUITab extends CoreTester
{

    public static final String MAIN_TAB_PANEL = String.format(GUIXpath.Div.ANY, "mainTabPanel");
    public static final String X_TAB_PANEL = String.format(GUIXpath.Div.ANY, "tabBar");
    public static final String TABS_CONTENT_CONTAINER = String.format(GUIXpath.Div.ANY,
            "tabLayoutPanelContentContainer");
    public static final String MORE_TABS = String.format(GUIXpath.Div.ANY, "sub");

    public static final String MORE_SYSTEM_TABS_ADMIN = X_TAB_PANEL + MORE_TABS
                                                        + "[count(.//ancestor::div[@id='gwt-debug-panelWrapper'])=0 "
                                                        + "and count("
                                                        + ".//ancestor::table[@id='gwt-debug-Info'])=0]";
    private static final String MORE_TABS_ROOT_CONTENT_ADMIN =
            X_TAB_PANEL + MORE_TABS + "[count(.//ancestor::div[@id='gwt-debug-panelWrapper'])=1]";
    private static final String MORE_TABS_ROOT_CONTENT_OPERATOR = "(//div[@id='gwt-debug-tabBar'])"
                                                                  + "[1]//div[@id='gwt-debug-sub']";
    private static final String MORE_TABS_CONTENT = GUIXpath.Div.ID_PATTERN + MORE_TABS;
    private static final String X_TABS_PANEL = GUIXpath.Div.ID_PATTERN
                                               + "//div[contains(@class, 'gwt-TabLayoutPanelTabs')]";

    public static final String X_SUB_TAB_PANEL = "//div[@id='gwt-debug-tabBarPopup']";

    public static final String CAPTION = "caption";
    public static final String CODE = "value";
    public static final String COUNT_OBJECTS = "countObjects";
    public static final String HIDE_SINGLE_TAB = "hideSingleTab";
    public static final String DEPENDABLE_CONTENTS = "dependableContents";
    public static final String CONSIDER_LIST_FILTER = "considerListFilter";
    public static final String CACHE_OBJECTS_ON_TAB = "cacheNumberObjectsOnTab";
    public static final String X_CACHE_OBJECTS_ON_TAB = String.format(GUIXpath.Any.ANY_VALUE, CACHE_OBJECTS_ON_TAB);

    public static final String X_REFRESH_BTN = "//div[@id='gwt-debug-refresh']";
    public static final String X_CLOSE_BTN = "//div[@id='gwt-debug-close']";
    public static final String X_ROOT_TAB = Div.MAIN_CONTENT_HEADER + "//*[contains(@id, 'gwt-debug-Tab.')]";
    public static final String X_TAB = GUIXpath.Div.ID_PATTERN + "//*[contains(@id, 'gwt-debug-Tab.')]";
    /** Айдишник вкладки "Правило импорта" на карточке таблицы соответствий */
    public static final String IMPORT_CONFIG_TAB = "importConfig";
    /** Айдишник вкладки "История" на карточке таблицы соответствий */
    public static final String IMPORT_HISTORY_TAB = "importHistory";
    public static final String TAB_BAR_DEBUG_ID_PREFIX = "gwt-debug-TabBar.tabs_";
    public static final String TAB_DEBUG_ID_PREFIX = "gwt-debug-Tab.";
    public static final String TAB_BAR_CONTENT = "//div[@id='gwt-debug-TabBar.%s']";
    public static final String EDIT_TABS_ICON = TAB_BAR_CONTENT + String.format(GUIXpath.Span.ANY_ICON, "editTabs");

    /**
     * Добавляет вкладку на контент с вкладками
     * @param tabBarContentId debugId контента
     * @param title Название вкладки
     * @param code Код вкладки
     */
    public static void addTab(String tabBarContentId, String title, @Nullable String code)
    {
        GUIContent.clickEditTabs(tabBarContentId);
        GUITab.clickAdd();
        GUITab.setTabTitle(title);
        if (null != code)
        {
            GUITab.setTabCode(code);
        }
        GUIForm.applyLastModalForm();
        GUITab.closeEditTabsForm();
    }

    /**
     * Проверить, что в админке в главном контенте Панель вкладок отсутствует кнопка "Еще"
     */
    public static void assertAdminRootMoreTabsAbsence()
    {
        Assert.assertTrue("Кнопка 'Еще' присутствует в панели вкладок!",
                tester.waitDisappear(MORE_TABS_ROOT_CONTENT_ADMIN));
    }

    /**
     * Проверить, что в админке в главном контенте Панель вкладок присутствует кнопка "Еще"
     */
    public static void assertAdminRootMoreTabsPresent()
    {
        Assert.assertTrue("Кнопка 'Еще' отсутствует в панели вкладок!",
                tester.waitAppear(MORE_TABS_ROOT_CONTENT_ADMIN));
    }

    /**
     * Проверить, что в админке в панели с системными вкладками отсутствует кнопка "Еще"
     */
    public static void assertAdminSystemMoreTabsAbsence()
    {
        Assert.assertTrue("Кнопка 'Еще' присутствует в главной панели вкладок!",
                tester.waitDisappear(MORE_SYSTEM_TABS_ADMIN));
    }

    /**
     * Проверить, что в адмике на панели с системными вкладками присутствует кнопка "Еще"
     */
    public static void assertAdminSystemMoreTabsPresent()
    {
        Assert.assertTrue("Кнопка 'Еще' не присутствует в главной панели вкладок!",
                tester.waitAppear(MORE_SYSTEM_TABS_ADMIN));
    }

    /**
     * Проверить состояние чекбокса Учитывать фильтрацию списка для подсчета количества
     * (только на форме редактирования)
     * @param considerListFilter
     */
    public static void assertConsiderListFilter(boolean considerListFilter)
    {
        GUITester.assertCheckboxState(GUIXpath.InputComplex.ANY_VALUE, considerListFilter, CONSIDER_LIST_FILTER);
    }

    /**
     * Проверить, что в контенте Панель вкладок отсутствует кнопка "Еще"
     * @param tabBar контент Панель вкладок
     */
    public static void assertContentMoreTabsAbsence(ContentForm tabBar)
    {
        Assert.assertTrue("Кнопка 'Еще' присутствует в панели вкладок!",
                tester.waitDisappear(MORE_TABS_CONTENT, tabBar.getXpathId()));
    }

    /**
     * Проверить, что в контенте Панель вкладок присутствует кнопка "Еще"
     * @param tabBar контент Панель вкладок
     */
    public static void assertContentMoreTabsPresent(ContentForm tabBar)
    {
        Assert.assertTrue("Кнопка 'Еще' отсутствует в панели вкладок!",
                tester.waitAppear(MORE_TABS_CONTENT, tabBar.getXpathId()));
    }

    /**
     * Проверить состояние чекбокса Отображать количество объектов на вкладке
     * (только на форме редактирования)
     * @param countObjects
     */
    public static void assertCountObjects(boolean countObjects)
    {
        GUITester.assertCheckboxState(GUIXpath.InputComplex.ANY_VALUE, countObjects, COUNT_OBJECTS);
    }

    /**
     * Проверить состояние чекбокса Скрывать корешок вкладки, если она единственная
     * @param hideSingleTabs значение, которое нужно проверить
     */
    public static void assertHideSingleTab(boolean hideSingleTabs)
    {
        GUITester.assertCheckboxState(GUIXpath.InputComplex.ANY_VALUE, hideSingleTabs, HIDE_SINGLE_TAB);
    }

    /**
     * Проверить, что в операторе в корневой панели вкладок отсутствует кнопка "Еще"
     */
    public static void assertOperatorRootMoreTabsAbsence()
    {
        Assert.assertTrue("Кнопка 'Еще' присутствует в главной панели вкладок!",
                tester.waitDisappear(MORE_TABS_ROOT_CONTENT_OPERATOR));
    }

    /**
     * Проверить, что в операторе в корневой панели вкладок присутствует кнопка "Еще"
     */
    public static void assertOperatorRootMoreTabsPresent()
    {
        Assert.assertTrue("Кнопка 'Еще' отсутствует в главной панели вкладок!",
                tester.waitAppear(MORE_TABS_ROOT_CONTENT_OPERATOR));
    }

    /**
     * Проверить присутствие профиля на форме редактирования вкладки
     * @param profile проверяемый профиль
     */
    public static void assertPresentProfile(SecurityProfile profile)
    {
        Assert.assertTrue("Профиля на форме редактирования нет", tester.waitAppear(GUIXpath.Any.ANY, profile
                .getCode()));
    }

    /**
     * Проверить присутствие метки
     * @param tag проверяемая метка
     */
    public static void assertPresentTag(Tag tag)
    {
        Assert.assertTrue("Метки на форме редактирования нет", tester.waitAppear(GUIXpath.Any.ANY, tag.getCode()));
    }

    /**
     * Проверяет корневые вкладки на карточке объекта в интерфейсе оператора.
     * @param tabTitles названия вкладок в том порядке, в котором они должны отображаться
     */
    public static void assertRootTabs(String... tabTitles)
    {
        List<WebElement> tabs = tester.findElements(X_ROOT_TAB);
        Assert.assertEquals("Полученное количество вкладок в контенте не совпало с ожидаемым.", tabTitles.length,
                tabs.size());
        for (int i = 0; i < tabTitles.length; ++i)
        {
            Assert.assertEquals("Порядок вкладок не совпал с ожидаемым", tabTitles[i], tester.getText(tabs.get(i)));
        }
    }

    /**
     * Проверить, что всплывающий список со вкладками исчез
     */
    public static void assertSubPanelAbsence()
    {
        Assert.assertTrue("Всплывающая панель вкладок не скрылась!", tester.waitDisappear(X_SUB_TAB_PANEL));
    }

    /**
     * Проверить, что всплывающий список содержит названия вкладок в указанном порядке
     *  @param expectedTitles названия ожидаемых вкладок в указанном порядке
     *  (Ожидаемое количество вкладок может быть меньше, но порядок должен быть тот же)
     */
    public static void assertSubPanelContainsTabs(String... expectedTitles)
    {
        assertSubPanelPresent();
        GUITester.assertFindElements(X_SUB_TAB_PANEL + "//tr", Lists.newArrayList(expectedTitles), true, false);
    }

    /**
     * Проверить, что всплывающий список со вкладками появился
     */
    public static void assertSubPanelPresent()
    {
        Assert.assertTrue("Всплывающий список не появился", tester.waitAppear(X_SUB_TAB_PANEL));
    }

    /**
     * Проверить, что всплывающий список содержит названия вкладок в указанном порядке
     *  @param expectedTitles названия ожидаемых вкладок в указанном порядке
     *  (Ожидаемое количество вкладок должно точно совпадать с реальным, но порядок должен быть тот же)
     *
     */
    public static void assertSubPanelTabs(String... expectedTitles)
    {
        assertSubPanelPresent();
        GUITester.assertFindElements(X_SUB_TAB_PANEL + "//tr", Lists.newArrayList(expectedTitles), true, true);
    }

    /**
     * Проверить отсутствие набора вкладок на текущей странице
     * (Для вызова метода необходимо находиться в странице с вкладками)
     * @param tabs набор моделей вкладок
     */
    public static void assertTabAbsence(ContentTab... tabs)
    {
        for (ContentTab tab : tabs)
        {
            String message = String.format("На текущей странице присутствует вкладка: %s", tab.getTitle());
            Assert.assertTrue(message, tester.waitDisappear(GUIXpath.Any.ID_PATTERN, tab.getXpathId()));
        }
    }

    /**
     * Осуществляет проверку нужной вкладки на карточке объекта на активность
     * @param tabId идентификатор вкладки
     */
    public static void assertTabActive(String tabId)
    {
        GUITester.assertPresent(GUIXpath.SpecificComplex.SELECTED_TAB, String.format(
                "Вкладка с кодом %s неактивна, но ожидалось, что будет активна", tabId), tabId);
    }

    /**
     * Проверить название вкладки на форме редактирования вкладок
     *
     * @param tab - модель вкладки
     */
    public static void assertTabOnEditForm(ContentTab tab)
    {
        String msg = "Название вкладки не совпало с ожидаемым " + tab.getTitle();
        GUITester.assertExists(String.format(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_TEXT, tab.getXpathId(), tab
                .getTitle()), true, msg);
    }

    /**
     * Проверить название вкладки на форме редактирования вкладок
     */
    public static void assertTabOnEditForm(String tabID, String title)
    {
        String msg = "Название вкладки не совпало с ожидаемым " + title;
        GUITester.assertExists(String.format(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_TEXT, tabID, title), true, msg);
    }

    /**
     * Проверить наличие набора вкладок на текущей странице
     * (Для вызова метода необходимо находиться в странице с вкладками)
     * @param tabs набор моделей вкладок
     */
    public static void assertTabPresent(ContentTab... tabs)
    {
        for (ContentTab tab : tabs)
        {
            String message = String.format("На текущей странице отсутствует вкладка: %s", tab.getTitle());
            Assert.assertTrue(message, tester.waitAppear(GUIXpath.Any.ID_PATTERN, tab.getXpathId()));
        }
    }

    /**
     * Проверить, что контенты присутствуют/отсутствуют в зависимости от значения true/false
     */
    public static void assertTabPresent(Map<ContentTab, Boolean> tabs)
    {
        for (Map.Entry<ContentTab, Boolean> entry : tabs.entrySet())
        {
            if (entry.getValue())
            {
                assertTabPresent(entry.getKey());
            }
            else
            {
                assertTabAbsence(entry.getKey());
            }
        }
    }

    /**
     * Проверяет наличие вкладки на карточке по идентификатору
     * @param id идентификатор вкладки
     */
    public static void assertTabPresent(String id)
    {
        GUITester.assertPresent(GUIXpath.Div.ANY, String.format(
                "Вкладка с id %s не была найдена в таблице соответствий", id), GUITab.IMPORT_CONFIG_TAB);
    }

    /**
     * Проверить вкладки в котенте "Панель вкладок"
     * Метод проверяет ВСЕ вкладки, в том числе во вложенных контентах
     * @param content - модель контента "Панель вкладок"
     * @param tabTitles - название вкладок в том порядке в котором они должны отображаться
     */
    public static void assertTabs(ContentForm content, String... tabTitles)
    {
        List<WebElement> tabs = tester.findElements(X_TAB, content.getXpathId());
        Assert.assertEquals("Полученное количество вкладок в контенте не совпало с ожидаемым.", tabTitles.length,
                tabs.size());
        for (int i = 0; i < tabTitles.length; ++i)
        {
            Assert.assertEquals("Порядок вкладок не совпал с ожидаемым", tabTitles[i], tester.getText(tabs.get(i)));
        }
    }

    /**
     * Проверить, что на панели вкладок вкладки скрыты
     * @param tabBar модель контента "Панель вкладок"
     */
    public static void assertTabsAbsence(ContentForm tabBar)
    {
        GUIContent.assertPresent(tabBar);
        Assert.assertTrue("Панель вкладок присутствует ", tester.waitDisappear(X_TABS_PANEL, tabBar.getXpathId()));
    }

    /**
     * Проверить, что вкладка открыта в контенте
     * @param content контент, на котором расположена панель вкладок
     * @param tab ожидаемая открытая вкладка
     */
    public static void assertTabSelected(ContentForm content, ContentTab tab)
    {
        String errorMessage = String.format("Вкладка '%s' не открыта в контенте '%s'", tab.getTitle(),
                content.getTitle());
        Assert.assertEquals(errorMessage, tab.getXpathId(), GUITester.getAttributeProperty(
                GUIXpath.Div.ID_PATTERN + "//div[contains(@class,'selected')]//div[contains(@id, 'gwt-debug-Tab.')]",
                "id", content.getXpathId()));
    }

    /**
     * Проверить, что вкладка открыта на карточке объекта
     * @param tab ожидаемая открытая вкладка
     */
    public static void assertTabSelected(ContentTab tab)
    {
        assertTabPresent(tab);
        String errorMessage = String.format("Вкладка '%s' не открыта на карточке объекта", tab.getTitle());
        Assert.assertEquals(errorMessage, tab.getXpathId(), getSelectedTabIdInRootContent());
    }

    /**
     * Проверить, что на панели вкладок вкладки отображены
     * @param tabBar модель контента "Панель вкладок"
     */
    public static void assertTabsPresence(ContentForm tabBar)
    {
        GUIContent.assertPresent(tabBar);
        Assert.assertTrue("Панель вкладок отсутствует ", tester.waitAppear(X_TABS_PANEL, tabBar.getXpathId()));
    }

    /**
     * Кликнуть по кнопке "Добавить вкладку" на форме редактирования вкладок
     */
    public static void clickAdd()
    {
        tester.click(GUIXpath.Complex.CONTAINER_ON_FORM + GUIXpath.Div.ADD);
    }

    /**
     * Нажать кнопку "Еще" в корневом контенте Панель вкладок в админке
     */
    public static void clickAdminRootMoreTabs()
    {
        assertAdminRootMoreTabsPresent();
        tester.click(MORE_TABS_ROOT_CONTENT_ADMIN);
    }

    /**
     * Нажать кнопку "Еще" в админке на панели с системными вкладками
     */
    public static void clickAdminSystenMoreTabs()
    {
        assertAdminSystemMoreTabsPresent();
        tester.click(MORE_SYSTEM_TABS_ADMIN);
    }

    /**
     * Нажать на кнопку "Еще" в контенте Панель вкладок
     * @param tabBar контент Панель вкладок
     */
    public static void clickContentMoreTabs(ContentForm tabBar)
    {
        WaitTool.waitMills(
                2000);//Ожидание на то, чтоб кнопка Еще успела занять конечное положение после изменения размера окна
        assertContentMoreTabsPresent(tabBar);
        tester.click(MORE_TABS_CONTENT, tabBar.getXpathId());
    }

    /**
     * Кликнуть по кнопке "Удалить" в строке вкладки на форме редактирования вкладок
     *
     * @param tab - модель вкладки
     */
    public static void clickDelete(ContentTab tab)
    {
        tester.click(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_DEL3, tab.getXpathId());
    }

    /**
     * Кликнуть по кнопке редактирования вкладки на форме редактирования вкладок
     *
     * @param tab - модель вкладки
     */
    public static void clickEdit(ContentTab tab)
    {
        tester.click(GUIXpath.Complex.ROW_IN_CONTAINER_EDIT, tab.getXpathId());
    }

    /**
     * Кликнуть по кнопке редактирования вкладки на форме редактирования вкладок
     *
     * @param index - индекс вкладки
     */
    public static void clickEditByIndex(int index)
    {
        tester.click(GUIXpath.SpecificComplex.TAB_N_ROW_EDIT, index);
    }

    /**
     * Кликнуть по кнопке "Переместить вниз" на форме редактирования вкладок
     *
     * @param tab - модель вкладки
     */
    public static void clickMoveDown(ContentTab tab)
    {
        tester.click(GUIXpath.Complex.ROW_IN_CONTAINER_DOWN, tab.getXpathId());
    }

    /**
     * Кликнуть по кнопке "Переместить вниз" на форме редактирования вкладок
     *
     * @param index - индекс вкладки
     */
    public static void clickMoveDownByIndext(int index)
    {
        tester.click(GUIXpath.SpecificComplex.TAB_N_ROW_DOWN, index);
    }

    /**
     * Кликнуть по кнопке "Переместить вверх" на форме редактирования вкладок
     *
     * @param tab - модель вкладки
     */
    public static void clickMoveUp(ContentTab tab)
    {
        tester.click(GUIXpath.Complex.TAB_ROW_UP, tab.getXpathId());
    }

    /**
     * Перейти на вкладку
     * (Для вызова метода необходимо находиться в странице с вкладками)
     * @param tab модель вкладки
     */
    public static void clickOnTab(ContentTab tab)
    {
        clickOnTab(tab.getXpathId());
    }

    /**
     * Перейти на вкладку
     * (Для вызова метода необходимо находиться в странице с вкладками)
     *
     * @param tabId debugId вкладки
     */
    public static void clickOnTab(String tabId)
    {
        if (GUIXpath.Constant.OPERATOR_MODULE.equals(GUITester.getModule()))
        {
            tester.click(GUIXpath.Any.ID_PATTERN + "//a", tabId);
        }
        else
        {
            tester.click(GUIXpath.Any.ID_PATTERN, tabId);
        }
    }

    /**
     * Перейти на вкладку
     * (Для вызова метода необходимо находиться в странице с вкладками)
     *
     * @param text надпись на вкладке
     */
    public static void clickOnTabByText(String text)
    {
        String xpath = String.format(GUIXpath.Div.ANY_CONTAINS2, TAB_DEBUG_ID_PREFIX) + String.format(
                GUIXpath.A.CONTAINS_TEXT_PATTERN, text);
        tester.click(xpath);
    }

    /**
     * Нажать кнопку "Еще" в корневом контенте Панель вкладок в операторе
     */
    public static void clickOperatorRootMoreTabs()
    {
        assertOperatorRootMoreTabsPresent();
        tester.clickOnInvisibleElement(MORE_TABS_ROOT_CONTENT_OPERATOR);
    }

    /**
     * Нажимает на кнопку включения/отключения всех вкладок на форме редактирования вкладок.
     */
    public static void clickSwitchAllTabs()
    {
        tester.click(SpecificComplex.ROW_IN_CONTAINER_SWITCH, "gwt-debug-Tab.");
    }

    /**
     * Нажимает на кнопку включения/отключения в строке вкладки на форме редактирования вкладок.
     * @param tab модель вкладки
     */
    public static void clickSwitchTab(ContentTab tab)
    {
        tester.click(SpecificComplex.ROW_IN_CONTAINER_SWITCH, tab.getXpathId());
    }

    /**
     * Закрыть форму редактирования вкладок. Проверить что форма закрылась.
     */
    public static void closeEditTabsForm()
    {
        tester.click(X_CLOSE_BTN);
        GUIForm.assertDialogDisappear("Форма редактирования вкладок не закрылась");
    }

    /**
     * Удалить вкладку tab с карточки метакласса
     * @param tab модель вкладки
     */
    public static void deleteCardTab(ContentTab tab)
    {
        tester.findDisplayed(GUIContent.X_EDIT_TAB_BUTTON).click();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_DEL3, tab.getXpathId());
        GUIForm.confirmByYes();
        GUITab.closeEditTabsForm();
    }

    /**
     * Удалить вкладку tab с карточки метакласса и проверить получение ошибки
     * @param tab модель вкладки
     * @param error строка с текстом ошибкой
     * @param args аргументы текста ошибки
     */
    public static void deleteCardTabAssertError(ContentTab tab, String error, Object... args)
    {
        tester.findDisplayed(GUIContent.X_EDIT_TAB_BUTTON).click();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_DEL3, tab.getXpathId());
        GUIForm.clickYes();
        GUIForm.applyFormAssertError(String.format(error, args));
    }

    /**
     * Удалить профиль на вкладке
     * @param profile - модель профиля
     */
    public static void deleteProfile(SecurityProfile profile)
    {
        tester.click(GUIXpath.Any.ANY + GUIXpath.Span.CLOSE2, profile.getCode());
    }

    /**
     * Удалить вкладку tab из панели вкладок tabPanel и проверить получение ошибки
     * @param tabPanel модель панели вкладок
     * @param tab модель вкладки
     * @param error строка с текстом ошибкой
     * @param args аргументы текста ошибки
     */
    public static void deleteTabAssertError(ContentForm tabPanel, ContentTab tab, String error, Object... args)
    {
        tester.moveMouse(TAB_BAR_CONTENT, 10, 10, tabPanel.getCode());
        tester.findDisplayed(EDIT_TABS_ICON, tabPanel.getCode()).click();
        GUIForm.assertFormAppear(GUIXpath.Div.FORM_CONTAINS);
        tester.click(GUIXpath.SpecificComplex.ROW_IN_CONTAINER_DEL3, tab.getXpathId());
        GUIForm.clickYes();
        GUIError.assertDialogError(String.format(error, args));
    }

    /**
     * Удалить метку на вкладке
     * @param tag - модель метки
     */
    public static void deleteTag(Tag tag)
    {
        tester.click(GUIXpath.Any.ANY + GUIXpath.Span.CLOSE2, tag.getCode());
    }

    /**
     * Получить id выбранной вкладки на карточке объекта
     * @return id выбранной вкладки
     */
    public static String getSelectedTabId()
    {
        return GUITester.getAttributeProperty(
                GUIXpath.Div.MAIN_TAB_BAR
                + "//div[contains(@class,'selected')]//div[contains(@id, 'gwt-debug-Tab.')]",
                "id");
    }

    /**
     * Получить id выбранной вкладки в корневом контенте "Панель вкладок" на карточке объекта
     * @return id выбранной вкладки
     */
    public static String getSelectedTabIdInRootContent()
    {
        return GUITester.getAttributeProperty(
                GUIXpath.Div.MAIN_TAB_BAR
                + "//div[contains(@class,'selected')]//div[contains(@id, 'gwt-debug-Tab.')]",
                "id");
    }

    /**
     * Установить значение чекбокса Учитывать фильтрацию списка для подсчета количества
     * (только на форме редактирования)
     * @param considerListFilter
     */
    public static void setConsiderListFilter(boolean considerListFilter)
    {
        tester.setCheckbox(GUIXpath.InputComplex.ANY_VALUE, considerListFilter, CONSIDER_LIST_FILTER);
    }

    /**
     * Установить значение чекбокса Отображать количество объектов на вкладке
     * (только на форме редактирования)
     * @param countObjects
     */
    public static void setCountObjects(boolean countObjects)
    {
        tester.setCheckbox(GUIXpath.InputComplex.ANY_VALUE, countObjects, COUNT_OBJECTS);
    }

    /**
     * Ввести код на форме добавления/редактирования вкладки
     * @param code
     */
    public static void setTabCode(String code)
    {
        tester.sendKeys(GUIXpath.Any.DEBUG_VALUE, code);
    }

    /**
     * Ввести название на форме добавления/редактирования вкладки
     *
     * @param title - новое название
     */
    public static void setTabTitle(String title)
    {
        tester.sendKeys(GUIXpath.Any.ANY_VALUE, title, CAPTION);
    }

    /**
     * Установить значение чекбокса Кэшировать количество объектов на вкладке (только на форме редактирования)
     * @param cacheEnable true - включить чекбокс, false - выключить
     */
    public static void setCacheCountObjects(boolean cacheEnable)
    {
        tester.setCheckbox(GUIXpath.InputComplex.ANY_VALUE, cacheEnable, CACHE_OBJECTS_ON_TAB);
    }

    /**
     * Получить id всех верхнеуровневых вкладок контента "Панель вкладок"
     * @param content - Панель вкладок, у которой хотим получить все верхнеуровневые вкладки
     * @return Список id вкладок
     */
    public static List<String> getTabsFromTabBar(ContentForm content)
    {
        return GUITester.getAttributePropertyElements(
                String.format("(" + GUITab.TAB_BAR_CONTENT, content.getCode()) + "//div[@id='gwt-debug-tabBar'])[1]" +
                "//div[contains(@id,'gwt-debug-Tab.')]", "id");
    }

    /**
     * Проверить присутствие верхнеуровневых вкладок в контенте "Панель вкладок"
     * Метод проверяет только верхнеуровневые вкладки, без учета вкладок во вложенных контентах
     * @param content - модель контента "Панель вкладок"
     * @param tabs - вкладки, присутствие которых надо проверить
     */
    public static void assertTabsPresenceOnTabBar(ContentForm content, ContentTab... tabs)
    {
        List<String> tabIds = getTabsFromTabBar(content);
        for (ContentTab tab : tabs)
        {
            Assert.assertTrue(String.format("Вкладка %s не найдена в контенте %s", tab.getTitle(), content.getTitle()),
                    tabIds.contains(tab.getXpathId()));
        }
    }

    /**
     * Проверить отсутствие верхнеуровневых вкладок в контенте "Панель вкладок"
     * Метод проверяет только верхнеуровневые вкладки, без учета вкладок во вложенных контентах
     * @param content - модель контента "Панель вкладок"
     * @param tabs - вкладки, отсутствие которых надо проверить
     */
    public static void assertTabsAbsenceOnTabBar(ContentForm content, ContentTab... tabs)
    {
        List<String> tabIds = getTabsFromTabBar(content);
        for (ContentTab tab : tabs)
        {
            Assert.assertFalse(String.format("Вкладка %s найдена в контенте %s", tab.getTitle(), content.getTitle()),
                    tabIds.contains(tab.getXpathId()));
        }
    }

    /**
     * Проверить состояние чекбокса Кэшировать количество объектов на вкладке
     * (только на форме редактирования)
     * @param countObjects
     */
    public static void assertCountCashedObjects(boolean countObjects)
    {
        GUITester.assertCheckboxState(GUIXpath.InputComplex.ANY_VALUE, countObjects, CACHE_OBJECTS_ON_TAB);
    }

    /**
     * Проверить название вкладки
     * @param tab модель вкладки
     * @param title ожидаемое название
     */
    public static void assertTabTitle(ContentTab tab, String title)
    {
        Assert.assertEquals(title, tester.getText(String.format(GUIXpath.Any.ID_PATTERN,
                tab.getXpathId())));
    }
}
