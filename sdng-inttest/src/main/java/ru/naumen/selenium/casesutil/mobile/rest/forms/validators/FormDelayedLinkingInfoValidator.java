package ru.naumen.selenium.casesutil.mobile.rest.forms.validators;

import static ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder.ATTRIBUTE;
import static ru.naumen.selenium.casesutil.mobile.rest.objects.MobileObjectPropertiesBuilder.OBJECT;
import static ru.naumen.selenium.util.Json.GSON;
import static ru.naumen.selenium.util.Json.MAP_TYPE;

import java.util.Map;

import org.junit.Assert;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;

public class FormDelayedLinkingInfoValidator implements FormValuesValidator
{
    private final Bo object;
    private final Attribute attribute;

    public FormDelayedLinkingInfoValidator(Bo object, Attribute attribute)
    {
        this.object = object;
        this.attribute = attribute;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath)
    {
        String actualValues = response.extract().jsonPath().getString(absolutePath);
        Map<String, Object> actualInfo = GSON.fromJson(actualValues, MAP_TYPE);

        Assert.assertEquals("Delayed linking object uuid doesn't match.",
                object.getUuid(), actualInfo.get(OBJECT));
        Assert.assertEquals("Delayed linking attribute code doesn't match.",
                attribute.getCode(), actualInfo.get(ATTRIBUTE));
    }
}
