package ru.naumen.selenium.casesutil.model;

import ru.naumen.selenium.casesutil.model.sets.SettingsSet;

/**
 * Модель объекта, который может быть размечен комплектом.
 */
public interface ISettingsSetModel
{
    /**  Комплект, которым размечен элемент */
    String SETTINGS_SET = "settingsSet";

    /**
     * @return комплект, которым размечен объект
     */
    String getSettingsSet();

    /**
     * Устанавливает комплект, к которому относится настройка.
     * @param settingsSet комплект настроек
     */
    void setSettingsSet(SettingsSet settingsSet);
}
