package ru.naumen.selenium.casesutil.mobile.rest.auth.push;

import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.concurrent.Immutable;

import jakarta.annotation.Nullable;

/**
 * Класс, агрегирующий push-токены различных провайдеров push-уведомлений
 *
 * <AUTHOR>
 * @since 21.01.2025
 */
@Immutable
public final class PushTokens
{
    /**
     * Создать пустой билдер
     */
    public static PushTokensBuilder builder()
    {
        return new PushTokensBuilder();
    }

    /**
     * Создать билдер с заполненными полями
     */
    public static PushTokensBuilder builder(Map<String, String> pushTokens)
    {
        return new PushTokensBuilder(pushTokens);
    }

    private final Map<PushProvider, String> tokens;

    PushTokens(Map<PushProvider, String> tokens)
    {
        this.tokens = tokens;
    }

    /**
     * @param provider провайдер push-уведомлений
     * @return push-токен соответствующего провайдера push-уведомлений
     */
    @Nullable
    public String getToken(PushProvider provider)
    {
        return tokens.get(provider);
    }

    /**
     * Получить содержимое объекта в виде словаря
     */
    public Map<String, String> asMap()
    {
        return tokens.entrySet().stream()
                .collect(Collectors.toUnmodifiableMap(entry -> entry.getKey().getCode(), Entry::getValue));
    }

    /**
     * @return true, если токенов нет, иначе - false
     */
    public boolean isEmpty()
    {
        return tokens.isEmpty();
    }

    @Override
    @SuppressWarnings({ "null" })
    public boolean equals(Object obj)
    {
        if (obj == this)
        {
            return true;
        }
        if (obj == null || obj.getClass() != this.getClass())
        {
            return false;
        }

        PushTokens that = (PushTokens)obj;
        if (this.isEmpty() && that.isEmpty())
        {
            return true;
        }
        if (!containsSamePushProviders(tokens.keySet(), that.tokens.keySet()))
        {
            return false;
        }
        return tokens.entrySet().stream()
                .allMatch(entry -> entry.getValue().equals(that.tokens.get(entry.getKey())));
    }

    private static boolean containsSamePushProviders(Set<?> that, Set<?> other)
    {
        return that.size() == other.size() && that.containsAll(other);
    }

    @Override
    public int hashCode()
    {
        return tokens.values().stream().mapToInt(String::hashCode).sum();
    }

    @Override
    public String toString()
    {
        if (tokens.isEmpty())
        {
            return "PushTokens[]";
        }

        Iterator<Entry<PushProvider, String>> iterator = tokens.entrySet().iterator();
        Map.Entry<PushProvider, String> firstEntry = iterator.next();

        StringBuilder sb = new StringBuilder("PushTokens[");
        sb.append(firstEntry.getKey()).append('=').append(firstEntry.getValue());
        while (iterator.hasNext())
        {
            Map.Entry<PushProvider, String> entry = iterator.next();
            sb.append(", ").append(entry.getKey()).append('=').append(entry.getValue());
        }
        return sb.append(']').toString();
    }
}