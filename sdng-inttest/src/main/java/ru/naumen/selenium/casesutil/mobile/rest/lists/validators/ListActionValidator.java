package ru.naumen.selenium.casesutil.mobile.rest.lists.validators;

import io.restassured.response.ValidatableResponse;

/**
 * Валидатор списочного действия.
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
@FunctionalInterface
public interface ListActionValidator
{
    /**
     * Проверяет параметры полученного списочного действие на соответствие ожидаемым значениям
     *
     * @param response полученный ответ сервера со списочным действием
     * @param absolutePath полный путь от корня ответа до проверяемого списочного действия
     */
    void validate(ValidatableResponse response, String absolutePath);
}
