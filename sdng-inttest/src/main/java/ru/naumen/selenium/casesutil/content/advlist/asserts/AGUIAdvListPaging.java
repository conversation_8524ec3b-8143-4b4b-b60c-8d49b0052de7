package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static org.junit.Assert.assertEquals;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

import java.util.Arrays;
import java.util.List;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListPagingType;
import ru.naumen.selenium.casesutil.content.advlist.PageSize;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для проверок настроек пейджинга через интерфейс
 * <AUTHOR>
 * @since 17.04.2015
 */
public class AGUIAdvListPaging extends GUIAdvListPagingType
{
    public AGUIAdvListPaging(String contentXpath, PagingType type)
    {
        super(contentXpath, type);
    }

    /**
     * Проверить отсутствие пейджера
     */
    public void absence()
    {
        GUITester.assertExists(getContentXpath(), true, "Контент отсутствует.");
        String pager = String.format(getContentXpath() + PAGER, getPagingCode());
        GUITester.assertExists(pager, false, getType().getTitle() + " присутствует.");
    }

    /**
     * Проверить кол-во объектов в списке с пересчитыванием
     * <p>
     * Принудительно пересчитывает объекты и вызывает {@link #countObjectsWithoutClick(int)}
     * @param expected ожидаемое кол-во объектов
     */
    public void countObjects(int expected)
    {
        Assert.assertTrue("Контент не загрузился", tester.waitDisappear(ADVLIST_LOAD_IMG));
        long startTime = System.currentTimeMillis();
        do
        {
            tester.click(getContentXpath() + PAGER_COUNTER, getPagingCode());
        }
        while (PAGER_COUNTER_LINK_TITLE.equals(tester.getText(getContentXpath() + PAGER_COUNTER, getPagingCode()))
               && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000);
        countObjectsWithoutClick(expected);
    }

    /**
     * Проверить кол-во объектов в списке
     * <p>
     * При expected==-1 проверяет наличие кнопки "посчитать". В других случаях проверяет количество объектов.
     * @param expected ожидаемое кол-во объектов
     */
    public void countObjectsWithoutClick(int expected)
    {
        String expectedValue = expected == -1 ? PAGER_COUNTER_LINK_TITLE : String.valueOf(expected);
        GUITester.assertTextPresentWithMsg(getContentXpath() + PAGER_COUNTER, expectedValue,
                "Полученное кол-во объектов не совпало с ожидаемым.", getPagingCode());
    }

    /**
     * Проверить номер текущей страницы
     * @param expected ожидаемый номер текущей страницы
     */
    public void currentPage(int expected)
    {
        int actual;
        long startTime = System.currentTimeMillis();
        do
        {
            actual = getCurrentPageNumber();
        }
        while (expected != actual && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000);
        assertEquals("Полученный номер теущей страницы не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Проверить набор ссылок панели навигации по страницам
     * @param linksText набор текста ссылок (обязательно должен присутствовать весь набор текста ссылок)
     */
    public void linksText(String... linksText)
    {
        List<String> expected = Arrays.asList(linksText), actual;
        long startTime = System.currentTimeMillis();
        do
        {
            try
            {
                actual = getLinksText();
            }
            catch (org.openqa.selenium.TimeoutException e)
            {
                WaitTool.waitMills(500);
                actual = getLinksText();
            }
        }
        while (!expected.equals(actual) && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000);
        assertEquals("Ожидаемый набор текста ссылок, не совпал с полученным", expected, actual);
    }

    /**
     * Проверить отсутствие текста ссылки на панели навигации по страницам
     * @param linkText текст ссылки который должен присутствовать
     */
    public void linkTextAbsence(String linkText)
    {
        List<String> actual;
        long startTime = System.currentTimeMillis();
        do
        {
            actual = getLinksText();
        }
        while (actual.contains(linkText) && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000);
        String message = String.format("В адвлисте обнаружена ссылка с текстом %s", linkText);
        Assert.assertFalse(message, actual.contains(linkText));
    }

    /**
     * Проверить присутствие текста ссылки на панели навигации по страницам
     * @param linkText текст ссылки который должен отсутствовать
     */
    public void linkTextPresent(String linkText)
    {
        List<String> actual;
        long startTime = System.currentTimeMillis();
        do
        {
            actual = getLinksText();
        }
        while (!actual.contains(linkText) && System.currentTimeMillis() - startTime < WaitTool.WAIT_TIME * 1000);
        String message = String.format("В адвлисте не обнаружена ссылка с текстом %s", linkText);
        Assert.assertTrue(message, actual.contains(linkText));
    }

    /**
     * Проверить количество строк на странице в пейджере
     * @param expected ожидаемое количество строк на странице
     */
    public void pageSize(PageSize expected)
    {
        GUITester.assertValueWithMsg(PAGER_SIZE_INPUT, expected.get(),
                "Полученное количество строк на странице не совпало с ожидаемым", getPagingCode());
    }

    /**
     * Проверить наличие пейджера
     */
    public void presence()
    {
        GUITester.assertExists(getContentXpath(), true, "Контент отсутствует.");
        String pager = String.format(getContentXpath() + PAGER, getPagingCode());
        GUITester.assertExists(pager, true, getType().getTitle() + " отсутствует.");
    }

    /**
     * Получить номер текущей страницы
     * @return возвращает номер текущей страницы
     */
    private int getCurrentPageNumber()
    {
        String currentId = GUITester
                .getAttributeProperty(getContentXpath() + PAGER_CURRENT_PAGE, "id", getPagingCode());
        return Integer.parseInt(currentId.replaceAll("gwt-debug-page-", "").replaceAll("-current", ""));
    }

    /**
     * Получить набор названий ссылок, содержащихся на данный момент на панели управления переходами между страницами
     */
    private List<String> getLinksText()
    {
        return GUITester.getTextElements(getContentXpath() + PAGER_PAGES_LINKS, getPagingCode());
    }
}
