package ru.naumen.selenium.casesutil.content.advlist.presentation;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.PRS_INPUT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.PRS_SETTINGS_BTN;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListPresentation;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.content.presentation.Presentation;

/**
 * Утилитарные методы работы с представлениями advList-а
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListPresentation extends GUIAdvlist
{
    private AGUIAdvListPresentation asserts;
    private GUIAdvListSaveView saveView;
    private GUIAdvListSettingsView settingsView;

    public GUIAdvListPresentation(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Получение утилитарных методов для проверок, связанных с представлениями advList-а, через интерфейс
     * @return {@link AGUIAdvListPresentation}
     */
    public AGUIAdvListPresentation asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListPresentation(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Развернуть впадающий список с представлениями
     */
    public void expand()
    {
        tester.click(getContentXpath() + PRS_INPUT);
    }

    /**
     * Открыть форму настройки видов
     */
    public void openSettingsView()
    {
        GUISelect.expand(getContentXpath() + PRS_INPUT);
        tester.click(PRS_SETTINGS_BTN);
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUIForm.assertDialogCaption("Настройка видов");
    }

    /**
     * Получение утилитарных методов для проверок, связанных с формой сохранения представлений advList-а, через
     * интерфейс
     * @return {@link GUIAdvListSaveView}
     */
    public GUIAdvListSaveView saveView()
    {
        if (saveView == null)
        {
            saveView = new GUIAdvListSaveView(getContentIdOrXpath());
        }
        return saveView;
    }

    /**
     * Поиск представления по строке
     * @param string строка поиска
     */
    public void search(String string)
    {
        tester.sendKeys(getContentXpath() + PRS_INPUT, string);
    }

    /**
     * Выбрать представление в advList-е по id представления
     * (если null, будет выбран [Выберите вид])
     * @param prs модель представления
     */
    public void selectById(Presentation prs)
    {
        GUISelect.select(getContentXpath() + PRS_INPUT, prs == null ? GUISelect.EMPTY_SELECTION_ITEM : prs.getUuid());
    }

    /**
     * Выбрать представление в advList-е по названию представления
     * (если null, будет выбран [Выберите вид])
     * @param prs модель представления
     */
    public void selectByTitle(Presentation prs)
    {
        GUISelect.selectByTitle(getContentXpath() + PRS_INPUT, prs == null ? GUISelect.SELECT_VIEW : prs.getTitle());
    }

    /**
     * Получение утилитарных методов для проверок, связанных с формой настроек видов advList-а, через интерфейс
     * @return {@link GUIAdvListSettingsView}
     */
    public GUIAdvListSettingsView settingsView()
    {
        if (settingsView == null)
        {
            settingsView = new GUIAdvListSettingsView(getContentIdOrXpath());
        }
        return settingsView;
    }
}
