package ru.naumen.selenium.casesutil.mobile;

import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.Cleaner;

/**
 * Методы управления версиями мобильного приложения на стенде
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public final class DSLMobileVersion
{
    private static final String SET_SUPPORTED_VERSION_SCRIPT_TEMPLATE = """
            def mobileVersionService = beanFactory.getBean('mobileVersionService')
            def versions = mobileVersionService.getSupportedVersions()
            def targetVersion = '%s'
            def needAdd = %s
            if (versions.contains(targetVersion) == needAdd)
            {
                return false
            }
            mobileVersionService.setSupportedVersions(needAdd ? versions + targetVersion : versions - targetVersion)
            return true""";

    /**
     * Добавляет версию мобильного приложения в список доступных. Предназначен для этапа общей подготовки тестов<br>
     * <b>ВАЖНО:</b> тесты, использующие текущий метод должны быть исправлены после выхода соответствующей версии API
     *
     * @param version добавляемая версия
     */
    public static void addSupportedVersionOnPrepareTest(MobileVersion version)
    {
        Cleaner.afterAllTest(true, () ->
                ScriptRunner.executeScript(SET_SUPPORTED_VERSION_SCRIPT_TEMPLATE, version.getCode(), Boolean.FALSE));
        addSupportedVersion(version);
    }

    /**
     * Добавляет версию мобильного приложения в список доступных. Предназначен для использования внутри теста<br>
     * <b>ВАЖНО:</b> тесты, использующие текущий метод должны быть исправлены после выхода соответствующей версии API.
     * Если тест проверял доступность элемента интерфейса в определённой версии API, то такой тест следует удалить.
     *
     * @param version добавляемая версия
     */
    public static void addSupportedVersionOnRuntimeTest(MobileVersion version)
    {
        Cleaner.afterTest(() ->
                ScriptRunner.executeScript(SET_SUPPORTED_VERSION_SCRIPT_TEMPLATE, version.getCode(), Boolean.FALSE));
        addSupportedVersion(version);
    }

    private static void addSupportedVersion(MobileVersion version)
    {
        String wasSet =
                ScriptRunner.executeScript(SET_SUPPORTED_VERSION_SCRIPT_TEMPLATE, version.getCode(), Boolean.TRUE);
        if (!Boolean.parseBoolean(wasSet))
        {
            throw new IllegalArgumentException("Версия '%s' уже является доступной!".formatted(version.getCode()));
        }
    }

    private DSLMobileVersion()
    {
    }
}
