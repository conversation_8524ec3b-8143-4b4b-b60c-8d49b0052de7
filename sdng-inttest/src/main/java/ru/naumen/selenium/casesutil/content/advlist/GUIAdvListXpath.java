package ru.naumen.selenium.casesutil.content.advlist;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Any;
import ru.naumen.selenium.casesutil.GUIXpath.Div;

/**
 * Класс для хранения констант, необходимых для формирования xpath-ов для advList-ов.
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListXpath
{
    /**Путь до полосы загрузки адвлиста*/
    public static final String ADVLIST_LOAD_IMG = "//div[@id='gwt-debug-outer']//table[@id='gwt-debug-table']//tbody"
                                                  + "[2]//img";

    /**Константы для панели инструментов*/
    public static final String TOOL_BUTTON = "//*[contains(@id,'gwt-debug-%s')]";
    public static final String TOOL_EXPORT_EMAIL_VALUE = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                         + "//input[@id='gwt-debug-email-value']";
    public static final String TOOL_BTN_BY_TEXT = "//div[contains(@id, 'gwt-debug-%s') and .//div[text()='%s']]";

    /**Фейковый класс для идентификации нажатия элемента*/
    public static final String CLASS_SELECTED_ITEM = "[contains(@class, 'gwt-debug-selected-item')]";
    public static final String CLASS_NOT_SELECTED_ITEM = "[contains(@class, 'gwt-debug-not-selected-item')]";

    /**Кнопки на панели инструментов*/
    public static final String BTN_REFRESH = "refresh";
    public static final String BTN_EXPORT = "exportAdvlist";
    public static final String BTN_ADD = "add";
    public static final String BTN_LINK = "link";
    public static final String BTN_ADD_FILE = "addFile";
    public static final String BTN_REMOVED = "ShowRemoved";
    public static final String BTN_SAVE_PRESENTATION = "saveAdvlistPrs";
    public static final String XPATH_BTN_SAVE_PRESENTATION = String.format("//div[contains(@id,'gwt-debug-%s.')]",
            BTN_SAVE_PRESENTATION);
    public static final String BTN_SORTING = "showAdvlistSort";
    public static final String BTN_FILTERING = "showAdvlistFilter";
    public static final String BTN_APPLY_FILTER = "applyAndHideAdvlistFilter";
    public static final String BTN_SAVE_FILTER = "gwt-debug-apply";
    public static final String BTN_SHOW_FILTER = "applyAdvlistFilter";
    public static final String BTN_CANCEL_FILTER = "cancelAdvlistFilter";
    public static final String BTN_RESET_FILTER = "resetAdvlistFilter";
    public static final String BTN_RESET_FILTER_MINIMIZED = "reset";
    public static final String BTN_FIRE_USER_EVENT = "fireUserEvent";
    public static final String BTN_EDIT_PROPERTIONS = "editProperties";
    public static final String BTN_EDIT = "edit";
    public static final String BTN_DEL = "del";
    public static final String BTN_ADD_CUSTOM_FORM = "addCustomForm";
    public static final String BTN_CREATE_NEW_REPORT = "createNewReport";

    /**Константы для редактируемой панели инструментов*/
    public static final String EDITABLE_TOOL_CONTENT = "//div[@id='gwt-debug-content']";
    public static final String EDITABLE_TOOL_PANEL_CONTENT_BY_KIND = "//div[@id='gwt-debug-%sBlock']";
    public static final String EDITABLE_ACTION_TOOL_PANEL = String
            .format("(%s)[1]//div[contains(@id, 'gwt-debug-ToolPanel.')]", EDITABLE_TOOL_CONTENT);
    public static final String EDITABLE_MASS_TOOL_PANEL = String
            .format("(%s)[2]//div[contains(@id, 'gwt-debug-ToolPanel.')]", EDITABLE_TOOL_CONTENT);
    public static final String EDITABLE_TOOL_TOOLS = "//div[@id='gwt-debug-tools']";
    public static final String EDITABLE_TOOL_CONTENT_TOOL_BARS = "//table[contains(@id, 'gwt-debug-ToolBar.')]";
    public static final String EDITABLE_TOOL_VIEW_BTN = "//a[@id='gwt-debug-viewToolPanel']";
    public static final String EDITABLE_TOOL_RESET_BTN = "//a[@id='gwt-debug-resetToolPanel']";
    public static final String EDITABLE_TOOL_COPY_FROM_TMPL_BTN = "//a[@id='gwt-debug-copyFromTemplateIcon']";
    public static final String EDITABLE_TOOL_ADD_MENU_ITEM = "//a[@id='gwt-debug-addObjectActionIcon']";
    public static final String EDITABLE_TOOL_ADD_BUTTON = EDITABLE_TOOL_CONTENT_TOOL_BARS
                                                          + "//div[contains(@id, 'gwt-debug-add')]";
    public static final String EDITABLE_TOOL_ADD_CAPTION = EDITABLE_TOOL_CONTENT_TOOL_BARS
                                                           + "//*[contains(@id, 'gwt-debug-add')]";
    public static final String EDITABLE_TOOL_COPY_PARENT_BTN = "//a[@id='gwt-debug-copyFromParentIcon']";
    public static final String EDITABLE_TOOL_COPY_ACTIONS_FROM_PARENT_BTN = "//a[@id='gwt-debug"
                                                                            + "-copyActionsFromParentIcon']";
    public static final String EDITABLE_TOOL_MENU_POSITION = "//*[@id='gwt-debug-%s-label']";
    public static final String EDITABLE_TOOL_MENU_POSITION_INPUT = "//input[@id='gwt-debug-%s-input']";

    public static final String EDITABLE_TOOL_EDIT_BTN = String.format(GUIXpath.Span.ANY_ICON, "editToolPanel");
    public static final String EDITABLE_TOOL_FORM_ACTION_FIELD = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                 + "//div[@id='gwt-debug-action-caption']//span";
    // TODO xpath проверить после коммита задач NSDPRD-3957 NSDPRD-4007 NSDPRD-4008
    public static final String EDITABLE_TOOL_FORM_ACTION_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                 + "//div[@id='gwt-debug-action-value']//input";
    public static final String EDITABLE_TOOL_FORM_ATTRIBUTE_TO_SAVE_FILE_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                                 + "//div[@id='gwt-debug"
                                                                                 + "-attributeToSaveFile-value"
                                                                                 + "']//input";
    public static final String EDITABLE_TOOL_FORM_ADD_FILE_ATTRIBUTE_GROUP_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                                   + "//div[@id='gwt-debug"
                                                                                   + "-attributeGroupForAddFile-value"
                                                                                   + "']//input";
    public static final String EDITABLE_TOOL_FORM_PRESENTATION_FIELD = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                       + "//div[@id='gwt-debug-presentation-value"
                                                                       + "']//input";
    public static final String EDITABLE_TOOL_FORM_PRESENTATION_NOT_EDITABLE = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                              + "//div[@id='gwt-debug-presentation"
                                                                              + "-caption']//span";
    public static final String EDITABLE_TOOL_FORM_APPLIED_TO_FIELD = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                     + "//div[@id='gwt-debug-appliedToType-value"
                                                                     + "']//input";
    public static final String EDITABLE_TOOL_FORM_APPLIED_TO_NOT_EDITABLE = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                            + "//div[@id='gwt-debug-appliedToType"
                                                                            + "-caption']//span";
    public static final String EDITABLE_TOOL_FORM_ICON_FIELD = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                               + "//div[@id='gwt-debug-icon-value']//input";
    public static final String OBJECT_ACTION_TOOL_ACTION_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                 + "//div[@id='gwt-debug-objectAction-value']//input";

    public static final String EDITABLE_TOOL_FORM_MASS_EDIT_FORM = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                   + "//div[@id='gwt-debug-massEditForm-value']//input";
    public static final String EDITABLE_TOOL_FORM_TITLE_FIELD = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                                + GUIXpath.Input.TITLE_VALUE;
    public static final String EDITABLE_TOOL_LINK = "//div[contains(@class, 'actionLink') and contains(@id, "
                                                    + "'gwt-debug-%s.')]";
    public static final String EDITABLE_TOOL_BTN = "//*[contains(@id, 'gwt-debug-%s')]";
    public static final String EDITABLE_TOOL_BTN_BY_TEXT = Div.BUTTON_BY_TEXT;
    public static final String EDITABLE_TOOL_LINK_BY_TEXT = "//div[contains(@class, 'actionLink') and ./div/div[text"
                                                            + "()='%s']]";
    public static final String EDITABLE_TOOL_BTN_IMAGE = EDITABLE_TOOL_BTN + "//div[@id='gwt-debug-icon']";
    public static final String EDITABLE_TOOL_BTN_TEXT = EDITABLE_TOOL_BTN + "/div/div[@id='gwt-debug-text']";
    public static final String EDITABLE_TOOL_SEPARATOR_ID = "separator";
    public static final String FAST_CHANGE_STATE_TOOL_SEPARATOR_ID = "fastChangeStateSeparator";
    public static final String TRANSITION_CHANGE_STATE_TOOL_ID = "transitionChangeState";
    public static final String EDITABLE_TOOL_TEMPLATE = "newToolTemplate";
    public static final String EDITABLE_TOOL_SEPARATOR = EDITABLE_TOOL_TOOLS
                                                         + "//div[@id='gwt-debug-separator.separator']";
    public static final String EDITABLE_TOOL_SEPARATORS = "(%s" + EDITABLE_TOOL_CONTENT
                                                          + "//div[@id='gwt-debug-separator.separator'])[%d]";
    public static final String EDITABLE_TOOL_CONTEXT_MENU = "//div[@id='gwt-debug-toolPanelContextMenu']";
    public static final String EDITABLE_TOOL_CONTEXT_MENU_ITEMS = EDITABLE_TOOL_CONTEXT_MENU + "//*[@id]";
    public static final String EDITABLE_TOOL_EDIT_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                        + "//td[@id='gwt-debug-edit']";
    public static final String EDITABLE_TOOL_MOVE_RIGHT_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                              + "//td[@id='gwt-debug-moveRight']";
    public static final String EDITABLE_TOOL_MOVE_LEFT_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                             + "//td[@id='gwt-debug-moveLeft']";
    public static final String EDITABLE_TOOL_DELETE_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                          + "//td[@id='gwt-debug-delete']";
    public static final String EDITABLE_TOOL_DELETE_ITEM_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                               + "//td[@id='gwt-debug-deleteItem']";
    public static final String EDITABLE_TOOL_ADD_CONTEXT_MENU_OPTION = EDITABLE_TOOL_CONTEXT_MENU
                                                                       + "//td[@id='gwt-debug-add']";
    public static final String TOOL_PANEL_CONTEXT_MENU_PLACE_RIGHT = EDITABLE_TOOL_CONTEXT_MENU
                                                                     + "//td[@id='gwt-debug-placeRight']";
    public static final String TOOL_PANEL_CONTEXT_MENU_PLACE_BOTTOM = EDITABLE_TOOL_CONTEXT_MENU
                                                                      + "//td[@id='gwt-debug-placeBottom']";
    public static final String TOOL_PANEL_CONTEXT_MENU_SHOW_ON_HOVER = EDITABLE_TOOL_CONTEXT_MENU
                                                                       + "//td[@id='gwt-debug-showOnlyOnHover']";
    public static final String TOOL_PANEL_CONTEXT_MENU_SHOW_ALWAYS = EDITABLE_TOOL_CONTEXT_MENU
                                                                     + "//td[@id='gwt-debug-showAlways']";
    public static final String EDITABLE_TOOL_PUSHABLE_INPUT = "//*[@id='gwt-debug-pushable-value-input']";
    public static final String EDITABLE_TOOL_RETURN_TO_LIST_INPUT = "//*[@id='gwt-debug-returnToListAfterCreation"
                                                                    + "-value-input']";

    public static final String EDIT_TOOL_PANEL = "//div[contains(@id,'gwt-debug-ToolPanel.')]";
    public static final String EDIT_TOOL_PANEL_NOT_TOOLS = "//div[contains(@id,'gwt-debug-ToolPanel.') and not"
                                                           + "(contains(@id,'gwt-debug-ToolPanel.tools'))]";
    public static final String CHECKBOX_EDIT_TOOL_PANEL_INPUT = "//*[@id='gwt-debug-use-system-settings-value']//input";
    public static final String CHECKBOX_EDIT_TOOL_PANEL_LABEL = "//*[@id='gwt-debug-use-system-settings-value']//label";
    public static final String ICON_EDIT_TOOL_PANEL = "//span[contains(@id,'gwt-debug-EDIT_TOOL_PANEL')]";
    public static final String ICON_EDIT_ADVLIST_DEFAULT_PRS = "//span[@id='gwt-debug-EDIT_ADVLIST_DEFAULT_PRS']";
    public static final String ICON_EDIT = "//span[@id='gwt-debug-EDIT']";
    public static final String ICON_DELETE = "//*[@id='gwt-debug-DELETE']";
    public static final String ICON_FILTER_SETTINGS = "//*[@id='gwt-debug-FILTER_SETTINGS']";
    public static final String ICON_LIST_TEMPLATE = "//*[@id='gwt-debug-LIST_TEMPLATE_APPLY']";
    public static final String ICON_COPY_TO_TEMPLATE = "//*[@id='gwt-debug-COPY_TO_TEMPLATE']";

    /**Константы для представлений*/
    public static final String PRS_PRESENTATIONS_ID = "AdvlistPrsSelectTool";
    public static final String PRS_PRESENTATIONS = String.format("//div[contains(@id,'gwt-debug-%s.')]",
            PRS_PRESENTATIONS_ID);
    public static final String PRS_MODIFIED_STAR = PRS_PRESENTATIONS + "//div[text()='*']";
    public static final String PRS_INPUT = PRS_PRESENTATIONS + "//input";
    public static final String PRS_SETTINGS_BTN = "//a[@id='gwt-debug-settingsCustomization']";

    /**Константы для формы "Сохранение вида"*/
    public static final String SAVE_VIEW_DEFAULT_CHECKBOX = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                            + "//input[@id='gwt-debug-defaultCheckboxAdvlistSettings"
                                                            + "-value-input']";
    public static final String SAVE_VIEW_PUBLIC_CHECKBOX = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                           + "//input[@id='gwt-debug-commonCheckboxAdvlistSettings"
                                                           + "-value-input']";
    public static final String X_SAVE_VIEW_CURRENT_RADIO = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                           + "//*[@id='gwt-debug-current']";
    public static final String X_SAVE_VIEW_CURRENT_RADIO_INPUT = X_SAVE_VIEW_CURRENT_RADIO + GUIForm.X_VALUE_INPUT;
    public static final String X_SAVE_VIEW_CURRENT_RADIO_LABEL = X_SAVE_VIEW_CURRENT_RADIO + GUIForm.X_VALUE_LABEL;
    public static final String X_SAVE_VIEW_NEW_RADIO = GUIXpath.Div.PROPERTY_DIALOG_BOX + "//*[@id='gwt-debug-new']";
    public static final String X_SAVE_VIEW_NEW_RADIO_INPUT = X_SAVE_VIEW_NEW_RADIO + GUIForm.X_VALUE_INPUT;
    public static final String X_SAVE_VIEW_NEW_RADIO_LABEL = X_SAVE_VIEW_NEW_RADIO + GUIForm.X_VALUE_LABEL;
    public static final String SAVE_VIEW_TITLE = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                 + "//input[@id='gwt-debug-titleAdvlistSettings-value']";
    public static final String SAVE_VIEW_EVERYONE_VALUE = "*";
    public static final String SAVE_VIEW_PUBLIC_TREE_ID = "gwt-debug-commonTreeAdvlistSettings-value";

    public static final String BUTTON_PANEL = "//div[@id='gwt-debug-buttonPanel']";
    public static final String BUTTONS_SAVE = "//*[@id='gwt-debug-apply']";
    public static final String BUTTON_SAVE_PATH = "//tr[@class='dialogMiddle']"
                                                  + "//td[@class='dialogMiddleCenter']//div[@id='gwt-debug"
                                                  + "-hierarchyFilterFrom-value']"
                                                  + "//div[@id='gwt-debug-buttons']";
    public static final String ADD_ATTRIBUTE_TOOL = "//span[@id='gwt-debug-addIcon']";
    public static final String ATTRIBUTE_VALUE_TOOL = "//div[@id='gwt-debug-%s-value']";

    /**Константы для формы "Настройка видов"*/
    public static final String SETT_VIEW_FORM = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                + "//table[@id='gwt-debug-advListSettings']";
    public static final String SETT_VIEW_ROW = SETT_VIEW_FORM + "//tr[@id='%s']";
    public static final String SETT_VIEW_TITLE_CELL = SETT_VIEW_ROW + "//div[@id='gwt-debug-title']";
    public static final String SETT_VIEW_TITLE = "//div[@id='gwt-debug-titleView']";
    public static final String SETT_VIEW_TITLE_VIEW = SETT_VIEW_TITLE_CELL + SETT_VIEW_TITLE;
    public static final String SETT_VIEW_TITLE_EDIT = SETT_VIEW_TITLE_CELL + "//input[@id='gwt-debug-titleChange']";
    public static final String SETT_VIEW_MODE_TEXT = SETT_VIEW_TITLE_CELL + "//div[@id='gwt-debug-%s']";
    public static final String SETT_VIEW_MODE_LINK = SETT_VIEW_TITLE_CELL + "//a[@id='gwt-debug-%s']";
    public static final String SETT_VIEW_COMMON_ROWS = SETT_VIEW_FORM
                                                       + "//tr[@id and preceding::td[text()='Общие виды'] and "
                                                       + "following::td[text()='Личные виды']]";
    public static final String SETT_VIEW_PRIVATE_ROWS = SETT_VIEW_FORM
                                                        + "//tr[@id and preceding::td[text()='Личные виды']]";
    public static final String SETT_VIEW_CURRENT_DEFAULT = "//div[@id='gwt-debug-currentDefault']/div/span";
    public static final String SETT_VIEW_EDIT_ICON = SETT_VIEW_ROW + "//span[@_code='edit2']"; // fontIcon
    public static final String SETT_VIEW_SAVE_ICON = SETT_VIEW_ROW + "//span[@_code='save2']"; // fontIcon
    public static final String SETT_VIEW_DEL_ICON = SETT_VIEW_ROW + GUIXpath.Span.DEL_2_ICON; // fontIcon
    public static final String SETT_VIEW_COMMON_PRS = SETT_VIEW_FORM + "//tr[not(@id)][1]";
    public static final String SETT_VIEW_PRIVATE_PRS = SETT_VIEW_FORM + "//tr[not(@id)][2]";
    public static final String SETT_VIEW_COMMON_ROW = SETT_VIEW_FORM
                                                      + "//tr[@id='%s' and preceding::td[text()='Общие виды'] and "
                                                      + "following::td[text()='Личные виды']]";
    public static final String SETT_VIEW_PRIVATE_ROW = SETT_VIEW_FORM
                                                       + "//tr[@id='%s' and preceding::td[text()='Личные виды']]";
    public static final String SETT_VIEW_RECIPIENTS = SETT_VIEW_ROW + "//div[@id='gwt-debug-ownersTree']";

    /**Константы для панели настройки фильтрации*/
    public static final String FILTER_PANEL = "//div[contains(@id, 'gwt-debug-ListFilter.')]";
    public static final String FILTER_MIN_PANEL = "//table[contains(@id, 'gwt-debug-ListFilterMinimized.')]";
    public static final String CONDITION_SPAN = "//*[@id='gwt-debug-condition']/span";
    public static final String FILTER_MIN_PANEL_CONDITION_VALUE = FILTER_MIN_PANEL + CONDITION_SPAN;
    public static final String FILTER_SAVED_TITLE = FILTER_MIN_PANEL + "//div[@id='gwt-debug-condition']";
    public static final String FILTER_CHANGE_LINK = FILTER_MIN_PANEL + "//a[@id='gwt-debug-change']";
    public static final String FILTER_RESET_LINK = FILTER_MIN_PANEL + "//a[@id='gwt-debug-reset']";
    public static final String FILTER_AND = "//div[contains(@id,'gwt-debug-ListFilterAndElement.')][%d]";
    public static final String FILTER_OR = "//div[contains(@id,'gwt-debug-ListFilterOrElement.')][%d]";
    public static final String FILTER_AND_DELETE = FILTER_PANEL + FILTER_AND + "/div/span";
    public static final String FILTER_OR_DELETE = FILTER_PANEL + FILTER_AND + FILTER_OR + "/div/span";
    public static final String FILTER_ATTRS = FILTER_PANEL + FILTER_AND + FILTER_OR
                                              + "//div[@id='gwt-debug-attributeListBox']";
    public static final String FILTER_ATTRS_INPUT = FILTER_ATTRS + "//input";
    public static final String FILTER_CONDITIONS_INPUT = FILTER_PANEL + FILTER_AND + FILTER_OR
                                                         + String.format(GUIXpath.InputComplex.ANY_VALUE,
            "conditionListBox");
    public static final String FILTER_CONDITIONS_TIMEUNIT_INPUT = FILTER_PANEL + FILTER_AND + FILTER_OR
                                                                  + String.format(GUIXpath.InputComplex.ANY_VALUE,
            "date-timeUnit");
    public static final String FILTER_CONDITIONS_DIRECTION_INPUT = FILTER_PANEL + FILTER_AND + FILTER_OR
                                                                   + String.format(GUIXpath.InputComplex.ANY_VALUE,
            "date-direction");
    public static final String FILTER_VALUE_WIDGET = FILTER_PANEL + FILTER_AND + FILTER_OR
                                                     + "//*[@id='gwt-debug-valueWidget-value']";
    public static final String IGNORE_IF_EMPTY_IN_FILTER_CONDITIONS_INPUT = FILTER_PANEL + FILTER_AND + FILTER_OR
                                                                            + String.format(
            GUIXpath.InputComplex.ANY_VALUE, "ignoreIfEmpty");
    public static final String FILTER_VALUE_WIDGET_INPUT = FILTER_VALUE_WIDGET + "//descendant-or-self::input";

    public static final String FILTER_WIDGET_HYPERLINK_TEXT = FILTER_VALUE_WIDGET
                                                              + "//input[@id='gwt-debug-hyperlinkText']";
    public static final String FILTER_WIDGET_HYPERLINK_URL = FILTER_VALUE_WIDGET
                                                             + "//input[@id='gwt-debug-hyperlinkURL']";

    public static final String FILTER_WIDGET_DATE_FROM_INPUT = String
            .format("(%%s%s//input[@id='gwt-debug-dateTextBox'])[1]", FILTER_VALUE_WIDGET);
    public static final String FILTER_WIDGET_DATE_TO_INPUT = String
            .format("(%%s%s//input[@id='gwt-debug-dateTextBox'])[2]", FILTER_VALUE_WIDGET);

    public static final String FILTER_WIDGET_TIME_FROM_INPUT = String
            .format("(%%s%s//input[@id='gwt-debug-timeBox'])[1]", FILTER_VALUE_WIDGET);
    public static final String FILTER_WIDGET_TIME_TO_INPUT = String.format("(%%s%s//input[@id='gwt-debug-timeBox'])[2]",
            FILTER_VALUE_WIDGET);

    public static final String FILTER_WIDGET_DATE_FROM_IMG = String
            .format("(%%s%s" + GUIXpath.Any.DATE_PICKER_IMAGE + ")[1]", FILTER_VALUE_WIDGET);
    public static final String FILTER_WIDGET_DATE_TO_IMG = String
            .format("(%%s%s" + GUIXpath.Any.DATE_PICKER_IMAGE + ")[2]", FILTER_VALUE_WIDGET);

    public static final String FILTER_WIDGET_RADIOBUTTON_YES = FILTER_VALUE_WIDGET + "//label[text()='да']";
    public static final String FILTER_WIDGET_RADIOBUTTON_NO = FILTER_VALUE_WIDGET + "//label[text()='нет']";

    public static final String FILTER_WIDGET_TIME_INERVAL_LENGTH = String.format("(%%s%s//input)[1]",
            FILTER_VALUE_WIDGET);
    public static final String FILTER_WIDGET_TIME_INERVAL_INTERVAL = String
            .format("(%%s%s//*[@id='gwt-debug-dateTimeInterval'])", FILTER_VALUE_WIDGET);

    public static final String FILTER_AND_BUTTON = FILTER_PANEL
                                                   + "//div[@id='gwt-debug-listFilterDefault' and count("
                                                   + "./ancestor::div[contains(@id, "
                                                   + "'gwt-debug-ListFilterAndElement.')])=0]";
    public static final String FILTER_OR_BUTTON = FILTER_PANEL + FILTER_AND
                                                  + "//div[@id='gwt-debug-listFilterDefault']";
    public static final String COPY_FROM_TEMPLATE_BUTTON = "//*[@id='gwt-debug-copyFromTemplate']";

    public static final String BTN_APPLY_FILTER_DIALOG = "//div[@id='gwt-debug-filter']" + GUIXpath.Any.APPLY_BUTTON;
    public static final String BTN_CANCEL_FILTER_DIALOG = "//div[@id='gwt-debug-filter']//div[@id='gwt-debug-cancel']";
    public static final String BTN_CANCEL_SORTING_DIALOG = "//div[@id='gwt-debug-listSortEditor']//div[@id='gwt-debug"
                                                           + "-cancel']";

    /**Константы для настройки сортировки*/
    public static final String SORT_PANEL = "//div[contains(@id, 'gwt-debug-ListSort.')]";
    public static final String SORT_CHANGE_LINK = SORT_PANEL + "//a[@id='gwt-debug-change']";
    public static final String SORT_RESET_LINK = SORT_PANEL + "//a[@id='gwt-debug-reset']";
    public static final String SORT_ELEMENTS = SORT_PANEL + "//*[contains(@id,'gwt-debug-ListSortElement.')]";

    public static final String SORT_DIALOG = "//div[@id='gwt-debug-listSortEditor']";
    public static final String SORT_DIALOG_ATTRS_INPUT = SORT_DIALOG + "//div[@id='gwt-debug-newValueBox']//input";
    public static final String SORT_DIALOG_ADD_ATTR_BTN = SORT_DIALOG + "//span[@id='newValueAddImage']"; //fontIcon
    public static final String SORT_DIALOG_INPUT = SORT_DIALOG
                                                   + "//div[@id='gwt-debug-newValueBox']//input";
    public static final String SORT_DIALOG_ATTRS = SORT_DIALOG + "//tr[contains(@id, '@')]";
    public static final String SORT_DIALOG_ATTR = SORT_DIALOG + "//tr[contains(@id, '@%s')]";
    public static final String SORT_DIALOG_ATTR_DELETE = SORT_DIALOG_ATTR + "/td[4]//div"; //fontIcon
    public static final String SORT_DIALOG_ATTR_DIRECT = SORT_DIALOG_ATTR + "/td[1]//img[1]";
    public static final String SORT_DIALOG_ATTR_MOVE_UP = SORT_DIALOG_ATTR + "/td[2]";
    public static final String SORT_DIALOG_ATTR_MOVE_DOWN = SORT_DIALOG_ATTR + "/td[3]";
    public static final String SORT_DIALOG_APPLY = SORT_DIALOG + GUIXpath.Any.APPLY_BUTTON;

    public static final String SORT_IMG = SORT_ELEMENTS + "[.//div[text()='%s']]//img[%d]";
    public static final String SORT_DIALOG_IMG = SORT_DIALOG_ATTR + "//td[1]//img[%d]";

    public static final String SORT_DIRECT_ACTIVE = "data:image/png;base64,"
                                                    +
                                                    "iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAJklEQVR4XmNgQAOyNln"
                                                    + "/0cVQAEgBDKPLgQGyAqwK0SUxFKILYsMAqh4r87uX7SwAAAAASUVORK5CYII=";
    public static final String SORT_DIRECT_INACTIVE = "data:image/png;base64,"
                                                      +
                                                      "iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAJ0lEQVR4XmNgQAO7j1z6jy6GAkAKYBhdDgyQFWBViC6JoRBdEBsGAFAoUtGL6/U/AAAAAElFTkSuQmCC";
    public static final String SORT_REVERSE_ACTIVE = "data:image/png;base64,"
                                                     +
                                                     "iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAIklEQVR4XmOQtcn6TwgzgAC6IIYCGECXxFAAAwQVwAA2BQDURCvzjCk7EgAAAABJRU5ErkJggg==";
    public static final String SORT_REVERSE_INACTIVE = "data:image/png;base64,"
                                                       +
                                                       "iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAYAAACXU8ZrAAAAI0lEQVR4XmPYfeTSf0KYAQTQBTEUwAC6JIYCGCCoAAawKQAAd8VS0eCySooAAAAASUVORK5CYII=";

    /**Константы для настройки полей*/
    public static final String COLUMNS_ADD_BUTTON = "//div[@id='gwt-debug-advlistSettings']/span"; //fontIcon
    public static final String COLUMNS_ADD_ATTR_BUTTON = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                         + "//span[@id='newValueAddImage']"; //fontIcon
    public static final String COLUMNS_ATTRS = GUIXpath.Div.PROPERTY_DIALOG_BOX + "//tr[contains(@id, '@')]";
    public static final String COLUMNS_ATTR = GUIXpath.Div.PROPERTY_DIALOG_BOX + "//tr[contains(@id, '@%s')]";
    public static final String COLUMNS_ATTR_INPUT = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                    + "//div[@id='gwt-debug-newValueBox']//input";
    public static final String COLUMNS_ATTR_PRESENTATION = COLUMNS_ATTR + "/td[2]//span";
    public static final String COLUMNS_ATTR_DELETE = COLUMNS_ATTR + "/td[5]";
    public static final String COLUMNS_ATTR_MOVE_UP = COLUMNS_ATTR + "/td[3]";
    public static final String COLUMNS_ATTR_MOVE_DOWN = COLUMNS_ATTR + "/td[4]";

    /**Константы для содержимого контента*/
    public static final String CONTENT_TABLE = "//table";
    public static final String CONTENT_HIERARCHY_TABLE = "//table[@id='gwt-debug-hierarchyFilterItemsTable']//tr[@ "
                                                         + "__gwt_row = '%d']";
    public static final String CONTENT_TABLE_ROW = "//tr[@ __did = '%s']";
    public static final String CONTENT_TABLE_ROW_BY_NUMBER = "//tr[@__gwt_row = '%s']";
    public static final String CONTENT_HEAD = CONTENT_TABLE + "//thead//th[.//descendant-or-self::*[text()='%s']]";
    public static final String CONTENT_HEAD_COLUMNS = CONTENT_TABLE
                                                      + "//thead//th[not(.//div[@id='gwt-debug-notSelectedACell' or "
                                                      + "@id='gwt-debug-selectedACell']) and not("
                                                      + ".//img[@_code='advlistSettings']) and "
                                                      + ".//descendant-or-self::*[text()!='']]";
    public static final String CONTENT_ROWS = CONTENT_TABLE + "//tr[@__did]";
    public static final String CONTENT_ROW_BY_OBJ_ID = "//tr[@__did='%s']";
    public static final String CONTENT_ROW = CONTENT_TABLE + CONTENT_ROW_BY_OBJ_ID;
    public static final String CONTENT_ROW_INHIERGRID = CONTENT_TABLE + "//tr[@debug-id='%s']";
    // TODO добавить td, у которой __did содержит title (когда у td проставят __did в адвлистах в админке)
    public static final String CONTENT_HEADER_CELL = CONTENT_TABLE + "//*[@__attrfqn='%s']";
    public static final String CONTENT_FAST_FILTER = String.format(GUIXpath.Span.ANY_ICON,
            "advlistFastFilter"); // fontIcon
    public static final String CONTENT_ROW_BY_TITLE = CONTENT_TABLE
                                                      + "//tr[@__did]//a[.//descendant-or-self::*[text()='%s']]";
    public static final String CONTENT_ROW_BY_TITLE_START_WITH_CONTAINS = CONTENT_TABLE
                                                                          + "//tr[@__did]//a["
                                                                          + ".//descendant-or-self::*[starts-with"
                                                                          + "(text(), '%s') and contains(text(), "
                                                                          + "'%s')]]";
    public static final String CONTENT_ROW_PICT = CONTENT_TABLE + "//span[@id='%s.%s']"; // fontIcon
    public static final String CONTENT_HIERARCHY_PICT = CONTENT_HIERARCHY_TABLE + "//span[@_code='%s']";
    public static final String CONTENT_CELL = CONTENT_ROW
                                              + "//td[substring(@__did, string-length(@__did)-string-length('%s')+1)"
                                              + "='%s']";
    public static final String CONTENT_CELL_LINK = CONTENT_CELL + "//a[contains(@href, '%s')]";
    public static final String CONTENT_COLUMN_CELLS = CONTENT_TABLE + "//tr//td[contains(@__did, '@%s')]";
    public static final String CONTENT_COLUMN_CELLS_BY_ATTR_VALUE = CONTENT_TABLE
                                                                    + "//tr[td[contains(@__did, '@%s')]//*[text()"
                                                                    + "='%s']]";
    public static final String CONTENT_COLUMN_CELLS_BY_ANY_TEXT_VALUE = CONTENT_TABLE + "//tr[td[div[text()='%s']]]";
    public static final String CONTENT_COLUMN_CELLS_WITH_LINK_BY_ANY_TEXT_VALUE =
            CONTENT_TABLE + "//tr[td[a[div[text()='%s']]]]";

    public static final String CONTENT_IMG_DISK = "data:image/png;base64,"
                                                  +
                                                  "iVBORw0KGgoAAAANSUhEUgAAAAsAAAALCAYAAACprHcmAAAAaElEQVR42mP4jwAPgTgTiOUYgABEA3EaVBwMGKD0LiDmYcACQOJQebBikE5+BjwAJA9SB2LkMBABwOqAhDSRiqVBBBuRitlINjmTSMWZIOIermBDC757JIczDNyDxpgEVJEElH8PpgAARIyv0jrt/M8AAAAASUVORK5CYII=";

    /**Константы для массовых операций*/
    public static final String NOT_SELECTED_CHECKBOX = "//div[@id='gwt-debug-notSelectedACell']";
    public static final String MASS_OPERATION_PANEL = "//div[contains(@id, 'gwt-debug-AdvListMassOperation')]";
    public static final String MASS_OPERATION_LIGHT_PANEL = "//div[contains(@id, 'gwt-debug-AdvListMassOperationLight"
                                                            + "')]";
    public static final String MASS_LINK = MASS_OPERATION_PANEL + Div.ANY_CONTAINS;
    public static final String MASS_LINK_LIGHT = MASS_OPERATION_LIGHT_PANEL + Div.ANY_CONTAINS;
    public static final String MASS_LINKS = MASS_OPERATION_PANEL + String.format(Div.ANY_CLASS_CONTAINS, "actionLink");
    public static final String MASS_LINK_BY_TITLE = MASS_OPERATION_PANEL + EDITABLE_TOOL_LINK_BY_TEXT;
    public static final String MASS_FAST_CHANGE_STATE = MASS_OPERATION_PANEL + "//div[@id='gwt-debug-%s.%s']";
    public static final String MASS_ROW_CHECKBOX = CONTENT_ROW + NOT_SELECTED_CHECKBOX;
    public static final String MASS_ROW_CHECKBOX_IN_HIERGRID = CONTENT_ROW_INHIERGRID + "/td[2]";
    public static final String MASS_HEAD_CHECKBOX = CONTENT_TABLE + NOT_SELECTED_CHECKBOX;
    public static final String MASS_LABEL_PANEL = MASS_OPERATION_PANEL + "/div[1]";
    /**Константы для меню действий с объектом*/
    public static final String OBJECT_ACTIONS_MENU = "//div[@id='gwt-debug-objectActionsContextMenu']";
    public static final String OBJECT_ACTIONS_MENU_ICON = "//div[@id='gwt-debug-objectActionsCell']";
    public static final String OBJECT_ACTIONS_MENU_ITEM = OBJECT_ACTIONS_MENU + "//*[@id='gwt-debug-%s']";
    /**Константы для пейджинга*/
    public static final String PAGER = "//div[@id='gwt-debug-%sPager']";
    public static final String PAGER_SIZE_INPUT = PAGER + "//div[@id='gwt-debug-pageSize']//input";
    public static final String PAGER_COUNTER = PAGER + "//div[@id='gwt-debug-counter']";
    public static final String PAGER_PAGES = PAGER + "//div[@id='gwt-debug-pages']";
    public static final String PAGER_PAGES_LINKS = PAGER_PAGES + "//div[contains(@id, 'gwt-debug-page-')]";
    public static final String PAGER_CURRENT_PAGE = PAGER_PAGES
                                                    + "//input[contains(@id, 'gwt-debug-page-') and contains(@id, "
                                                    + "'-current')]";
    public static final String PAGER_PAGE_LINK = PAGER_PAGES + "//div[@id='gwt-debug-page-%d']";
    public static final String PAGER_PAGES_NEXT = PAGER_PAGES + "//div[@id='gwt-debug-page-next']";
    public static final String PAGER_PAGES_PREV = PAGER_PAGES + "//div[@id='gwt-debug-page-prev']";
    public static final String PAGER_PAGES_SHIFT_PREV = PAGER_PAGES + "//div[@id='gwt-debug-page-shift'][1]";
    public static final String PAGER_PAGES_SHIFT_NEXT = PAGER_PAGES + "//div[@id='gwt-debug-page-shift'][last()]";
    public static final String PAGER_COUNTER_LINK_TITLE = "посчитать";
    /**Константы для инлайн-редактирования*/
    public static final String EDIT_CELL_ID = "gwt-debug-EditablePropertyContent";
    public static final String EDIT_CELL = "//div[@id='" + EDIT_CELL_ID + "']";
    public static final String EDIT_CELL_APPLY_BTN = "//div[@id='gwt-debug-PropertyDialogBox']" + String.format(
            GUIXpath.Span.ANY_ICON, Any.APPLY_BUTTON_CODE);
    public static final String EDIT_CELL_CANCEL_BTN = "//div[@id='gwt-debug-PropertyDialogBox']" + String.format(
            GUIXpath.Span.ANY_ICON, "cancel");
    public static final String EDIT_CELL_INPUT = EDIT_CELL + "//input";
    public static final String ERROR_MESSAGE = "//div[@id='gwt-debug-errorMessage']";
    public static final String SELECTED_CHECKBOX = "//div[@id='gwt-debug-selectedACell']";
    public static final String MASS_ROW_SELECTED_CHECKBOX = CONTENT_ROW + SELECTED_CHECKBOX;
}