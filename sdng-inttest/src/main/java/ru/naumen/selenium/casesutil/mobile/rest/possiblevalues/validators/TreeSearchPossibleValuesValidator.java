package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор для результатов поиска по дереву возможных значений атрибута.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class TreeSearchPossibleValuesValidator
        extends BaseTreePossibleValuesValidator<TreeSearchPossibleValuesValidator>
{
    private int foundAmount;

    @Override
    public TreeSearchPossibleValuesValidator with(BaseTreePossibleValueValidator<?>... validators)
    {
        super.with(validators);
        this.foundAmount = this.validators.size();
        return this;
    }

    /**
     * Позволяет валидировать количество найденных возможных значений
     *
     * @param foundAmount количество найденных возможных значений
     */
    public TreeSearchPossibleValuesValidator foundAmount(int foundAmount)
    {
        this.foundAmount = foundAmount;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        super.validate(response, absolutePath, version);
        response.body(absolutePath + ".foundAmount", is(foundAmount));
    }
}
