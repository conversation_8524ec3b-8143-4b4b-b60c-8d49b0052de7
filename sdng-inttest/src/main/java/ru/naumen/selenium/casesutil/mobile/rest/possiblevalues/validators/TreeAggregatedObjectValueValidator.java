package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.mobile.rest.forms.validators.FormValuesValidator;
import ru.naumen.selenium.casesutil.model.ModelUuid;

/**
 * Валидатор для объекта, являющегося частью возможного значения в дереве.
 *
 * <AUTHOR>
 * @since 17.02.2025
 */
public class TreeAggregatedObjectValueValidator implements FormValuesValidator
{
    private final String uuid;
    private final String title;

    public TreeAggregatedObjectValueValidator(ModelUuid model)
    {
        this(model.getUuid(), model.getTitle());
    }

    public TreeAggregatedObjectValueValidator(String uuid, String title)
    {
        this.uuid = uuid;
        this.title = title;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath)
    {
        response.body(absolutePath + "?.uuid", is(uuid));
        response.body(absolutePath + "?.title", is(title));
    }
}
