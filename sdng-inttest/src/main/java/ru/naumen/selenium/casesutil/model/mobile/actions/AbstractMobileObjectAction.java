package ru.naumen.selenium.casesutil.model.mobile.actions;

public abstract class AbstractMobileObjectAction
{
    private String code;
    private MobileActionType type;
    private String caption;
    private String formCode;

    public AbstractMobileObjectAction(String code, MobileActionType type, String caption)
    {
        this.code = code;
        this.type = type;
        this.caption = caption;
    }

    public MobileActionType getActionType()
    {
        return type;
    }

    public String getType()
    {
        return type.getName();
    }

    public String getCode()
    {
        return code;
    }

    public String getCaption()
    {
        return caption;
    }

    public String getFormCode()
    {
        return formCode;
    }

    public void setType(MobileActionType type)
    {
        this.type = type;
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setCaption(String caption)
    {
        this.caption = caption;
    }

    protected void setFormCode(String formCode)
    {
        this.formCode = formCode;
    }
}
