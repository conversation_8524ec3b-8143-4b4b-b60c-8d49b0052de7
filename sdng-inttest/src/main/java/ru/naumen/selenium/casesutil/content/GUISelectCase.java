package ru.naumen.selenium.casesutil.content;

import java.util.List;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.ContentType;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для работы с контентом "Выбор типа объекта"
 * <AUTHOR>
 * @since 15 окт. 2013 г.
 *
 */
public class GUISelectCase extends CoreTester
{
    public static final String SELECT_CASE_FIELD =
            GUIXpath.Div.PROPERTY_DIALOG_BOX + GUIXpath.InputComplex.CASE_PROPERTY_VALUE;
    private static final String SELECT_CASE_INPUT_VALUE = String.format(GUIXpath.Any.ANY_VALUE, "caseProperty")
                                                          + GUIXpath.Input.INPUT_PREFIX;
    private static final String SELECT_CASE_FIELD_ON_DIALOG =
            GUIXpath.Div.PROPERTY_DIALOG_BOX + SELECT_CASE_INPUT_VALUE;

    private static final long WAIT_TIME = 100;

    /**
     * Проверить количество типов в выпадающем списке на форме
     * @param expectedNumber ожидаемое количество типов
     */
    public static void assertCountElements(int expectedNumber)
    {
        GUISelect.assertCountElements(SELECT_CASE_FIELD_ON_DIALOG, expectedNumber);
        GUISelect.hideSelect(SELECT_CASE_FIELD_ON_DIALOG);
    }

    /**
     * Проверить элементы в выпадающем списке типов на модальной форме
     * @param expected ожидаемый список названий элементов
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param ignoreEmpty true - игнорировать наличие [не указано], false - проверять наличие
     */
    public static void assertSelectOnForm(List<String> expected, boolean order, boolean match, boolean ignoreEmpty)
    {
        assertSelect(expected, order, match, ignoreEmpty, true);
    }

    /**
     * Проверить элементы в выпадающем списке типов
     * @param expected ожидаемый список названий элементов
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param ignoreEmpty true - игнорировать наличие [не указано], false - проверять наличие
     */
    public static void assertSelect(List<String> expected, boolean order, boolean match, boolean ignoreEmpty)
    {
        assertSelect(expected, order, match, ignoreEmpty, false);
    }

    private static void assertSelect(List<String> expected, boolean order, boolean match, boolean ignoreEmpty,
            boolean isModalForm)
    {
        String selectXpath = isModalForm ? SELECT_CASE_FIELD_ON_DIALOG : SELECT_CASE_INPUT_VALUE;
        tester.clickWithFocusCheck(selectXpath);
        GUISelect.assertSelect(selectXpath, expected, order, match, ignoreEmpty);
    }

    /**
     * Проверяет, что на карточке находится единственный контент "Выбор типа объекта" и возвращает его id
     */
    public static String getSingleContentId()
    {
        List<WebElement> contents = tester.findElements(GUIXpath.Div.ALL_CONTENTS_BY_TYPE,
                ContentType.SELECT_CASE.getType());
        Assert.assertEquals("Полученное количество контентов 'Выбор типа объекта' не совпало с ожидаемым.", 1,
                contents.size());
        WebElement element = contents.get(0);
        return element.getAttribute("id");
    }

    /**
     * Выбирает значение из выпадающего списка типов на инлайн форме по названию
     * @param title название элемента списка
     */
    public static void selectByTitle(String title)
    {
        selectByTitle(title, false);
    }

    /**
     * Выбирает значение из выпадающего списка типов на модальной форме по названию
     * @param title название элемента списка
     */
    public static void selectByTitleOnModalForm(String title)
    {
        selectByTitle(title, true);
    }

    /**
     * Выбирает значение из выпадающего списка типов на модальной форме по названию
     * @param title название элемента списка
     * @param isModalForm выбор типа на модальной форме
     */
    private static void selectByTitle(String title, boolean isModalForm)
    {
        String xpath = isModalForm ? SELECT_CASE_FIELD_ON_DIALOG : SELECT_CASE_INPUT_VALUE;
        tester.click(xpath);
        WaitTool.waitMills(WAIT_TIME);
        tester.click(GUIXpath.Any.TEXT_PATTERN, title);
    }
}
