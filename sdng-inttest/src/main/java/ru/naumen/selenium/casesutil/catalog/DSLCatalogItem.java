package ru.naumen.selenium.casesutil.catalog;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.junit.Assert;

import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.SdDataUtils;
import ru.naumen.selenium.casesutil.escalation.GUIEscalation;
import ru.naumen.selenium.casesutil.model.ModelMap;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalog.DAOCatalog.SystemCatalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.core.Cleaner;
import ru.naumen.selenium.core.exception.ErrorInCodeException;
import ru.naumen.selenium.core.exception.TestingSystemException;
import ru.naumen.selenium.modules.IModuleCatalogItem;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.FileUtils;
import ru.naumen.selenium.util.Json;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с элементами справочника
 *
 * <AUTHOR>
 * @since 08.11.2011
 */
public class DSLCatalogItem
{
    public static final String ADD_CALL_ICON = "add_call_icon";
    public static final String ADD_ELEMENT_ICON = "add_element_icon";
    public static final String ADD_COMMENT_ICON = "add_comment_icon";

    /**
     * Модуль для работы с элементами справочника
     */
    private static final IModuleCatalogItem CATALOG_ITEM_MODULE = ScriptModules.getModuleCatalogItem();

    /**
     * Добавить элементы справочника определяемые их моделью
     * @param items модели элементов справочников
     */
    public static void add(CatalogItem... items)
    {
        List<Map<String, Object>> elements = new ArrayList<>();
        for (CatalogItem item : items)
        {
            Map<String, Object> dataForScript = prepareForAddOrEdit(item);
            dataForScript.put("fqn", item.getParentFqn());
            elements.add(dataForScript);
        }

        List<Map<String, String>> results = CATALOG_ITEM_MODULE.addCatalogItem(elements);

        int index = 0;
        for (Map<String, String> result : results)
        {
            items[index].setUuid(result.get("uuid"));
            items[index].setExists(true);

            switch (SystemCatalog.getByCode(items[index].getParentCode()))
            {
                case SERVICETIME:
                case RULESSETTINGS:
                    break;
                case TIMEZONE:
                    items[index].setCode(result.get("code"));
                    if (Boolean.parseBoolean(result.get("yetExist")))
                    {
                        items[index].setTitle(result.get("title"));
                        items[index].setExists(false);
                    }
                    break;
                case CATEGORY,
                     CLOSURECODE,
                     IMPACT,
                     PRIORITY,
                     SEVERITY,
                     SYSTEM_ICON,
                     VECTOR_ICONS,
                     ICONS_FOR_CONTROLS,
                     UNKNOWN,
                     URGENCY,
                     BLACKLIST_SUBJECT,
                     BLACKLIST_CONTENT:
                {
                    items[index].setIconUuid(result.get("fileUuid"));
                    break;
                }
                default:
                    throw new ErrorInCodeException("Системный справочник с кодом " + items[index].getParentCode()
                                                   + " не найден.");
            }
            index++;
        }
    }

    /**
     * Поместить элемент справочника в архив
     * @param item модель элемента справочника
     */
    public static void archive(CatalogItem item)
    {
        editAttributes(item, DAOAttribute.createPseudo("", "removed", Boolean.TRUE.toString()));
    }

    /**
     * Проверяет отсутствие элемента справочника
     * @param item элемент справочника для проверки
     */
    public static void assertAbsence(CatalogItem item)
    {
        Assert.assertFalse("Элемент справочника присутствует в системе. CatalogItem code: " + item.getCode(),
                isPresent(item));
    }

    /**
     * Проверяет, что элемент справочника в архиве
     * @param item элемент справочника для проверки
     */
    public static void assertArchived(CatalogItem item)
    {
        Assert.assertTrue("Элемент справочника не в архиве. CatalogItem code: " + item.getCode(), isArchived(item));
    }

    /**
     * Проверяет, что элемент справочника не в архиве
     * @param item элемент справочника для проверки
     */
    public static void assertNotArchived(CatalogItem item)
    {
        Assert.assertFalse("Элемент справочника в архиве. CatalogItem code: " + item.getCode(), isArchived(item));
    }

    /**
     * Проверяет присутствие элемента справочника
     * @param item элемент справочника для проверки
     */
    public static void assertPresent(CatalogItem item)
    {
        Assert.assertTrue("Элемент справочника отсутствует в системе. CatalogItem code: " + item.getCode(),
                isPresent(item));
    }

    /**
     * Метод для удаления элемента справочника
     * @param items модели элементов справочников
     */
    public static void delete(CatalogItem... items)
    {
        List<String> uuids = new ArrayList<>();
        for (CatalogItem model : items)
        {
            uuids.add(model.getUuid());
            model.setExists(false);
        }
        ScriptModules.getModuleObject().deleteObjects(uuids);
    }

    /**
     * Редактировать элементы справочника, определяемые моделями
     * @param items модели элементов справочника
     */
    public static void edit(CatalogItem... items)
    {
        List<Map<String, Object>> elements = new ArrayList<>();
        for (CatalogItem item : items)
        {
            Map<String, Object> map = prepareForAddOrEdit(item);
            map.put("uuid", item.getUuid());
            elements.add(map);
        }

        CATALOG_ITEM_MODULE.editCatalogItems(elements);
    }

    /**
     * Редактировать значение атрибутов элемента справочника.
     * Реализация метода взята из {@link ru.naumen.selenium.casesutil.bo.DSLBo}, т.к. элемент справочника по сути
     * тот же БО
     * @param model модель элемента справочника
     * @param attributes список моделей атрибутов для редактирования с значениями поля VALUE, которые должны стать в БО
     */
    public static void editAttributes(CatalogItem model, Attribute... attributes)
    {
        List<Map<String, String>> data = new ArrayList<>();
        for (Attribute attribute : attributes)
        {
            Map<String, String> dataForScript = new HashMap<>();
            dataForScript.put("UUID", model.getUuid());
            dataForScript.put(attribute.getCode(), attribute.getValue());
            data.add(dataForScript);
        }
        ScriptModules.getModuleBo().editBo(data);
    }

    /**
     * Изменить родителя у элемента справочника
     * @param item модель элемента справочника
     * @param newParent модель нового родителя
     */
    public static void editParent(CatalogItem item, CatalogItem newParent)
    {
        item.setParentUuid(newParent.getUuid());
        edit(item);
    }

    /**
     * Получить существующий элементы справочника. Актуально для системных справочников
     * @param catalog системный каталог {@link SystemCatalog}
     * @param code код элемента
     * @return Модель {@link CatalogItem}
     */
    public static CatalogItem getExistingCatalogItem(SystemCatalog catalog, String code)
    {
        String scriptTemplate = """
                def item = utils.get('%s', ['code': '%s']);
                return item.title + ',' + item.UUID;""";
        String catalogCode = !SystemCatalog.ICONS_FOR_CONTROLS.equals(catalog) ? catalog.getCode() : "sys_icon";
        String[] result = ScriptRunner.executeScript(scriptTemplate, catalogCode, code).split(",");

        CatalogItem catalogItem = new CatalogItem();
        catalogItem.setTitle(result[0]);
        catalogItem.setUuid(result[1]);
        catalogItem.setCode(code);
        return catalogItem;
    }

    /**
     * Получить uuid изображения элемента справочника
     * @param item элемент справочника
     * @return uuid изображения элемента справочника
     */
    public static String getFileUuidFromItem(CatalogItem item)
    {
        String scriptPattern = "utils.get('%s').icon.UUID";
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, item.getUuid()));
        ArrayList<String> list = Json.GSON.fromJson(script.runScript().get(0), Json.LIST_STRING_TYPE);
        return list.get(0);
    }

    /**
     * Получение файлов, связанных с векторной иконкой справочника векторных изображений
     * @param icon векторная иконка
     * @return файлы, связанные с иконкой
     */
    public static Map<String, Map<String, String>> getFiles(CatalogItem icon)
    {
        return CATALOG_ITEM_MODULE.getVectorCatalogItemFiles(icon.getCode());
    }

    /**
     * Получить множество uuid-ов таблиц соответствий, которые существуют в эскалации
     * @return множество uuid-ов
     */
    public static Set<String> getRuleSettingsEscalationUuids()
    {
        //@formatter:off
        String scriptPattern = "def GSON_SELF = new com.google.gson.GsonBuilder().serializeNulls().create();\n"
                             + "return GSON_SELF.toJson(utils.find('rulesSettings', ['type':'esc'])*.UUID)";
        //@formatter:on
        ScriptRunner script = new ScriptRunner(scriptPattern);
        return Json.GSON.fromJson(script.runScript().get(0).trim(), Json.SET_TYPE);
    }

    /**
     * @param catalog справочник
     * @param code кол элемента справочника
     * @return uuid элемента справочника
     */
    public static String getUuid(Catalog catalog, String code)
    {
        CatalogItem item = new CatalogItem();
        item.setCode(code);
        item.setParentCode(catalog.getCode());
        ModelMap result = SdDataUtils.getCatalogItemByCode(item);
        return result.get("UUID");
    }

    /**
     * Проверяет наличие элемента справочника по коду
     * (если элемент присутствует, то добавляет в модель {@link CatalogItem#UUID})
     *
     * @param item код элемента справочника
     * @return boolean присутствует элемент или нет
     */
    public static boolean isPresent(CatalogItem item)
    {
        //@formatter:off
        String scriptPattern = "def item = utils.get('%s', ['code': '%s']);%n"
                             + "return item == null ? null : item.UUID";
        //@formatter:on
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, item.getParentCode(), item.getCode()));
        String result = script.runScript().get(0).trim();
        if (!StringUtils.isEmpty(result))
        {
            item.setUuid(result);
            return true;
        }
        return false;
    }

    /**
     * Восстановить элемент справочника из архива
     *
     * @param item модель элемента справочника
     */
    public static void restore(CatalogItem item)
    {
        editAttributes(item, DAOAttribute.createPseudo("", "removed", Boolean.FALSE.toString()));
    }

    /**
     * Проверяет находится ли элемент справочника в архиве
     *
     * @param item элемент справочника для проверки
     * @return boolean true - элемент присутствует, false - элемент отсутствует
     */
    private static boolean isArchived(CatalogItem item)
    {
        //@formatter:off
        String scriptPattern = "def item = utils.get('%s', ['code': '%s']);%n"
                             + "return item.removed";
        //@formatter:on
        ScriptRunner script = new ScriptRunner(String.format(scriptPattern, item.getParentCode(), item.getCode()));
        String result = script.runScript().get(0).trim();
        return Boolean.parseBoolean(result);
    }

    /**
     * Подготовить данные об элементе справочника для передачи в скрипт добавления или редактирования
     *
     * @param catalogItem модель элемента справочника.
     * @return возвращает данные для скрипта
     */
    private static Map<String, Object> prepareForAddOrEdit(CatalogItem catalogItem)
    {
        Map<String, Object> dataForScript = new HashMap<>();
        dataForScript.put("code", catalogItem.getCode());
        dataForScript.put("title", catalogItem.getTitle());
        dataForScript.put("localizedTitle", catalogItem.getLocalizedTitle());
        dataForScript.put("color", catalogItem.getColor());
        dataForScript.put("correctColor", catalogItem.getCorrectColor());
        dataForScript.put("removed", catalogItem.isRemoved());
        dataForScript.put("parent", catalogItem.getParentUuid());
        dataForScript.put("level", catalogItem.getLevel());
        dataForScript.put("folder", catalogItem.getIsFolder());
        dataForScript.put("periods", catalogItem.getPeriodsMap());
        dataForScript.put("exclusions", catalogItem.getExclusionsMap());
        dataForScript.put("description", catalogItem.getDescription());
        dataForScript.put("sourceAttrs", catalogItem.getRulesSettingsSources());
        dataForScript.put("settingsSet", catalogItem.getSettingsSet());
        if (catalogItem.getIsEscalationRule() != null)
        {
            if (!Boolean.parseBoolean(catalogItem.getIsEscalationRule()))
            {
                dataForScript.put("targetAttrs", catalogItem.getRulesSettingsTarget());
            }
            else
            {
                dataForScript.put("type", "esc");
                dataForScript.put("targetAttrs",
                        Json.GSON.toJson(Sets.newHashSet(GUIEscalation.ESCALATION_TARGET_DATA)));
                dataForScript.put("defaultObject", catalogItem.getDefaultEscalation());
            }
            dataForScript.put("linkedClasses", catalogItem.getObjects() == null ? Json.listToString("serviceCall")
                    : catalogItem.getObjects());
        }
        if (catalogItem.getIconPath() != null)
        {
            Map<String, Object> iconData = new HashMap<>();
            final File file = Paths.get(catalogItem.getIconPath()).toFile();
            if (file.exists())
            {
                iconData.put("fileName", file.getName());
                iconData.put("contentType", FileUtils.getType(catalogItem.getIconPath()));
                try
                {
                    iconData.put("content", org.apache.commons.io.FileUtils.readFileToByteArray(file));
                }
                catch (IOException e)
                {
                    throw new TestingSystemException("Ошибка при чтении файла.", e);
                }
            }
            dataForScript.put("iconData", Json.GSON.toJson(iconData));
        }
        return dataForScript;
    }

    /**
     * Создание модели элемента справочника "Классы обслуживания" для персонального класса обслуживания сотрудника.
     * Созданная модель автоматически регистрируется в очереди на удаление.
     *
     * @param serviceTime базовый элемент класса обслуживания, на основании которого будет создан элемент
     *                    персонального класса обслуживания сотрудника
     * @param employee    сотрудник для которого создается класс обслуживания
     * @return модель элемента персонального класса обслуживания для сотрудника
     */
    public static CatalogItem addPersonalServiceTime(CatalogItem serviceTime, Bo employee)
    {
        String code = "p_" + serviceTime.getCode();
        ScriptRunner createScript = new ScriptRunner(
                String.format("def item = api.serviceTime.createPersonalServiceTime('%s','%s', '%s', false); return "
                              + "item.title + ',' + item.UUID; ", code, serviceTime.getUuid(), employee.getUuid()));
        String[] result = createScript.runScript().get(0).trim().split(",");
        Cleaner.afterTest(true, () -> deletePersonalServiceTime(employee));

        CatalogItem personalCatalogItem = new CatalogItem();
        personalCatalogItem.setTitle(result[0]);
        personalCatalogItem.setParentCode(SystemCatalog.SERVICETIME.getCode());
        personalCatalogItem.setCode(code);
        personalCatalogItem.setUuid(result[1]);
        return personalCatalogItem;
    }

    /**
     * Получить элемент справочника "Классы обслуживания" для персонального класса обслуживания сотрудника
     *
     * @param employee сотрудник, персональный класс обслуживания которого необходимо получить
     * @return модель элемента персонального класса обслуживания для сотрудника или null, если у сотрудника
     * отсутствует персональный класс обслуживания
     */
    @Nullable
    public static CatalogItem getPersonalServiceTime(Bo employee)
    {
        ScriptRunner createScript = new ScriptRunner(
                String.format("def item = api.serviceTime.getPersonalServiceTime('%s');%n return item == null ? '' :"
                              + "item.title + ',' + item.UUID + ',' + item.code; ", employee.getUuid()));
        String result = createScript.runScript().get(0).trim();
        if (result.isEmpty())
        {
            return null;
        }

        String[] resArray = result.split(",");
        CatalogItem personalCatalogItem = new CatalogItem();
        personalCatalogItem.setTitle(resArray[0]);
        personalCatalogItem.setParentCode(SystemCatalog.SERVICETIME.getCode());
        personalCatalogItem.setUuid(resArray[1]);
        personalCatalogItem.setCode(resArray[2]);
        return personalCatalogItem;
    }

    /**
     * Удаление элемента справочника "Классы обслуживания" для персонального класса обслуживания сотрудника
     *
     * @param employee объект для которого удаляется персональный класс обслуживания
     * @return true, если элемент был удален
     */
    public static boolean deletePersonalServiceTime(Bo employee)
    {
        ScriptRunner deleteScript = new ScriptRunner(
                String.format("api.serviceTime.deletePersonalServiceTime('%s');", employee.getUuid()));
        String wasDeleted = deleteScript.runScript().get(0).trim();
        return Boolean.parseBoolean(wasDeleted);
    }
}
