package ru.naumen.selenium.casesutil.model.adminprofiles;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;

/**
 * Фабрика моделей профилей администрирования
 * <AUTHOR>
 * @since 05.05.2024
 */
public class DAOAdminProfile
{
    /**
     * Создает модель профиля администрирования.
     * (Модель регистрируется в очередь на удаление.)
     * @return модель профиля администрирования
     */
    public static AdminProfile createAdminProfile()
    {
        return createAdminProfile(ModelUtils.createCode());
    }

    /**
     * Создает модель профиля администрирования c указанным кодом.
     * (Модель регистрируется в очередь на удаление.)
     * @param code код профиля администрирования
     * @return модель профиля администрирования
     */
    public static AdminProfile createAdminProfile(String code)
    {
        return createAdminProfile(code, ModelUtils.createTitle());
    }

    /**
     * Создает модель профиля администрирования c указанным кодом и названием.
     * (Модель регистрируется в очередь на удаление.)
     * @param code код профиля администрирования
     * @param title название профиля администрирования
     * @return модель профиля администрирования
     */
    public static AdminProfile createAdminProfile(String code, String title)
    {
        AdminProfile adminProfile = ModelFactory.create(AdminProfile.class);
        adminProfile.setCode(code);
        adminProfile.setTitle(title);
        adminProfile.setDescription(ModelUtils.createDescription());
        return adminProfile;
    }
}