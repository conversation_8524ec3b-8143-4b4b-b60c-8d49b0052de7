package ru.naumen.selenium.casesutil.content.advlist.presentation;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.SAVE_VIEW_DEFAULT_CHECKBOX;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.SAVE_VIEW_EVERYONE_VALUE;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.SAVE_VIEW_PUBLIC_CHECKBOX;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.SAVE_VIEW_PUBLIC_TREE_ID;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.SAVE_VIEW_TITLE;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.X_SAVE_VIEW_CURRENT_RADIO_LABEL;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.X_SAVE_VIEW_NEW_RADIO_LABEL;

import java.util.List;
import java.util.Set;

import org.junit.Assert;

import com.google.common.collect.Lists;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.DSLPresentation;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListToolPanel;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListSaveView;
import ru.naumen.selenium.casesutil.interfaceelement.BoTree;
import ru.naumen.selenium.casesutil.model.content.presentation.DAOPresentation.PresentationType;
import ru.naumen.selenium.casesutil.model.content.presentation.Presentation;
import ru.naumen.selenium.core.exception.ErrorInCodeException;

/**
 * Утилитарные методы для работы с формой сохранения представлений адвлиста
 * <AUTHOR>
 * @since 30.06.2013
 */
public class GUIAdvListSaveView extends GUIAdvlist
{
    private AGUIAdvListSaveView asserts;
    private GUIAdvListToolPanel toolPanel = new GUIAdvListToolPanel(getContentIdOrXpath());

    public GUIAdvListSaveView(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Получение утилитарных методы для проверок, связанных с настройкой полей advList-а, через интерфейс
     * @return {@link AGUIAdvListSaveView}
     */
    public AGUIAdvListSaveView asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListSaveView(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Получить дерево для поля "Разделить вид с..."
     * @return дерево для поля "Разделить вид с..."
     */
    public BoTree ownersTree()
    {
        return new BoTree(SAVE_VIEW_PUBLIC_TREE_ID, false);
    }

    /**
     * Сохранить представление (нужно находится на странице с advList-ом)
     * @param presentation модель представления
     */
    public void savePresentation(Presentation presentation)
    {
        boolean isCurrent = PresentationType.CURRENT.equals(presentation.getType());
        // если сохраняется не текущее представления, то новому представлению нужно будет получить id
        Set<String> beforeIds = null;
        if (!isCurrent)
        {
            beforeIds = DSLPresentation.getPresentationIds(presentation.getMetaclassCode());
        }

        toolPanel.clickSavePrs();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);

        // выбираем  тип, если создается новое представление из текущего или сохраняется текущее
        if (isCurrent || presentation.isFromExists())
        {
            asserts().prsTypeRadiobutton(PresentationType.CURRENT);
            asserts().enablePrsType();
            setPrsType(presentation.getType());
        }
        // иначе проверяем, что переключатель не доступен для выбора
        else
        {
            asserts().fromEmptyPrs();
        }
        setTitle(presentation.getTitle());
        setPublic(presentation.isPublic());
        if (presentation.isPublic())
        {
            List<String> nodes = presentation.getPublicNodes();
            setOwners(presentation, nodes.toArray(new String[nodes.size()]));
        }
        setDefault(presentation.getDefault());
        GUIForm.applyModalForm();
        presentation.setExists(true);

        // если было сохранено не текущее представления, то устанавливаем новому представлению id
        if (!isCurrent)
        {
            Set<String> afterIds = DSLPresentation.getPresentationIds(presentation.getMetaclassCode());
            afterIds.removeAll(beforeIds);
            Assert.assertEquals("Было добавлено больше одного представления (либо ниодного).", 1, afterIds.size());
            presentation.setUuid(afterIds.iterator().next());
        }
    }

    /**
     * Установить значение чекбокса "открывать по умолчанию (для текущего пользователя)"
     * @param value устанавливаемое значение (true/false)
     */
    public void setDefault(boolean value)
    {
        tester.setCheckbox(SAVE_VIEW_DEFAULT_CHECKBOX, value);
    }

    /**
     * Устанавливает значение в дерево "Разделить вид с..." на форме сохранения вида
     * @param presentation модель представления
     * @param nodes набор узлов, которые надо выставить (каждый узел это набор uuid'ов через точку)
     */
    public void setOwners(Presentation presentation, String... nodes)
    {
        BoTree tree = ownersTree();
        if (!tree.exists())
        {
            return;
        }
        if (nodes.length == 1 && SAVE_VIEW_EVERYONE_VALUE.equals(nodes[0]))
        {
            tree.setNotIdSpan();
        }
        else
        {
            tree.unsetNotIdSpan();
            for (String node : nodes)
            {
                List<String> uuids = Lists.newArrayList(node.split("\\."));
                tree.setElementInMultiSelectTree(uuids.toArray(new String[uuids.size()]));
            }
        }
    }

    /**
     * Установить режим сохранения представления - текущее или новое
     * @param type режим - {@link PresentationType}
     */
    public void setPrsType(PresentationType type)
    {
        switch (type)
        {
            case CURRENT:
                tester.click(X_SAVE_VIEW_CURRENT_RADIO_LABEL);
                break;
            case NEW:
                tester.click(X_SAVE_VIEW_NEW_RADIO_LABEL);
                break;
            default:
                throw new ErrorInCodeException("Указан не верный тип представления: " + type);
        }
    }

    /**
     * Установить значение чекбокса "общий вид"
     * @param value устанавливаемое значение (true/false)
     */
    public void setPublic(boolean value)
    {
        tester.setCheckbox(SAVE_VIEW_PUBLIC_CHECKBOX, value);
    }

    /**
     * Заполнить поле "Название вида"
     * @param title название вида
     */
    public void setTitle(String title)
    {
        tester.sendKeys(SAVE_VIEW_TITLE, title);
    }
}
