package ru.naumen.selenium.casesutil.content;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static ru.naumen.selenium.core.WaitTool.REPETITION_PERIOD;
import static ru.naumen.selenium.core.WaitTool.WAIT_TIME;
import static ru.naumen.selenium.core.WebTester.IGNORE_STALE_ELEMENT_EXCEPTION;

import java.awt.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.StringTokenizer;

import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;

import com.google.common.base.Function;
import com.google.common.base.Preconditions;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.interfaceelement.GUIDatePicker;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.interfaceelement.RelationTree;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.DAOContentForm.PositionContent;
import ru.naumen.selenium.casesutil.model.content.gantt.Constants;
import ru.naumen.selenium.casesutil.model.content.gantt.GanttContent;
import ru.naumen.selenium.casesutil.model.content.gantt.ResourceParams;
import ru.naumen.selenium.casesutil.model.content.gantt.WorkParams;
import ru.naumen.selenium.core.WaitTool;
import ru.naumen.selenium.core.WebTester;
import ru.naumen.selenium.core.config.Config;
import ru.naumen.selenium.util.CollectionUtils;
import ru.naumen.selenium.util.Colors;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с контентом Диаграмма Ганта через интерфейс
 *
 * <AUTHOR>
 * @since Mar 14, 2014
 */
public class GUIGanttContent extends CoreTester
{
    public static final String X_SCALE = String.format(GUIXpath.InputComplex.ANY_VALUE, "scale");

    public static final String X_SERVICETIME = String.format(GUIXpath.InputComplex.ANY_VALUE, "serviceTime");

    public static final String X_PROFILES = String.format(GUIXpath.InputComplex.ANY_VALUE, "profiles");

    public static final String X_POSITION = String.format(GUIXpath.InputComplex.ANY_VALUE, "position");

    public static final String X_GANTT = "//div[@id='%s']";

    public static final String X_WORK_LINKED_CURRENT_OBJECT = String.format(GUIXpath.InputComplex.ANY_VALUE,
            "workLinkedWithCurrentObject");

    public static final String X_WORK_METACLASS = String.format(GUIXpath.InputComplex.ANY_VALUE, "workMetaClass");

    public static final String X_FILTER_MODE = String.format(GUIXpath.InputComplex.ANY_VALUE, "filterMode");

    public static final String X_WORK_METACLASS_CAPTION_VALUE = String.format(GUIXpath.Div.ID_PATTERN + "//span",
            "gwt-debug-workMetaClass-caption");

    public static final String X_WORK_METACLASSES = String.format(GUIXpath.InputComplex.ANY_VALUE, "workMetaClasses");

    public static final String X_WORK_LINKED_ATTR_CHAIN = String.format(GUIXpath.Any.ANY_VALUE, "workLinkedAttrChain");

    public static final String X_WORK_RESOURCE = String.format(GUIXpath.InputComplex.ANY_VALUE, "resource");

    public static final String X_WORK_START_DATE = String.format(GUIXpath.InputComplex.ANY_VALUE, "startDate");

    public static final String X_WORK_END_DATE = String.format(GUIXpath.InputComplex.ANY_VALUE, "endDate");

    public static final String X_PREVIOUS_WORK = String.format(GUIXpath.InputComplex.ANY_VALUE, "previousWork");

    /** Группа атрибутов для редактирования работ */
    public static final String X_WORK_ATTRIBUTE_GROUP = String.format(GUIXpath.InputComplex.ANY_VALUE,
            "workAttributeGroup");

    public static final String X_RESOURCE_LINKED_CURRENT_OBJECT_CAPTION = String
            .format(GUIXpath.Div.ID_PATTERN + "//span", "gwt-debug-resourceLinkedWithCurrentObject-caption");

    public static final String X_RESOURCE_IS_CURRENT_OBJECT_CAPTION = String.format(GUIXpath.Div.ID_PATTERN + "//span",
            "gwt-debug-resourceIsCurrentObject-caption");

    public static final String X_RESOURCE_METACLASS_CAPTION = String.format(GUIXpath.Div.ID_PATTERN + "//span",
            "gwt-debug-resourceMetaClass-caption");

    public static final String X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE = String.format(GUIXpath.Any.ANY_VALUE,
            "resourceLinkedWithCurrentObject");

    public static final String X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT = X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE
                                                                              + "//input";

    public static final String X_RESOURCE_IS_CURRENT_OBJECT_VALUE = String.format(GUIXpath.Any.ANY_VALUE,
            "resourceIsCurrentObject");

    public static final String X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT = X_RESOURCE_IS_CURRENT_OBJECT_VALUE
                                                                          + "//input";

    public static final String X_RESOURCE_METACLASS = String.format(GUIXpath.InputComplex.ANY_VALUE,
            "resourceMetaClass");

    public static final String X_RESOURCE_METACLASSES = String.format(GUIXpath.InputComplex.ANY_VALUE,
            "resourceMetaClasses");

    /** Атрибут связи с ресурсами */
    public static final String X_RESOURCE_LINKED_ATTR_CHAIN = String.format(GUIXpath.Any.ANY_VALUE,
            "resourceLinkedAttrChain");

    /** Группа атрибутов для фильтрации ресурсов */
    public static final String X_RESOURCE_ATTRIBUTE_GROUP_VALUE_INPUT = String.format(GUIXpath.InputComplex.ANY_VALUE,
            "resourceAttributeGroup");

    public static final String X_BUTTON_FASTBACKWARD = GUIContent.X_CONTENT_OUTER_PATTERN
                                                       + "//*[contains(@id,'gwt-debug-ganttDateFastBackward.')]";

    public static final String X_BUTTON_BACKWARD = GUIContent.X_CONTENT_OUTER_PATTERN
                                                   + "//*[contains(@id,'gwt-debug-ganttDateBackward.')]";

    public static final String X_BUTTON_FORWARD = GUIContent.X_CONTENT_OUTER_PATTERN
                                                  + "//*[contains(@id,'gwt-debug-ganttDateForward.')]";

    public static final String X_BUTTON_FASTFORWARD = GUIContent.X_CONTENT_OUTER_PATTERN
                                                      + "//*[contains(@id,'gwt-debug-ganttDateFastForward.')]";

    public static final String X_BUTTON_ZOOM_IN = GUIContent.X_CONTENT_OUTER_PATTERN
                                                  + "//*[contains(@id,'gwt-debug-ganttZoomIn.')]";

    public static final String X_BUTTON_ZOOM_OUT = GUIContent.X_CONTENT_OUTER_PATTERN
                                                   + "//*[contains(@id,'gwt-debug-ganttZoomOut.')]";

    public static final String X_BUTTON_REFRESH = GUIContent.X_CONTENT_OUTER_PATTERN
                                                  + "//*[contains(@id,'gwt-debug-ganttRefresh.')]";

    public static final String X_BUTTON_ADD_WORK = GUIContent.X_CONTENT_OUTER_PATTERN
                                                   + "//*[contains(@id,'gwt-debug-ganttAddWork.')]";

    public static final String X_START_DATE_SELECT = GUIContent.X_CONTENT_OUTER_PATTERN
                                                     + "//*[contains(@id,'gwt-debug-StartDateSelectTool.')"
                                                     + "]//input[@id='gwt-debug-dateTextBox']";

    public static final String X_SCALE_SELECT = GUIContent.X_CONTENT_OUTER_PATTERN
                                                + "//*[contains(@id,'gwt-debug-ScaleSelectTool.')]//input";

    public static final String X_FILTER_MODE_SELECT = GUIContent.X_CONTENT_OUTER_PATTERN
                                                      + "//*[contains(@id,'gwt-debug-FilterModeSelectTool.')]//input";

    private static final String X_CONTENT_NAMES = GUIContent.X_CONTENT_OUTER_PATTERN
                                                  + "//div[@id='gwt-debug-taskNamePanel']";

    private static final String X_CONTENT_INTERVALS = GUIContent.X_CONTENT_OUTER_PATTERN
                                                      + "//div[@id='gwt-debug-taskIntervalPanel']";

    public static final String X_RESOURCE_NAMES_ROWS = X_CONTENT_NAMES + "/div";

    public static final String X_WORK_NAMES_ROWS = X_CONTENT_NAMES
                                                   + "/div[@id='%s']//div[contains(@id,'$') or contains(@id,'"
                                                   + Constants.SHOW_MORE_WORKS + "')]";

    private static final String X_WORK_NAME_ROW = X_CONTENT_NAMES + "/div[@id='%s']//div[@id='%s']";

    private static final String X_MORE_WORKS_NAME_ROW = X_WORK_NAME_ROW + "/div";

    public static final String X_MORE_RESOURCES_NAME_ROW = X_CONTENT_NAMES + "//div[@id='%s']/div";

    private static final String X_NAME_ROW = X_CONTENT_NAMES + "//div[@id='%s']";

    private static final String X_NAME_ROW_TITLE = X_NAME_ROW + "//*[contains(text(),'%s')]";

    private static final String X_ROW_BUTTON = X_CONTENT_NAMES + "//div[@id='%s']" + GUIXpath.Span.EDIT_ICON;

    public static final String X_GRID_ROW_EXPAND_BUTTON = GUIContent.X_CONTENT_OUTER_PATTERN
                                                          + "//*[@id='taskGrid']//div[@id='%s']/span[@_code='expand']";

    public static final String X_ROW_EXPAND_DOWN_BUTTON = X_CONTENT_NAMES
                                                          + "//div[@id='%s']/span[@_code='expand' and contains"
                                                          + "(@class, 'down')]";

    public static final String X_ROW_EXPAND_UP_BUTTON = X_CONTENT_NAMES
                                                        + "//div[@id='%s']/span[@_code='expand' and contains(@class, "
                                                        + "'up')]";

    public static final String X_SVG = GUIContent.X_CONTENT_OUTER_PATTERN + Other.SVG;
    public static final String X_PANEL = GUIContent.X_CONTENT_OUTER_PATTERN + "//div[@id='gwt-debug-boundary-panel']";

    public static final String X_SVG_RELATION = X_SVG + "//*[name()='path' and @id='rel-%s-%s']";

    public static final String X_SVG_RELATION_START = X_SVG + "//*[name()='path' and starts-with(@id, 'rel-%s-')]";

    public static final String X_SVG_RELATION_END = X_SVG + "//*[name()='path' and contains(substring(@id, 5),'-%s')]";

    public static final String X_GANTT_RESOURCE = X_CONTENT_INTERVALS + "//div[contains(@id, 'resource-%s')]";

    public static final String X_GANTT_TASK = X_CONTENT_INTERVALS + "//div[@id='task-%s']";

    public static final String X_GANTT_TASK_DEADLINE = X_CONTENT_INTERVALS + "//div[@id='deadline-task-%s']";

    public static final String X_GANTT_TASK_OVERDUE = X_GANTT_TASK + "//div[@id='overdue']";

    public static final String GANTT_TASK = GUIContent.X_CONTENT_OUTER_PATTERN + "//div[@id='task-%s']";

    public static final String GANTT_TASK_SELECTED = "//div[contains(@id, '%s') and contains(@class, 'taskSelected')]";

    public static final String GANTT_TASK_PRESENTATION = "//div[contains(@id, '%s') and contains(@class, 'task')]";

    public static final String GANTT_TASK_WITHOUT_RESOURCE = "//div[contains(@id, 'withoutResourceUuid') and contains"
                                                             + "(@class, 'task')]";

    public static final String X_EDIT_BUTTON = GUIXpath.Div.ID_PATTERN + GUIXpath.Span.EDIT_ICON;

    public static final String X_HELPER = "//table[@id='gwt-debug-dnd-helper']";

    public static final String X_HELPER_START = X_HELPER + "//div[@id='gwt-debug-start']";

    public static final String X_HELPER_FINISH = X_HELPER + "//div[@id='gwt-debug-finish']";

    public static final String X_HELPER_RESOURCE = X_HELPER + "//div[@id='gwt-debug-resource']";

    public static final String X_GANTT_LEFT_RING = X_CONTENT_INTERVALS + "//div[@id='gwt-debug-left-ring-%s']";

    public static final String X_GANTT_RIGHT_RING = X_CONTENT_INTERVALS + "//div[@id='gwt-debug-right-ring-%s']";

    public static final String X_GANTT_REMOVE_WIDGET = "//div[@id='gwt-debug-remove-link-widget']";

    public static final String X_SVG_TEMP_ARROW = X_SVG + "//*[name()='path' and @id='temp-arrow']";

    public static final String X_STATE_COLOR_CIRCLE =
            X_CONTENT_NAMES + "//div[@id='colorCircle-%s']" + GUIXpath.Div.COLOR_CIRCLE;

    public static final String X_GANTT_RIGHT_SLIDE_TRIANGLE = GANTT_TASK + "//div[@id='right-slideTriangle-task-%s']";

    public static final String X_GANTT_LEFT_SLIDE_TRIANGLE = GANTT_TASK + "//div[@id='left-slideTriangle-task-%s']";

    public static final String X_GANTT_LEFT_SLIDE_ON_CANVAS_TRIANGLE = GUIContent.X_CONTENT_OUTER_PATTERN
                                                                       + "//div[@id='left-slideTriangleOnCanvas-task"
                                                                       + "-%s']";

    public static final String X_GANTT_RIGHT_SLIDE_ON_CANVAS_TRIANGLE = GUIContent.X_CONTENT_OUTER_PATTERN
                                                                        + "//div[@id='right-slideTriangleOnCanvas"
                                                                        + "-task-%s']";

    public static final String X_DATE_HEADER = GUIContent.X_CONTENT_OUTER_PATTERN
                                               + "//div[@class='firstHeaderRow']//div[text()='%s']";

    public static final String X_DAY_HEADER = GUIContent.X_CONTENT_OUTER_PATTERN
                                              + "//div[@class='secondHeaderRow']//div//div";

    public static final String X_WEEK_HEADER = GUIContent.X_CONTENT_OUTER_PATTERN
                                               + "//div[@class='firstHeaderRow']//div//div[text()='%s']";

    //Контекстное меню
    public static final String GO_TO_CARD = "//*[@id='gwt-debug-goToCard-%s']";

    public static final String EDIT = "//*[@id='gwt-debug-edit-%s']";

    public static final String DELETE = "//*[@id='gwt-debug-delete-%s']";

    public static final String BREAK_LINK = "//*[@id='gwt-debug-breakLink-%s']";

    public static final String CONTEXT_MENU = "//*[@id='gwt-debug-ganttContextMenu']";

    public static final String CARD_LINK = "//a[contains (@href, '%s')]";

    public static final String X_GANTT_COLUMN = GUIContent.X_CONTENT_OUTER_PATTERN + "//*[@id='column-%s']";
    public static final String X_GANTT_COLUMNS = GUIContent.X_CONTENT_OUTER_PATTERN
                                                 + "//*[contains(@id,'column-') and @id!='column-']";

    public static final String GANTT_HINT = GUIContent.X_CONTENT_OUTER_PATTERN
                                            + String.format(GUIXpath.Span.ANY_ICON, "hint");

    /**
     * Добавить связь между работами (от правой границы к левой)
     * @param gantt - контент диаграммы
     * @param predecessor - предок
     * @param successor - потомок
     */
    public static void addPredecessorWork(GanttContent gantt, Bo predecessor, Bo successor)
    {
        moveToRightRing(gantt, predecessor);
        assertRightRingAppear(gantt, predecessor);
        clickRingHoldAndMove(gantt, predecessor, false, successor, true);
        assertLeftRingAppear(gantt, successor);
        assertTempArrowAppear(gantt);
        releaseLeftButton();
    }

    /**
     * Добавить связь между работами (от левой границы к правой)
     * @param gantt - контент диаграммы
     * @param successor - потомок
     * @param predecessor - предок
     */
    public static void addSuccessorWork(GanttContent gantt, Bo successor, Bo predecessor)
    {
        moveToLeftRing(gantt, successor);
        assertLeftRingAppear(gantt, successor);
        clickRingHoldAndMove(gantt, successor, true, predecessor, false);
        assertRightRingAppear(gantt, predecessor);
        assertTempArrowAppear(gantt);
        releaseLeftButton();
    }

    /**
     * Проверить присутствует ли кнопка "Добавить" в контенте типа Диаграмма Ганта.
     * @param gantt модель контента
     * @param isPresent true - кнопка присутствует, false - отсутствует.
     */
    public static void assertAddWorkButton(GanttContent gantt, boolean isPresent)
    {
        String msg = String.format("Кнопка 'Добавить' %s на диаграмме", isPresent ? "отсутствует" : "присутствует");
        String xpath = String.format(X_BUTTON_ADD_WORK, gantt.getXpathId());
        GUITester.assertExists(xpath, isPresent, msg);
    }

    /**
     * Проверить, являются ли все видимые колонки белыми (или серыми)
     * @param gantt - контент диаграммы
     * @param isWhite - должны ли быть колонки белыми или серыми
     */
    public static void assertAllColumnsWhiteOrGrey(GanttContent gantt, boolean isWhite)
    {
        List<WebElement> columns = tester.findDisplayedElementsWithoutScroll(X_GANTT_COLUMNS, gantt.getXpathId());
        for (int i = 1; i <= columns.size(); i++)
        {
            assertColumnWhiteOrGrey(gantt, isWhite, i);
        }
    }

    /**
     * Является ли оба угла работы косыми
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertBothWorkCornerUncertain(GanttContent gantt, Bo work)
    {
        assertRightWorkCornerUncertain(true, gantt, work);
        assertLeftWorkCornerUncertain(true, gantt, work);
    }

    /**
     * Проверить, является ли цвет колонки белым или серым
     * @param gantt - контент диаграммы
     * @param isWhite - должна ли быть колонка белой (или серой)
     * @param columnNumbers - номер колонки по порядку
     */
    public static void assertColumnWhiteOrGrey(GanttContent gantt, boolean isWhite, int... columnNumbers)
    {
        for (int columnNumber : columnNumbers)
        {
            String color = GUITester.waitCssValue(X_GANTT_COLUMN, "background-color", isWhite ? Colors.TRANSPARENT
                            : Colors.SMOKY_WHITE,
                    gantt.getXpathId(), columnNumber);
            String msg = "Цвет колонки #" + columnNumber + " не совпал с ожидаемым. Полученный цвет " + color;
            assertTrue(msg, color != null && color.equalsIgnoreCase(isWhite ? Colors.TRANSPARENT : Colors.SMOKY_WHITE));
        }
    }

    /**
     * Проверить контекстное меню содержащее пункты определяемые булевыми флагами
     * @param workObj работа в диаграмме гантта, над которой вызывается контекстное меню
     * @param toCard пункт "Перейти на карточку"
     * @param edit пункт "Редактировать"
     * @param breakLink пункт "Разорвать связь"
     * @param delete пункт "Удалить"
     */
    public static void assertContextMenu(Bo workObj, Boolean toCard, Boolean edit, Boolean breakLink, Boolean delete)
    {
        assertTrue("Контекстное меню не найдено", tester.waitAppear(GUIGanttContent.CONTEXT_MENU));
        GUITester.assertExists(String.format(GO_TO_CARD, workObj.getUuid()), toCard, getmsgForContext(toCard));
        GUITester.assertExists(String.format(EDIT, workObj.getUuid()), edit, getmsgForContext(edit));
        GUITester.assertExists(String.format(BREAK_LINK, workObj.getUuid()), breakLink, getmsgForContext(breakLink));
        GUITester.assertExists(String.format(DELETE, workObj.getUuid()), delete, getmsgForContext(delete));
    }

    /**
     * Проверить, содержится ли в диаграмме заголовок с соответствующей датой
     * @param expected (true/false) ожидаем, содержится заголовок или нет
     * @param gantt - контент диаграммы
     * @param headerText - ожидаемый текст заголовка
     */
    public static void assertDateHeader(boolean expected, GanttContent gantt, String headerText)
    {
        String xpath = String.format(X_DATE_HEADER, gantt.getXpathId(), headerText);
        GUITester.assertExists(xpath, expected,
                "В диаграмме заголовок" + (expected ? "отсутстсвует" : "присутстсвует"));
    }

    /**
     * Проверить отсутствие вертикальной черты - дедлайна
     * @param gantt модель контента
     * @param task модель работы
     */
    public static void assertDeadlineAbsence(GanttContent gantt, Bo task)
    {
        String msg = String.format("Отсутствует дедлайн работы %s", task.getTitle());
        assertTrue(msg, tester.waitDisappear(X_GANTT_TASK_DEADLINE, gantt.getXpathId(), task.getUuid()));
    }

    /**
     * Проверить наличие вертикальной черты - дедлайна
     * @param gantt модель контента
     * @param task модель работы
     */
    public static void assertDeadlinePresence(GanttContent gantt, Bo task)
    {
        String msg = String.format("Отсутствует дедлайн работы %s", task.getTitle());
        assertTrue(msg, tester.waitAppear(X_GANTT_TASK_DEADLINE, gantt.getXpathId(), task.getUuid()));
    }

    /**
     * Проверить, что контекстное меню исчезло
     */
    public static void assertDisappearContextMenu()
    {
        assertTrue("Контекстное меню не исчезло", tester.waitDisappear(GUIGanttContent.CONTEXT_MENU));
    }

    /**
     * Проверить значение в поле "Режим фильтрации"
     * @param gantt модель контента
     */
    public static void assertFilterMode(GanttContent gantt)
    {
        GUITester.assertValue(X_FILTER_MODE_SELECT, gantt.getFilterMode().getTitle(), gantt.getXpathId());
    }

    /**
     * Проверяет правильность заполнения всех полей на форме в соответсвии с моделью
     * (проверяет только те поля, которые есть на форме, логику появления/исчезновения полей проверяем в
     * соответствующих тестах)
     */
    public static void assertFormFields(GanttContent gantt)
    {
        GUITester.assertValue(GUIXpath.PropertyDialogBoxContent.CAPTION_VALUE, gantt.getTitle());
        GUITester.assertCheckboxState(GUIXpath.PropertyDialogBoxContent.SHOW_CAPTION_VALUE_INPUT,
                Boolean.parseBoolean(gantt.getShowTitle()));
        GUITester.assertValue(X_SCALE, gantt.getScale().getTitle());
        // TODO NSDPRD-2711
        //GUISelect.assertValue(X_SERVICETIME, gantt.getServiceTime());
        GUIMultiSelect.assertSelectedSize(X_PROFILES, gantt.getProfileCodes().size());

        if (tester.isPresence(X_PROFILES))
        {
            GUIMultiSelect.assertSelected(X_PROFILES, Iterables.toArray(gantt.getProfileCodes(), String.class));
        }
        if (tester.isPresence(X_POSITION))
        {
            GUITester.assertValue(X_POSITION, PositionContent.valueOf(gantt.getPosition()).getTitle());
        }

        WorkParams work = gantt.getWorkParams();
        GUITester.assertCheckboxState(X_WORK_LINKED_CURRENT_OBJECT, work.isLinkedWithCurrentObject());
        if (tester.isPresence(X_WORK_LINKED_ATTR_CHAIN))
        {
            RelationTree workTree = new RelationTree(X_WORK_LINKED_ATTR_CHAIN);
            String value = !work.getLinkedAttrChain().isEmpty() ? work.getLinkedAttrChain().get(0).getTitle() : null;
            workTree.assertValue(value);
        }
        if (tester.isPresence(X_WORK_METACLASS))
        {
            GUISelect.assertValue(X_WORK_METACLASS, work.getWorkMetaClass());
        }
        else
        {
            GUITester.assertTextPresent(X_WORK_METACLASS_CAPTION_VALUE, StringUtils.getTitle(work.getWorkMetaClass()));
        }
        GUIMultiSelect.assertSelectedSize(X_WORK_METACLASSES, work.getWorkMetaClasses().size());
        GUIMultiSelect.assertSelected(X_WORK_METACLASSES, Iterables.toArray(work.getWorkMetaClasses(), String.class));
        GUISelect.assertValue(X_WORK_RESOURCE, work.getResource());
        GUISelect.assertValue(X_WORK_START_DATE, work.getStartDate());
        GUISelect.assertValue(X_WORK_END_DATE, work.getEndDate());
        GUISelect.assertValue(X_PREVIOUS_WORK, work.getPreviousWork());
        GUISelect.assertValue(X_WORK_ATTRIBUTE_GROUP, work.getAttributeGroup());

        ResourceParams resource = gantt.getResourceParams();

        if (tester.isPresence(X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT))
        {
            GUITester.assertCheckboxState(X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT,
                    resource.isLinkedWithCurrentObject());
        }
        else if (tester.isPresence(X_RESOURCE_LINKED_CURRENT_OBJECT_CAPTION))
        {
            String text = StringUtils.toYesNo(resource.isLinkedWithCurrentObject());
            GUITester.assertTextPresent(X_RESOURCE_LINKED_CURRENT_OBJECT_CAPTION, text);
        }

        if (tester.isPresence(X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT))
        {
            GUITester.assertCheckboxState(X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT,
                    resource.isResourceIsCurrentObject());
        }
        else if (tester.isPresence(X_RESOURCE_IS_CURRENT_OBJECT_CAPTION))
        {
            String text = StringUtils.toYesNo(resource.isResourceIsCurrentObject());
            GUITester.assertTextPresent(X_RESOURCE_IS_CURRENT_OBJECT_CAPTION, text);
        }

        if (tester.isPresence(X_RESOURCE_METACLASS))
        {
            GUISelect.assertValue(X_RESOURCE_METACLASS, resource.getResourceMetaClass());
        }

        if (tester.isPresence(X_RESOURCE_METACLASSES))
        {
            GUIMultiSelect.assertSelectedSize(X_RESOURCE_METACLASSES, resource.getResourceMetaClasses().size());
            GUIMultiSelect.assertSelected(X_RESOURCE_METACLASSES,
                    Iterables.toArray(resource.getResourceMetaClassFqns(), String.class));
        }

        if (tester.isPresence(X_RESOURCE_ATTRIBUTE_GROUP_VALUE_INPUT))
        {
            GUISelect.assertValue(X_RESOURCE_ATTRIBUTE_GROUP_VALUE_INPUT, resource.getAttributeGroup());
        }
    }

    /**
     * Проверить значения параметров в помощнике
     * @param start - атрибут даты начала работы
     * @param finish - атрибут даты завершения работы
     * @param resource - ресурс
     */
    public static void assertHelperValues(Attribute start, Attribute finish, Bo resource)
    {
        GUITester.assertExists(X_HELPER, true, "Помощник отсутствует");
        String expectedStart = start.getValue() == null ? start.getTitle() : start.getTitle() + " " + start.getValue();
        String expectedFinish = finish.getValue() == null ? finish.getTitle()
                : finish.getTitle() + " " + finish.getValue();

        GUITester.assertTextPresentWithMsg(X_HELPER_START, expectedStart,
                "Полученное значение начала работы в помощнике не соответствует ожидаемому");
        GUITester.assertTextPresentWithMsg(X_HELPER_FINISH, expectedFinish,
                "Полученное значение финиша работы в помощнике не соответствует ожидаемому");
        String resourceValue = resource != null ? resource.getTitle() : Constants.WITHOUT_RESOURCE_TITLE;
        GUITester.assertTextContains(X_HELPER_RESOURCE, resourceValue);
    }

    /**
     * Проверить текст подсказки для режима фильтрации работ
     * @param gantt- контент диаграммы
     * @param hintText - текст подсказки
     */
    public static void assertHintTitle(GanttContent gantt, String hintText)
    {
        WebElement hintElem = tester.findDisplayed(GANTT_HINT, gantt.getXpathId());
        assertEquals(hintText, hintElem.getAttribute("title"));
    }

    /**
     * Присутствует ли левый треугольник быстрого перемещения к старту работы
     * @param isPresent - присутствует или отсутствует (true/false)
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     * @param onCanvas - на холсте или на работе(true - холст, false - работа)
     */
    public static void assertLeftQuickMoveTrianglePresence(boolean isPresent, GanttContent gantt, Bo triangleSource,
            boolean onCanvas)
    {
        String msg;
        String xpath;
        if (onCanvas)
        {
            xpath = String.format(X_GANTT_LEFT_SLIDE_ON_CANVAS_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid());

        }
        else
        {
            xpath = String.format(X_GANTT_LEFT_SLIDE_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid(),
                    triangleSource.getUuid());
        }

        if (isPresent)
        {
            msg = String.format("Отсутствует левый треугольник быстрого перемещения : %s", triangleSource.getTitle());
            GUITester.assertExists(xpath, true, msg);
        }
        else
        {
            msg = String.format("Присутствует левый треугольник быстрого перемещения : %s", triangleSource.getTitle());
            GUITester.assertExists(xpath, false, msg);
        }
    }

    /**
     * Присутствует ли левое кольцо
     * @param gantt - контент диаграммы
     * @param ringSource - работа, к которой привязано кольцо
     */
    public static void assertLeftRingAppear(GanttContent gantt, Bo ringSource)
    {
        String msg = String.format("Отсутствует левое кольцо: %s", ringSource.getTitle());
        assertTrue(msg, tester.waitAppear(X_GANTT_LEFT_RING, gantt.getXpathId(), ringSource.getUuid()));
    }

    /**
     * Отсутствует ли левое кольцо
     * @param gantt - контент диаграммы
     * @param ringSource - работа, к которой привязано кольцо
     */
    public static void assertLeftRingDisappear(GanttContent gantt, Bo ringSource)
    {
        String msg = String.format("Присутствует левое кольцо: %s", ringSource.getTitle());
        assertTrue(msg, tester.waitDisappear(X_GANTT_LEFT_RING, gantt.getXpathId(), ringSource.getUuid()));
    }

    /**
     * Является ли левый угол работы косым
     * @param isUncertain - является ли косым, true -да, false - нет
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertLeftWorkCornerUncertain(boolean isUncertain, GanttContent gantt, Bo work)
    {
        String msg = "Левый угол работы " + work.getTitle();
        msg += isUncertain ? " не косой" : " косой";
        boolean actual = GUITester.waitAttributePropertyContains(X_GANTT_TASK, "class", "leftCornerUncertain",
                isUncertain, gantt.getXpathId(), work.getUuid());
        assertTrue(msg, actual);
    }

    /**
     * Проверить отсутствие признака просроченности работы (часть, закрашенная красным)
     * @param gantt модель контента
     * @param task модель работы
     */
    public static void assertOverdueAbsence(GanttContent gantt, Bo task)
    {
        String msg = String.format("Работа %s помечена как просроченная", task.getTitle());
        assertTrue(msg, tester.waitDisappear(X_GANTT_TASK_OVERDUE, gantt.getXpathId(), task.getUuid()));
    }

    /**
     * Проверить наличие признака просроченности работы (часть, закрашенная красным)
     * @param gantt модель контента
     * @param task модель работы
     */
    public static void assertOverduePresence(GanttContent gantt, Bo task)
    {
        String msg = String.format("Работа %s не помечена как просроченная", task.getTitle());
        assertTrue(msg, tester.waitAppear(X_GANTT_TASK_OVERDUE, gantt.getXpathId(), task.getUuid()));
    }

    /**
     * Проверить наличие кнопки 'Обновить' в диаграмме Ганта
     * @param gantt модель контента
     */
    public static void assertRefreshPresence(GanttContent gantt)
    {
        String xpath = String.format(X_BUTTON_REFRESH, gantt.getXpathId());
        GUITester.assertExists(xpath, true, "Кнопка 'Обновить' отсутствует в контенте 'Диаграмма Ганта'");
    }

    /**
     * Присутсвует ли виджет удаления связи
     * @param gantt - контент диаграммы
     */
    public static void assertRemoveWidgetAppear(GanttContent gantt)
    {
        assertEquals("Количество виджетов удаления больше одного.",
                tester.findNumberElements(X_GANTT_REMOVE_WIDGET, 1, gantt.getXpathId()).size(), 1);
    }

    /**
     * Отсутсвует ли виджет удаления связи
     * @param gantt - контент диаграммы
     */
    public static void assertRemoveWidgetDisappear(GanttContent gantt)
    {
        assertTrue("Присутствует виджет удаления связи", tester.waitDisappear(X_GANTT_REMOVE_WIDGET, gantt
                .getXpathId()));
    }

    /**
     * Проверить, присутствует ли хотя бы одна полоса загруженности у ресурса на диаграмме
     * @param isPresent присутствует/отсутствует (true/false)
     * @param gantt модель контента
     * @param resource модель ресурса, может быть null, тогда будет считаться для [Без ресурса]
     */
    public static void assertResourceLinePresence(boolean isPresent, GanttContent gantt, Bo resource)
    {
        String resourceId = resource == null ? Constants.WITHOUT_RESOURCE_UUID : resource.getUuid();
        if (isPresent)
        {
            GUITester.assertExists(String.format(X_GANTT_RESOURCE, gantt.getXpathId(), resourceId), true,
                    "отсутствует полоса загруженности ресурса");
        }
        else
        {
            GUITester.assertExists(String.format(X_GANTT_RESOURCE, gantt.getXpathId(), resourceId), false,
                    "присутствует полоса загруженности ресурса");
        }
    }

    /**
     * Проверить контент на состояние отображаемых ресурсов.
     *
     * @param gantt модель контента
     * @param withoutResources должен ли присутствовать ресурс [Без ресурса]
     * @param isShowMore должна ли быть ссылка [Загрузить еще ресурсы]
     * @param objects ожидаемый порядок следования моделей
     */
    public static void assertResources(GanttContent gantt, boolean withoutResources, boolean isShowMore, Bo... objects)
    {
        int expectedNumber = objects.length + (withoutResources ? 1 : 0) + (isShowMore ? 1 : 0);
        List<WebElement> gridElements = tester.findNumberElements(X_RESOURCE_NAMES_ROWS, expectedNumber, gantt
                .getXpathId());
        List<String> actual = new ArrayList<>();
        String rowXpathTemplate = String.format("(" + X_RESOURCE_NAMES_ROWS + ")[%%d]", gantt.getXpathId());
        for (int i = 0; i < gridElements.size(); i++)
        {
            String rowXpath = String.format(rowXpathTemplate, i + 1);
            //Во внутреннем скролле стандартный автоскролл selenium-а не отрабатывает, и элемент не видим
            if (!tester.waitAppear(rowXpath) && tester.find(rowXpath) != null)
            {
                tester.scrollWindowToElement(tester.find(rowXpath));
                assertTrue("Ресурс не отображается", tester.waitAppear(rowXpath));
            }
            actual.add(GUITester.getAttributeProperty(rowXpath, "id"));
        }
        List<String> expected = ModelUtils.getUuids(objects);
        if (isShowMore)
        {
            expected.add(Constants.SHOW_MORE_RESOURCES);
        }
        if (withoutResources)
        {
            expected.add(Constants.WITHOUT_RESOURCE_UUID);
        }
        assertEquals("Полученный список ресурсов не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Присутствует ли правый треугольник быстрого перемещения к финишу работы
     * @param isPresent - присутствует или отсутствует (true/false)
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     * @param onCanvas - на холсте или на работе(true - холст, false - работа)
     */
    public static void assertRightQuickMoveTrianglePresence(boolean isPresent, GanttContent gantt, Bo triangleSource,
            boolean onCanvas)
    {
        String msg;
        String xpath;
        if (onCanvas)
        {
            xpath = String.format(X_GANTT_RIGHT_SLIDE_ON_CANVAS_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid());
        }
        else
        {
            xpath = String.format(X_GANTT_RIGHT_SLIDE_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid(),
                    triangleSource.getUuid());
        }

        if (isPresent)
        {
            msg = String.format("Отсутствует правый треугольник быстрого перемещения : %s", triangleSource.getTitle());
            GUITester.assertExists(xpath, isPresent, msg);
        }
        else
        {
            msg = String.format("Присутствует правый треугольник быстрого перемещения : %s", triangleSource.getTitle());
            GUITester.assertExists(xpath, isPresent, msg);
        }
    }

    /**
     * Присутствует ли правое кольцо
     * @param gantt - контент диаграммы
     * @param ringSource - работа, к которой привязано кольцо
     */
    public static void assertRightRingAppear(GanttContent gantt, Bo ringSource)
    {
        String msg = String.format("Отсутствует правое кольцо: %s", ringSource.getTitle());
        assertTrue(msg, tester.waitAppear(X_GANTT_RIGHT_RING, gantt.getXpathId(), ringSource.getUuid()));
    }

    /**
     * Присутствует ли правое кольцо
     * @param gantt - контент диаграммы
     * @param ringSource - работа, к которой привязано кольцо
     */
    public static void assertRightRingDisappear(GanttContent gantt, Bo ringSource)
    {
        String msg = String.format("Присутствует правое кольцо: %s", ringSource.getTitle());
        assertTrue(msg, tester.waitDisappear(X_GANTT_RIGHT_RING, gantt.getXpathId(), ringSource.getUuid()));
    }

    /**
     * Является ли правый угол работы косым
     * @param isUncertain - является ли косым, true -да, false - нет
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertRightWorkCornerUncertain(boolean isUncertain, GanttContent gantt, Bo work)
    {
        String msg = "Правый угол работы " + work.getTitle();
        msg += isUncertain ? " не косой" : " косой";
        boolean actual = GUITester.waitAttributePropertyContains(X_GANTT_TASK, "class", "rightCornerUncertain",
                isUncertain, gantt.getXpathId(), work.getUuid());
        assertTrue(msg, actual);
    }

    /**
     * Проверить значение в поле "Масштаб"
     * @param gantt модель контента
     */
    public static void assertScale(GanttContent gantt)
    {
        GUITester.assertValue(X_SCALE_SELECT, gantt.getScale().getTitle(), gantt.getXpathId());
    }

    /**
     * Проверить значение в поле "Дата начала"
     * @param gantt модель контента
     * @param date
     */
    public static void assertStartDate(GanttContent gantt, String date)
    {
        GUITester.assertValue(X_START_DATE_SELECT, date, gantt.getXpathId());
    }

    /**
     * Проверить значение в поле "Дата начала", дата должна быть в часовом интервале от указанной
     * @param gantt модель контента
     * @param date дата
     */
    public static void assertStartDateInHourInterval(GanttContent gantt, String date)
    {
        String value = GUITester.getValue(X_START_DATE_SELECT, gantt.getXpathId());
        SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy HH:mm");
        try
        {
            long actualValue = sdf.parse(value).getTime();
            Date dateParam = sdf.parse(date);
            Calendar cal = Calendar.getInstance();
            cal.setTime(dateParam);
            cal.add(Calendar.HOUR_OF_DAY, -1);
            long start = cal.getTime().getTime();
            cal.add(Calendar.HOUR_OF_DAY, 2);
            long finish = cal.getTime().getTime();
            assertTrue("Стартовая дата не находится в часовом интервале от даты " + date,
                    actualValue >= start && actualValue <= finish);
        }
        catch (ParseException e)
        {
            throw new AssertionError(e);
        }
    }

    /**
     * Проверить цвет работы
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertStateColorCircleAbsence(GanttContent gantt, Bo work)
    {
        String msg = String.format("Присутствует круг с цветом статуса: %s", work.getTitle());
        assertTrue(msg, tester.waitDisappear(X_STATE_COLOR_CIRCLE, gantt.getXpathId(), work.getUuid()));
    }

    /**
     * Проверить, совпадает ли цвет круга статуса работы с ожидаемым цветом
     * @param gantt модель контента
     * @param work работа
     * @param color ожидаемый цвет
     */
    public static void assertStateColorCircleColor(GanttContent gantt, Bo work, String color)
    {
        assertStateColorCirclePresence(gantt, work);
        WebElement circle = tester.findDisplayed(X_STATE_COLOR_CIRCLE, gantt.getXpathId(), work.getUuid());
        assertEquals("Цвет работы не совпал с ожидаемым.", color.toLowerCase(Locale.ENGLISH), Config.isChrome()
                ? rgba2hex(circle.getCssValue("background-color"))
                : rgb2hex(circle.getCssValue("background-color")));
    }

    /**
     * Проверить присутствие круга статуса для работы
     * @param gantt модель контента
     * @param work работа
     */
    public static void assertStateColorCirclePresence(GanttContent gantt, Bo work)
    {
        String msg = String.format("Отсутствует круг с цветом статуса: %s", work.getTitle());
        assertTrue(msg, tester.waitAppear(X_STATE_COLOR_CIRCLE, gantt.getXpathId(), work.getUuid()));
    }

    /**
     * Присутствует ли временная стрелка добавления связи
     * @param gantt - контент диаграммы
     */
    public static void assertTempArrowAppear(GanttContent gantt)
    {
        assertTrue("Отсутствует временная стрелка добавления связей между работами",
                tester.waitAppear(X_SVG_TEMP_ARROW, gantt.getXpathId()));
    }

    /**
     * Отсутствует ли временная стрелка добавления связи
     * @param gantt - контент диаграммы
     */
    public static void assertTempArrowDisappear(GanttContent gantt)
    {
        assertTrue("Присутствует временная стрелка добавления связей между работами",
                tester.waitDisappear(X_SVG_TEMP_ARROW, gantt.getXpathId()));
    }

    /**
     * Проверить, что неопределенная работа расположена в центре видимой части диаграммы
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertUncertainWorkIsCentered(final GanttContent gantt, final Bo work)
    {
        Boolean result = WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, 250L, WebTester.IGNORE_EXCEPTIONS,
                new Function<WebDriver, Boolean>()
                {
                    @Override
                    public Boolean apply(WebDriver input)
                    {
                        WebElement outer = tester.findDisplayed(GUIContent.X_CONTENT_OUTER_PATTERN, gantt.getXpathId());
                        WebElement task = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
                        int ganttHalfWidth = (outer.getSize().getWidth() - 350) / 2;
                        int workHalfWidth = Integer.parseInt(task.getCssValue("left").replace("px", "")) - 350
                                            + (75 / 2);
                        return Math.abs(ganttHalfWidth - workHalfWidth) < 10;
                    }
                });
        assertTrue("Работа " + work.getTitle() + " не находится по середине холста.", result != null && result);
    }

    /**
     * Проверить, что неделя с соответствующим заголовком имеет ожидаемое количество дней
     * в видимой части диаграммы
     * @param gantt - контент диаграммы
     * @param headerText - заголовок месяца, у которого проверяются дни
     * @param length ожидаемое количество дней
     */
    //TODO Заказать id для проверки через кол-во элементов
    public static void assertWeekLength(GanttContent gantt, String headerText, int length)
    {
        GUITester.assertElementsSize(X_WEEK_HEADER, length * 50, 24, 10, gantt.getXpathId(), headerText);
    }

    /**
     * Проверить, соответствует ли ширина работы стандартному размеру для неопределенных работ
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertWidthOfWorkIsUncertain(final GanttContent gantt, final Bo work)
    {
        assertTrue("Ширина работы не соответствует стандартному размеру для неопределенных работ",
                assertWidthOfWorkWithUncertain(gantt, work, true));
    }

    /**
     * Проверить цвет работы
     * @param gantt - контент диаграммы
     * @param work - работа
     * @param color - 16ричный код цвета
     */
    public static void assertWorkColor(GanttContent gantt, Bo work, String color)
    {
        WebElement task = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        assertEquals("Цвет работы не совпал с ожидаемым.", color.toLowerCase(Locale.ENGLISH), Config.isChrome()
                ? rgba2hex(task.getCssValue("background-color"))
                : rgb2hex(task.getCssValue("background-color")));
    }

    /**
     * Проверить, выбрана ли работа в контенте Диаграмма Ганта
     * @param gantt Диаграмма Ганта, в которой проверяем
     * @param workObj проверяемая работа,
     * @param isSelected true - работа присутствует, false - работа отсутствует
     */
    public static void assertWorkExists(GanttContent gantt, Bo workObj, boolean isSelected)
    {
        String msg = "Работа " + (isSelected ? "отсутствует" : "присутствует") + " на диаграмме";
        GUITester.assertExists(String.format(GANTT_TASK, gantt.getXpathId(), workObj.getUuid()), isSelected, msg);
    }

    /**
     * Проверить количество входящих связей у работы.
     * @param gantt модель контента
     * @param work модель проверяемой работы
     * @param expected ожидаемое количество
     */
    public static void assertWorkInputRelationCount(GanttContent gantt, Bo work, int expected)
    {
        assertWorkRelationCount(gantt, work, expected, true);
    }

    /**
     * Проверить, что работа находится у левого края холста ДГ
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertWorkIsLeft(final GanttContent gantt, final Bo work)
    {
        Boolean result = WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, 250L, WebTester.IGNORE_EXCEPTIONS,
                new Function<WebDriver, Boolean>()
                {
                    @Override
                    public Boolean apply(WebDriver input)
                    {
                        WebElement task = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
                        int ganttCoordLeftSide = 350;
                        int workLeftSide = Integer.parseInt(task.getCssValue("left").replace("px", ""));
                        return ganttCoordLeftSide == workLeftSide;
                    }
                });
        assertTrue("Работа " + work.getTitle() + " не находится у левой части холста.", result != null && result);
    }

    /**
     * Проверить, что работа не отображается как незапланированная
     * @param gantt Диаграмма Ганта, в которой проверяем
     * @param work Проверяемая работа
     */
    public static void assertWorkIsNotUncertain(final GanttContent gantt, final Bo work)
    {
        assertWidthOfWorkIsNotUncertain(gantt, work);
        assertRightWorkCornerUncertain(false, gantt, work);
        assertLeftWorkCornerUncertain(false, gantt, work);
    }

    /**
     * Проверить, что работа находится у правого края холста ДГ
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    public static void assertWorkIsRight(final GanttContent gantt, final Bo work)
    {
        Boolean result = WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, 250L, WebTester.IGNORE_EXCEPTIONS,
                new Function<WebDriver, Boolean>()
                {
                    @Override
                    public Boolean apply(WebDriver input)
                    {
                        WebElement outer = tester.findDisplayed(GUIContent.X_CONTENT_OUTER_PATTERN, gantt.getXpathId());
                        WebElement task = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
                        int ganttCoordRightSide = outer.getSize().getWidth();
                        int workRightSide = Integer.parseInt(task.getCssValue("left").replace("px", ""))
                                            + task.getSize().getWidth();
                        return Math.abs(ganttCoordRightSide - workRightSide) < 5;
                    }
                });
        assertTrue("Работа " + work.getTitle() + " не находится у правой части холста.", result != null && result);
    }

    /**
     * Проверить отсутствие связи у работы с другими работами
     * @param gantt модель контента
     * @param work модель проверяемой работы
     */
    public static void assertWorkNotRelated(GanttContent gantt, Bo work)
    {
        assertWorkInputRelationCount(gantt, work, 0);
        assertWorkOutputRelationCount(gantt, work, 0);
    }

    /**
     * Проверить количество исходящих связей у работы.
     * @param gantt модель контента
     * @param work модель проверяемой работы
     * @param expected ожидаемое количество
     */
    public static void assertWorkOutputRelationCount(GanttContent gantt, Bo work, int expected)
    {
        assertWorkRelationCount(gantt, work, expected, false);
    }

    /**
     * Проверить отсутствие связи между работами
     * @param gantt модель контента
     * @param from модель работы из которой исходит связь
     * @param to модель работы в которую входит связь
     */
    public static void assertWorkRelationAbsence(GanttContent gantt, Bo from, Bo to)
    {
        String msg = String.format("Присутствует связь: %s -> %s", from.getTitle(), to.getTitle());
        assertTrue(msg, tester.waitDisappear(X_SVG_RELATION, gantt.getXpathId(), from.getUuid(), to.getUuid()));
    }

    /**
     * Проверить количество входящих/исходящих связей у работы.
     * @param gantt модель контента
     * @param work модель проверяемой работы
     * @param imputExpected ожидаемое количество входящих связей
     * @param outputExpected  ожидаемое количество исходящих связей
     */
    public static void assertWorkRelationCount(GanttContent gantt, Bo work, int imputExpected, int outputExpected)
    {
        assertWorkInputRelationCount(gantt, work, imputExpected);
        assertWorkOutputRelationCount(gantt, work, outputExpected);
    }

    /**
     * Проверить наличие связи между работами
     * @param gantt модель контента
     * @param from модель работы из которой исходит связь
     * @param to модель работы в которую входит связь
     */
    public static void assertWorkRelationPresence(GanttContent gantt, Bo from, Bo to)
    {
        String msg = String.format("Отсутствует связь: %s -> %s", from.getTitle(), to.getTitle());
        assertTrue(msg, tester.waitAppear(X_SVG_RELATION, gantt.getXpathId(), from.getUuid(), to.getUuid()));
    }

    /**
     * Проверить контент на состояние отображаемых работ в указанном ресурсе.
     * (проверка по id работ)
     *
     * @param gantt модель контента
     * @param resourceId id проверяемого ресурса
     * @param isShowMore должна ли быть ссылка [Показать еще работы]
     * @param order с учетом порядка/без учета порядка
     * @param match проверка на полное совпадение
     * @param workIds ожидаемый набор id-ов работ
     */
    public static void assertWorks(GanttContent gantt, String resourceId, boolean isShowMore, boolean order,
            boolean match, String... workIds)
    {
        if (workIds.length == 0)
        {
            assertTrue("У ресурса есть работы: " + resourceId,
                    tester.waitDisappear(X_WORK_NAMES_ROWS, gantt.getXpathId(), resourceId));
            return;
        }

        if (!tester.isPresence(X_WORK_NAMES_ROWS, gantt.getXpathId(), resourceId))
        {
            tester.scrollWindowToElement(tester.find(X_WORK_NAMES_ROWS, gantt.getXpathId(), resourceId));
        }

        List<WebElement> gridElements = tester.findElements(X_WORK_NAMES_ROWS, gantt.getXpathId(), resourceId);
        List<String> actual = new ArrayList<>();

        assertTrue("В контенте нет ни одной работы", tester.waitAppear(X_WORK_NAMES_ROWS, gantt.getXpathId(),
                resourceId));

        for (WebElement row : gridElements)
        {
            actual.add(row.getAttribute("id"));
            //Во внутреннем скролле стандартный автоскролл selenium-а не отрабатывает, и элемент не видим
            if (!row.isDisplayed())
            {
                tester.scrollWindowToElement(row);
                assertTrue("Работа не отображается", row.isDisplayed());
            }
        }

        List<String> expected = Lists.newArrayList(workIds);
        if (isShowMore)
        {
            expected.add(Constants.SHOW_MORE_WORKS);
        }
        if (match)
        {
            assertEquals("Полученный список работ не совпал с ожидаемым", order ? expected : Sets.newHashSet(expected),
                    order ? actual : Sets.newHashSet(actual));
        }
        else
        {
            assertTrue("Полученный список работ не совпал с ожидаемым",
                    order ? CollectionUtils.orderContains(expected, actual) : actual.containsAll(expected));
        }
    }

    /**
     * Проверить, выбрана ли работа в контенте Диаграмма Ганта
     * @param gantt Диаграмма Ганта, в которой проверяем
     * @param workObj проверяемая работа, может быть null, тогда будет проверятся псевдоресурс [Без ресурса]
     * на диаграмме должны присутствовать другие работы, чтобы можно было проверять псевдоресурс
     * @param isSelected true - работа выбрана, false - работа выбрана
     */
    public static void assertWorkSelected(GanttContent gantt, Bo workObj, Boolean isSelected)
    {
        String workId = workObj == null ? Constants.WITHOUT_RESOURCE_UUID : workObj.getUuid();
        if (isSelected)
        {
            assertTrue("Работа не выбрана", tester.waitAppear(X_GANTT + GANTT_TASK_SELECTED, gantt.getXpathId(),
                    workId));
        }
        else
        {
            assertTrue("Работа выбрана", tester.waitDisappear(X_GANTT + GANTT_TASK_SELECTED, gantt.getXpathId(),
                    workId));
        }
    }

    /**
     * Проверить контент на состояние отображаемых работ в указанном ресурсе
     * (проверка по названию работ)
     *
     * @param gantt модель контента
     * @param resourceId id проверяемого ресурса
     * @param workNames ожидаемый набор работ
     */
    public static void assertWorksTitle(GanttContent gantt, String resourceId, String... workNames)
    {
        assertTrue("В контенте нет ни одной работы", tester.waitAppear(X_WORK_NAMES_ROWS, gantt.getXpathId(),
                resourceId));
        List<WebElement> gridElements = tester.findDisplayedElements(X_WORK_NAMES_ROWS, gantt.getXpathId(), resourceId);
        List<String> actual = new ArrayList<>();
        for (WebElement row : gridElements)
        {
            actual.add(row.getText().trim());
        }
        List<String> expected = Lists.newArrayList(workNames);
        assertEquals("Полученный список работ не совпал с ожидаемым.", expected, actual);
    }

    /**
     * Проверить, что в контент на холсте присутствует указанная работа с указаным именеем
     *
     * @param gantt модель контента
     * @param workId id работы
     * @param expectedTitle Ожидаемое название
     */
    public static void assertWorkTitleOnCanvas(GanttContent gantt, String workId, String expectedTitle)
    {
        String xpath = String.format(GUIContent.X_CONTENT_OUTER_PATTERN + "//*[@id='task-%s']", gantt.getXpathId(),
                workId);
        assertEquals("Название работы на холсте не совпало с ожидаемым.", expectedTitle, tester.getText(xpath));
    }

    /**
     * Кликнуть по пункту "Разорвать связь"
     * @param uuid уникальный идентификатор работы
     */
    public static void breakLink(String uuid)
    {
        tester.click(BREAK_LINK, uuid);
    }

    /**
     * Кликнуть по кнопке "Добавить", открыть форму добавления работы
     * @param gantt модель контента
     */
    public static void clickAddWork(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_ADD_WORK);
    }

    /**
     * Кликнуть по кнопке "Короткий переход назад"
     * @param gantt модель контента
     */
    public static void clickBackward(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_BACKWARD);
    }

    /**
     * Нажать пункт "Разорвать связь" в контекстном меню
     * @param workObj работа в Диаграмме Ганта
     */
    public static void clickContextMenuBreakLink(Bo workObj)
    {
        assertTrue("Контекстное меню не появлось", tester.waitAppear(GUIGanttContent.CONTEXT_MENU));
        tester.click(BREAK_LINK, workObj.getUuid());
    }

    /**
     * Нажать пункт "Удалить" в контекстном меню
     * @param workObj работа в Диаграмме Ганта
     */
    public static void clickContextMenuDelete(Bo workObj)
    {
        assertTrue("Контекстное меню не появлось", tester.waitAppear(GUIGanttContent.CONTEXT_MENU));
        tester.click(DELETE, workObj.getUuid());
    }

    /**
     * Нажать пункт "Редактировать" в контекстном меню
     * @param workObj работа в Диаграмме Ганта
     */
    public static void clickContextMenuEdit(Bo workObj)
    {
        assertTrue("Контекстное меню не появлось", tester.waitAppear(GUIGanttContent.CONTEXT_MENU));
        tester.click(EDIT, workObj.getUuid());
    }

    /**
     * Перейти на карточку объекта через контекстное меню
     * @param workObj работа в Диаграмме Ганта
     */
    public static void clickContextMenuGoToCard(Bo workObj)
    {
        assertTrue("Контекстное меню не появлось", tester.waitAppear(GUIGanttContent.CONTEXT_MENU));
        tester.click(CARD_LINK, workObj.getUuid());
    }

    /**
     * Кликнуть на пиктограмму редактирования (карандаш) напротив работы
     * @param work модель работы
     */
    public static void clickEditWork(Bo work)
    {
        tester.click(X_EDIT_BUTTON, work.getUuid());
        GUIForm.assertDialogAppear("Форма редактирования работы не появилась.");
    }

    /**
     * Кликнуть по кнопке "Длинный переход назад"
     * @param gantt модель контента
     */
    public static void clickFastBackward(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_FASTBACKWARD);
    }

    /**
     * Кликнуть по кнопке "Длинный переход вперед"
     * @param gantt модель контента
     */
    public static void clickFastForward(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_FASTFORWARD);
    }

    /**
     * Кликнуть по кнопке "Короткий переход вперед"
     * @param gantt модель контента
     */
    public static void clickForward(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_FORWARD);
    }

    /**
     * Кликнуть на левый треугольник быстрого перемещения к старту работы, расположенному на холсте ДГ
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     */
    public static void clickLeftQuickMoveTriangleOnCanvasPresence(GanttContent gantt, Bo triangleSource)
    {
        tester.click(X_GANTT_LEFT_SLIDE_ON_CANVAS_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid());
    }

    /**
     * Кликнуть на левый треугольник быстрого перемещения к старту работы
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     */
    public static void clickLeftQuickMoveTrianglePresence(GanttContent gantt, Bo triangleSource)
    {
        tester.click(X_GANTT_LEFT_SLIDE_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid(), triangleSource
                .getUuid());
    }

    /**
     * Кликнуть по кнопке "Обновить"
     * @param gantt модель контента
     */
    public static void clickRefresh(GanttContent gantt)
    {
        clickButton(gantt, X_BUTTON_REFRESH);
    }

    /**
     * Кликнуть на связь между работами
     * @param gantt контент диаграммы
     * @param from модель работы, из которой исходит связь
     * @param to модель работы, в которую входит связь
     */
    public static void clickRelation(GanttContent gantt, Bo from, Bo to)
    {
        tester.clickOnInvisibleElement(X_SVG_RELATION, gantt.getXpathId(), from.getUuid(), to.getUuid());
    }

    /**
     * Кликнуть на иконку удаления связи между работами
     * @param gantt - контент диаграммы
     */
    public static void clickRemoveWidget(GanttContent gantt)
    {
        tester.click(X_GANTT_REMOVE_WIDGET, gantt.getXpathId());
    }

    /**
     * Кликнуть на правый треугольник быстрого перемещения к старту работы, расположенному на холсте ДГ
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     */
    public static void clickRightQuickMoveTriangleOnCanvas(GanttContent gantt, Bo triangleSource)
    {
        tester.click(X_GANTT_RIGHT_SLIDE_ON_CANVAS_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid());
    }

    /**
     * Кликнуть на правый треугольник быстрого перемещения к финишу работы
     * @param gantt - контент диаграммы
     * @param triangleSource - работа, на которой расположен треугольник
     */
    public static void clickRightQuickMoveTrianglePresence(GanttContent gantt, Bo triangleSource)
    {
        tester.click(X_GANTT_RIGHT_SLIDE_TRIANGLE, gantt.getXpathId(), triangleSource.getUuid(), triangleSource
                .getUuid());
    }

    /**
     * Кликнуть по кольцу создания связи и переместить курсор к указанной работе
     * @param gantt контент диаграммы
     * @param from модель работы, от которой созадается связь
     * @param leftSideFrom true - связь создается слева от <code>from</code>, false - справа
     * @param to модель работы, к которой созадается связь
     * @param leftSideTo true - связь создается слева от <code>to</code>, to - справа
     */
    public static void clickRingHoldAndMove(GanttContent gantt, Bo from, boolean leftSideFrom, Bo to,
            boolean leftSideTo)
    {
        //Ожидание добавлено от случайных срабатываний
        WaitTool.waitMills(200);
        WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, REPETITION_PERIOD, IGNORE_STALE_ELEMENT_EXCEPTION,
                input ->
                {
                    WebElement ring = tester.findDisplayed(leftSideFrom ? X_GANTT_LEFT_RING : X_GANTT_RIGHT_RING, gantt
                                    .getXpathId(),
                            from.getUuid());
                    String elemXpath = String.format(X_GANTT_TASK, gantt.getXpathId(), to.getUuid());
                    WebElement reciever = tester.findDisplayed(elemXpath);

                    int xOffset = tester.getElementTopLeftCornerX(elemXpath);
                    int yOffset = tester.getElementTopLeftCornerY(elemXpath);

                    //Определение смещения по оси X, если связь создаётся справа от кольца
                    int xOffsetLeftSideFrom = (reciever.getSize().getWidth() / 2) + 5;
                    new Actions(tester.getWebDriver()).moveToElement(ring)
                            .clickAndHold()
                            .moveToElement(reciever, leftSideTo ? xOffset - 5 : xOffsetLeftSideFrom, yOffset + 10)
                            .build()
                            .perform();
                    return true;
                });

        //Ожидание добавлено от случайных срабатываний
        WaitTool.waitMills(200);
    }

    /**
     * Кликнуть по указанной строке
     * @param gantt модель контента
     * @param bo отображаемый в строке объект
     */
    public static void clickRow(GanttContent gantt, Bo bo)
    {
        clickRow(gantt, bo.getUuid(), bo.getTitle());
    }

    /**
     * Кликнуть по указанной строке
     * @param gantt модель контента
     * @param rowId идентификатор строки
     * @param title название объекта в строке
     */
    public static void clickRow(GanttContent gantt, String rowId, String title)
    {
        scrollToRow(gantt, rowId);
        tester.click(X_NAME_ROW_TITLE, gantt.getXpathId(), rowId, title);
    }

    /**
     * Кликнуть на кнопку увеличения масштаба
     * @param gantt модель контента
     */
    public static void clickZoomIn(GanttContent gantt)
    {
        tester.click(X_BUTTON_ZOOM_IN, gantt.getXpathId());
    }

    /**
     * Кликнуть на кнопку уменьшения масштаба
     * @param gantt модель контента
     */
    public static void clickZoomOut(GanttContent gantt)
    {
        tester.click(X_BUTTON_ZOOM_OUT, gantt.getXpathId());
    }

    /**
     * Начать передвигать работу
     * @param gantt модель контента
     * @param work - работа
     * @param xOffset - смещение по x координате
     * @param yOffset - смещение по y координате
     */
    public static void dragWork(GanttContent gantt, Bo work, int xOffset, int yOffset)
    {
        Actions actions = new Actions(tester.getWebDriver());
        String sourceXpath = String.format(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        int yOffsetWork = tester.getElementTopLeftCornerY(sourceXpath) + 10;
        WebElement source = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        //Невидимая часть работы в хроме недоступна для клика
        int xOffSetWork = 0;
        //@formatter:off
        actions.moveToElement(source, 10 + xOffSetWork, yOffsetWork)
               .clickAndHold()
               .moveByOffset(xOffset, yOffset)
               .build()
               .perform();
        //@formatter:on
    }

    /**
     * Начать передвигать работу по вертикали на указанный ресурс
     * @param gantt модель контента
     * @param work - работа
     * @param resourceId - id ресурса, на который перемещаем работу
     */
    public static void dragWorkVertical(GanttContent gantt, Bo work, String resourceId)
    {
        Actions actions = new Actions(tester.getWebDriver());
        WebElement source = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        WebElement destination = tester.findDisplayed(X_NAME_ROW, gantt.getXpathId(), resourceId);
        int yOffset = destination.getLocation().getY() - source.getLocation().getY();
        //@formatter:off
        actions.moveToElement(destination) // для скролла до нужного элемента
               .moveToElement(source)
               .clickAndHold()
               .moveByOffset(0, yOffset)
               .build()
               .perform();
        //@formatter:on
    }

    /**
     * Развернуть ресурс с указанным идентификатором
     * @param gantt модель контента
     * @param rowId
     */
    public static void expandResource(GanttContent gantt, String rowId)
    {
        assertTrue("Ресурс отсутствует, либо уже развернут: " + rowId,
                tester.waitAppear(X_ROW_EXPAND_DOWN_BUTTON, gantt.getXpathId(), rowId));
        tester.click(X_ROW_EXPAND_DOWN_BUTTON, gantt.getXpathId(), rowId);
    }

    /**
     * Заполняет поля на форме на основе модели
     */
    public static void fillFields(GanttContent gantt)
    {
        GUIFillContent.fillTitleAndCodeOnCardForm(gantt);
        GUIFillContent.fillPosition(gantt);

        GUISelect.select(X_SCALE, gantt.getScale().name());
        // TODO NSDPRD-2711
        //GUISelect.select(X_SERVICETIME, gantt.getServiceTime() != null ? gantt.getServiceTime().getCode() : null);

        WorkParams work = gantt.getWorkParams();

        if (tester.isPresence(X_WORK_LINKED_CURRENT_OBJECT))
        {
            tester.setCheckbox(X_WORK_LINKED_CURRENT_OBJECT, work.isLinkedWithCurrentObject());
            //Добавлена проверка наличия элементов, чтобы при перестроении формы успевали загрузиться/исчезнуть
            //все поля перед следующей проверкой tester.isPresence, т.к. tester.isPresence ничего не ждет
            String msg = "Связанный атрибут " + (work.isLinkedWithCurrentObject() ? "отсутствует" : "присутствует");
            GUITester.assertExists(X_WORK_LINKED_ATTR_CHAIN, work.isLinkedWithCurrentObject(), msg);
        }

        if (tester.isPresence(X_WORK_LINKED_ATTR_CHAIN))
        {
            RelationTree workTree = new RelationTree(X_WORK_LINKED_ATTR_CHAIN);
            workTree.setElement(work.getTargetClassAttrFqn());
        }

        if (tester.isPresence(X_WORK_METACLASS))
        {
            String workMetaClass = work.getWorkMetaClass() != null ? work.getWorkMetaClass().getFqn() : null;
            GUISelect.select(X_WORK_METACLASS, workMetaClass);
        }

        if (tester.isPresence(X_WORK_METACLASSES))
        {
            GUIMultiSelect.select(X_WORK_METACLASSES, Iterables.toArray(work.getWorkMetaClasses(), String.class));
        }

        GUISelect.select(X_WORK_RESOURCE, work.getResource() != null ? work.getResource().getCode() : null);
        GUISelect.select(X_WORK_START_DATE, work.getStartDate() != null ? work.getStartDate().getCode() : null);
        GUISelect.select(X_WORK_END_DATE, work.getEndDate() != null ? work.getEndDate().getCode() : null);
        GUISelect.select(X_WORK_ATTRIBUTE_GROUP,
                work.getAttributeGroup() != null ? work.getAttributeGroup().getCode() : null);

        ResourceParams resource = gantt.getResourceParams();
        if (tester.isPresence(X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT))
        {
            tester.setCheckbox(X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT, resource.isLinkedWithCurrentObject());
            String msg = "Связанный атрибут " + (resource.isLinkedWithCurrentObject() ? "отсутствует" : "присутствует");
            GUITester.assertExists(X_RESOURCE_LINKED_ATTR_CHAIN, resource.isLinkedWithCurrentObject(), msg);
        }
        if (tester.isPresence(X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT))
        {
            tester.setCheckbox(X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT, resource.isResourceIsCurrentObject());
            String msg = "Ресурс метакласса " + (resource.isResourceIsCurrentObject() ? "отсутствует" : "присутствует");
            GUITester.assertExists(X_RESOURCE_METACLASSES, !resource.isResourceIsCurrentObject(), msg);
        }
        if (tester.isPresence(X_RESOURCE_METACLASS))
        {
            String resourceMetaClass = resource.getResourceMetaClass() != null
                    ? resource.getResourceMetaClass().getFqn()
                    : null;
            GUISelect.select(X_RESOURCE_METACLASS, resourceMetaClass);
        }
        if (tester.isPresence(X_RESOURCE_METACLASSES))
        {
            GUIMultiSelect.select(X_RESOURCE_METACLASSES,
                    Iterables.toArray(resource.getResourceMetaClassFqns(), String.class));
        }

        if (tester.isPresence(X_RESOURCE_LINKED_ATTR_CHAIN))
        {
            RelationTree resourceTree = new RelationTree(X_RESOURCE_LINKED_ATTR_CHAIN);
            resourceTree.setElement(resource.getTargetClassAttrFqn());
        }

        if (tester.isPresence(X_RESOURCE_ATTRIBUTE_GROUP_VALUE_INPUT))
        {
            String attributeGroup = resource.getAttributeGroup() != null ? resource.getAttributeGroup().getCode()
                    : null;
            GUISelect.select(X_RESOURCE_ATTRIBUTE_GROUP_VALUE_INPUT, attributeGroup);
        }
    }

    /**
     * Получить координату по X левой границы ресурса на диаграмме
     * @param gantt модель контента
     * @param resource модель ресурса, может быть null, тогда будет считаться для [Без ресурса]
     * @param num порядковый номер полосы ресурса на диаграмме (начиная с 1)
     * @return координата по X левой границы ресурса на диаграмме
     */
    public static int getResourceLeftBorder(GanttContent gantt, Bo resource, int num)
    {
        String resourceId = resource == null ? Constants.WITHOUT_RESOURCE_UUID : resource.getUuid();
        List<WebElement> resourceElements = tester.findDisplayedElements(X_GANTT_RESOURCE, gantt.getXpathId(),
                resourceId);
        WebElement resourceElement = resourceElements.get(num - 1);
        tester.scrollWindowToElement(resourceElement);
        return resourceElement.getLocation().getX();
    }

    /**
     * Получить координату по X правой границы ресурса на диаграмме
     * @param gantt модель контента
     * @param resource модель ресурса, может быть null, тогда будет считаться для [Без ресурса]
     * @param num порядковый номер полосы ресурса на диаграмме (начиная с 1)
     * @return координата по X правой границы ресурса на диаграмме
     */
    public static int getResourceRightBorder(GanttContent gantt, Bo resource, int num)
    {
        String resourceId = resource == null ? Constants.WITHOUT_RESOURCE_UUID : resource.getUuid();
        List<WebElement> resourceElements = tester.findDisplayedElements(X_GANTT_RESOURCE, gantt.getXpathId(),
                resourceId);
        WebElement resourceElement = resourceElements.get(num - 1);
        tester.scrollWindowToElement(resourceElement);
        return resourceElement.getLocation().getX() + resourceElement.getSize().getWidth();
    }

    /**
     * Получить координату по X левой границы работы на диаграмме
     * @param gantt модель контента
     * @param task модель ресурса
     * @return координата по X левой границы работы на диаграмме
     */
    public static int getTaskLeftBorder(GanttContent gantt, Bo task)
    {
        WebElement taskElement = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), task.getUuid());
        return taskElement.getLocation().getX();
    }

    /**
     * Получить координату по X правой границы работы на диаграмме
     * @param gantt модель контента
     * @param task модель ресурса
     * @return координата по X правой границы работы на диаграмме
     */
    public static int getTaskRightBorder(GanttContent gantt, Bo task)
    {
        WebElement taskElement = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), task.getUuid());
        return taskElement.getLocation().getX() + taskElement.getSize().getWidth();
    }

    /**
     * Получить набор координат по X правых границ работ на диаграмме
     * @param gantt модель контента
     * @param tasks модели ресурсов
     * @return набор координат по X правых границ работ на диаграмме
     */
    public static Set<Integer> getTasksRightBorders(GanttContent gantt, Bo... tasks)
    {
        Set<Integer> borders = new HashSet<>();
        for (Bo task : tasks)
        {
            borders.add(getTaskRightBorder(gantt, task));
        }
        return borders;
    }

    /**
     * Навести курсор мыши на левое кольцо работы
     * @param gantt контент диаграммы
     * @param work работа
     */
    public static void moveToLeftRing(GanttContent gantt, Bo work)
    {
        Actions actions = new Actions(tester.getWebDriver());
        WebElement source = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        int xOffset = tester.getElementTopLeftCornerX(X_GANTT_TASK, gantt.getXpathId(), work.getUuid()) - 3;
        int yOffset = 0;
        actions.moveToElement(source, xOffset, yOffset).build().perform();
    }

    /**
     * Навести курсор мыши на правое кольцо работы
     * @param gantt контент диаграммы
     * @param work работа
     */
    public static void moveToRightRing(GanttContent gantt, Bo work)
    {
        Actions actions = new Actions(tester.getWebDriver());
        WebElement source = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        int xOffset = (source.getSize().getWidth() / 2) + 3;
        int yOffset = 0;
        actions.moveToElement(source, xOffset, yOffset).build().perform();
    }

    /**
     * Открыть форму Добавления контента Диаграмма Ганта
     */
    public static void openAddForm(GanttContent gantt)
    {
        GUIContent.openAddForm(gantt);
        GUISelect.selectByXpath(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE,
                String.format(GUIContent.X_CONTENT_TYPE_VALUE, gantt.getType()));
    }

    /**
     * Открыть форму редактирования работы через иконку редактирования на Диаграмме Ганта
     * @param gantt диаграмма ганта
     * @param work работа для редактирования
     */
    public static void openEditWorkForm(GanttContent gantt, Bo work)
    {
        scrollToRow(gantt, work.getUuid());
        tester.click(X_ROW_BUTTON, gantt.getXpathId(), work.getUuid());
        GUIForm.assertDialogAppear("Форма редактирования работы не открылась");
    }

    /**
     * Отпустить левую кнопку мыши
     */
    public static void releaseLeftButton()
    {
        Actions actions = new Actions(tester.getWebDriver());
        actions.release().build().perform();
    }

    /**
     * Удалить связь между работами
     * @param gantt - контент диаграммы
     * @param predecessor - предок
     * @param successor - потомок
     */
    public static void removePredecessorWork(GanttContent gantt, Bo predecessor, Bo successor)
    {
        clickRelation(gantt, predecessor, successor);
        assertRemoveWidgetAppear(gantt);
        clickRemoveWidget(gantt);
        GUIForm.confirmByYes();
    }

    /**
     * Конвертер rgb представления цвета в hex
     * @param colorStr цвет, закодированный rgb
     * @return hex цвет, закодированный в hex
     */
    public static String rgb2hex(String colorStr)
    {
        String[] subStr = colorStr.substring(4).replace(")", "").split(",");
        int r = Integer.parseInt(subStr[0].trim());
        int g = Integer.parseInt(subStr[1].trim());
        int b = Integer.parseInt(subStr[2].trim());
        return String.format("#%02x%02x%02x", r, g, b);
    }

    /**
     * Конвертер rgba представления цвета в hex
     * @param colorStr цвет, закодированный rgba
     * @return hex цвет, закодированный в hex
     */
    public static String rgba2hex(String colorStr)
    {
        String s1 = colorStr.substring(5);
        StringTokenizer st = new StringTokenizer(s1);
        int r = Integer.parseInt(st.nextToken(",").trim());
        int g = Integer.parseInt(st.nextToken(",").trim());
        int b = Integer.parseInt(st.nextToken(",").trim());
        Color c = new Color(r, g, b);
        return "#" + Integer.toHexString(c.getRGB()).substring(2);
    }

    /**
     * Правый клик по задаче
     * @param workObj работа в диаграмме ганта
     */
    public static void rightClickOnTask(GanttContent gantt, Bo workObj)
    {
        tester.contextClick(GUIGanttContent.GANTT_TASK, gantt.getXpathId(), workObj.getUuid());
    }

    /**
     * Выполняет скроллинг до строки
     * @param gantt модель контента
     * @param rowId идентификатор строки
     */
    public static void scrollToRow(GanttContent gantt, String rowId)
    {
        assertTrue("В контенте нет ни одного загруженного ресурса или работы",
                tester.waitAppear(X_RESOURCE_NAMES_ROWS, gantt.getXpathId()));
        tester.scrollWindowToElement(rowId);
    }

    /**
     * Выбрать работу в диаграмме гантта
     * @param gantt контект диаграмма гантта
     * @param workObj работа в диаграмме гантта, может быть Null, тогда будет выбран псевдоресурс [Без ресурса]
     * на диаграмме должны присутствовать другие работы, чтобы можно было выбирать псевдоресурс
     */
    public static void selectWork(GanttContent gantt, Bo workObj)
    {
        WebElement source;
        if (workObj == null)
        {
            source = tester.findDisplayed(GUIGanttContent.GANTT_TASK_WITHOUT_RESOURCE);
        }
        else
        {
            source = tester.findDisplayed(GUIGanttContent.GANTT_TASK_PRESENTATION, workObj.getUuid());
        }
        int xOffSet = 0;
        int yOffset = 0;

        Actions actions = new Actions(tester.getWebDriver());
        actions.moveToElement(source, xOffSet - 10, yOffset).click().perform();
    }

    /**
     * Выбрать дату в календаре
     * @param gantt - контент диаграммы
     * @param date дата в формате dd.MM.yyyy
     */
    public static void setDate(GanttContent gantt, String date)
    {
        GUIDatePicker.clickDatePickerImg(gantt.getXpathId());
        GUIDatePicker.setDate(date);
    }

    /**
     * Установить значение режима фильтрации
     * @param gantt - контент диаграммы ганта
     */
    public static void setFilterMode(GanttContent gantt)
    {
        GUISelect.select(X_FILTER_MODE_SELECT, gantt.getFilterMode().name(), gantt.getXpathId());
    }

    /**
     * Установить чекбокс "Ресурсом является текущий объект" на форме добавления/редактирования контента
     */
    public static void setResourceCurrentObject(boolean resourceCurrentObject)
    {
        tester.setCheckbox(X_RESOURCE_IS_CURRENT_OBJECT_VALUE_INPUT, resourceCurrentObject);
    }

    /**
     * Установить чекбокс "Ресурсы связаны с текущим объектом" на форме добавления/редактирования контента
     */
    public static void setResourceLinkedCurrentObject(boolean linked)
    {
        tester.setCheckbox(X_RESOURCE_LINKED_CURRENT_OBJECT_VALUE_INPUT, linked);
    }

    public static void setScale(GanttContent gantt)
    {
        GUISelect.select(X_SCALE_SELECT, gantt.getScale().name(), gantt.getXpathId());
    }

    public static void setStartDate(GanttContent gantt, String startDate)
    {
        //TODO задержка добавлена в связи с тем, что в тестах Gantt3Test.test3(4) после перехода на вкладку,
        //через какое-то время перезагружается поле ввода даты. Поэтому введя изначально быстро дату она
        // перезаписывается.
        WaitTool.waitMills(1000);
        tester.sendKeys(X_START_DATE_SELECT, startDate, gantt.getXpathId());
        tester.actives().pressingEnterKey();
        if (!startDate.equals(GUITester.getValue(X_START_DATE_SELECT, gantt.getXpathId())))
        {
            tester.sendKeys(X_START_DATE_SELECT, startDate, gantt.getXpathId());
            tester.actives().pressingEnterKey();
        }
    }

    /**
     * Установить чекбокс "Работы связаны с текущим объектом" на форме добавления/редактирования контента
     */
    public static void setWorkLinkedCurrentObject(boolean linked)
    {
        tester.setCheckbox(X_WORK_LINKED_CURRENT_OBJECT, linked);
    }

    /**
     * Кликнуть по ссылке [Загрузить еще ресурсы] в указанном контенте
     * @param gantt модель контента
     */
    public static void showMoreResources(GanttContent gantt)
    {
        assertTrue("Отсутствует ссылка [Загрузить еще ресурсы]",
                tester.waitAppear(X_MORE_RESOURCES_NAME_ROW, gantt.getXpathId(), Constants.SHOW_MORE_RESOURCES));
        tester.click(X_MORE_RESOURCES_NAME_ROW, gantt.getXpathId(), Constants.SHOW_MORE_RESOURCES);
    }

    /**
     * Кликнуть по ссылке [Загрузить еще работы] в указанном ресурсе
     * @param gantt модель контента
     * @param resource модель ресурса
     */
    public static void showMoreWorks(GanttContent gantt, Bo resource)
    {
        String xpath = String.format(X_MORE_WORKS_NAME_ROW, gantt.getXpathId(), resource.getUuid(),
                Constants.SHOW_MORE_WORKS);
        tester.scrollWindowToElement(tester.find(xpath));
        tester.click(xpath);
    }

    /**
     * "Растянуть" левую сторону работы
     * @param gantt модель контента
     * @param work - работа
     * @param xOffset - на сколько пикселей растянуть
     */
    public static void startLeftStretchingWork(GanttContent gantt, Bo work, int xOffset)
    {
        Actions actions = new Actions(tester.getWebDriver());
        String workXpath = String.format(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        WebElement source = tester.findDisplayed(workXpath);
        int yOffset = tester.getElementTopLeftCornerY(workXpath);
        int moveToElemOffsetX = tester.getElementTopLeftCornerX(workXpath) + 1;
        int moveToElemOffsetY = tester.getElementTopLeftCornerY(workXpath) + 10;
        //@formatter:off
        actions.moveToElement(source, moveToElemOffsetX, moveToElemOffsetY)
               .clickAndHold()
               .moveByOffset(-xOffset, yOffset)
               .build()
               .perform();
        //@formatter:on
    }

    /**
     * "Растянуть" правую сторону работы
     * @param gantt модель контента
     * @param work - работа
     * @param xOffset - на сколько пикселей растянуть
     */
    public static void startRightStretchingWork(GanttContent gantt, Bo work, int xOffset)
    {
        Actions actions = new Actions(tester.getWebDriver());
        String workXpath = String.format(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
        WebElement source = tester.findDisplayed(workXpath);
        int yOffset = tester.getElementTopLeftCornerY(workXpath);
        int moveToElemOffsetX = (source.getSize().getWidth() / 2) - 1;
        int moveToElemOffsetY = tester.getElementTopLeftCornerY(workXpath) + 10;
        //@formatter:off
        actions.moveToElement(source, moveToElemOffsetX, moveToElemOffsetY)
               .clickAndHold()
               .moveByOffset(xOffset, yOffset)
               .build()
               .perform();
        //@formatter:on
    }

    /**
     * Проверить, что ширина работы не соответствует стандартному размеру для неопределенных работ
     * @param gantt - контент диаграммы
     * @param work - работа
     */
    private static void assertWidthOfWorkIsNotUncertain(final GanttContent gantt, final Bo work)
    {
        assertTrue("Ширина работы соответствует стандартному размеру для неопределенных работ",
                assertWidthOfWorkWithUncertain(gantt, work, false));
    }

    /**
     * Сравнить ширину работы с со стандартным размером для неопределенных работ
     *
     * @param gantt - контент диаграммы
     * @param work - работа
     * @param isUncertain true - ширина должна совпадать, false - отличаться
     */
    private static boolean assertWidthOfWorkWithUncertain(final GanttContent gantt, final Bo work, boolean isUncertain)
    {
        final int WIDTH_FOR_UNCERTAIN = 75;
        Boolean result = WaitTool.waitSomething(tester.getWebDriver(), WAIT_TIME, 250L, WebTester.IGNORE_EXCEPTIONS,
                new Function<WebDriver, Boolean>()
                {
                    @Override
                    public Boolean apply(WebDriver input)
                    {
                        WebElement task = tester.findDisplayed(X_GANTT_TASK, gantt.getXpathId(), work.getUuid());
                        return isUncertain ? task.getSize().getWidth() == WIDTH_FOR_UNCERTAIN
                                : task.getSize().getWidth() != WIDTH_FOR_UNCERTAIN;
                    }
                });
        return result != null && result;
    }

    /**
     * Проверить количество входящих/исходящих связей у работы.
     * @param gantt модель контента
     * @param work модель проверяемой работы
     * @param expected ожидаемое количество
     * @param isInput true - проверяем входящие связи, false - исходящие.
     */
    private static void assertWorkRelationCount(GanttContent gantt, Bo work, int expected, boolean isInput)
    {
        String msg = String.format(
                "Ожидаемое количество %s связей, у работы '%s', не совпало с полученым. (Ожидалось: %d)",
                isInput ? "входящих" : "исходящих", work.getTitle(), expected);
        String xPath = String.format(isInput ? X_SVG_RELATION_END : X_SVG_RELATION_START, gantt.getXpathId(),
                work.getUuid());
        Preconditions.checkArgument(expected >= 0,
                "Ожидаемое значение должно быть больше 0. Ожидаемое значение:" + expected);
        if (expected == 0)
        {
            assertTrue(msg, tester.waitDisappear(xPath));
        }
        else
        {
            try
            {
                List<WebElement> els = tester.findDisplayedElements(xPath);
                assertEquals(msg, expected, els.size());
            }
            catch (NoSuchElementException e)
            {
                fail(msg);
            }
        }
    }

    private static void clickButton(GanttContent gantt, String xButton)
    {
        assertTrue("В контенте нет ни одного загруженного ресурса или работы",
                tester.waitAppear(X_RESOURCE_NAMES_ROWS, gantt.getXpathId()));
        tester.click(xButton, gantt.getXpathId());
    }

    /**
     * Метод используется в проверке контекстного меню, в зависимости от флага возвращает то, или иное сообщение
     * @param flag флаг
     * @return сообщение, которое показывается при неудачной проверке
     */
    private static String getmsgForContext(boolean flag)
    {
        String present = "Пункт контекстного меню присутствует";
        String absent = "Пункт контекстного меню отсутствует";
        return flag ? absent : present;
    }
}
