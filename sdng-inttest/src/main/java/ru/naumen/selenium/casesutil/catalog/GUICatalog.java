package ru.naumen.selenium.casesutil.catalog;

import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы со справочниками через интерфейс
 * <AUTHOR>
 * @since 30.03.2013
 *
 */
public class GUICatalog extends CoreTester
{
    private static final String CATALOGS_USER = "//div[@id='gwt-debug-NavigationTree.catalogs:user-content']";

    private static final String ADD_CATALOG = CATALOGS_USER + String.format(GUIXpath.Span.ANY_ICON, "addMC");

    private static final String VISIBILITY_ADD = CATALOGS_USER + "//a[@id='gwt-debug-title']";

    private static final String IMG_FOLD_ON =
            "//div[@id='gwt-debug-withFolders-value']/span[@_code='" + GUIXpath.Constant.YES
            + "']";
    private static final String IMG_FOLD_OFF =
            "//div[@id='gwt-debug-withFolders-value']/span[@_code='" + GUIXpath.Constant.NO
            + "']";
    private static final String IMG_ON =
            "//div[@id='gwt-debug-flat-value']/span[@_code='" + GUIXpath.Constant.YES + "']";

    private static final String IMG_OFF =
            "//div[@id='gwt-debug-flat-value']/span[@_code='" + GUIXpath.Constant.NO + "']";

    /**Карточка справочника, поле кода*/
    static final String X_TEXT_FIELD_INFO_CODE_VALUE = "//div[@id='gwt-debug-code-value']";

    /**Карточка справочника, поле имени*/
    public static final String X_TEXT_FIELD_INFO_TITLE_VALUE = "//div[@id='gwt-debug-title-value']";

    public static final String NAV_TREE_CATALOGS_ITEM = "//div[@id='gwt-debug-catalogsNavigationTree"
                                                        + ".catalogs:']//div[@id='gwt-debug-NTreeItemContent"
                                                        + ".catalog:%s']//a";

    public static final String NAV_TREE_CATALOGS_IMG = "//*[@id='gwt-debug-catalogsNavigationTree"
                                                       + ".catalogs:-image']/span";
    public static final String NAV_TREE_CATALOGS_IMG_PATTERN = "//*[@id='gwt-debug-NavigationTree"
                                                               + ".catalogs:%s-image']/span";
    public static final String NAV_TREE_CATALOGS_SYSTEM_IMG = String.format(NAV_TREE_CATALOGS_IMG_PATTERN, "system");
    public static final String NAV_TREE_CATALOGS_USER_IMG = String.format(NAV_TREE_CATALOGS_IMG_PATTERN, "user");

    /** Кнопка добавить элемент справочника в карточке справочника. */
    public static final String X_BUTTON_ADD_ELEMEMT = "//div[@id='gwt-debug-addElememt']";

    /**
     * Текстовое поле, содержащие код элемента справочника на карточке элемента
     * справочника
     */
    public static final String X_TEXT_FIELD_CODE_VALUE = "//div[@id='gwt-debug-code-value']";

    /**
     * Кнопка "Раскрыть действия над элементом справочника" на карточке элемента
     * справочника
     */
    public static final String X_BUTTON_EXPAND = "//div[@id='gwt-debug-sub']";

    /**
     * Кнопка экспорта таблицы соответствий.
     */
    public static final String X_BUTTON_EXPORT = "//div[@id='gwt-debug-exportAdvlist']";

    /**
     * Кнопка импорта таблицы соответствий.
     */
    public static final String X_BUTTON_IMPORT = "//div[@id='gwt-debug-import']";

    /** Описание элемента справочника на карточке элемента справочника. */
    static final String X_TEXT_FIELD_DESCRIPTION_VALUE = "//*[@id='gwt-debug-description-value']";

    /** Ссылка "к справочнику %название справочника%". */
    static final String X_LINK_BACK_TO_CATALOG =
            Div.MAIN_CONTENT_HEADER + "//a[@class='gwt-Anchor' and starts-with(@href,'#catalog:')]";

    /** Сссылка на ячейку таблицы списка элементов справочника по коду элемента. Два параметра: 1 - код элемента
     * справочник; 2 - код колонки */
    static final String CELL_BY_CODE = "//td[@__did='catalogItem@code']//*[text() = '%s']/ancestor::tr/td[@__did='%s']";
    /** Сссылка на ячейку таблицы списка элементов справочника по коду элемента. Два параметра: 1 - код элемента
     * справочник; 2 - код колонки */
    static final String CELL_ICON_BY_CODE =
            "//td[@__did='title']//*[text() = '%s']/ancestor::tr" + GUIXpath.Span.ANY_ICON;
    /** шаблон xPatha поиска пиктограммы для элементов справочника. Два параметра: 1 - код элемента справочник; 2 -
     * код колонки */
    static final String X_PICTOGRAM_PATTERN = CELL_BY_CODE + "//span";
    static final String X_RESTORE_PICTOGRAM_PATTERN = X_PICTOGRAM_PATTERN + "[@_code='restore']";
    /**Элементы на форме добавления справочника*/
    public static final String FLAT_CATALOG_CHECKBOX = "//input[@id='gwt-debug-flat-value-input']";

    public static final String CATALOG_WITH_FOLDERS_CHECKBOX = "//input[@id='gwt-debug-withFolders-value-input']";
    public static final String ADD_FOLDER_TO_CATALOG_BUTTON = "//div[@id='gwt-debug-addGroup']";
    public static final String CATALOG_FOLDER_BY_CODE = "//*[@id='%s.folder']";
    public static final String CATALOG_ELEMENT_BY_CODE = "//*[@id='%s.%s.color']";
    public static final String CATALOG_ELEMENT_CODE_BY_CODE = "//*[@id='%s.%s.color']/../../."
                                                              + "./td[@__did='catalogItem@code']/a";
    public static final String CATALOG_ELEMENT_COLOR_BY_CODE = "//*[@id='%s.%s.color']/../../."
                                                               + "./td[@__did='catalogItem@color']//div[@class"
                                                               + "='colorCircle']";
    public static final String COLOR_VALUE = "//div[@id='gwt-debug-color-value']//input";
    public static final String X_CATALOG_INFO = GUIXpath.divGwtDebugId("info");
    public static final String X_CATALOG_CONTENT = GUIXpath.divGwtDebugId("content");
    public static final String RS_ITEMS = X_CATALOG_CONTENT + "//a[contains(@id,'.rulesSettings.color')]";

    public static final String X_CODE_CAPTION = "//div[@id='gwt-debug-code-caption']";

    public static final String X_CODE_VALUE = "//div[@id='gwt-debug-code-value']";
    public static final String X_FLAT_CAPTION = "//div[@id='gwt-debug-flat-caption']";
    public static final String X_FLAT_VALUE = "//div[@id='gwt-debug-flat-value']/span";
    public static final String X_FOLDER_CAPTION = "//div[@id='gwt-debug-withFolders-caption']";
    public static final String X_FOLDER_VALUE = "//div[@id='gwt-debug-withFolders-value']/span";
    private static final String MESSAGE_CAPTION = "Полученное название атрибута не совпало с ожидаемым.";

    private static final String MESSAGE_VALUE = "Полученное значение атрибута не совпало с ожидаемым.";
    public static final String BTN_EDIT = "gwt-debug-edit";
    public static final String BTN_COPY = "gwt-debug-copy";
    public static final String BTN_ARCHIVE = "gwt-debug-remove";
    public static final String BTN_DELETE = "gwt-debug-delete";
    public static final String BTN_DEL = "gwt-debug-del";
    public static final String BTN_DOWNLOAD = "gwt-debug-import";
    public static final String BTN_EXPORT_ADVLIST = "gwt-debug-exportAdvlist";
    public static final String BTN_ADD = "gwt-debug-add";

    public static final String BTN_REFRESH = "gwt-debug-refresh";
    public static final String BTN_ADD_ELEMENT = "gwt-debug-addElememt";
    public static final String BTN_ADD_FOLDER = "gwt-debug-addGroup";
    public static final String X_BTN_SWITCH_VERSION = "//div[@id='gwt-debug-default']";
    public static final String X_BTN_UPLOAD_EXCLUSIONS = "//div[@id='gwt-debug-uploadBtn']";
    /**Путь до ссылки "к справочнику"*/
    public static final String BACK_LINK = String.format(GUINavigational.BACK_LINK_CONTAINS_PATTERN, "к справочнику");
    public static final String X_CATALOG_ITEM = X_CATALOG_CONTENT + "//tr[contains(@__did, \"%s\")]";

    public static final Map<String, String> BUTTON_NAMES = new HashMap<>();

    public static final String EDIT_PICTOGRAM = "edit";
    public static final String COPY_PICTOGRAM = "copy";
    public static final String REMOVE_RESTORE_PICTOGRAM = "removeRestore";
    public static final String DELETE_PICTOGRAM = "delete";

    static
    {
        BUTTON_NAMES.put(BTN_EDIT, "Редактировать");
        BUTTON_NAMES.put(BTN_REFRESH, "Переиндексировать");
        BUTTON_NAMES.put(BTN_ADD_ELEMENT, "Добавить элемент");
        BUTTON_NAMES.put(BTN_ADD_FOLDER, "Добавить папку");
        BUTTON_NAMES.put(BTN_DOWNLOAD, "Загрузить");
        BUTTON_NAMES.put(BTN_EXPORT_ADVLIST, "Выгрузить");
        BUTTON_NAMES.put(BTN_COPY, "Копировать");
        BUTTON_NAMES.put(BTN_ARCHIVE, "Поместить в архив");
        BUTTON_NAMES.put(BTN_DEL, "Удалить");
    }

    /**
     * Проверка отсутствия пользовательского справочника в системе
     * <li>Переходим в интерфейс администратора и проверяем, что данного справочника нет в дереве панели навигации.</li>
     * <li>Передаем в модель справочника информацию об отсутствии его в системе.</li>
     * @param model модель справочника
     */
    public static void assertAbsence(Catalog model)
    {
        //Переходим в интерфейс администратора
        GUINavigational.goToAdminUI();
        //Проверяем, что справочника с данным идентификатором не существует в системе
        Assert.assertFalse("Справочник с уникальным кодом обнаружен в системе. Id: " + model.getCode(),
                DSLCatalog.isPresent(model.getCode()));
        //Передаем в контейнер пользовательского справочника информацию о том, что данного справочника нет в системе
        model.setExists(false);
    }

    /**
     * Проверить карточку справочника
     * @param model модель справочника
     */
    public static void assertCard(Catalog model)
    {
        GUITester.assertTextContains(Div.HEADER_TITLE, model.getTitle());
        assertCardBlocks(model);
        assertCardAttributes(model);
    }

    /**
     * Проверить атрибуты справочника на карточке справочника
     * @param model модель справочника
     */
    public static void assertCardAttributes(Catalog model)
    {
        //Название
        Assert.assertEquals(MESSAGE_CAPTION, "Название", tester.getText(X_CATALOG_INFO + GUIXpath.Div.TITLE_CAPTION));
        Assert.assertEquals(MESSAGE_VALUE, model.getTitle(),
                tester.getText(X_CATALOG_INFO + GUIXpath.Any.TITLE_VALUE));
        //Код
        Assert.assertEquals(MESSAGE_CAPTION, "Код", tester.getText(X_CATALOG_INFO + X_CODE_CAPTION));
        Assert.assertEquals(MESSAGE_VALUE, model.getCode(), tester.getText(X_CATALOG_INFO + X_CODE_VALUE));
        //Описание
        Assert.assertEquals(MESSAGE_CAPTION, "Описание",
                tester.getText(X_CATALOG_INFO + GUIXpath.Div.DESCRIPTION_CAPTION));
        Assert.assertEquals(MESSAGE_VALUE, model.getDescription(),
                tester.getText(X_CATALOG_INFO + GUIXpath.Div.DESCRIPTION_VALUE));
        //Плоский
        Assert.assertEquals(MESSAGE_CAPTION, "Плоский", tester.getText(X_CATALOG_INFO + X_FLAT_CAPTION));
        boolean flat = tester.find(X_CATALOG_INFO + X_FLAT_VALUE).getAttribute("class").contains(GUIXpath.Constant.YES);
        Assert.assertEquals(MESSAGE_VALUE, model.getFlatCaption(), String.valueOf(flat));
        //С папками
        Assert.assertEquals(MESSAGE_CAPTION, "С папками", tester.getText(X_CATALOG_INFO + X_FOLDER_CAPTION));
        boolean folder = tester.find(X_CATALOG_INFO + X_FOLDER_VALUE)
                .getAttribute("class")
                .contains(GUIXpath.Constant.YES);
        Assert.assertEquals(MESSAGE_VALUE, model.getWithFolderCaption(), String.valueOf(folder));
    }

    /**
     * Проверить названия блоков на карточке справочника
     * @param model модель справочника
     */
    public static void assertCardBlocks(Catalog model)
    {
        String msg = "Полученное название блока на карточке справочника не совпал с ожидаемым.";
        String actual = tester.getText(X_CATALOG_INFO + "/div/span");
        Assert.assertEquals(msg, "Атрибуты справочника", actual);
        actual = tester.getText(X_CATALOG_CONTENT + "/div/span");
        Assert.assertEquals(msg, "Элементы справочника", actual);
    }

    /**
     * Проверка, что следующие кнопки отсутствуют на карточке справочника, в блоке "Атрибуты справочника"
     * @param buttonCodes коды кнопок (Константы данного класса, начинающиеся с префикса BTN_)
     */
    public static void assertCatalogButtonAbsence(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertTrue(String.format("На карточке справочника присутствует кнопка с кодом: '%s'", button),
                    tester.waitDisappear(X_CATALOG_INFO + GUIXpath.Div.ID_PATTERN, button));
        }
    }

    /**
     * Проверка что кнопка на карточке каталога активна/не активна
     * @param button код кнопки
     * @param enabled true - проверить что кнопка активна, false - неактивна
     */
    public static void assertCatalogButtonEnabled(String button, boolean enabled)
    {
        String value = tester.find(X_CATALOG_INFO + GUIXpath.Div.ID_PATTERN, button).getAttribute("disabled");
        if (enabled)
        {
            Assert.assertTrue(String.format("Кнопка с кодом: '%s' не активна", button),
                    null == value || !value.equals("true"));
        }
        else
        {
            Assert.assertTrue(String.format("Кнопка с кодом: '%s' активна", button), "true".equals(value));
        }
    }

    /**
     * Проверка, что следующие кнопки присутствуют на карточке справочника, в блоке "Атрибуты справочника"
     * @param buttonCodes коды кнопок (Константы данного класса, начинающиеся с префикса BTN_)
     */
    public static void assertCatalogButtonPresent(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertEquals("Полученное название кнопки не совпало с ожидаемым.", BUTTON_NAMES.get(button),
                    tester.getText(X_CATALOG_INFO + GUIXpath.Div.ID_PATTERN, button));
        }
    }

    /**
     * Проверить, что блок информации о справочнике отсутсвует
     */
    public static void assertCatalogInfoAbsence()
    {
        String message = "Блок информации о справочнике присутствует";
        Assert.assertTrue(message, tester.waitDisappear(X_CATALOG_INFO));
    }

    /**
     * Проверяет наличие или отсутствие иконки действия в строке элемента справочника.
     * @param item модель элемента справочника
     * @param action код действия
     * @param visible true, если ожидается, что иконка присутствует, иначе false
     */
    public static void assertIconVisible(CatalogItem item, String action, boolean visible)
    {
        String xPath = String.format(X_PICTOGRAM_PATTERN, item.getCode(), action);
        String message = String.format("В строке %s иконка действия '%s'.", visible ? "отсутствует" : "присутствует",
                action);
        Assert.assertTrue(message, visible ? tester.waitAppear(xPath) : tester.waitDisappear(xPath));
    }

    /**
     * Проверка, что следующие кнопки отсутствуют на карточке справочника, в блоке "Элементы справочника"
     * @param buttonCodes коды кнопок (Константы данного класса, начинающиеся с префикса BTN_)
     */
    public static void assertItemButtonAbsence(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertTrue(String.format("На карточке справочника присутствует кнопка с кодом: '%s'", button),
                    tester.waitDisappear(X_CATALOG_CONTENT + GUIXpath.Div.ID_PATTERN, button));
        }
    }

    /**
     * Проверка, что следующие кнопки присутствуют на карточке справочника, в блоке "Элементы справочника"
     * @param buttonCodes коды кнопок (Константы данного класса, начинающиеся с префикса BTN_)
     */
    public static void assertItemButtonPresent(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertEquals("Полученное название кнопки не совпало с ожидаемым.", BUTTON_NAMES.get(button),
                    tester.getText(X_CATALOG_CONTENT + GUIXpath.Div.ID_PATTERN, button));
        }
    }

    /**
     * Проверить наличие пиктограмы в таблице "Элементы справочника"
     * @param item модель элемента справочника
     * @param actions части id элемента, характеризующая действие - значения [edit, copy, archive, delete]
     * @param exists - true - должен присутствовать, false - отсутствовать
     */
    public static void assertPictogramsByTitleExists(CatalogItem item, boolean exists, String... actions)
    {
        for (String action : actions)
        {
            String msg = "Пиктограма в таблице " + item.getTitle() + (exists ? "отсутствует" : "присутствует");
            GUITester.assertExists(String.format(CELL_ICON_BY_CODE, item.getTitle(), action), exists, msg);
        }
    }

    /**
     * Проверить, что элемент находится в архиве в таблице "Элементы справочника"
     * @param item модель элемента справочника
     */
    public static void assertArchived(CatalogItem item)
    {
        String msg = "Элемент не находится в архиве";
        GUITester.assertExists(String.format(X_RESTORE_PICTOGRAM_PATTERN, item.getCode(),
                REMOVE_RESTORE_PICTOGRAM), true, msg);
    }

    /**
     * Проверка существования справочника в системе
     * <li>Переходим в интерфейс администратора и проверяем наличие справочника в дереве панели навигации.</li>
     * <li>Переходим в карточку справочника и проверяем соответствие названия, кода и описания контейнера
     * справочника, а так же такие свойства: "Плоский справочник" и "Справочник с папками".</li>
     * <li>Если справочник пользовательского типа, то сообщаем ему о том что он присутствует в системе.</li>
     * @param model модель справочника
     */
    public static void assertPresent(Catalog model)
    {
        //Переходим в интерфейс администратора
        GUINavigational.goToAdminUI();
        //Проверяем, что справочник с данным идентификатором существует в системе
        Assert.assertTrue("Справочник с уникальным кодом не найден в системе. Id: " + model.getCode(),
                DSLCatalog.isPresent(model.getCode()));
        //Переходим в карточку справочника
        goToCard(model);
        //В контенте "Атрибуты справочника" проверяем название
        String expectedTitle = tester.getText(X_TEXT_FIELD_INFO_TITLE_VALUE);
        String actualTitle = model.getTitle();
        expectedTitle = StringUtils.removeWhitespace(expectedTitle);
        actualTitle = StringUtils.removeWhitespace(actualTitle);
        Assert.assertEquals("Название справочника не совпало c ожидаемым. Полученое название справочника: "
                            + expectedTitle + ", ожидаемое название справочника: " + actualTitle, expectedTitle,
                actualTitle);
        //В контенте "Атрибуты справочника" проверяем код
        String expectedCode = tester.getText(X_TEXT_FIELD_INFO_CODE_VALUE);
        Assert.assertEquals("Код справочника не совпал c ожидаемым. Полученый код справочника: " + expectedCode
                            + ", ожидаемый код справочника: " + model.getCode(), expectedCode, model.getCode());
        //В контенте "Атрибуты справочника" проверяем описание
        String expectedDescription = tester.getText(GUIXpath.Div.DESCRIPTION_VALUE);
        String actualDescription = model.getDescription();
        expectedDescription = StringUtils.removeWhitespace(expectedDescription);
        actualDescription = StringUtils.removeWhitespace(actualDescription);
        Assert.assertEquals(
                "Описание справочника не совпало с ожидаемым. Полученое описание справочника: " + expectedDescription
                + ", ожидаемое описание справочника: " + actualDescription,
                expectedDescription, actualDescription);
        //Проверяем является ли плоским справочник
        if (Boolean.valueOf(model.getFlatCaption()))
        {
            Assert.assertTrue("Справочник не является плоским.", tester.waitAppear(IMG_ON));
        }
        else
        {
            Assert.assertTrue("Справочник является плоским.", tester.waitAppear(IMG_OFF));
        }
        //Проверяем может ли иметь справочник папки
        if (Boolean.valueOf(model.getWithFolderCaption()))
        {
            Assert.assertTrue("Справочник не может содержать папки.", tester.waitAppear(IMG_FOLD_ON));
        }
        else
        {
            Assert.assertTrue("Справочник может содержать папки.", tester.waitAppear(IMG_FOLD_OFF));
        }
        //Проверяем системный ли справочник и если да, то сообщаем ему, что он создан в системе
        if (!Boolean.valueOf(model.getIsSystem()))
        {
            model.setExists(true);
        }
    }

    /**
     * Вызвать форму добавления в пользовательского справочника
     * <li>Переходим в интерфейс администратора.</li>
     * <li>Раскрываем дерево панели навигации до пользовательских справочников и  нажимаем кнопку добавить
     * в дереве навигации.</li>
     */
    public static void callAddForm()
    {
        //Переходим в интерфейс администратора
        GUINavigational.goToAdminUI();
        tester.goToPage(StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#catalogs:user");
        clickAddForm();
    }

    /**
     * Кликнуть на пиктограмму добавления пользовательского справочника, находясь в Интерфейсе Администратора,
     * при раскрытом дереве пользовательских справочников
     */
    public static void clickAddForm()
    {
        int xOffset = tester.getElementTopLeftCornerX(VISIBILITY_ADD);
        int yOffset = tester.getElementTopLeftCornerY(VISIBILITY_ADD);
        tester.moveMouse(VISIBILITY_ADD, xOffset, yOffset);
        tester.click(ADD_CATALOG);
    }

    /**
     * Кликнуть по ссылке "к справочнику %название справочника%". (Должны
     * находиться на карточке элемента справочника)
     */
    public static void clickBackToCatalog()
    {
        tester.click(X_LINK_BACK_TO_CATALOG);
    }

    /**
     * Метод раскрывает/скрывает дерево Справочников
     * использовать в Интерфейса Администратора
     */
    public static void clickCatalogTree()
    {
        tester.click(GUICatalog.NAV_TREE_CATALOGS_IMG);
    }

    /**
     * Кликнуть по пиктограмме редактировать в таблице "Элементы справочника".
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     */
    public static void clickEditCatalogItem(CatalogItem item)
    {
        clickPictogram(item, EDIT_PICTOGRAM);
    }

    /**
     * Кликнуть по пиктограмме "Копировать" в таблице "Элементы справочника".
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     */
    public static void clickCopyCatalogItem(CatalogItem item)
    {
        clickPictogram(item, COPY_PICTOGRAM);
    }

    /**
     * Кликнуть по пиктограмме поместить в архив в таблице "Элементы справочника".
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     */
    public static void clickRemoveCatalogItem(CatalogItem item)
    {
        clickPictogram(item, REMOVE_RESTORE_PICTOGRAM);
    }

    /**
     * Кликнуть по кнопке "Удалить" на карточке справочника.
     * Для работы метода необходимо находится в карточке справочника
     */
    public static void clickDelete()
    {
        tester.click(GUIXpath.Div.DEL);
    }

    /**
     * Кликнуть по пиктограмме удалить в таблице "Элементы справочника".
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     */
    public static void clickDeleteCatalogItem(CatalogItem item)
    {
        clickPictogram(item, DELETE_PICTOGRAM);
    }

    /**
     * Кликнуть по пиктограмме в таблице "Элементы справочника".
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     * @param action часть id элемента, характеризующая действие - значения [edit, copy, archive, delete]
     */
    public static void clickPictogram(CatalogItem item, String action)
    {
        tester.click(X_PICTOGRAM_PATTERN, item.getCode(), action);
    }

    /**
     * Кликнуть по пиктограмме в таблице "Элементы справочника". Для работы
     * метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     * @param action часть id элемента, характеризующая действие - значения [edit, copy, archive, delete]
     */
    public static void clickPictogramByTitle(CatalogItem item, String action)
    {
        tester.click(CELL_ICON_BY_CODE, item.getTitle(), action);
    }

    /**
     * Кликнуть по пиктограмме удаления элемента справочника и подтвердить.
     * Для работы метода необходимо находится в карточке справочника
     * @param item модель элемента справочника
     */
    public static void clickPictogramRemoveAndConfirm(CatalogItem item)
    {
        clickPictogram(item, "delete");
        GUIForm.assertQuestionAppear("Форма подтверждения удаления не появилась.");
        GUIForm.confirmByYes();
    }

    /**
     * Кликнуть по кнопке "Переиндексировать" на карточке справочника
     */
    public static void clickRefreshButton()
    {
        tester.click(String.format(X_CATALOG_INFO + GUIXpath.Div.ID_PATTERN, GUICatalog.BTN_REFRESH));
    }

    /**
     * Кликнуть по кнопке "Используемые версии/Старые версии". (Должны
     * находиться на карточке справочника)
     */
    public static void clickSwitchVersion()
    {
        tester.click(X_BTN_SWITCH_VERSION);
    }

    /**
     * Кликнуть по кнопке "Загрузить исключения". (Должны
     * находиться на карточке справочника)
     */
    public static void clickUploadExclusions()
    {
        tester.click(X_BTN_UPLOAD_EXCLUSIONS);
    }

    /**
     * Заполнение полей на форме добавления справочника
     * @param model модель справочника
     */

    public static void fillUserCatalogFields(Catalog model)
    {
        setTitle(model.getTitle());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, model.getCode());
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.INPUT_FIELD_DESCRIPTION, model.getDescription());
        tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.INPUT_FLAT_VALUE_INPUT,
                Boolean.valueOf(model.getFlatCaption()));
        tester.setCheckbox(GUIXpath.PropertyDialogBoxContent.INPUT_WITH_FOLDERS_VALUE_INPUT,
                Boolean.valueOf(model.getWithFolderCaption()));
    }

    /**
     * Возвращает значение ячйки таблицы элементов каталога. Для определения ячейки используется код элемента
     * справочника
     * и код атрибута, значение которого требуется
     *
     * @param item элемент справочника
     * @param attribute код атрибут, значение которого требуется получить
     * @return значение ячйки таблицы элементов каталога
     */
    public static String getCellValue(CatalogItem item, String attribute)
    {
        return tester.getText(CELL_BY_CODE, item.getCode(), attribute);
    }

    /**
     * Перейти в карточку справочника
     * @param model модель справочника
     */
    public static void goToCard(Catalog model)
    {
        goToCard(model.getCode());
    }

    /**
     * Перейти на карточку справочника определяемую его кодом
     * @param catalogCode код справочника
     */
    public static void goToCard(String catalogCode)
    {
        tester.goToPage(
                StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#catalog:" + catalogCode);
    }

    /**
     * Переход в каталог системного справочника Таблицы соответствий
     */
    public static void goToCatalogRulesSettings()
    {
        tester.goToPage(
                StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#catalog:rulesSettings");
        tester.refresh();
    }

    /**
     * Открыть форму редактирования
     */
    public static void openEditForm()
    {
        tester.click(GUIXpath.Div.EDIT);
        GUIForm.assertDialogAppear("Форма редактирования справочника не появилась.");
    }

    /**
     * Установить код справочника
     * @param code код справочника
     */
    public static void setCode(String code)
    {
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.CODE_VALUE_OR_VALUE, code);
    }

    /**
     * Установить название справочника
     * @param title название справочника
     */
    public static void setTitle(String title)
    {
        GUITester.assertPresent(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG,
                "Отсутсвует поле ввода названия");
        tester.sendKeys(GUIXpath.PropertyDialogBoxContent.TITLE_VALUE_ON_DIALOG, title);
    }

    /**
     * Пытается удалить справочник и проверяет сообщение об невозможности удаления
     * Для работы метода необходимо находится в карточке справочника
     * @param expectedErrorMessage ожидаемое сообщение об ошибке
     */
    public static void tryToDeleteAndCheckErrorMessage(String expectedErrorMessage)
    {
        GUIForm.openDeleteForm();
        GUIForm.clickYes();
        GUIForm.assertErrorMessageOnForm(expectedErrorMessage);
    }

    /**
     * Получить значение Текущее время в данном часовом поясе для указанного пояса из списка элементов на карточке
     * справочника Часовые пояса
     * @param timeZone часовой пояс, для которого получаем значение
     * @return Текущее время в данном часовом поясе для указанного пояса в виде строки
     */
    public static String getTimeZoneCurrentTime(CatalogItem timeZone)
    {
        String xPath = GUIXpath.Other.TABLE + String.format(GUIXpath.Other.TR_DID_PATTERN, timeZone.getUuid())
                       + String.format(GUIXpath.Other.TD_DID_PATTERN_TWO, "timezone", "currentTimeTZ")
                       + GUIXpath.A.A_PREFIX;
        return tester.getText(xPath).substring(0, 19);
    }

    /**
     * Получить значение Текущее время в данном часовом поясе для Часового пояса сервера из атрибутов справочника
     * Часовые пояса
     * @return Текущее время в данном часовом поясе для Часового пояса серверав виде строки
     */
    public static String getServerCurrentTime()
    {
        String xPath = String.format(GUIXpath.Div.ANY_VALUE, "serverCurrentTime");
        return tester.getText(xPath).substring(0, 19);
    }
}
