package ru.naumen.selenium.casesutil.mobile.rest.lists.validators;

import static org.hamcrest.CoreMatchers.containsString;
import static org.hamcrest.CoreMatchers.is;

import org.hamcrest.Matcher;

import io.restassured.response.ValidatableResponse;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.MetaClassType;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

/**
 * Валидатор для списочного действие типа "Сортировка".
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
public class SortListActionValidator extends AbstractListActionValidator<SortListActionValidator>
{
    private Attribute[] attributes = new Attribute[0];

    SortListActionValidator(String code, String title)
    {
        super(code, title);
    }

    /**
     * Позволяет валидировать список атрибутов списка, для которых доступна сортировка.
     */
    public SortListActionValidator attributes(Attribute... attributes)
    {
        this.attributes = attributes;
        return this;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath)
    {
        super.validate(response, absolutePath);

        for (int i = 0; i < attributes.length; i++)
        {
            String code = attributes[i].getCode();
            Matcher<String> attributeFqnMatcher = !MetaClassType.CODE.equals(code)
                    ? is(attributes[i].getFullFqn())
                    : containsString(attributes[i].getCode());
            response.body(absolutePath + "?.attributes?.getAt(" + i + ")?.attributeFqn", attributeFqnMatcher);
            response.body(absolutePath + "?.attributes?.getAt(" + i + ")?.title", is(attributes[i].getTitle()));
        }
        response.body(absolutePath + "?.attributes?.size()", is(attributes.length));
    }
}
