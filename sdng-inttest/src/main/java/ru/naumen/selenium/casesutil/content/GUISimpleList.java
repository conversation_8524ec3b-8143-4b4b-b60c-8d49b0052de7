package ru.naumen.selenium.casesutil.content;

import java.util.List;
import java.util.Set;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import java.util.HashSet;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.content.ContentForm;

/**
 * Утилитарные методы для работы с контентов "Список объектов" и "Список вложенных объектов" в интерфейсе оператора
 * <AUTHOR>
 * @since 04.02.2012
 */
public class GUISimpleList extends CoreTester
{
    /**Шаблон xPath-а пути до ссылки "Выйти из архива", на контенте список вложенных объектов. В шаблон передавать id
     *  контента.*/
    public static final String X_ARCHIVE_LINK = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                                + "'gwt-debug-DefaultListPresenterContextLogic')]//*[contains(@id,"
                                                + "'gwt-debug-ToolBar')]//*[contains(@id,"
                                                + "'gwt-debug-ShowRemoved.')]";

    /**Шаблон xPath-а пути до ссылки "Добавить связь", на контенте список вложенных объектов. В шаблон передавать id
     * контента.*/
    private static final String X_LINK_LINK = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                              + "'gwt-debug-DefaultListPresenterContextLogic')]//*[contains(@id,"
                                              + "'gwt-debug-ToolBar')]//*[contains(@id,"
                                              + "'gwt-debug-link')]";

    /**Шаблон xPath-а пути до ссылки "Добавить", на контенте список вложенных объектов. В шаблон передавать id
     * контента.*/
    private static final String X_ADD_LINK = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                             + "'gwt-debug-DefaultListPresenterContextLogic')]//*[contains(@id,"
                                             + "'gwt-debug-ToolBar')]//*[contains(@id,"
                                             + "'gwt-debug-add')]";

    private static final String X_CELLS_OF_THE_ROW_IN_TABLE = "//*[@id='%s']//*[@id='gwt-debug-outer']//tbody/tr"
                                                              + "[@__did]";

    /**Шаблон XPath-a пути до ячейки таблицы. В шаблон передавать id контента и id ячейки таблицы.*/
    private static final String X_CELL_OF_THE_TABLE = "//*[@id='%s']//tr[@__did='%s']/td[contains(@__did, '%s')]";

    private static final String X_DEFAULTLIST_BODY_PTRN = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                                          + "'gwt-debug-DefaultListPresenterContextLogic')]";

    private static final String X_DEFAULTLIST_CONTENT_TABLE_PTRN = X_DEFAULTLIST_BODY_PTRN + "/div/div/table[1]";

    private static final String X_RELATED_OBJECT_LIST = "//*[@id='gwt-debug-DefaultListPresenterContextLogic"
                                                        + ".%s']//*[@id='gwt-debug-scrollableArea']/table/tbody[1]/tr";

    /**
     * Проверить отсутствие в контенте объекта с указанным uuid-ом
     * @param content модель контента
     * @param uuid - uuid проверяемого объекта
     */
    public static void assertAbsenceObject(ContentForm content, String uuid)
    {
        Assert.assertTrue("В контенте присутствует строка с объектом, uuid: " + uuid, tester
                .waitDisappear(X_DEFAULTLIST_CONTENT_TABLE_PTRN + "/tbody/tr[@__did='%s']", content.getXpathId(),
                        uuid));
    }

    /**
     * Проверить присутствие кнопки "Добавить"
     * @param content модель контента
     */
    public static void assertAddButtonPresent(ContentForm content)
    {
        Assert.assertTrue("Кнопка \"Добавить\" отсутствует.", tester.waitAppear(X_ADD_LINK, content.getXpathId()));
    }

    /**
     * Проверить присутствие кнопки "Добавить связь"
     * @param content модель контента
     */
    public static void assertAddLinkButtonPresent(ContentForm content)
    {
        Assert.assertTrue("Кнопка \"Добавить связь\" отсутствует.",
                tester.waitAppear(X_LINK_LINK, content.getXpathId()));
    }

    /**
     * Проверить значение элемента ячейки
     * @param content модель контента
     * @param bo модель БО, у которого проверяются атрибуты
     * @param attributeCode код атрибута в контенте
     * @param expected ожидаемое значение атрибута
     */
    public static void assertCellValue(ContentForm content, Bo bo, String attributeCode, String expected)
    {
        String updatedCode;
        if (!Model.TITLE.equals(attributeCode) && !attributeCode.contains("@"))
        {
            updatedCode = "@" + attributeCode;
        }
        else
        {
            updatedCode = attributeCode;
        }
        GUITester.assertTextContains(X_CELL_OF_THE_TABLE, expected, content.getXpathId(), bo.getUuid(), updatedCode);
    }

    /**
     * Проверить список найденных элементов по Uuid-y
     * @param content контент со списком элементов
     * @param expected ожидаемый список моделей БО - не может быть пустым
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     */

    public static void assertEqualListsByUuid(ContentForm content, List<? extends ModelUuid> expected, boolean order,
            boolean match)
    {
        List<String> uuids = ModelUtils.getUuids(expected);
        GUITester.assertEqualListsByWebElementProperty(X_RELATED_OBJECT_LIST, "__did", uuids, order, match,
                content.getCode());
    }

    /**
     * Проверяет отсутствие объекта с заданным uuid в списке объектов
     * @param content контент в котором поверяется отсутствие
     * @param bo объект, отсутствие которого проверяется
     */

    public static void assertObjectAbsence(ContentForm content, Bo... bos)
    {
        for (Bo bo : bos)
        {
            Assert.assertTrue("Объект присутствует в списке, uuid: " + bo.getUuid(),
                    tester.waitDisappear("//div[@id='" + content.getXpathId() + "']" + "//tr[@__did='%s']",
                            bo.getUuid()));
        }
    }

    /**
     * Проверяет наличие объекта в списке объектов
     * @param content контент в котором поверяется наличие
     * @param bo объект, наличие которого проверяется
     */
    public static void assertObjectPresent(ContentForm content, Bo... bos)
    {
        for (Bo bo : bos)
        {
            Assert.assertTrue("Объект отсутствует в списке, uuid: " + bo.getUuid(),
                    tester.waitAppear("//div[@id='" + content.getXpathId() + "']" + "//tr[@__did='%s']", bo.getUuid()));
        }
    }

    /**
     * Проверяет наличие объекта с заданным uuid в списке объектов
     * @param content - контент в котором поверяется наличие
     * @param uuid - uuid объекта
     */
    public static void assertObjectPresent(ContentForm content, String... uuids)
    {
        for (String uuid : uuids)
        {
            Assert.assertTrue("Объект отсутствует в списке, uuid: " + uuid,
                    tester.waitAppear("//div[@id='" + content.getXpathId() + "']" + "//tr[@__did='%s']", uuid));
        }
    }

    /**
     * Проверяет в контенте значения указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент)
     * @param content модель контента
     * @param bo модель БО, у которого проверяются атрибуты
     * @param attributes перечень проверяемых атрибутов в контенте. В данном методе используются следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), значение атрибута (AttributeModel.VALUE).
     */
    public static void assertSimpleListAttributeValue(ContentForm content, Bo bo, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            String attributeCode = attribute.getCode();
            assertCellValue(content, bo, attributeCode, attribute.getValue());
        }
    }

    /**
     * Нажать ссылку "Добавить" на списке вложенных объектов или связанных объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список вложенных объектов"
     * или контент "Список связанных объектов")
     * @param content модель контента типа "Список вложенных объектов" или "Список связанных объектов"
     */
    public static void clickAdd(ContentForm content)
    {
        clickAction(content, X_ADD_LINK);
    }

    /**
     * Нажать ссылку "Добавить связь" на списке связанных объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента типа "Список связанных объектов"
     */
    public static void clickAddRelation(ContentForm content)
    {
        clickAction(content, X_LINK_LINK);
    }

    /**
     * Кликнуть по ячейке списка
     * @param content модель контента
     * @param object объект (строка)
     * @param attr атрибут (колонка)
     */
    public static void clickCell(ContentForm content, Bo object, Attribute attr)
    {
        String xpath = String.format(X_CELL_OF_THE_TABLE + "/*", content.getXpathId(), object.getUuid(),
                attr.getCode());
        tester.clickOnInvisibleElement(xpath);
    }

    /**
     * Нажать ссылку "Разорвать связь" на списке связанных объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента типа "Список связанных объектов"
     * @param boModel модель бизнес объекта, аналог которого расположен в списке связанных объектов
     */
    public static void clickDeleteRelationPictogram(ContentForm content, Bo boModel)
    {
        tester.click("//div[@id='%s']//span[@id='delLink.%s']", content.getXpathId(), boModel.getUuid());
    }

    /**
     * Удалить связь с объектом из контента
     * @param content модель контента типа "Список связанных объектов"
     * @param boModel модель объекта
     */
    public static void deleteRelation(ContentForm content, Bo boModel)
    {
        clickDeleteRelationPictogram(content, boModel);
        GUIForm.confirmByYes();
    }

    /**
     * Получить UUID объектов содержащиеся в списке (/вложенных) объектов по id атрибута, которому соответствуют
     * ячейки данной
     * колонки списка, в режиме отображения списка неархивных объектов.
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список вложенных объектов")
     * @param content модель контента типа "Список вложенных объектов"
     * @return возвращает набор uuid-ов объектов содержащихся в простом списке
     */
    public static Set<String> getUUIDsUnarchive(ContentForm content)
    {
        //Проверяем контент, чтобы убедиться, что страница полностью загрузилась
        GUIContent.assertPresent(content);
        return getUUIDsByAttrId(content);
    }

    /**
     * Показать Архив в простом списке (/вложенных) объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список (/вложенных) объектов")
     * @param content модель контента типа "Список (/вложенных) объектов"
     */
    public static void showArchive(ContentForm content)
    {
        tester.click(X_ARCHIVE_LINK, content.getXpathId());
    }

    /**
     * Показать список (не архив) в простом списке (/вложенных) объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список (/вложенных) объектов")
     * @param content модель контента типа "Список (/вложенных) объектов"
     */
    public static void showList(ContentForm content)
    {
        tester.click(X_ARCHIVE_LINK, content.getXpathId());
    }

    /**
     * Нажать ссылку "Добавить", "Добавить связь" и т.д. на списке объектов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список объектов")
     * @param content модель контента типа "Список xxx объектов"
     */
    private static void clickAction(ContentForm content, String xPath)
    {
        tester.click(xPath, content.getXpathId());
    }

    /**
     * Получить UUID объектов содержащиеся в списке (/вложенных) объектов по id атрибута, которому соответствуют
     * ячейки данной
     * колонки списка.
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список вложенных объектов")
     * @param content модель контента типа "Список вложенных объектов"
     * @return возвращает набор uuid-ов объектов содержащихся в простом списке
     */
    private static Set<String> getUUIDsByAttrId(ContentForm content)
    {
        Set<String> result = new HashSet<>();
        if (!tester.waitPresence(X_CELLS_OF_THE_ROW_IN_TABLE, content.getXpathId()))
        {
            return result;
        }
        List<WebElement> cells = tester.findElements(X_CELLS_OF_THE_ROW_IN_TABLE, content.getXpathId());
        for (WebElement cell : cells)
        {
            String did = cell.getAttribute("__did");
            if (null != did)
            {
                result.add(did);
            }
        }
        return result;
    }
}
