package ru.naumen.selenium.casesutil.scripts.attrs.validators;

import java.util.Map;

import org.junit.Assert;

/**
 * Валидатор возможного значения атрибута, полученного через методы скриптового API.
 * Позволяет валидировать результаты для атрибутов типов "Набор типов класса".
 * @see AbstractPossibleValueValidator
 *
 * <AUTHOR>
 * @since 04.12.2024
 */
@SuppressWarnings({ "java:S1845", "java:S5960" }) // код тестов
public class MetaClassPossibleValueValidator extends AbstractPossibleValueValidator<MetaClassPossibleValueValidator>
{
    private static final String FQN = "fqn";
    private static final String CODE = "code";

    private final String fqn;

    /**
     * Создаёт валидатор для элемента дерева возможных значений атрибута
     *
     * @param fqn FQN класса/типа
     * @param title название
     */
    public MetaClassPossibleValueValidator(String fqn, String title)
    {
        super(title);
        this.fqn = fqn;
    }

    @Override
    public void validate(Map<String, Object> actualValue, String pathPrefix)
    {
        super.validate(actualValue, pathPrefix);

        Assert.assertEquals(pathPrefix + FQN, fqn, actualValue.get(FQN));
        Assert.assertEquals(pathPrefix + CODE, fqn, actualValue.get(CODE));
    }
}
