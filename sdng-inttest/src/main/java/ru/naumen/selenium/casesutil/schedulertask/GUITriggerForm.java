package ru.naumen.selenium.casesutil.schedulertask;

import static ru.naumen.selenium.core.AbstractTestCaseBase.tester;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger;
import ru.naumen.selenium.casesutil.model.schedulertask.Trigger.TriggerType;
import ru.naumen.selenium.util.StringUtils;

/**
 * Утилитарные методы для работы с формами добавления и редактирования триггеров
 *
 * <AUTHOR>
 * @since 17.10.2024
 */
public class GUITriggerForm
{
    private static final String RANDOMIZE_CHECKBOX_XPATH =
            Div.PROPERTY_DIALOG_BOX_CONTENT + "//span[@id='gwt-debug-randomizeDelay-value']";
    private static final String EXECUTION_PERIOD_XPATH =
            GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.PERIOD_VALUE;
    private static final String EXECUTION_STRATEGY_XPATH =
            GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.InputComplex.CALCULATE_STRATEGY_VALUE;

    private static final String CONCRETE_TRIGGER_TYPE_CODE = "CONCRETE_DATE";
    private static final String PERIODIC_TRIGGER_TYPE_CODE = "PERIODIC";

    private static final String FROM_START_STRATEGY_CODE = "FROM_START";
    private static final String FROM_LAST_EXECUTION_STRATEGY_CODE = "FROM_LAST_EXECUTION";

    private static final String WEEKLY_PERIOD_CODE = "WEEKLY";
    private static final String MONTHLY_PERIOD_CODE = "MONTHLY";
    private static final String YEARLY_PERIOD_CODE = "YEARLY";

    /**
     * Нажать кнопку "Добавить" триггер
     */
    public static void clickAddTrigger()
    {
        tester.click(GUIXpath.Div.ADD);
    }

    /**
     * Заполнить все поля на форме добавления/редактирования задачи планировщика типа Скрипт с новым скриптом
     */
    public static void fillAddForm(Trigger trigger)
    {
        TriggerType triggerType = TriggerType.fromValue(trigger.getTriggerType());
        if (TriggerType.CONCRETE_DATE.equals(triggerType))
        {
            selectConcreteDateType();
            String executionDate = trigger.getExecutionDate();
            int indexOf = executionDate.indexOf(' ');
            tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.DATE_TEXT_BOX,
                    executionDate.substring(0, indexOf));
            tester.sendKeys(GUIXpath.Div.PROPERTY_DIALOG_BOX_CONTENT + GUIXpath.Input.TIME_BOX,
                    executionDate.substring(indexOf));
        }
        else if (TriggerType.PERIODIC.equals(triggerType))
        {
            selectPeriodicType();
            if (StringUtils.isEmpty(trigger.getPeriod()))
            {
                GUISchedulerTaskList.fillPeriodicPeriodTriggerForm(trigger, trigger.getStartDate());
            }
            else
            {
                GUISchedulerTaskList.fillPeriodicIntervalTriggerForm(trigger, trigger.getStartDate());
            }
        }
    }

    /**
     * Нажать кнопку "Редактировать" триггер
     */
    public static void clickEditTrigger()
    {
        tester.click(GUIXpath.Div.EDIT);
    }

    /**
     * Выбрать тип триггера - "Выполнение в определенную дату и время"
     */
    public static void selectConcreteDateType()
    {
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE, CONCRETE_TRIGGER_TYPE_CODE);
    }

    /**
     * Выбрать тип триггера - "Периодическое выполнение"
     */
    public static void selectPeriodicType()
    {
        GUISelect.select(GUIXpath.PropertyDialogBoxContent.TYPE_LIST_VALUE, PERIODIC_TRIGGER_TYPE_CODE);
    }

    /**
     * Выбрать стратегию пересчета расписания триггера - "От момента последнего выполнения"
     */
    public static void selectFromLastExecutionStrategy()
    {
        GUISelect.select(EXECUTION_STRATEGY_XPATH, FROM_LAST_EXECUTION_STRATEGY_CODE);
    }

    /**
     * Выбрать стратегию пересчета расписания триггера - "С момента начала выполнения"
     */
    public static void selectFromStartStrategy()
    {
        GUISelect.select(EXECUTION_STRATEGY_XPATH, FROM_START_STRATEGY_CODE);
    }

    /**
     * Выбрать период выполнения триггера - "Еженедельно"
     */
    public static void selectWeeklyPeriod()
    {
        GUISelect.select(EXECUTION_PERIOD_XPATH, WEEKLY_PERIOD_CODE);
    }

    /**
     * Выбрать период выполнения триггера - "Ежемесячно"
     */
    public static void selectMonthlyPeriod()
    {
        GUISelect.select(EXECUTION_PERIOD_XPATH, MONTHLY_PERIOD_CODE);
    }

    /**
     * Выбрать период выполнения триггера - "Ежегодно"
     */
    public static void selectYearlyPeriod()
    {
        GUISelect.select(EXECUTION_PERIOD_XPATH, YEARLY_PERIOD_CODE);
    }

    /**
     * Проверить присутствие элемента "Рандомизировать время запуска задачи для снижения нагрузки на систему" на
     * форме дорме добавления/редактирования
     */
    public static void assertRandomizeCheckboxPresent()
    {
        GUITester.assertPresent(RANDOMIZE_CHECKBOX_XPATH,
                "Отсутствует элемент \"Рандомизировать время запуска задачи для снижения нагрузки на систему\"");
    }

    /**
     * Проверить отсутствие элемента "Рандомизировать время запуска задачи для снижения нагрузки на систему" на
     * форме дорме добавления/редактирования
     */
    public static void assertRandomizeCheckboxAbsent()
    {
        GUITester.assertAbsent(RANDOMIZE_CHECKBOX_XPATH,
                "Присутствует элемент \"Рандомизировать время запуска задачи для снижения нагрузки на систему\"");
    }
}
