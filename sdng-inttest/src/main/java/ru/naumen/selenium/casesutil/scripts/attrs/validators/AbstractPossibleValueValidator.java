package ru.naumen.selenium.casesutil.scripts.attrs.validators;

import static ru.naumen.selenium.casesutil.scripts.attrs.DSLAttributesApi.MESSAGE_PREFIX_PATTERN;

import java.util.List;
import java.util.Map;

import org.junit.Assert;

import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.scripts.attrs.DSLAttributesApi;

/**
 * Абстрактный валидатор возможного значения атрибута, полученного через методы скриптового API.
 * Совместим с методами {@link DSLAttributesApi#listPossibleValues(Attri<PERSON><PERSON>, <PERSON>)} и
 * {@link DSLAttributesApi#listPossibleValues(Attribute, Bo, ru.naumen.selenium.casesutil.scripts.attrs.AttributePossibleValuesParams)}.
 *
 * <AUTHOR>
 * @since 04.12.2024
 */
@SuppressWarnings({ "java:S1845", "java:S5960" }) // код тестов
public abstract class AbstractPossibleValueValidator<S extends AbstractPossibleValueValidator<?>>
        implements PossibleValueValidator<Map<String, Object>>
{
    private static final String TITLE = "title";
    private static final String SELECTABLE = "selectable";
    private static final String LEAF = "leaf";
    private static final String CHILDREN = "children";

    private final String title;
    private boolean selectable = true;
    private boolean leaf = true;
    private MetaClassPossibleValueValidator[] children;

    protected AbstractPossibleValueValidator(String title)
    {
        this.title = title;
    }

    /**
     * Позволяет валидировать флаг того, что текущее значение можно выбрать
     *
     * @param selectable флаг доступности для выбора
     */
    @SuppressWarnings("unchecked")
    public S selectable(boolean selectable)
    {
        this.selectable = selectable;
        return (S)this;
    }

    /**
     * Позволяет валидировать флаг того, что текущее значение является не раскрываемым узлом дерева
     *
     * @param leaf флаг не раскрываемости узла дерева
     */
    @SuppressWarnings("unchecked")
    public S leaf(boolean leaf)
    {
        this.leaf = leaf;
        return (S)this;
    }

    /**
     * Позволяет валидировать дочерние возможные значения
     *
     * @param children валидаторы для дочерних возможных значений
     */
    @SuppressWarnings("unchecked")
    public S children(@Nullable MetaClassPossibleValueValidator... children)
    {
        this.children = children;
        this.leaf = false;
        return (S)this;
    }

    @Override
    @SuppressWarnings({ "unchecked", "DuplicatedCode" })
    public void validate(Map<String, Object> actualValue, String pathPrefix)
    {
        Assert.assertEquals(pathPrefix + TITLE, title, actualValue.get(TITLE));
        Assert.assertEquals(pathPrefix + SELECTABLE, selectable, actualValue.get(SELECTABLE));
        Assert.assertEquals(pathPrefix + LEAF, leaf, actualValue.get(LEAF));

        List<Map<String, Object>> actualChildren = (List<Map<String, Object>>)actualValue.get(CHILDREN);
        if (children == null)
        {
            Assert.assertEquals(actualChildren, List.of());
        }
        else
        {
            for (int i = 0; i < children.length; i++)
            {
                Map<String, Object> child = actualChildren.get(i);
                String currentMessagePrefix = pathPrefix + MESSAGE_PREFIX_PATTERN.formatted(i);
                children[i].validate(child, currentMessagePrefix);
            }
            Assert.assertEquals(children.length, actualChildren.size());
        }
    }
}
