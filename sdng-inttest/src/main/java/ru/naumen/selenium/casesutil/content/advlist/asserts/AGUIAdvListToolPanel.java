package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.BTN_REFRESH;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.BUTTON_PANEL;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.CLASS_NOT_SELECTED_ITEM;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.CLASS_SELECTED_ITEM;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.EDITABLE_TOOL_EDIT_BTN;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.EDITABLE_TOOL_LINK_BY_TEXT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.ICON_EDIT_TOOL_PANEL;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.TOOL_BTN_BY_TEXT;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.TOOL_BUTTON;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.GUIXpath.Other;
import ru.naumen.selenium.casesutil.GUIXpath.Span;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;

/**
 * Утилитарные методы для проверок, связанных с панелью инструментов advList-а, через интерфейс
 * <AUTHOR>
 * @since 25.05.2015
 */
public class AGUIAdvListToolPanel extends GUIAdvlist
{
    public static final String ICON_EDIT = "EDIT";
    public static final String ICON_DELETE = "DELETE";
    public static final String ICON_FILTER_SETTINGS = "FILTER_SETTINGS";
    public static final String ICON_LIST_TEMPLATE = "LIST_TEMPLATE_APPLY";
    public static final String ICON_COPY_TO_TEMPLATE = "COPY_TO_TEMPLATE";

    public AGUIAdvListToolPanel(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверить присутствие/отсутствие инструмента по названию
     * @param btnCode - код кнопки
     * @param title название
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    public void presenceOrAbsenceByTitle(String btnCode, String title, boolean isPresent)
    {
        String xpath = getContentXpath() + String.format(TOOL_BTN_BY_TEXT, btnCode, title);
        String message = String.format("%s инструмент %s", isPresent ? "Отсутствует" : "Присутствует", title);
        GUITester.assertExists(xpath, isPresent, message);
    }

    /**
     * Проверить настройки (расположение и hover) и отображение панели инструментов для указанного атрибута
     * @param attrCode код атрибута
     * @param locationBottom расположение под атрибутом
     * @param showAlways показывать всегда
     */
    public void checkAttributeToolSettings(String attrCode, boolean locationBottom, boolean showAlways)
    {
        boolean isAdmin = GUITester.getCurrentUrl().contains("admin");
        String toolPanelXpath = getAttributeToolPanelXpath(attrCode);
        WebElement toolPanel = GUITester.tester.find(toolPanelXpath);
        String actual = toolPanel.getCssValue("display");
        String expected = locationBottom ? "block" : "inline-block";
        Assert.assertEquals("Расположение панели инструментов для атрибута '"
                            + attrCode + "' не совпало с ожидаемым.", expected, actual);
        tester.scrollToElement(Div.HEADER_TITLE);
        tester.moveTo(Div.HEADER_TITLE);
        actual = toolPanel.getCssValue("visibility");
        expected = showAlways || isAdmin ? "visible" : "hidden"; // решили в админке отображать всегда
        //expected = "visible";
        Assert.assertEquals("Отображение панели инструментов для атрибута '"
                            + attrCode + "' не совпало с ожидаемым без наведения на контент.", expected, actual);
        tester.scrollToElement(getContentXpath());
        tester.moveTo(getContentXpath());
        tester.scrollToElement(toolPanelXpath);
        tester.moveTo(toolPanelXpath);
        actual = toolPanel.getCssValue("visibility");
        expected = "visible";
        Assert.assertEquals("Отображение панели инструментов для атрибута '"
                            + attrCode + "' не совпало с ожидаемым с наведением на контент.", expected, actual);
    }

    /**
     * Проверить название кнопки (и если название не пусто, то нет всплывающей подсказки)
     * @param buttonCode код кнопки (Константы {@link ru.naumen.selenium.casesutil.content.advlist2.GUIAdvListXpath}, 
     * начинающиеся с префикса BTN_)
     * @param buttonName название кнопки
     */
    public void buttonName(String buttonCode, String buttonName)
    {
        String message = "Полученное название кнопки не совпало с ожидаемым.";
        GUITester.assertTextPresentWithMsg(getContentXpath() + TOOL_BUTTON, buttonName, message, buttonCode);
        if (StringUtils.isNotEmpty(buttonName))
        {
            Assert.assertEquals("У кнопки " + buttonName + " есть всплывающая подсказка.", "",
                    GUITester.getAttributeProperty(getContentXpath() + TOOL_BUTTON, "title", buttonCode));
        }
    }

    /**
     * Проверить отсутствие кнопки в advlist'е
     * @param buttonCodes коды кнопок (Константы {@link ru.naumen.selenium.casesutil.content.advlist2.GUIAdvListXpath}, 
     * начинающиеся с префикса BTN_)
     */
    public void buttonsAbsence(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertTrue(String.format("В контенте притутствует кнопка с кодом: '%s'", button),
                    tester.waitDisappear(getContentXpath() + TOOL_BUTTON, button));
        }
    }

    /**
     * Проверка присутствие кнопки в advlist'е
     * @param buttonCodes коды кнопок (Константы {@link ru.naumen.selenium.casesutil.content.advlist2.GUIAdvListXpath}, 
     * начинающиеся с префикса BTN_)
     */
    public void buttonsPresence(String... buttonCodes)
    {
        for (String button : buttonCodes)
        {
            Assert.assertTrue(String.format("В контенте остутствует кнопка с кодом: '%s'", button),
                    tester.waitAppear(getContentXpath() + TOOL_BUTTON, button));
        }
    }

    /**
     * При наведении курсора на панель действий карточки объекта проверить, что
     * элемент управления "Перейти к настройке панели действий"
     * в зависимости от значения параметра в методе должен либо отобразится, либо нет.
     * @param isPresent true/false - должен присутствовать/отсутствовать
     */
    public void editableToolEditOnEditableToolPanel(boolean isPresent)
    {
        String editableToolPanelXpath = getContentXpath() + Other.PREFIX;
        tester.moveMouse(editableToolPanelXpath, 1, 1);
        String msg = "Элемент управления \"Перейти к настройке панели действий\" "
                     + (isPresent ? "не отобразился" : "отобразился");
        GUITester.assertExists(editableToolPanelXpath + EDITABLE_TOOL_EDIT_BTN, isPresent, msg);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления отображается
     * @param exist false - не должен отображать, true - должен отображаться
     * @param idIconButtons - id иконок элемента управления
     */
    public void controlIsVisibleOnContentHover(boolean exist, String... idIconButtons)
    {
        for (String id : idIconButtons)
        {
            tester.moveTo(getContentXpath());
            String xpath = String.format(getContentXpath() + BUTTON_PANEL + GUIXpath.Span.ANY, id);
            boolean actual = exist ? tester.waitAppear(xpath) : tester.waitDisappear(xpath);
            String msg = String.format("Элемент управления '%s' %s", id,
                    exist ? "должен отображаться, но не отображается" : "не должен отображаться, но отображается");
            Assert.assertTrue(msg, actual);
        }
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Перейти к настройке панели действий"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertActionPanelSettingsIconVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_EDIT_TOOL_PANEL, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Удалить"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertDeleteIconVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_DELETE, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Редактировать"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertEditIconVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_EDIT, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Настройка ограничений при фильтрации"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertFilterRestrictSettingsVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_FILTER_SETTINGS, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Скопировать настройки из шаблона"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertCopySettingsFromTemplateVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_LIST_TEMPLATE, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Создать шаблон на базе списка"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void assertCreateTemplateFromListVisibleOnContentHover(boolean isPresent)
    {
        assertControlVisibleOnContentHover(ICON_COPY_TO_TEMPLATE, isPresent);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления отображается
     * @param idIconButton - id иконки элемента управления
     * @param exist false - не должен отображать, true - должен отображаться
     */
    private void assertControlVisibleOnContentHover(String idIconButton, boolean exist)
    {
        tester.moveTo(getContentXpath());
        String xpath = getContentXpath() + BUTTON_PANEL + idIconButton;
        boolean actual = exist ? tester.waitAppear(xpath) : tester.waitDisappear(xpath);
        String msg = String.format("Элемент управления '%s' %s", idIconButton,
                exist ? "должен отображаться, но не отображается" : "не должен отображаться, но отображается");
        Assert.assertTrue(msg, actual);
    }

    /**
     * При наведении курсора на контент проверить, что элемент управления "Перейти к настройке панели действий"
     * в зависимости от значения параметра должен либо отобразиться, либо нет.
     */
    public void iconEditableToolOnContent(boolean isPresent)
    {
        //добавить проверку на рамку
        tester.moveMouse(getContentXpath(), 1, 1);
        tester.moveTo(getContentXpath());
        boolean icon = tester.waitForElementDisplayed(getContentXpath() + BUTTON_PANEL + ICON_EDIT_TOOL_PANEL,
                isPresent, true);
        String msg = String.format(
                "Отображение элемента управления \"Перейти к настройке панели действий\" Ожидался: %s, получен %s!",
                isPresent, icon);
        Assert.assertTrue(msg, icon);
    }

    /**
     * Проверить, что кнопка Обновлять список в advlist'е не нажата
     * <br>
     * Проверки:
     * <ol>
     * <li>Кнопка обновления списка присутствует в advlist'е
     * <li>Кнопка обновления списка имеет не нажатое состояние
     * </ol>
     */
    public void refreshButtonNotPushed()
    {
        buttonNotPushed(BTN_REFRESH);
    }

    /**
     * Проверить, что кнопка не нажата
     *
     * @param buttonCode код кнопки
     */
    public void buttonNotPushed(String buttonCode)
    {
        String contentXpath = getContentXpath();
        buttonsPresence(buttonCode);
        Assert.assertTrue("Кнопка нажата",
                tester.waitAppear(contentXpath + TOOL_BUTTON + CLASS_NOT_SELECTED_ITEM, buttonCode));
    }

    /**
     * Проверить, что кнопка обновления в advlist'е нажата
     * <br>
     * Проверки:
     * <ol>
     * <li>Кнопка обновления списка присутствует в advlist'е
     * <li>Кнопка обновления списка имеет нажатое состояние
     * </ol>
     */
    public void refreshButtonPushed()
    {
        buttonPushed(BTN_REFRESH);
    }

    /**
     * Проверить, что кнопка нажата
     *
     * @param buttonCode код кнопки
     */
    public void buttonPushed(String buttonCode)
    {
        String contentXpath = getContentXpath();
        buttonsPresence(buttonCode);
        Assert.assertTrue("Кнопка не нажата",
                tester.waitAppear(contentXpath + TOOL_BUTTON + CLASS_SELECTED_ITEM, buttonCode));
    }

    /**
     * Проверить, присутствие ссылки по названию
     * @param title код действия инструмента
     */
    public void toolLink(String title)
    {
        String msg = "Ссылка'" + title + "' отсутствует в advlist";
        GUITester.assertExists(getContentXpath() + String.format(EDITABLE_TOOL_LINK_BY_TEXT, title), true, msg);
    }

    public void toolLinkAbsence(String title)
    {
        String msg = "Ссылка'" + title + "' присутствует в advlist";
        GUITester.assertExists(getContentXpath() + String.format(EDITABLE_TOOL_LINK_BY_TEXT, title), false, msg);
    }

    /**
     * Проверить наличие/отсутствие иконки настройки панели инструментов для атрибута
     * @param attrCode код атрибута
     */
    public void attributeToolSettingsExists(String attrCode, boolean exists)
    {
        String msg = String.format("Иконка настройки для атрибута с кодом %s %s",
                attrCode, exists ? "отсутствует" : "присутствует");
        String xpath = getAttributeXpath(attrCode);
        tester.scrollToElement(xpath);
        tester.moveTo(xpath);
        GUITester.assertExistsWithoutScroll(getAttributeToolSettingsIconXpath(attrCode), exists, msg);
    }

    /**
     * Получить xpath иконки настройки панели инструментов для атрибута
     * @param attrCode код атрибута
     */
    public String getAttributeToolSettingsIconXpath(String attrCode)
    {
        return getAttributeToolPanelXpath(attrCode) + String.format(Span.ANY, "toolBarSettings");
    }

    /**
     * Получить xpath панели инструментов для атрибута
     * @param attrCode код атрибута
     */
    public String getAttributeToolPanelXpath(String attrCode)
    {
        return getAttributeXpath(attrCode) + "/following-sibling::div";
    }

    /**
     * Получить xpath значения атрибута
     * @param attrCode код атрибута
     */
    public String getAttributeXpath(String attrCode)
    {
        return String.format(getContentXpath() + Div.ANY_VALUE, attrCode);
    }
}
