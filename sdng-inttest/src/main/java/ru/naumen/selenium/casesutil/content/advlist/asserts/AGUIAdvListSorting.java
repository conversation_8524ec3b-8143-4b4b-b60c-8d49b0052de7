package ru.naumen.selenium.casesutil.content.advlist.asserts;

import static ru.naumen.selenium.casesutil.GUITester.waitAttributeProperty;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

import java.util.List;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvlist;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

/**
 * Утилитарные методы для проверок, связанных с сортировкой advList-а, через интерфейс
 * <AUTHOR>
 * @since 25.05.2015
 */
public class AGUIAdvListSorting extends GUIAdvlist
{
    public AGUIAdvListSorting(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Проверить сортировку атрибутов на форме настройки полей сортировки (до сохранения)
     * @param expected ожидаемый список и порядок сортировки атрибутов
     */
    public void attrsOnDialog(Attribute... expected)
    {
        GUITester.assertFindElements(SORT_DIALOG_ATTRS, ModelUtils.getTitles(expected), true, true);
    }

    /**
     * Проверить выпадающий список с атрибутами, по которым возможна сортировка, на форме "Настройка полей сортировки"
     * (должна быть открыта форма "Настройка полей сортировки")
     * @param order с учетом порядка (true/false)
     * @param match true - в списке больше не должно быть других элементов, false - в списке могут быть и другие
     *              элементы
     * @param attrs набор моделей ожидаемых атрибутов, если набор пуст, то проверяется, что поле задизейблено
     */
    public void attrsSelectOnDialog(boolean order, boolean match, Attribute... attrs)
    {
        if (attrs.length == 0)
        {
            Assert.assertFalse("Поле для выбора атрибута для сортировки не задизейблено.",
                    tester.findDisplayed(SORT_DIALOG_INPUT).isEnabled());
        }
        else
        {
            List<String> expected = ModelUtils.getTitles(attrs);
            GUISelect.assertSelectWithoutEmpty(SORT_DIALOG_INPUT, expected, order, match, 1, 1);
        }
    }

    /**
     * Проверить список полей, по которым настроена сортировка (полное совпадение с учетом порядка)
     * (должна быть раскрыта панель настройки сортировки, т.е. нажата кнопка "Сортировка...")
     * Если атрибуты не указаны, проверяет что атрибуты отсутствуют
     * @param attributes ожидаемый набор моделей атрибутов
     */
    public void currentSorting(Attribute... attributes)
    {
        List<String> attrList = ModelUtils.getTitles(attributes);
        if (attrList.isEmpty())
        {
            String msg = "Ожидаемый список полей настроенной сортировки не пуст.";
            GUITester.assertExists(getContentXpath() + SORT_ELEMENTS, false, msg);
        }
        else
        {
            GUITester.assertFindElements(getContentXpath() + SORT_ELEMENTS, attrList, true, true);
        }
    }

    /**
     * Проверить направление сортировки по атрибуту на панели настройки сортировки
     * (т.е. должна быть нажата кнопка "Сортировка...")
     * @param attr модель атрибута у которого нужно проверить направление сортировки
     * @param expected ожидаемое направление сортировки: true - прямая; false - обратная
     */
    public void directSorting(Attribute attr, boolean expected)
    {
        String msg = "Ожидаемая сортировка элементов не совпала с полученной";
        String xpath = getContentXpath() + SORT_IMG;

        String directExpected = expected ? SORT_DIRECT_ACTIVE : SORT_DIRECT_INACTIVE;
        String directActual = waitAttributeProperty(xpath, "src", directExpected, attr.getTitle(), 1);
        Assert.assertEquals(msg, directExpected, directActual);

        String reverseExpected = expected ? SORT_REVERSE_INACTIVE : SORT_REVERSE_ACTIVE;
        String reverseActual = waitAttributeProperty(xpath, "src", reverseExpected, attr.getTitle(), 2);
        Assert.assertEquals(msg, reverseExpected, reverseActual);
    }

    /**
     * Проверить направление сортировки по атрибуту на форме настройки полей сортировки
     * @param attributeCode Код атрибута у которого нужно проверить направление сортировки
     * @param isDirectSortExpected ожидаемое направление сортировки: true - прямая; false - обратная
     */
    public void directSortingOnDialog(Attribute attr, boolean expected)
    {
        String msg = "Ожидаемая сортировка элементов не совпала с полученной";

        String directExpected = expected ? SORT_DIRECT_ACTIVE : SORT_DIRECT_INACTIVE;
        String directActual = waitAttributeProperty(SORT_DIALOG_IMG, "src", directExpected, attr.getCode(), 1);
        Assert.assertEquals(msg, directExpected, directActual);

        String reverseExpected = expected ? SORT_REVERSE_INACTIVE : SORT_REVERSE_ACTIVE;
        String reverseActual = waitAttributeProperty(SORT_DIALOG_IMG, "src", reverseExpected, attr.getCode(), 2);
        Assert.assertEquals(msg, reverseExpected, reverseActual);
    }

    /**
     * Проверить отсутствие панели с настроенной сортировкой
     */
    public void panelAbsence()
    {
        GUITester.assertExists(getContentXpath() + SORT_PANEL, false, "Панель с настроенной сортировкой присутствует.");
    }

    /**
     * Проверить присутствие панели с настроенной сортировкой
     */
    public void panelPresence()
    {
        GUITester.assertExists(getContentXpath() + SORT_PANEL, true, "Панель с настроенной сортировкой отсутствует.");
    }
}
