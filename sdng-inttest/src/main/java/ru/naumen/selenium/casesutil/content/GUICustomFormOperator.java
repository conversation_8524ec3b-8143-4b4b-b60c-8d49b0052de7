package ru.naumen.selenium.casesutil.content;

import static java.util.stream.Collectors.toList;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.comment.GUIComment;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.BooleanType;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;

import com.google.common.collect.Lists;

/**
 * Общие утилитарные методы для настройки форм смены типа\ответственного в интерфейсе
 * <AUTHOR>
 * @since 24 окт. 2017 г.
 */
public abstract class GUICustomFormOperator extends CoreTester
{
    public static final String INPUT_VALUE = GUIXpath.Div.FORM_CONTAINS.concat("//input[@id='gwt-debug-%s-value']");
    public static final String INPUT_ANY = GUIXpath.Div.FORM_CONTAINS.concat("//input[contains(@id,'gwt-debug-%s')]");

    public static final String MASS_EDIT_BLOCK_LABEL = "//div[@class='gwt-HTML mass-edit-block-label']";
    public static final String MASS_EDIT_BLOCK_LABEL_TITLE = "//div[@class='gwt-HTML mass-edit-block-label' and text"
                                                             + "()='%s']";
    public static final String MASS_EDIT_ATTR_DESC = "//div[@id='gwt-debug-%s-description' and text()='%s']";
    public static final String MASS_EDIT_BLOCK_LABEL_FIRST_ABOVE = "(" + GUIXpath.Div.FORM_CONTAINS
                                                                   + "%s//ancestor::tr[1]/preceding-sibling::*"
                                                                   + MASS_EDIT_BLOCK_LABEL + ")[last()]";
    public static final String MASS_EDIT_ATTR_DESC_FIRST_BELOW = GUIXpath.Div.FORM_CONTAINS
                                                                 + "%s//ancestor::div//preceding-sibling::*"
                                                                 + MASS_EDIT_ATTR_DESC;

    /**
     * Проверить отсутствие атрибута
     * @param attribite - атрибут
     */
    public static void assertAbsenceAttribute(Attribute attribite)
    {
        String message = String.format("Атрибут %s появился на форме изменения типа", attribite.getTitle());
        Assert.assertTrue(message, tester.waitDisappear(INPUT_VALUE, attribite.getCode()));
    }

    /**
     * Проверить отсутсвие поля ввода комменатрия на форме
     */
    public static void assertAbsenceCommentOnForm()
    {
        GUIComment.assertCommentAbsenceOnForm();
    }

    /**
     * Проверить порядок атрибутов
     * @param attribites - атрибуты
     */
    public static void assertAttributeOrder(Attribute... attributes)
    {
        Object attr[] = tester.findElements(INPUT_ANY, "").stream().filter(a ->
        {
            boolean flag = false;
            for (Attribute attribute : attributes)
            {
                if (a.getAttribute("id").contains(attribute.getCode()))
                {
                    flag = true;
                }
            }
            return flag;
        }).map(a ->
        {
            return a.getAttribute("id").split("gwt-debug-")[1].split("-value")[0];
        }).collect(Collectors.toList()).toArray();

        Object expect[] = Lists.newArrayList(attributes).stream().map(a ->
        {
            return a.getCode();
        }).collect(Collectors.toList()).toArray();
        Assert.assertArrayEquals("Порядок атрибутов не соблюден", attr, expect);
    }

    /**
     * Проверить присутствие атрибутов на форме массового редактирования <br>
     * и что после названия каждого из атрибутов расположено соответствующее описание
     * @param attrs массив атрибутов
     */
    public static void assertMassEditAttributesDescription(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            String code = attr.getCode();
            String attrXpath = attributeXpathOnMassEditForm(attr, code);
            GUITester.assertExists(GUIXpath.Div.FORM_CONTAINS + attrXpath, true,
                    String.format("Атрибут '%s' отсутствует на форме", attr.getTitle()));
            GUITester.assertExists(String.format(MASS_EDIT_ATTR_DESC_FIRST_BELOW, attrXpath, code, attr
                            .getDescription()),
                    true,
                    String.format("У атрибута '%s' отсутствует описание '%s'", attr.getTitle(), attr.getDescription()));
        }
    }

    /**
     * Проверить присутствие атрибутов в общем блоке на форме массового редактирования
     * @param attrs массив атрибутов
     */
    public static void assertMassEditBlockAttributes(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            String attrXpath = attributeXpathOnMassEditForm(attr, attr.getCode());

            GUITester.assertExists(GUIXpath.Div.FORM_CONTAINS + attrXpath, true,
                    String.format("Атрибут '%s' отсутствует на форме", attr.getTitle()));
            GUITester.assertExists(String.format(MASS_EDIT_BLOCK_LABEL_FIRST_ABOVE, attrXpath), false,
                    String.format("Атрибут '%s' не входит в общий блок", attr.getTitle()));
        }
    }

    /**
     * Проверить присутствие атрибутов в блоке для типа на форме массового редактирования
     * @param metaClass модель типа
     * @param attrs массив атрибутов
     */
    public static void assertMassEditBlockAttributes(MetaClass metaClass, Attribute... attrs)
    {
        String blockTitle = String.format("Только для типа %s", metaClass.getTitle());

        GUITester.assertExists(String.format(GUIXpath.Div.FORM_CONTAINS + MASS_EDIT_BLOCK_LABEL_TITLE, blockTitle),
                true,
                String.format("Блок '%s' отсутствует на форме", blockTitle));

        for (Attribute attr : attrs)
        {
            String attrXpath = attributeXpathOnMassEditForm(attr, metaClass.getFqn() + "@" + attr.getCode());

            WebElement block = tester.find(MASS_EDIT_BLOCK_LABEL_FIRST_ABOVE, attrXpath);
            Assert.assertEquals(String.format("Атрибут '%s' не расположен в блоке '%s'", attr.getTitle(), blockTitle),
                    blockTitle, block.getText());
        }
    }

    /**
     * Проверить отсутствие атрибутов в общем блоке на форме массового редактирования
     * @param attrs массив атрибутов
     */
    public static void assertMassEditBlockAttributesAbsence(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            String attrXpath = attributeXpathOnMassEditForm(attr, attr.getCode());

            if (tester.isPresence(GUIXpath.Div.FORM_CONTAINS + attrXpath))
            {
                GUITester.assertExists(String.format(MASS_EDIT_BLOCK_LABEL_FIRST_ABOVE, attrXpath), true,
                        String.format("Атрибут '%s' входит в общий блок", attr.getTitle()));
            }
        }
    }

    /**
     * Проверить заголовки блоков на форме массового редактирования    
     * @param order с учетом порядка (true/false) 
     * @param expectedTitles ожидаемые значения заголовков
     */
    public static void assertMassEditBlockTitles(boolean order, String... expectedTitles)
    {
        List<String> expected = Arrays.stream(expectedTitles).collect(toList());

        if (expected.isEmpty())
        {
            GUITester.assertCountElements(GUIXpath.Div.FORM_CONTAINS + MASS_EDIT_BLOCK_LABEL, 0);
            return;
        }

        //@formatter:off
        List<String> actual = tester.findDisplayedElements(GUIXpath.Div.FORM_CONTAINS + MASS_EDIT_BLOCK_LABEL)
            .stream().map(e -> e.getText())
            .collect(toList());
        //@formatter:on

        Assert.assertEquals("Действительное количество блоков отличается от ожидаемого", expected.size(),
                actual.size());

        for (int i = 0; i < expected.size(); ++i)
        {

            if (order)
            {
                Assert.assertEquals("Порядок блоков отличается от ожидаемого", expected.get(i), actual.get(i));
            }
            else
            {
                Assert.assertTrue("Блока '%s' не существует", actual.contains(expected.get(i)));
            }
        }

    }

    /**
     * Проверить заголовки блоков на форме массового редактирования
     * @param expected ожидаемые значения заголовков
     */
    public static void assertMassEditBlockTitles(String... expectedTitles)
    {
        assertMassEditBlockTitles(true, expectedTitles);
    }

    /**
     * Проверить присутствие атрибута
     * @param attribite - атрибут
     */
    public static void assertPresentAttribute(Attribute attribite)
    {
        String message = String.format("Атрибут %s не появился на форме изменения типа", attribite.getTitle());
        Assert.assertTrue(message, tester.waitAppear(INPUT_VALUE, attribite.getCode()));
    }

    /**
     * Проверить присутвие поля ввода комментария на форме
     */
    public static void assertPresentCommentOnForm()
    {
        GUIComment.assertCommentPresentOnForm();
    }

    /**
     * Проверить хинт (текст подсказки) в значении атрибута на форме массового редактирования
     * @param attr - ожидаемый текст подсказки
     * @param hint - ожидаемый текст подсказки
     */
    public static void assertSelectHint(Attribute attr, String hint)
    {
        WebElement element = tester.findDisplayed(String.format(GUIXpath.InputComplex.ANY_VALUE, attr.getCode()));
        Assert.assertEquals(hint, element.getAttribute("placeholder"));
    }

    /**
     * Нажать кнопку "Очистить значение во всех выбранных объектах" на форме массового редактирования для атрибута
     * @param code код атрибута
     */
    public static void clickClearAttributeMassEditForm(String code)
    {
        tester.moveMouse(String.format(GUIXpath.Any.ANY_CAPTION, code), 5, 5);
        tester.click(String.format(GUIXpath.Span.CLEAR_ICON, code));
    }

    /**
     * Нажать кнопку "Отменить редактирование атрибута на форме" на форме массового редактирования для атрибута
     * @param code код атрибута
     */
    public static void clickResetAttributeMassEditForm(String code)
    {
        tester.moveMouse(String.format(GUIXpath.Any.ANY_CAPTION, code), 5, 5);
        tester.click(String.format(GUIXpath.Span.REFRESH_ICON, code));
    }

    /**
     * Получить xpath атрибута на форме массового редактирования
     * @param attr модель атрибута
     * @param attrMassEditCode код атрибута на форме массового редактирования
     * @return xpath атрибута
     */
    private static String attributeXpathOnMassEditForm(Attribute attr, String attrMassEditCode)
    {
        switch (attr.getType())
        {
            case BooleanType.CODE:
                return String.format(GUIXpath.Any.ANY_VALUE_CONTAINS, attrMassEditCode);
            default:
                return String.format(GUIXpath.Any.ANY_CAPTION, attrMassEditCode);
        }
    }
}
