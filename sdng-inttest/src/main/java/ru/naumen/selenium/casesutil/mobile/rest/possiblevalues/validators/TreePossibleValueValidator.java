package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues.validators;

import static org.hamcrest.CoreMatchers.is;

import io.restassured.response.ValidatableResponse;
import jakarta.annotation.Nullable;
import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;

/**
 * Валидатор для возможного значения атрибута в дереве.
 *
 * <AUTHOR>
 * @since 01.08.2024
 */
public class TreePossibleValueValidator
        extends BaseTreePossibleValueValidator<TreePossibleValueValidator>
{
    static final TreePossibleValueValidator EMPTY = new TreePossibleValueValidator(null, null);

    private final Object uuid;

    TreePossibleValueValidator(@Nullable String uuid, @Nullable String title)
    {
        super(title);

        this.uuid = uuid;
    }

    @Override
    public void validate(ValidatableResponse response, String absolutePath, MobileVersion version)
    {
        if (this == EMPTY)
        {
            return;
        }

        if (uuid != null)
        {
            response.body(absolutePath + "?.uuid", is(uuid));
        }

        super.validate(response, absolutePath, version);
    }
}
