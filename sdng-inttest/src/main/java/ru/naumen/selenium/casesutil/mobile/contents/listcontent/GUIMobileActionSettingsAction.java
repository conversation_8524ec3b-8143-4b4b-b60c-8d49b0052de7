package ru.naumen.selenium.casesutil.mobile.contents.listcontent;

import static ru.naumen.selenium.casesutil.CoreTester.tester;

import org.openqa.selenium.By;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;

/**
 * Объект, предоставляющий действия, доступные при работе с конкретным действием из таблицы на форме настроек
 * действий контентов "Список вложенных объектов" и "Список связанных объектов"
 *
 * <AUTHOR>
 * @since 08.05.2024
 */
public class GUIMobileActionSettingsAction
{
    private static final String ACTION_COLUMN_XPATH = "/td[%s]";
    private static final String ACTION_COLUMN_WITH_TEXT_XPATH = ACTION_COLUMN_XPATH + "/div[text()='%s']";
    private static final String ACTION_COLUMN_WITH_CHECKBOX_XPATH = ACTION_COLUMN_XPATH + "//*[@class='%s fontIcon']";
    private static final String ACTION_ROW_DISABLED = "contains(@class, 'itemRemoved')]";
    private static final String ACTION_ROW_ENABLED = "not(contains(@class, 'itemRemoved'))]";
    private static final String YES = "yes";
    private static final String EMPTY = "empty";

    private final GUIMobileActionSettingsTable table;
    private final String actionXpath;
    private final String actionStylesXpath;

    public GUIMobileActionSettingsAction(GUIMobileActionSettingsTable table, String actionXpath,
            String actionStylesXpath)
    {
        this.table = table;
        this.actionXpath = actionXpath;
        this.actionStylesXpath = actionStylesXpath;
    }

    /**
     * Проверяет столбец "Название" выбранного действия
     *
     * @param title ожидаемое название
     */
    public GUIMobileActionSettingsAction assertTitle(String title)
    {
        String actionTitle = ACTION_COLUMN_WITH_TEXT_XPATH.formatted(
                MobileListContentActionColumns.TITLE.getColumn(), title);
        GUITester.assertExists(actionXpath + actionTitle, true);
        return this;
    }

    /**
     * Проверяет столбец "Действие" выбранного действия
     *
     * @param type ожидаемый тип действия
     */
    public GUIMobileActionSettingsAction assertType(MobileListContentActionType type)
    {
        String actionType = ACTION_COLUMN_WITH_TEXT_XPATH.formatted(
                MobileListContentActionColumns.TYPE.getColumn(), type.getActionTitle());
        GUITester.assertExists(actionXpath + actionType, true);
        return this;
    }

    /**
     * Проверяет наличие галочки в столбце "Передавать геопозицию устройства" выбранного действия
     *
     * @param isRequiredGeo true - ожидается наличие галочки, false - отсутствие галочки
     */
    public GUIMobileActionSettingsAction assertRequiredGeo(boolean isRequiredGeo)
    {
        String actionRequiredGeo = ACTION_COLUMN_WITH_CHECKBOX_XPATH.formatted(
                MobileListContentActionColumns.REQUIRED_GEOLOCATION.getColumn(), isRequiredGeo ? YES : EMPTY);
        GUITester.assertExists(actionXpath + actionRequiredGeo, true);
        return this;
    }

    /**
     * Проверяет что данные строки действия соответствуют переданным данным
     *
     * @param action объект, хранящий ожидаемые значения столбцов текущей строки действия
     */
    public GUIMobileActionSettingsAction assertAction(MobileListContentActionHolder action)
    {
        assertTitle(action.getTitle());
        assertType(action.getType());
        assertRequiredGeo(action.isRequiredGeo());
        return this;
    }

    /**
     * Проверяет, что строка активна (содержит действие, доступное в контенте)
     */
    public GUIMobileActionSettingsAction assertEnabled()
    {
        tester.waitAppear(ScrollAlignment.NEAREST, ScrollAlignment.NEAREST, actionStylesXpath + ACTION_ROW_ENABLED);
        return this;
    }

    /**
     * Проверяет, что строка не активна (содержит действие, недоступное в контенте)
     */
    public GUIMobileActionSettingsAction assertDisabled()
    {
        tester.waitAppear(ScrollAlignment.NEAREST, ScrollAlignment.NEAREST, actionStylesXpath + ACTION_ROW_DISABLED);
        return this;
    }

    /**
     * Возвращает true, если действие в строке имеет тот же тип, что и ожидается, false - в противном случае
     *
     * @param expectedType ожидаемый тип действия в строке
     */
    public boolean isType(MobileListContentActionType expectedType)
    {
        String actionType = ACTION_COLUMN_WITH_TEXT_XPATH.formatted(
                MobileListContentActionColumns.TYPE.getColumn(), expectedType.getActionTitle());
        return !tester.getWebDriver().findElements(By.xpath(actionXpath + actionType)).isEmpty();
    }

    /**
     * Удаляет действие в текущей строке таблицы. Возвращает в список действий таблицы
     */
    public GUIMobileActionSettingsTable delete()
    {
        if (!isType(MobileListContentActionType.SORT))
        {
            String actionDelete = ACTION_COLUMN_XPATH.formatted(MobileListContentActionColumns.DELETE.getColumn());
            tester.click(actionXpath + actionDelete);
        }
        return toTable();
    }

    /**
     * Возвращает в список действий таблицы
     */
    public GUIMobileActionSettingsTable toTable()
    {
        return table;
    }

    /**
     * Возвращает в список действий формы "настройки действий"
     */
    public GUIMobileListContentActionSettingsForm toForm()
    {
        return table.toForm();
    }
}
