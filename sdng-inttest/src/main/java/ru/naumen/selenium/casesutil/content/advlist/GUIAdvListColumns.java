package ru.naumen.selenium.casesutil.content.advlist;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListColumns;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.casesutil.model.ModelUtils;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;

import java.util.ArrayList;

import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.*; // NOPMD

/**
 * Утилитарные методы работы с настройкой полей advList-а
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListColumns extends GUIAdvlist
{
    private AGUIAdvListColumns asserts;

    public GUIAdvListColumns(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Добавить атрибут на форме "Настройка полей"
     */
    public void addAttr(Attribute attr)
    {
        String value = String.format(GUIXpath.SpecificComplex.POPUP_SELECT_CONTAINS, attr.getCode());
        GUISelect.selectByXpath(COLUMNS_ATTR_INPUT, value);
        clickAddAttr();
    }

    /**
     * Получение утилитарных методы для проверок, связанных с настройкой полей advList-а, через интерфейс
     * @return {@link AGUIAdvListColumns}
     */
    public AGUIAdvListColumns asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListColumns(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Нажать кнопку добавления колонки
     */
    public void clickAdd()
    {
        tester.click(getContentXpath() + COLUMNS_ADD_BUTTON);
    }

    /**
     * Нажать кнопку добавления атрибута на форме "Настройка полей"
     */
    public void clickAddAttr()
    {
        tester.click(COLUMNS_ADD_ATTR_BUTTON);
    }

    /**
     * Настроить список полей для отображения
     * (должна быть открыта форма "Настройка полей")
     * @param attributes модели атрибутов, которые должны отображаться
     */
    public void customizeColumn(Attribute... attributes)
    {
        Assert.assertTrue("Поле для выбора атрибутов отсутствует на форме 'Настройка полей'",
                tester.waitAppear(COLUMNS_ATTR_INPUT));
        //Добавляем поля
        for (Attribute attr : attributes)
        {
            if (!tester.isPresence(COLUMNS_ATTR, attr.getCode()))
            {
                addAttr(attr);
            }
        }
        //Удаляем все не нужные поля
        List<String> attrCodes = ModelUtils.getCodes(attributes);
        List<String> rowIds = GUITester.getAttributePropertyElements(COLUMNS_ATTRS, "id");
        List<String> currentOrder = new ArrayList<>();
        for (String id : rowIds)
        {
            String code = StringUtils.substringAfter(id, "@");
            if (!attrCodes.contains(code))
            {
                tester.click(COLUMNS_ATTR_DELETE, code);
            }
            else
            {
                currentOrder.add(code);
            }
        }
        //Сортируем (пузырьком)
        Assert.assertEquals("Ожидаемое кол-во атрибутов не совпало с полученным.", attrCodes.size(),
                currentOrder.size());
        for (String code : attrCodes)
        {
            int index = currentOrder.indexOf(code);
            currentOrder.remove(code);
            for (int i = 0; i < index; i++)
            {
                tester.click(COLUMNS_ATTR_MOVE_UP, code);
            }
        }
    }

    /**
     * Удалить атрибуты для отображения на форме настройки полей
     * (должна быть открыта форма "Настройка полей")
     * @param attrs набор моделей атрибутов, которые будут удалены
     */
    public void deleteAttrsOnDialog(Attribute... attrs)
    {
        for (Attribute attr : attrs)
        {
            tester.click(COLUMNS_ATTR_DELETE, attr.getCode());
        }
    }

    /**
     * Опустить атрибут на форме настройки полей
     * @param attr модель атрибута который нужно опустить
     */
    public void moveDownAttrOnDialog(Attribute attr)
    {
        tester.click(COLUMNS_ATTR_MOVE_DOWN, attr.getCode());
    }

    /**
     * Поднять атрибут на форме настройки полей
     * @param attr модель атрибута который нужно поднять
     */
    public void moveUpAttrOnDialog(Attribute attr)
    {
        tester.click(COLUMNS_ATTR_MOVE_UP, attr.getCode());
    }

    /**
     * Выбрать представление для атрибутов на форме "Настройка полей"
     * @param attr модель атрибута, в котором необходимо установить представление
     * @param presentation код представления атрибута
     */
    public void selectPresentation(Attribute attr, String presentation)
    {
        GUISelect.select(COLUMNS_ATTR_PRESENTATION, presentation, attr.getCode());
    }
}
