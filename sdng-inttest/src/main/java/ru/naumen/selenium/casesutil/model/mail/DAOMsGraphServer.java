package ru.naumen.selenium.casesutil.model.mail;

import ru.naumen.selenium.casesutil.model.ModelFactory;
import ru.naumen.selenium.casesutil.model.ModelUtils;

/**
 * Утилитарные методы для работы с моделями данных относящихся к MS Graph серверу
 *
 * <AUTHOR>
 * @since 20 сент. 2024
 */
public class DAOMsGraphServer
{
    /**
     * Создать модель MS Graph сервера с настройками по-умолчанию
     * (Модель регистрируется в очередь на удаление)
     * @return возвращает модель MS Graph сервера
     */
    public static MsGraphServer create()
    {
        return create(fillSendMailParams(new SendMailParam()));
    }

    /**
     * Создать модель MS Graph сервера с заданными настройками
     * (Модель регистрируется в очередь на удаление)
     * @param params настройки сервера
     * @return возвращает модель MS Graph сервера
     */
    public static MsGraphServer create(SendMailParam params)
    {
        return create(
                ModelUtils.createText(10),
                Boolean.TRUE,
                ModelUtils.createLogin(),
                ModelUtils.createPassword(),
                Boolean.FALSE,
                Boolean.FALSE,
                fillSendMailParams(params));
    }

    /**
     * Создать модель MS Graph сервера
     * (Модель регистрируется в очередь на удаление)
     * @param server адрес сервера
     * @param auth нужна ли авторизация
     * @param login логин
     * @param password пароль
     * @param enable включен/выключен
     * @param defaultConnection является ли соединением по-умолчанию
     * @param params параметры отправки писем
     * @return возвращает модель MS Graph сервера
     */
    public static MsGraphServer create(String server, boolean auth, String login,
            String password, boolean enable, boolean defaultConnection, SendMailParam params)
    {
        MsGraphServer model = ModelFactory.create(MsGraphServer.class);
        model.setServer(server);
        model.setAuth(String.valueOf(auth));
        model.setLogin(login);
        model.setPassword(password);
        model.setDefault(defaultConnection);
        model.setEnable(String.valueOf(enable));
        model.setParams(params);
        model.setClientId(ModelUtils.createCode());
        model.setApplicationId(ModelUtils.createCode());
        return model;
    }

    /**
     * Заполнить модель для настроек исходящей почты параметрами по-умолчанию
     * @return возвращает модель настроек исходящей почты
     */
    private static SendMailParam fillSendMailParams(SendMailParam params)
    {
        params.setFeedbackAddress(ModelUtils.createEmail());
        params.setSystemMail(ModelUtils.createEmail());
        params.setSenderName("");
        return params;
    }
}