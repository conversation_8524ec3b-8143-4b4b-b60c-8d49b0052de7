package ru.naumen.selenium.casesutil.content;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.openqa.selenium.WebElement;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.core.WaitTool;

/**
 * Утилитарные методы для работы с контентов "История изменений объекта" в интерфейсе оператора
 * <AUTHOR>
 * @since 22.08.2012
 */
public class GUIEventList extends CoreTester
{
    public static final int MAX_ROW_IN_CONTENT = 2500;

    /**
     * Колонки, присуствтующие в контенте
     */
    public interface ColumnId
    {
        /**Колонка "Описание"*/
        String COLUMN_ID_EVENT_MESSAGES = "eventMessages";
        /**Колонка "IP адрес"*/
        String COLUMN_ID_EVENT_IP = "eventIP";
        /**Колонка "Aвтор события"*/
        String COLUMN_ID_EVENT_SENDER_TITLE = "senderTitle";
        /**Колонка "Скрипт"*/
        String COLUMN_ID_EVENT_SCRIPT = "script";
        /**Колонка "Событие"*/
        String COLUMN_ID_EVENT_CATEGORY = "eventCategoryName";
    }

    public static final String EVENT_ACTION_USER = "eventActionProcessor";
    public static final String SYSTEM_USER = "system";
    public static final String FAST_EDIT_OBJ_CAT_CODE = "fastEdit";
    public static final String EDIT_OBJ_CAT_CODE = "edit";

    /**
     * Поиск в eventList'е с переданными параметрами(представление: Сложный список)
     * @param content модель контента
     * @param params параметры для поиска события
     */
    public static void assertEventAbsence(ContentForm content, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIEventListXpath.X_LIST, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        GUITester.assertElementNotExistWithRefresh(resultXpath.toString(), WaitTool.ITERATION_WAIT_TIME);
    }

    /**
     * Проверить отсутствие в списке событий(адвлист) указанного события
     * @param content модель контента
     * @param params параметры для поиска события
     */
    public static void assertEventAdvlistAbsence(ContentForm content, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIXpath.Div.ID_PATTERN + GUIAdvListXpath.CONTENT_ROWS, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        GUITester.assertElementNotExistWithRefresh(resultXpath.toString(), WaitTool.ITERATION_WAIT_TIME);
    }

    /**
     * Проверить присутствие в списке событий(адвлист) указанного события
     * @param content модель контента
     * @param params параметры для поиска события
     */
    public static void assertEventAdvlistPresent(ContentForm content, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIXpath.Div.ID_PATTERN + GUIAdvListXpath.CONTENT_ROWS, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        GUITester.assertElementExistsWithRefresh(resultXpath.toString(), WaitTool.ITERATION_WAIT_TIME);
    }

    /**
     * Проверить количество записей ,определенной категории события, в контенте 'История изменений объекта'
     * c fromIndex по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @param expected ожидаемое количество событий определенной категории
     */
    public static void assertEventCategoryCount(ContentForm content, int fromIndex, int toIndex, String eventCategory,
            int expected)
    {
        // если записей в контенте нет вообще, то проверка должна проходить
        boolean rowsPresence = tester.isPresence(GUIEventListXpath.X_ROWS_IN_EVENT_LIST_DEFAULT, content.getXpathId());
        if (!rowsPresence && expected == 0)
        {
            return;
        }

        String msg = String.format(
                "Количество записей с событием %s в контенте 'История изменений объекта' не совпало с ожидаемым",
                eventCategory);
        Assert.assertEquals(msg, expected, getRowByCategory(content, fromIndex, toIndex, eventCategory).size());
    }

    /**
     * Проверить количество записей ,определенной категории события, в контенте 'История изменений объекта'.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param eventCategory категория события
     * @param expected ожидаемое количество событий определенной категории
     */
    public static void assertEventCategoryCount(ContentForm content, String eventCategory, int expected)
    {
        assertEventCategoryCount(content, 0, MAX_ROW_IN_CONTENT, eventCategory, expected);
    }

    /**
     * Проверить что отсутствуют события определенной категории , в контенте 'История изменений объекта'
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param eventCategory категория события
     */
    public static void assertEventCategoryIsAbsence(ContentForm content, String eventCategory)
    {
        String msg = String.format("В контенте 'История изменений объекта' в колонке 'Событие' присутствуют записи: %s",
                eventCategory);
        Assert.assertTrue(msg, getRowByCategory(content, 0, MAX_ROW_IN_CONTENT, eventCategory).isEmpty());
    }

    /**
     * Проверить что встречается хотябы одно событие определенной категории , в контенте 'История изменений объекта'
     * c fromIndex по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     */
    public static void assertEventCategoryIsPresent(ContentForm content, int fromIndex, int toIndex,
            String eventCategory)
    {
        String msg = String.format("В контенте 'История изменений объекта' в колонке 'Событие' отсутствуют записи: %s",
                eventCategory);
        Assert.assertFalse(msg, getRowByCategory(content, fromIndex, toIndex, eventCategory).isEmpty());
    }

    /**
     * Проверить что встречается хотябы одно событие определенной категории ,
     * в контенте 'История изменений объекта' в течении времени ожидания.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)оку
     * @param eventCategoryName категория события
     */
    public static void assertEventCategoryIsPresentWithWait(ContentForm content, String eventCategoryName)
    {
        long currentTime = System.currentTimeMillis();
        boolean notExists;
        do
        {
            WaitTool.waitMills(250);
            tester.refresh();
            notExists = getRowByCategory(content, 0, MAX_ROW_IN_CONTENT, eventCategoryName).isEmpty();
        }
        while (notExists && System.currentTimeMillis() - currentTime < WaitTool.WAIT_TIME * 1000);
        String msg = String.format("В контенте 'История изменений объекта' в колонке 'Событие' отсутствуют записи: %s",
                eventCategoryName);
        Assert.assertFalse(msg, notExists);
    }

    /**
     * Проверить ip адреса в контенте 'История изменений объекта' указанного события c fromIndex по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @param ipAddress ожидаемый ip адрес
     */
    public static void assertEventIpAddress(ContentForm content, int fromIndex, int toIndex, String eventCategory,
            String ipAddress)
    {
        assertEventCategoryIsPresent(content, fromIndex, toIndex, eventCategory);
        Set<String> messages = getEventIpAddressByCategory(content, fromIndex, toIndex, eventCategory);
        String msg = String.format(
                "Ожидаемый ip адрес (%s) не найден в сообщениях типа '%s' в контенте 'История изменений объекта' не "
                + "найдено. ",
                ipAddress, eventCategory);
        Assert.assertTrue(msg, messages.contains(ipAddress));
    }

    /**
     * Проверить сообщения в контента 'История изменений объекта' указанного события c fromIndex по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @param message ожидаемое сообщение
     * @param alternativeMessages альтернативные ожидаемые сообщения
     */
    public static void assertEventMessages(ContentForm content, int fromIndex, int toIndex, String eventCategory,
            String message, String... alternativeMessages)
    {
        assertEventCategoryIsPresent(content, fromIndex, toIndex, eventCategory);
        Set<String> messages = getEventMessagesByCategory(content, fromIndex, toIndex, eventCategory);
        String msg;
        if (alternativeMessages.length == 0)
        {
            msg = String.format(
                    "Ожидаемое сообщение типа '%s' в контенте 'История изменений объекта' не найдено. \n"
                    + "Ожидаемое сообщение: %s; \nСообщения в которых велся поиск: %s",
                    eventCategory, message, messages);
            Assert.assertTrue(msg, messages.contains(message));
        }
        else
        {
            Set<String> expectedMsgs = Sets.newHashSet(alternativeMessages);
            expectedMsgs.add(message);
            msg = String.format(
                    "Ни одно из ожидаемых сообщений типа '%s' в контенте 'История изменений объекта' не найдено. \n"
                    + "Ожидаемые сообщения: %s; \nСообщения в которых велся поиск: %s",
                    eventCategory, expectedMsgs, messages);
            messages.retainAll(expectedMsgs);
            Assert.assertFalse(msg, messages.isEmpty());
        }
    }

    /**
     * Проверить сообщения в контента 'История изменений объекта' указанного события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param eventCategory категория события
     * @param message ожидаемое сообщение
     * @param alternativeMessages альтернативные ожидаемые сообщения
     */
    public static void assertEventMessages(ContentForm content, String eventCategory, String message,
            String... alternativeMessages)
    {
        assertEventMessages(content, 0, MAX_ROW_IN_CONTENT, eventCategory, message, alternativeMessages);
    }

    /**
     * Проверить сообщения в контента 'История изменений объекта' указанного события c fromIndex по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @param message ожидаемое сообщение являющееся регулярным выражением
     */
    public static void assertEventMessagesAsRegex(ContentForm content, int fromIndex, int toIndex, String eventCategory,
            String message)
    {
        assertEventCategoryIsPresent(content, fromIndex, toIndex, eventCategory);
        Set<String> messages = getEventMessagesByCategory(content, fromIndex, toIndex, eventCategory);
        String msg = String.format(
                "Ожидаемое сообщение типа '%s' в контенте 'История изменений объекта' не найдено. "
                + "Ожидаемое сообщение: %s; Сообщения в которых велся поиск: %s",
                eventCategory, message, messages);
        Pattern pattern = Pattern.compile(message);
        boolean match = false;
        for (String actual : messages)
        {
            match = pattern.matcher(actual).matches();
            if (match)
            {
                break;
            }
        }
        Assert.assertTrue(msg, match);
    }

    /**
     * Проверить сообщения в контента 'История изменений объекта' указанного события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param eventCategory категория события
     * @param message ожидаемое сообщение являющееся регулярным выражением
     */
    public static void assertEventMessagesAsRegex(ContentForm content, String eventCategory, String message)
    {
        assertEventMessagesAsRegex(content, 0, MAX_ROW_IN_CONTENT, eventCategory, message);
    }

    /**
     * Проверить присутствие в списке событий указанного события
     * @param content модель контента
     * @param params параметры для поиска события
     */
    public static void assertEventPresent(ContentForm content, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIEventListXpath.X_LIST, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        GUITester.assertElementExistsWithRefresh(resultXpath.toString());
    }

    /**
     * Проверить присутствие в списке событий(адвлист) наличие определенного количества указанного события.
     * @param content модель контента
     * @param count количество указанного события, которое должно быть в списке. Если 0 или меньше 0, то проверяется,
     *             что событие отсутствует
     * @param params параметры для поиска события
     */
    public static void assertEventsCount(ContentForm content, int count, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIXpath.Div.ID_PATTERN + GUIAdvListXpath.CONTENT_ROWS, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        String contentXpath = String.format(GUIXpath.Div.ID_PATTERN, content.getXpathId());
        GUITester.assertElementsCountWithRefresh(contentXpath, resultXpath.toString(), count);
    }

    /**
     * Проверить часть сообщения в контента 'История изменений объекта' указанного события c fromIndex по toIndex
     * строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @param message ожидаемое сообщение
     */
    public static void assertPartOfEventMessages(ContentForm content, int fromIndex, int toIndex, String eventCategory,
            String message)
    {
        assertEventCategoryIsPresent(content, fromIndex, toIndex, eventCategory);
        Set<String> messages = getEventMessagesByCategory(content, fromIndex, toIndex, eventCategory);
        for (String currentMessage : messages)
        {
            if (currentMessage.contains(message))
            {
                return;
            }
        }
        String msg = String.format(
                "Ожидаемое сообщение типа '%s' в контенте 'История изменений объекта' не найдено. "
                + "Ожидаемое сообщение: %s; Сообщения в которых велся поиск: %s",
                eventCategory, message, messages);
        Assert.fail(msg);
    }

    /**
     * Проверить часть сообщения в контента 'История изменений объекта' указанного события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param eventCategory категория события
     * @param message ожидаемое сообщение
     */
    public static void assertPartOfEventMessages(ContentForm content, String eventCategory, String message)
    {
        assertPartOfEventMessages(content, 0, MAX_ROW_IN_CONTENT, eventCategory, message);
    }

    /**
     * Кликнуть на первую найденную ссылку поля "Скрипт" по названию title из контента 'История изменений объекта'
     * @param content модель контента 'История изменений объекта
     * @param title название скрипта (гиперссылки)
     */
    public static void clickEventScript(ContentForm content, String title)
    {
        tester.click("(" + GUIEventListXpath.CELL_SCRIPT_IN_EVENT_LIST + ")[1]", content.getXpathId(), title);
    }

    /**
     * Кликнуть чекбокс "Показывать историю вложенных объектов контента"
     * @param content модель контента
     */
    public static void clickShowRelated(ContentForm content)
    {
        tester.click(GUIEventListXpath.X_SHOW_RELATED_CHECKBOX_LABEL, content.getXpathId());
    }

    /**
     * Получает названия колонок, которые выводятся в контенте в списке.
     * @param content текущий контент
     * @return строку с именами колонок.
     */
    public static String getColumnNames(ContentForm content)
    {
        return tester.getText("//*[@id='%s']//*[@id='gwt-debug-outer']//thead", content.getXpathId());
    }

    /**
     * Получить данные из определенной строки в контенте 'История изменений объекта'.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param rowNumber номер строки (0 строка соответствует наивысшей строке в контенте)
     * @return возвращает отображение (Map) id столбцов в данные хранящиеся в соответствующих столбцах указанной строки.
     */
    public static Map<String, String> getDataFromRow(ContentForm content, int rowNumber)
    {
        List<WebElement> elements = tester.findElements(GUIEventListXpath.X_ROW_ELEMENTS_IN_EVENT_LIST_DEFAULT,
                content.getXpathId(),
                rowNumber + 1);
        Map<String, String> result = Maps.newHashMapWithExpectedSize(elements.size());
        for (WebElement el : elements)
        {
            String key = StringUtils.substringAfter(el.getAttribute("__did"), "event@").trim();
            result.put(key.isEmpty() ? "unknown" : key, el.getText());
        }
        return result;
    }

    /**
     * Получить набор ip адресов из контента 'История изменений объекта' с fromIndex
     * строки контента по toIndex строку с заданной категорией события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @return возвращает набор сообщений с fromIndex строки контента по toIndex строку по заданой категорией события.
     */
    private static Set<String> getEventIpAddressByCategory(ContentForm content, int fromIndex, int toIndex,
            final String eventCategory)
    {
        return getRowByCategory(content, fromIndex, toIndex, eventCategory).stream()
                .map(input -> input != null ? input.get(ColumnId.COLUMN_ID_EVENT_IP) : null)
                .collect(Collectors.toSet());
    }

    /**
     * Получить набор сообщений из контента 'История изменений объекта' с fromIndex
     * строки контента по toIndex строку с заданной категорией события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @return возвращает набор сообщений с fromIndex строки контента по toIndex строку по заданой категорией события.
     */
    public static Set<String> getEventMessagesByCategory(ContentForm content, int fromIndex, int toIndex,
            final String eventCategory)
    {
        return getRowByCategory(content, fromIndex, toIndex, eventCategory).stream()
                .map(input -> input != null ? input.get(ColumnId.COLUMN_ID_EVENT_MESSAGES) : null)
                .collect(Collectors.toSet());
    }

    /**
     * Получить набор значений для поля "Скрипт" из контента 'История изменений объекта' с fromIndex
     * строки контента по toIndex строку.
     * @param content модель контента 'История изменений объекта
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @return возвращает набор значений для поля "Скрипт" с fromIndex строки контента по toIndex строку.
     */
    public static List<String> getEventScript(ContentForm content, int fromIndex, int toIndex)
    {
        return getRowsFromIndexToIndex(content, fromIndex, toIndex).stream()
                .map(input -> input != null ? input.get(ColumnId.COLUMN_ID_EVENT_SCRIPT) : null)
                .collect(Collectors.toList());
    }

    /**
     * Получить все видимые строки из контента 'История изменений объекта' с fromIndex
     * строки контента по toIndex строку с заданой категорией события.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @param eventCategory категория события
     * @return возвращает список отображений (Map) id столбцов в данные хранящиеся в соответствующих столбцах, с
     * fromIndex
     * строки контента по toIndex строку с заданой категорией события.
     */
    public static List<Map<String, String>> getRowByCategory(ContentForm content, int fromIndex, int toIndex,
            final String eventCategory)
    {
        return getRowsFromIndexToIndex(content, fromIndex, toIndex).stream()
                .filter(input ->
                {
                    if (input == null || input.get(ColumnId.COLUMN_ID_EVENT_CATEGORY) == null || eventCategory == null)
                    {
                        return false;
                    }
                    return input.get(ColumnId.COLUMN_ID_EVENT_CATEGORY).equals(eventCategory);
                })
                .toList();
    }

    /**
     * Проверить параметры события в контенте "История изменений объекта".
     *
     * @param content модель контента "История изменений объекта" (Простое представление контента)
     * @param index индекс строки
     * @param expectedParams ожидаемые параметры события (проверяются только указанные параметры)
     */
    public static void assertEventParams(ContentForm content, int index, Map<String, String> expectedParams)
    {
        Map<String, String> actualData = getRowsFromIndexToIndex(content, index, index + 1).get(0);
        for (Map.Entry<String, String> param : expectedParams.entrySet())
        {
            Assert.assertEquals(param.getValue(), actualData.get(param.getKey()));
        }
    }

    /**
     * Получить все видимые строки из контента 'История изменений объекта' с fromIndex
     * строки контента по toIndex строку.
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @param fromIndex строка с которой идет поиск (0 - соответствует наивысшей строке контента), включая данную строку
     * @param toIndex строка по которую идет поиск, исключая данную строку
     * @return возвращает список отображений (Map) id столбцов в данные хранящиеся в соответствующих столбцах, с
     * fromIndex
     * строки контента по toIndex строку.
     */
    public static List<Map<String, String>> getRowsFromIndexToIndex(ContentForm content, int fromIndex, int toIndex)
    {
        if (fromIndex > toIndex || fromIndex < 0)
        {
            return List.of();
        }

        int rowsNumber = tester.findElements(GUIEventListXpath.X_ROWS_IN_EVENT_LIST_DEFAULT, content.getXpathId())
                .size();
        if (fromIndex >= rowsNumber)
        {
            return List.of();
        }
        int maxIndex = Math.min(toIndex, rowsNumber);

        List<Map<String, String>> result = new ArrayList<>(maxIndex - fromIndex);
        for (int index = fromIndex; index < maxIndex; index++)
        {
            result.add(getDataFromRow(content, index));
        }
        return result;
    }

    /**
     * Получить все видимые строки из контента 'История изменений объекта'
     * @param content модель контента 'История изменений объекта' (Простое представление контента)
     * @return возвращает список отображений (Map) id столбцов в данные хранящиеся в соответствующих столбцах.
     */
    public static List<Map<String, String>> getAllRows(ContentForm content)
    {
        int rowsNumber = tester.findElements(GUIEventListXpath.X_ROWS_IN_EVENT_LIST_DEFAULT, content.getXpathId())
                .size();
        List<Map<String, String>> result = new ArrayList<>(rowsNumber);
        for (int index = 0; index < rowsNumber; index++)
        {
            result.add(getDataFromRow(content, index));
        }
        return result;
    }

    /**
     * Проверить присутствие в списке событий указанного события
     * @param content модель контента
     * @param params параметры для поиска события
     * @return true - событие присутствует, false - событие не присутствует
     */
    public static boolean isEventPresent(ContentForm content, String... params)
    {
        StringBuilder resultXpath = new StringBuilder();
        resultXpath.append(String.format(GUIEventListXpath.X_LIST, content.getXpathId()));
        for (String param : params)
        {
            resultXpath.append(String.format("[contains(.,\"%s\")]", param));
        }
        return GUITester.isElementExistsWithRefresh(resultXpath.toString());
    }
}
