package ru.naumen.selenium.casesutil.mobile.rest.possiblevalues;

import static ru.naumen.selenium.casesutil.mobile.rest.MobileVersion.FROM_V15;
import static ru.naumen.selenium.casesutil.mobile.rest.forms.FormType.FORMS_WITH_CODE;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.base.Preconditions;

import ru.naumen.selenium.casesutil.mobile.rest.MobileVersion;
import ru.naumen.selenium.casesutil.mobile.rest.forms.DSLMobileForms;
import ru.naumen.selenium.casesutil.mobile.rest.forms.FormType;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.bo.Bo;
import ru.naumen.selenium.casesutil.model.catalog.Catalog;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.metaclass.MetaClass;
import ru.naumen.selenium.casesutil.model.mobile.AbstractMobileView;

/**
 * Билдер для параметров перевычисления формы в МК АПИ
 *
 * <AUTHOR>
 * @since Nov 19, 2019
 */
@SuppressWarnings("java:S1845")
public class MobilePossibleValuesParams
{
    private static final String UUID = "uuid";
    private static final String ATTRIBUTE_CODE = "attributeCode";
    private static final String CODE = "code";
    private static final String OFFSET = "offset";
    private static final String FIRST_RESULT = "firstResult";
    private static final String ATTRIBUTES = "attributes";
    private static final String POSITIONS = "positions";
    private static final String SEARCH_STRING = "searchString";
    private static final String PARENT = "parent";
    private static final String FORM_CODE = "formCode";
    private static final String CONTENT_CODE = "contentCode";
    private static final String FORM_TYPE = "formType";

    private String uuid;
    private final String attributeCode;
    private Integer offset;
    private Map<String, Integer> positions;
    private String searchString;
    private String parent;
    private String formCode;
    private final Map<String, Object> attributes;
    private String formType;

    /**
     * Создать билдер для тела запроса для возможных значений
     *
     * @param attribute атрибут, для которого запрашиваются возможные значения
     * @param objectTemplate значения атрибутов объекта с формы
     */
    public static MobilePossibleValuesParams create(Attribute attribute, Map<String, Object> objectTemplate)
    {
        return new MobilePossibleValuesParams(attribute, objectTemplate);
    }

    private MobilePossibleValuesParams(Attribute attribute, Map<String, Object> attributes)
    {
        this.attributeCode = attribute.getCode();
        this.attributes = attributes;
    }

    /**
     * Установить объект, для которого выполняется перевычисление (для всех форм кроме формы добавления)
     *
     * @param object объект
     * @return this
     */
    public MobilePossibleValuesParams setObject(Bo object)
    {
        this.uuid = object.getUuid();
        return this;
    }

    /**
     * Установить форму, для которой выполняется перевычисление (для форм добавления и редактирования)
     *
     * @param mobileView форма
     * @return this
     */
    public MobilePossibleValuesParams setForm(AbstractMobileView<?> mobileView)
    {
        this.formCode = mobileView.getCode();
        this.formType = DSLMobileForms.getFormType(mobileView).getType();
        return this;
    }

    /**
     * Установить код формы (или идентификатор пользовательского события), для которой выполняется перевычисление
     *
     * @param formCode код контента
     * @return this
     */
    public MobilePossibleValuesParams setFormCode(String formCode)
    {
        this.formCode = formCode;
        return this;
    }

    /**
     * Установить форму, для которой выполняется перевычисление (для форм без кода контента и формы
     * заполнения параметров ДПС)
     *
     * @param formType тип формы
     * @return this
     */
    public MobilePossibleValuesParams setFormType(FormType formType)
    {
        Preconditions.checkArgument(!FORMS_WITH_CODE.contains(formType), "Use setForm");

        this.formType = formType.getType();
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка всех найденных объектов
     *
     * @param offset значение сдвига
     */
    public MobilePossibleValuesParams setOffset(int offset)
    {
        this.offset = offset;
        return this;
    }

    /**
     * Устанавливает сдвиг относительно начала списка для метакласса
     *
     * @param metaClass метакласс
     * @param position значение сдвига
     */
    public MobilePossibleValuesParams addPosition(MetaClass metaClass, int position)
    {
        Preconditions.checkArgument(!metaClass.isCase(), "Must be a class");
        return addPosition(metaClass.getFqn(), position);
    }

    /**
     * Устанавливает сдвиг относительно начала списка для ЭС/НЭС с представлением в виде дерева
     *
     * @param catalog каталог
     * @param position значение сдвига
     */
    public MobilePossibleValuesParams addPosition(Catalog catalog, int position)
    {
        return addPosition(catalog.getCode(), position);
    }

    /**
     * Устанавливает сдвиг относительно начала списка для папок
     *
     * @param position значение сдвига
     */
    public MobilePossibleValuesParams addFolderPosition(int position)
    {
        return addPosition(CatalogItem.IS_FOLDER, position);
    }

    private MobilePossibleValuesParams addPosition(String fqn, int position)
    {
        if (this.positions == null)
        {
            this.positions = new HashMap<>();
        }
        this.positions.put(fqn, position);

        return this;
    }

    /**
     * Устанавливает родителя для получения дочерних узлов.
     * Предназначен для получения дочерних узлов ссылочных (СБО, НБО, ОС), агрегирующих (включая Ответственного)
     * атрибутов, справочников (ЭС, НЭС) и Соглашения/услуги.
     *
     * @param object родитель (бизнес-объект, элемент справочника или папка)
     */
    public MobilePossibleValuesParams setParent(ModelUuid object)
    {
        return setParent(object.getUuid());
    }

    /**
     * Устанавливает родителя для получения дочерних узлов.
     * Предназначен для получения дочерних узлов НТК и типа объекта.
     *
     * @param metaClass родительский метакласс
     */
    public MobilePossibleValuesParams setParent(MetaClass metaClass)
    {
        return setParent(metaClass.getFqn());
    }

    /**
     * Устанавливает родителя для получения дочерних узлов.
     * Предназначен для получения дочерних узлов атрибута Соглашения/услуга в плоском представлении.
     *
     * @param parent родитель
     */
    public MobilePossibleValuesParams setParent(String parent)
    {
        this.parent = parent;
        return this;
    }

    /**
     * Устанавливает текст поискового запроса
     *
     * @param searchString текст поискового запроса
     */
    public MobilePossibleValuesParams setSearchString(String searchString)
    {
        this.searchString = searchString;
        return this;
    }

    /**
     * Построить тело запроса для возможных значений
     */
    Map<String, Object> toBody(MobileVersion version)
    {
        Map<String, Object> pvProperties = new HashMap<>();
        if (uuid != null)
        {
            pvProperties.put(UUID, uuid);
        }
        if (FROM_V15.contains(version))
        {
            pvProperties.put(ATTRIBUTE_CODE, attributeCode);
        }
        else
        {
            pvProperties.put(CODE, attributeCode);
        }
        if (offset != null)
        {
            if (FROM_V15.contains(version))
            {
                pvProperties.put(OFFSET, offset);
            }
            else
            {
                pvProperties.put(FIRST_RESULT, offset);
            }
        }
        if (positions != null)
        {
            pvProperties.put(POSITIONS, positions);
        }
        if (searchString != null)
        {
            pvProperties.put(SEARCH_STRING, searchString);
        }
        if (formCode != null)
        {
            if (FROM_V15.contains(version))
            {
                pvProperties.put(FORM_CODE, formCode);
            }
            else
            {
                pvProperties.put(CONTENT_CODE, formCode);
            }
        }
        if (parent != null)
        {
            pvProperties.put(PARENT, List.of(parent));
        }
        pvProperties.put(ATTRIBUTES, attributes);
        if (formType != null)
        {
            pvProperties.put(FORM_TYPE, formType);
        }
        return pvProperties;
    }
}
