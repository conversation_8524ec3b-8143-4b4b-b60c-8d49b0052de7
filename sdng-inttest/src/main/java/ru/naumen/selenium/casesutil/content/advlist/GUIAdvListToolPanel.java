package ru.naumen.selenium.casesutil.content.advlist;

import java.io.File;

import org.junit.Assert;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.GUIExportUtils;
import ru.naumen.selenium.casesutil.bo.GUIButtonBar;
import ru.naumen.selenium.casesutil.content.GUIContent;
import ru.naumen.selenium.casesutil.content.advlist.asserts.AGUIAdvListToolPanel;
import ru.naumen.selenium.casesutil.interfaceelement.GUISelect;
import ru.naumen.selenium.core.WebTester.ScrollAlignment;

/**
 * Утилитарные методы работы с панелью инструментов advList-а
 * <AUTHOR>
 * @since 25.05.2015
 */
public class GUIAdvListToolPanel extends GUIAdvlist
{
    private AGUIAdvListToolPanel asserts;
    private GUIAdvListFiltering filtering;
    private GUIAdvListSorting sorting;
    private GUIAdvListCopyLink copyLink;

    public GUIAdvListToolPanel(String contentXpath)
    {
        super(contentXpath);
    }

    /**
     * Утилитарные средства для работы с формой "Копирование ссылки"
     */
    public class GUIAdvListCopyLink
    {

        /**
         * Кликнуть по чекбоксу "Отобразить список на отдельной странице"
         */
        public GUIAdvListCopyLink clickShowListInNewWindow()
        {
            tester.click(String.format(GUIXpath.Other.ANY_VALUE_LABEL, "showListInNewWindow"));
            Assert.assertTrue("На форме \"Копирование ссылки\" не отобразилось текстовое поле",
                    tester.waitAppear(GUIXpath.Any.URL_TEXT_AREA));
            return copyLink;
        }

        /**
         * Получить текстовое значение ссылки
         */
        public String getLink()
        {
            return tester.getElement(GUIXpath.Any.URL_TEXT_AREA).getAttribute("value");
        }
    }

    /**
     * Проверить отсутствие иконки "Добавить пользовательский инструмент" к атрибуту
     * @param attributeCode код атрибута
     */
    public void assertAbsenceButtonAddToolToAttr(String attributeCode)
    {
        tester.scrollIntoView(getContentXpath());
        tester.moveTo(getContentXpath());
        tester.moveTo(getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL, attributeCode);
        tester.waitDisappear(getContentXpath() + GUIAdvListXpath.ADD_ATTRIBUTE_TOOL);
        GUITester.assertExists(getContentXpath() + GUIAdvListXpath.ADD_ATTRIBUTE_TOOL, false,
                "Иконка \"Добавить пользовательский инструмент\" присутствует");
    }

    /**
     * Проверить что инструмент включен
     * @param toolTitle название контрола
     */
    public void assertAttributeToolButtonEnabledByTitle(String attributeCode, String toolTitle)
    {
        String opacity = "1";
        String value = GUITester.waitCssValue(
                getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL + "/parent::*" + GUIXpath.Any.TEXT_PATTERN,
                "opacity", opacity,
                attributeCode, toolTitle);
        Assert.assertTrue(String.format("Инструмент %s не включен", toolTitle), opacity.equals(value));
    }

    /**
     * Проверить невидимость кнопки, определенной заголовком в панеле операций атрибута
     * @param attributeCode код атрибута, в панели которого находится инструмент
     * @param buttonTitle заголовок кнопки
     */
    public void assertAttributeToolButtonInvisibleByTitle(String attributeCode, String buttonTitle)
    {
        tester.waitDisappear(
                getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL + "/parent::*" + GUIXpath.Any.TEXT_PATTERN,
                attributeCode, buttonTitle);
    }

    /**
     * Проверить видимость кнопки, определенной заголовком в панеле операций атрибута
     * @param attributeCode код атрибута, в панели которого находится инструмент
     * @param buttonTitle заголовок кнопки
     */
    public void assertAttributeToolButtonVisibleByTitle(String attributeCode, String buttonTitle)
    {
        GUITester.assertPresent(String.format(getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL + "/parent::*" +
                                              GUIXpath.Any.TEXT_PATTERN, attributeCode, buttonTitle),
                "Кнопка с указанным заголовком не "
                + "отображается");
    }

    /**
     * Проверить отсутствие кнопки "Экспорт" в списке
     */
    public void assertExportAbsence()
    {
        asserts().buttonsAbsence(GUIAdvListXpath.BTN_EXPORT);
    }

    /**
     * Проверить наличие кнопки "Экспорт" в списке
     */
    public void assertExportPresence()
    {
        asserts().buttonsPresence(GUIAdvListXpath.BTN_EXPORT);
    }

    /**
     * Проверить, что в панели действий имеется иконка с указанным кодом и хинтом
     * @param attrCode код атрибута
     * @param iconCode код иконки
     * @param hint хинт иконки
     */
    public void assertIconWithHint(String attrCode, String iconCode, String hint)
    {
        GUITester.assertPresent(getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL
                                + "/..//div[@title='%s']" + Div.ANY_CODE_CONTAINS,
                "Кнопка с укзанными атрибутами отсутствует",
                attrCode, hint, iconCode);
    }

    /**
     * Проверить активность режима разметки
     * @param active должен ли быть активен
     */
    public void assertLayoutMode(Boolean active)
    {
        GUITester.assertExists(getContentXpath() + GUIContent.LAYOUT_MODE_SPLITTER, active,
                String.format("Режим разметки %s", active ? "не активен" : "активен"));
    }

    /**
     * Получение утилитарных методов для проверок, связанных с панелью инструментов advList-а, через интерфейс
     * @return {@link AGUIAdvListToolPanel}
     */
    public AGUIAdvListToolPanel asserts()
    {
        if (asserts == null)
        {
            asserts = new AGUIAdvListToolPanel(getContentIdOrXpath());
        }
        return asserts;
    }

    /**
     * Нажать кнопку "Добавить"
     */
    public void clickAdd()
    {
        clickButton(GUIAdvListXpath.BTN_ADD);
    }

    /**
     * Нажать кнопку "Добавить/удалить объекты"
     */
    public void clickAddDeleteObjs()
    {
        clickButton(GUIButtonBar.BTN_ADD_DELETE_OBJS);
    }

    /**
     * Нажать кнопку "Добавить связь"
     */
    public void clickAddLink()
    {
        clickButton(GUIAdvListXpath.BTN_LINK);
    }

    /**
     * Нажать на иконку "Добавить пользовательский инструмент" к атрибуту в контенте Список свойств
     * @param attributeCode код атрибута, к которому добавить инструмент
     */
    public void clickAddToolToAttribute(String attributeCode)
    {
        String xpath = String.format(getContentXpath(), attributeCode);
        tester.moveMouse(xpath, tester.getElementTopLeftCornerX(xpath), tester.getElementTopLeftCornerY(xpath));
        String attrValue = String.format(getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL, attributeCode);
        tester.moveTo(attrValue);
        tester.click(attrValue + "/.." + GUIAdvListXpath.ADD_ATTRIBUTE_TOOL);
    }

    /**
     * Нажать кнопку определенную ее заголовком в панеле операций атрибута
     * @param attributeCode код атрибута, в панели которого находится инструмент
     * @param buttonTitle заголовок кнопки
     */
    public void clickAttributeToolButtonByTitle(String attributeCode, String buttonTitle)
    {
        tester.click(
                getContentXpath() + GUIAdvListXpath.ATTRIBUTE_VALUE_TOOL + "/parent::*" + GUIXpath.Any.TEXT_PATTERN,
                attributeCode,
                buttonTitle);
    }

    /**
     * Нажать на "Настройка панели действий" в панеле операций атрибута
     * @param attrCode код атрибута, в панели которого находится инструмент
     */
    public void clickAttributeToolPanelSettingsIcon(String attrCode)
    {
        String xpath = asserts().getAttributeXpath(attrCode);
        GUITester.assertPresent(xpath, "Контрол атрибута не отображается");
        tester.scrollToElement(xpath);
        tester.moveMouse(xpath, 10, 10);
        Assert.assertTrue("Окно настройки не появилось",
                tester.waitAppear(asserts().getAttributeToolSettingsIconXpath(attrCode)));
        tester.click(asserts().getAttributeToolSettingsIconXpath(attrCode));
    }

    /**
     * Нажать кнопку определенную ее кодом
     * @param buttonCode код кнопки (Константы {@link GUIAdvListXpath}, начинающиеся с префикса BTN_)
     */
    public void clickButton(String buttonCode)
    {
        asserts().buttonsPresence(buttonCode);
        tester.click(getContentXpath() + GUIAdvListXpath.TOOL_BUTTON, buttonCode);
    }

    /**
     * Нажать кнопку определенную ее кодом
     * @param buttonCode код кнопки (Константы {@link GUIAdvListXpath}, начинающиеся с префикса BTN_)
     * @param buttonName название кнопки
     */
    public void clickButton(String buttonCode, String buttonName)
    {
        asserts().buttonsPresence(buttonCode);
        asserts().buttonName(buttonCode, buttonName);
        tester.click(getContentXpath() + GUIAdvListXpath.TOOL_BUTTON, buttonCode);
    }

    /**
     * Нажать кнопку определенную ее кодом и названием
     * @param buttonCode код кнопки (Константы {@link GUIAdvListXpath}, начинающиеся с префикса BTN_)
     */
    public void clickButtonByCodeAndTitle(String buttonCode, String title)
    {
        asserts().buttonsPresence(buttonCode);
        tester.click(getContentXpath() + GUIAdvListXpath.TOOL_BTN_BY_TEXT, buttonCode, title);
    }

    /**
     * Нажать кнопку "Скопировать ссылку на список"
     */
    public GUIAdvListCopyLink clickCopyLinkToList()
    {
        clickButton(GUIButtonBar.BTN_COPY_LINK_TO_LIST);
        Assert.assertTrue("На форме \"Копирование ссылки\" не отобразилось текстовое поле",
                tester.waitAppear(GUIXpath.Any.URL_TEXT_AREA));
        return copyLink();
    }

    /**
     * Получение утилитарных методов для работы с формой "Копирование ссылки"
     * @return {@link GUIAdvListCopyLink}
     */
    public GUIAdvListCopyLink copyLink()
    {
        if (null == copyLink)
        {
            copyLink = new GUIAdvListCopyLink();
        }
        return copyLink;
    }

    /**
     * Нажать на иконку "Редактировать"
     */
    public void clickEdit()
    {
        tester.moveTo(getContentXpath());
        tester.moveTo(getContentXpath() + GUIAdvListXpath.BUTTON_PANEL);
        tester.click(getContentXpath() + GUIAdvListXpath.BUTTON_PANEL + GUIAdvListXpath.ICON_EDIT);
    }

    /**
     * Нажать кнопку "Редактировать"
     */
    public void clickEditPropertions()
    {
        clickButton(GUIAdvListXpath.BTN_EDIT_PROPERTIONS);
    }

    /**
     * Нажать на иконку "Перейти к настройке панели действий"
     */
    public void clickEditToolPanel()
    {
        clickEditToolPanel(ScrollAlignment.START, ScrollAlignment.NEAREST);
    }

    /**
     * Нажать на иконку "Настроить внешний вид по умолчанию"
     */
    public void clickEditAdvlistDefaultPresentation()
    {
        clickEditAdvlistDefaultPresentation(ScrollAlignment.START, ScrollAlignment.NEAREST);
    }

    /**
     * Нажать на иконку "Перейти к настройке панели действий"
     * @param verticalAlignment - стратегия скрола элемента по вертикали
     * @param horizontalAlignment - стратегия скрола элемента по горизонтали
     */
    public void clickEditToolPanel(ScrollAlignment verticalAlignment, ScrollAlignment horizontalAlignment)
    {
        tester.moveTo(getContentXpath());
        tester.moveMouse(getContentXpath(), 1, 1);
        tester.click(verticalAlignment, horizontalAlignment, true, getContentXpath() + GUIAdvListXpath.BUTTON_PANEL
                                                                   + GUIAdvListXpath.ICON_EDIT_TOOL_PANEL);
        tester.waitAsyncCall();
    }

    /**
     * Нажать на иконку "Настроить внешний вид по умолчанию"
     * @param verticalAlignment - стратегия скрола элемента по вертикали
     * @param horizontalAlignment - стратегия скрола элемента по горизонтали
     */
    public void clickEditAdvlistDefaultPresentation(ScrollAlignment verticalAlignment,
            ScrollAlignment horizontalAlignment)
    {
        tester.moveTo(getContentXpath());
        tester.moveMouse(getContentXpath(), 1, 1);
        tester.click(verticalAlignment, horizontalAlignment, true, getContentXpath() + GUIAdvListXpath.BUTTON_PANEL
                                                                   + GUIAdvListXpath.ICON_EDIT_ADVLIST_DEFAULT_PRS);
        tester.waitAsyncCall();
    }

    /**
     * Нажать кнопку "Экспорт списка"
     */
    public void clickExport()
    {
        clickButton(GUIAdvListXpath.BTN_EXPORT);
    }

    /**
     * Нажать кнопку "Фильтрация"
     */
    public void clickFiltering()
    {
        clickButton(GUIAdvListXpath.BTN_FILTERING);
    }

    /**
     * Нажать кнопку "Режим разметки" в карточке объекта метакласса
     */
    public void clickLayoutMode()
    {
        tester.findDisplayed(getContentXpath() + GUIContent.X_LAYOUT_MODE_BUTTON).click();
    }

    /**
     * Нажать кнопку "переместить вниз"
     */
    public void clickMoveDown()
    {
        tester.click(getContentXpath() + GUIContent.MOVE);
        tester.click(String.format(GUISelect.ELEMENT_BY_TITLE, "Переместить вниз"));
    }

    /**
     * Нажать кнопку "переместить влево"
     */
    public void clickMoveLeft()
    {
        tester.moveTo(getContentXpath() + GUIContent.MOVE);
        tester.click(getContentXpath() + GUIContent.MOVE);
        tester.click(String.format(GUISelect.ELEMENT_BY_TITLE, "Переместить влево"));
    }

    /**
     * Нажать кнопку "Расположить справа"
     */
    public void clickMoveRight()
    {
        tester.click(getContentXpath() + GUIContent.MOVE);
        tester.click(String.format(GUISelect.ELEMENT_BY_TITLE, "Расположить справа"));
    }

    /**
     * Нажать кнопку "переместить вверх"
     */
    public void clickMoveUp()
    {
        tester.moveTo(getContentXpath() + GUIContent.MOVE);
        tester.click(getContentXpath() + GUIContent.MOVE);
        tester.click(String.format(GUISelect.ELEMENT_BY_TITLE, "Переместить вверх"));
    }

    /**
     * Нажимает на кнопку с определенным кодом и названием.
     * @param buttonCode код кнопки
     * @param buttonName название кнопки
     */
    public void clickNamedButton(String buttonCode, String buttonName)
    {
        asserts().presenceOrAbsenceByTitle(buttonCode, buttonName, true);
        tester.click(getContentXpath() + GUIAdvListXpath.TOOL_BTN_BY_TEXT, buttonCode, buttonName);
    }

    /**
     * Нажать кнопку "Обновлять список"
     */
    public void clickRefresh()
    {
        clickButton(GUIAdvListXpath.BTN_REFRESH);
    }

    /**
     * Нажать кнопку "Сохранить вид"
     */
    public void clickSavePrs()
    {
        clickButton(GUIAdvListXpath.BTN_SAVE_PRESENTATION);
    }

    /**
     * Нажать кнопку "Сортировка"
     */
    public void clickSorting()
    {
        clickButton(GUIAdvListXpath.BTN_SORTING);
    }

    /**
     * Нажимает на пользовательскую кнопку с определенным названием.
     * @param buttonName название кнопки
     */
    public void clickUserEventButton(String buttonName)
    {
        clickNamedButton(GUIButtonBar.BTN_FIRE_USER_EVENT, buttonName);
    }

    /**
     * Экспортировать advlist (Нажать кнопку "Экспорт списка")
     */
    public void deferredExport()
    {
        clickExport();
        Assert.assertTrue("Отсутствует форма.", tester.waitAppear(GUIAdvListXpath.TOOL_EXPORT_EMAIL_VALUE));
    }

    /**
     * Перетащить контент вправо, на растояние его ширины
     */
    public void dragRight()
    {
        WebElement content = tester.find(getContentXpath());
        int xOffset = content.getSize().getWidth() / 2;
        tester.dragAndDropWithOffset(getContentXpath() + GUIContent.DRAG, xOffset, 0);
    }

    /**
     * Экспортировать advlist (Нажать кнопку "Экспорт списка")
     */
    public File exportFile()
    {
        return GUIExportUtils.export(
                String.format(getContentXpath() + GUIAdvListXpath.TOOL_BUTTON, GUIAdvListXpath.BTN_EXPORT), "exportSD",
                ".xlsx");
    }

    /**
     * Получение утилитарных методов работы с фильтрацией advList-а
     * @return {@link GUIAdvListFiltering}
     */
    public GUIAdvListFiltering filtering()
    {
        if (filtering == null)
        {
            filtering = new GUIAdvListFiltering(getContentIdOrXpath());
        }
        return filtering;
    }

    /**
     * Выполнить обновление списка.
     */
    public void refreshList()
    {
        asserts().buttonsPresence(GUIAdvListXpath.BTN_REFRESH);
        clickRefresh();
        clickRefresh();
    }

    /**
     * Ввести email на форму подтверждения отложенного экспорта
     * @param email почтовый адрес
     */
    public void setEmailInDeferredExport(String email)
    {
        tester.sendKeys(GUIAdvListXpath.TOOL_EXPORT_EMAIL_VALUE, email);
    }

    /**
     * Кнопка "Обновлять список": включена/выключена
     * @param pressed включена/выключена
     */
    public void setRefreshList(boolean pressed)
    {
        asserts().buttonsPresence(GUIAdvListXpath.BTN_REFRESH);
        if (tester.isPresence(getContentXpath() + GUIAdvListXpath.TOOL_BUTTON + GUIAdvListXpath.CLASS_SELECTED_ITEM,
                GUIAdvListXpath.BTN_REFRESH) != pressed)
        {
            clickRefresh();
        }
    }

    /**
     * Показать Архив в сложном списке
     */
    public void showArchive()
    {
        clickButton(GUIAdvListXpath.BTN_REMOVED, "Просмотреть архив");
    }

    /**
     * Нажать ссылку "Выйти из архива"
     */
    public void showList()
    {
        clickButton(GUIAdvListXpath.BTN_REMOVED, "Выйти из архива");
    }

    /**
     * Получение утилитарных методов работы с сортировкой advList-а
     * @return {@link GUIAdvListSorting}
     */
    public GUIAdvListSorting sorting()
    {
        if (sorting == null)
        {
            sorting = new GUIAdvListSorting(getContentIdOrXpath());
        }
        return sorting;
    }

    /**
     * Переместить контент влево (обращаемся по xpath'у кнопки перемещения)
     */
    public void clickMoveLeftByXpath()
    {
        tester.moveTo(getContentXpath());
        tester.moveMouse(getContentXpath(), 1, 1);
        tester.click(getContentXpath() + GUIContent.XPATH_MOVE_CONTENT);
        tester.click(String.format(GUISelect.ELEMENT_BY_TITLE, "Переместить влево"));
    }
}
