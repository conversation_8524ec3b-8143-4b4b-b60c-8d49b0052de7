package ru.naumen.selenium.casesutil.content;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.content.ContentForm;

/**
 * Утилитарные методы для работы с контентом "Список связанных объектов" в интерфейсе оператора
 * <AUTHOR>
 */
public class GUIRelatedList extends CoreTester
{
    /**Шаблон xPath-а пути до названия атрибута, из группы атрибутов, отображаемой на контенте. В шаблон передавать
     * id контента и название атрибута.*/
    private static final String X_CAPTION = "//*[@id='%s']//*[@id='gwt-debug-outer']//tr//th[text()='%s']";
    /**Шаблон xPath-а пути до значения атрибута, из группы атрибутов, отображаемой на контенте. В шаблон передавать
     * id контента и значение атрибута.*/
    private static final String X_VALUE = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[text()='%s']";
    /**Шаблон xPath-а пути до значения контента. В шаблон передавать id контента. */
    private static final String X_RELATED_LIST = "//*[@id='%s']//*[@id='gwt-debug-outer']";
    private static final String X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE = "//*[@id='gwt-debug-show-linked-objects-value']";
    public static final String X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE_INPUT = X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE
                                                                            + "//input";
    /** Значение параметра Класс объектов списка */
    public static final String CLASS_OBJ_LIST_VALUE = "//div[@id='gwt-debug-classList-caption']//span";
    /** Путь до чекбокса "Показывать в списке объекты, связанные с иерархией */
    private static final String X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_VALUE = "//*[@id='gwt-debug"
                                                                                      + "-showRelatedWithNested-value"
                                                                                      + "']";
    private static final String X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_INPUT =
            X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_VALUE + "//input";

    /**
     * Проверить количество строк расположеных на текущей странице в контенте "Список связанных объектов". 
     * @param content модель контента
     * @param expected ожидаемое количество строк
     */
    public static void assertNumberLinesOnCurrentPage(ContentForm content, int expected)
    {
        String rowsXpath = String.format(X_RELATED_LIST + "//*[@__gwt_row]", content.getXpathId());
        if (expected == 0)
        {
            Assert.assertTrue("В контенте присутствуют строки.", tester.waitDisappear(rowsXpath));
        }
        else
        {
            Assert.assertEquals("Полученное количество строк не совпало с ожидаемым.", expected,
                    tester.findDisplayedElements(rowsXpath).size());
        }
    }

    /**
     * Проверяет в контенте указанные атрибуты
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента "Список связанных объектов"
     * @param attributes перечень проверяемых атрибутов в контенте "Список связанных объектов". В данном методе
     *                   используются следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertRelatedListAttribute(ContentForm content, Attribute... attributes)
    {
        assertRelatedListAttributeCaption(content, attributes);
        assertRelatedListAttributeValue(content, attributes);
    }

    /**
     * Проверяет отсутствие в контенте указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента "Список связанных объектов"
     * @param attributes перечень проверяемых атрибутов в контенте "Список связанных объектов". В данном методе
     * используются следующие поля модели атрибута: значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertRelatedListAttributeAbsence(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            GUITester.assertAbsentElements(String.format(X_VALUE, content.getXpathId(), attribute.getValue()));
        }
    }

    /**
     * Проверить отсутствие чекбокса "Показывать объекты с двух сторон связи"
     */
    public static void assertShowLinkedCheckboxAbsence()
    {
        String msg = "Чекбокс \"Показывать объекты с двух сторон связи\" присутствует.";
        Assert.assertTrue(msg, tester.waitDisappear(X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE));
    }

    /**
     * Проверить значение чекбокса "Показывать объекты с двух сторон связи"
     * @param checked true-поставлен false-не поставлен
     */
    public static void assertShowLinkedCheckboxChecked(boolean checked)
    {
        GUITester.assertCheckboxState(X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE + "//input", checked);
    }

    /**
     * Проверить наличие чекбокса "Показывать объекты с двух сторон связи"
     */
    public static void assertShowLinkedCheckboxPresent()
    {
        String msg = "Чекбокс \"Показывать объекты с двух сторон связи\" не появился.";
        Assert.assertTrue(msg, tester.waitAppear(X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE));
    }

    /**
     * Проверить отсутствие чекбокса "Показывать в списке объекты, связанные с иерархией"
     */
    public static void assertShowObjectsCheckboxAbsence()
    {
        String msg = "Чекбокс \"Показывать в списке объекты, связанные с иерархией\" присутствует.";
        Assert.assertTrue(msg, tester.waitDisappear(X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_VALUE));
    }

    /**
     * Проверить наличие чекбокса "Показывать в списке объекты, связанные с иерархией"
     */
    public static void assertShowObjectsCheckboxPresent()
    {
        String msg = "Чекбокс \"Показывать в списке объекты, связанные с иерархией\" не появился.";
        Assert.assertTrue(msg, tester.waitAppear(X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_VALUE));
    }

    /**
     * Проверить значение чекбокса "Показывать в списке объекты, связанные с иерархией"
     * @param checked true-поставлен false-не поставлен
     */
    public static void assertShowRelatedWithNestedCheckboxChecked(boolean checked)
    {
        GUITester.assertCheckboxState(X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_VALUE + "//input", checked);
    }

    /**
     * Поставить/снять чекбокс "Показывать в списке объекты, связанные с иерархией" контента Список связанных объектов
     * @param value значение чекбокса
     */
    public static void fillCheckboxShowRelatedWithNestedCheckboxChecked(boolean value)
    {
        tester.setCheckbox(X_SHOW_OBJECT_RELATED_WITH_HIERARCHY_CHECKBOX_INPUT, value);
    }

    /**
     * Поставить/снять чекбокс  "Показывать объекты с двух сторон связи" контента Список связанных объектов
     * @param value значение чекбокса
     */
    public static void fillCheckboxShowLinked(Boolean value)
    {
        tester.setCheckbox(X_SHOW_LINKED_OBJECTS_CHECKBOX_VALUE_INPUT, value);
    }

    /**
     * Проверяет в контенте названия указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента "Список связанных объектов"
     * @param attributes перечень проверяемых атрибутов в контенте "Список связанных объектов". В данном методе
     *                   используются следующие
     * поля модели атрибута: название атрибута (AttributeModel.TITLE)
     */
    private static void assertRelatedListAttributeCaption(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            GUITester.assertTextContains(X_CAPTION, attribute.getTitle(), content.getXpathId(), attribute.getTitle());
        }
    }

    /**
     * Проверяет в контенте значения указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Список связанных объектов")
     * @param content модель контента "Список связанных объектов"
     * @param attributes перечень проверяемых атрибутов в контенте "Список связанных объектов". В данном методе
     *                   используются следующие
     * поля модели атрибута: значение атрибута (AttributeModel.VALUE). 
     */
    private static void assertRelatedListAttributeValue(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            GUITester.assertTextContains(X_VALUE, attribute.getValue(), content.getXpathId(), attribute.getValue());
        }
    }
}
