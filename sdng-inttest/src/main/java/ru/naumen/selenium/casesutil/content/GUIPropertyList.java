package ru.naumen.selenium.casesutil.content;

import java.util.List;

import org.junit.Assert;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebElement;

import ru.naumen.selenium.casesutil.CoreTester;
import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.attr.GUIAttribute;
import ru.naumen.selenium.casesutil.interfaceelement.GUIRichText;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.AttributeConstant.RichTextType;
import ru.naumen.selenium.casesutil.model.catalogitem.CatalogItem;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.file.SdFile;
import ru.naumen.selenium.casesutil.model.secgroup.SecurityGroup;
import ru.naumen.selenium.util.StringUtils;
import ru.naumen.selenium.util.UrlUtils;

/**
 * Утилитарные методы для работы с контентов "Параметры объекта" в интерфейсе оператора
 * <AUTHOR>
 * @since 07.02.2012
 */
public class GUIPropertyList extends CoreTester
{
    /**
     * Шаблон пути до контента.
     */
    public static final String X_CONTENT = "//*[@id='%s']";

    /**Шаблон xPath-а пути до названия атрибута, из группы атрибутов, отображаемой на контенте. В шаблон передавать
     * id контента и код (code) атрибута.*/
    public static final String X_CAPTION = "//*[@id='%s']//*[@id='gwt-debug-%s-caption']";

    /**Шаблон xPath-а пути до значения атрибута, из группы атрибутов, отображаемой на контенте. В шаблон передавать
     * id контента и код (code) атрибута.*/
    public static final String X_VALUE = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[@id='gwt-debug-%s-value']";

    /**Шаблон xPath-а на контенте "Параметры объекта". В шаблон передавать id контента.*/
    public static final String X_EDIT_LINK = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                             + "'gwt-debug-ToolPanel')]//*[contains(@id,'gwt-debug-ToolBar')"
                                             + "]//*[contains(@id,'gwt-debug-edit')]";

    public static final String X_TOOL_BAR = "//*[@id='%s']//*[@id='gwt-debug-outer']//*[contains(@id,"
                                            + "'gwt-debug-ToolPanel')]//*[contains(@id, 'gwt-debug-ToolBar.')]";

    /**
     * Проверяет отсутствие в контенте указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE). 
     */
    public static void assertAbsenceAttribute(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            Assert.assertTrue(String.format("Атрибут '%s' присутствует в контенте.", attribute.getTitle()),
                    tester.waitDisappear(X_CAPTION, content.getXpathId(), attribute.getCode()));
        }
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является название и индикатор цвета
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemColorAndTitleValue(ContentForm content, Attribute attribute,
            CatalogItem catalogItem)
    {
        String xPath = String.format(X_VALUE, content.getXpathId(), attribute.getCode());
        GUIAttribute.assertColorAndTitle(xPath, catalogItem.getColor(), catalogItem.getTitle());
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является название, выделенное цветом
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemColoredTitleValue(ContentForm content, Attribute attribute,
            CatalogItem catalogItem)
    {
        String xPath = String.format(X_VALUE, content.getXpathId(), attribute.getCode());
        GUIAttribute.assertColoredTitle(xPath, catalogItem.getTitle(), catalogItem.getColor());
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является индикатор цвета
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemColorValue(ContentForm content, Attribute attribute, CatalogItem catalogItem)
    {
        String xPath = String.format(X_VALUE, content.getXpathId(), attribute.getCode());
        GUIAttribute.assertColor(xPath, catalogItem.getColor());
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является изображение и название
     * элемента справочника.
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemImageAndTitleValue(ContentForm content, Attribute attribute,
            CatalogItem catalogItem)
    {
        String xPath = String.format(X_VALUE, content.getXpathId(), attribute.getCode());
        GUIAttribute.assertImageAndTitle(xPath, catalogItem.getIconUuid(), catalogItem.getTitle());
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является изображение,
     * которое ссылается на файл, на который ссылается элемент справочника.
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemImageValue(ContentForm content, Attribute attribute, CatalogItem catalogItem)
    {
        String xPath = String.format(X_VALUE, content.getXpathId(), attribute.getCode());
        GUIAttribute.assertImage(xPath, catalogItem.getIconUuid());
    }

    /**
     * Проверяет, что в контенте значением атрибута типа элемент справочника является только название
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attribute модель атрибута в контенте "Параметры объекта"
     * @param catalogItem модель элемента справочника - ожидаемое значение
     */
    public static void assertCatalogItemValue(ContentForm content, Attribute attribute, CatalogItem catalogItem)
    {
        GUITester.assertTextPresent(X_VALUE, catalogItem.getTitle(), content.getXpathId(), attribute.getCode());
    }

    /**
     * Проверить, что в контенте типа "Параметры объекта" отсутствует ссылка "Редактировать"
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertEditLinkAbsence(ContentForm content)
    {
        String message = "В контенте типа 'Параметры объекта' присутствует ссылка 'Редактировать'.";
        Assert.assertTrue(message, tester.waitDisappear(X_EDIT_LINK, content.getXpathId()));
    }

    /**
     * Проверить, что в контенте типа "Параметры объекта" присутствует ссылка "Редактировать"
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void assertEditLinkPresent(ContentForm content)
    {
        String message = "В контенте типа 'Параметры объекта' отсутствует ссылка 'Редактировать'.";
        Assert.assertTrue(message, tester.waitAppear(X_EDIT_LINK, content.getXpathId()));
    }

    /**
     * Проверяет что в значении атрибута типа "Набор групп пользователей" присутствует группа пользователей
     * @param content модель контента "Параметры объекта"
     * @param secGroupsAttr атрибут типа "Набор групп пользователей"
     * @param secGroup модель группы пользователей 
     */
    public static void assertGroupPresentsInSecGroups(ContentForm content, Attribute secGroupsAttr,
            SecurityGroup secGroup)
    {
        GUITester.assertTextContainsWithMsg(GUIPropertyList.X_VALUE, secGroup.getTitle(),
                String.format("Группа %s отсутствует в списке групп", secGroup.getTitle()), content.getXpathId(),
                secGroupsAttr.getCode());
    }

    /**
     * Проверяет, что контент не отображается на форме.
     * @param content модель контента
     */
    public static void assertHidden(ContentForm content)
    {
        Assert.assertTrue("Контент присутствует на форме.", tester.waitDisappear(X_CONTENT, content.getXpathId()));
    }

    /**
     * Проверяет присутствие в контенте указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE). 
     */
    public static void assertPresentsAttribute(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            Assert.assertTrue(String.format("Атрибут '%s' отсутствует в контенте.", attribute.getTitle()),
                    tester.waitAppear(X_CAPTION, content.getXpathId(), attribute.getCode()));
        }
    }

    /**
     * Проверяет в контенте указанные атрибуты
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertPropertyListAttribute(ContentForm content, Attribute... attributes)
    {
        assertPropertyListAttributeCaption(content, attributes);
        assertPropertyListAttributeValue(content, attributes);
    }

    /**
     * Проверить в контенте указанный атрибут
     * @param content модель контента "Параметры объекта"
     * @param attribute проверяемый атрибут в контенте "Параметры объекта". В данном методе используются следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertPropertyListAttribute(ContentForm content, Attribute attribute)
    {
        Attribute[] attributeForAssert = { attribute };
        assertPropertyListAttribute(content, attributeForAssert);
    }

    /**
     * Проверяет в контенте названия указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertPropertyListAttributeCaption(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            String messageAttrCaption = String
                    .format("Ожидаемое название атрибута (код атрибута '%s') в контенте \"Параметры объекта\" "
                            + "не совпало с полученным.", attribute.getCode());
            GUITester.assertTextContainsWithMsg(X_CAPTION, attribute.getTitle(), messageAttrCaption,
                    content.getXpathId(), attribute.getCode());
        }
    }

    /**
     * Проверяет в контенте значения указанных атрибутов
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertPropertyListAttributeValue(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            boolean isRichAttr = attribute.getType() != null
                                 && attribute.getType().equals(RichTextType.CODE)
                                 && !RichTextType.UNSAFE_VIEW.equals(attribute.getViewPresentation())
                                 && !RichTextType.UNSAFE_WIDE_VIEW.equals(attribute.getViewPresentation());
            if (isRichAttr)
            {
                GUIRichText.assertTextPropList(attribute);
            }
            else
            {
                String messageAttrValue = String.format(
                        "Ожидаемое значение '%s' атрибута (название атрибута '%s') в контенте \"Параметры объекта\" "
                        + "не совпало с полученным.",
                        attribute.getValue(), attribute.getTitle());

                GUITester.assertTextContainsWithMsg(X_VALUE, attribute.getValue(), messageAttrValue,
                        content.getXpathId(),
                        attribute.getCode());
            }
        }
    }

    /**
     * Проверяет в контенте значения указанного атрибута
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param contains должен ли атрибут содержать проверяемое значение (true - да,false - нет)
     * @param attribute проверяемый атрибут в контенте "Параметры объекта"
     * @param value проверяемое значение
     */
    public static void assertPropertyListAttributeValue(ContentForm content, Boolean contains, Attribute attribute,
            String value)
    {
        String valueAttr = getValueAttributeByCode(content, attribute.getCode());

        String messageAttrValue = String.format(
                "Значение '%s' атрибута (название атрибута '%s') в контенте \"Параметры объекта\" "
                + "%s ожидаемое значение %s",
                valueAttr, attribute.getTitle(), contains ? "не содержит" : "содержит", value);

        Assert.assertEquals(messageAttrValue, contains, valueAttr.contains(value));
    }

    /**
     * Проверяет в контенте значения указанных атрибутов- полное совпадение (значение атрибута был  null)
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE), название атрибута (AttributeModel.TITLE), 
     * значение атрибута (AttributeModel.VALUE). 
     */
    public static void assertPropertyListAttributeValueStrictly(ContentForm content, Attribute... attributes)
    {

        for (Attribute attribute : attributes)
        {
            String expected = attribute.getValue() == null ? "" : attribute.getValue();
            String messageAttrValue = String
                    .format("Ожидаемое значение атрибута ('%s') в контенте \"Параметры объекта\" "
                            + "не совпало с полученным.", attribute.getTitle());
            if ("richtext".equals(attribute.getType()))
            {
                GUIRichText.assertTextPropList(attribute);
            }
            else
            {
                GUITester.assertTextPresentWithMsg(X_VALUE + "/..", expected, messageAttrValue, content.getXpathId(),
                        attribute.getCode());
            }
        }
    }

    /**
     * Проверить значение атрибута, в котором могут присутствовать подстроки в случайном порядке через запятую
     * @param content модель контента типа "Параметры объекта"
     * @param attr модель проверяемого атрибута
     * @param pattern шаблон ожидаемого значения с одним параметром (в котором будут подстроки в случайном порядке)
     * @param args список параметров, порядок которых не известен, присутствующих в проверяемом значении через запятую
     */
    public static void assertShuffleAttrValue(ContentForm content, Attribute attr, String pattern, Object... args)
    {
        String actual = getValueAttributeByCode(content, attr.getCode());
        String msg = "Полученное значение атрибута '%s' (%s) не совпало с ожидаемым.";
        Assert.assertTrue(String.format(msg, attr.getTitle(), actual),
                StringUtils.checkShuffleStringPattern(actual, pattern, ", ", args));
    }

    /**
     * Проверить видимость тулбара в контенте типа "Параметры объекта"
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента типа "Параметры объекта"
     * @param visible должен быть видимым или нет
     */
    public static void assertToolBarVisible(ContentForm content, boolean visible)
    {
        String msg = "Тулбар " + (visible ? "невидим" : "видим");
        GUITester.assertExists(String.format(X_TOOL_BAR, content.getXpathId()), visible, msg);
    }

    /**
     * Проверяет, что в контенте значениями указанных атрибутов являются изображения
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE).
     */
    public static void assertValueIsImage(ContentForm content, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            String message = String.format("Значением атрибута (код атрибута '%s') в контенте \"Параметры объекта\" "
                                           + "не является изображение.", attribute.getCode());
            Assert.assertTrue(message, tester.waitAppear(X_VALUE + "//img", content.getXpathId(), attribute.getCode()));
        }
    }

    /**
     * Проверяет, что в контенте значениями указанных атрибутов являются ссылкой на файл
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента "Параметры объекта"
     * @param file - модель файла, находящийся в атрибуте
     * @param attributes перечень проверяемых атрибутов в контенте "Параметры объекта". В данном методе используются
     *                   следующие
     * поля модели атрибута: код атрибута (AttributeModel.CODE).
     */
    public static void assertValueIsLink(ContentForm content, SdFile file, Attribute... attributes)
    {
        for (Attribute attribute : attributes)
        {
            String actualValue = tester.findDisplayed(X_VALUE + "/a", content.getXpathId(), attribute.getCode())
                    .getAttribute("href").split("operator/")[1];
            String anchor = UrlUtils.reencodeUriComponent(tester.getWebDriver().getCurrentUrl().split("#")[1]);
            String fileUuid = String.format("download?uuid=%s&checkContent=true&back=%s",
                    file.getUuid(), anchor);
            Assert.assertEquals("Ожидаемая ссылка на файл не совпала с полученной", fileUuid, actualValue);
        }
    }

    /**
     * Проверяет, что контент отображается на форме.
     * @param content модель контента
     */
    public static void assertVisible(ContentForm content)
    {
        Assert.assertTrue("Контент отсутствует на форме.", tester.waitAppear(X_CONTENT, content.getXpathId()));
    }

    /**
     * Выполняет клик по ссылке, содержащейся в значении указанного атрибута.
     * @param content модель контента "Параметры объекта"
     * @param attribute атрибут, содержащий ссылку
     */
    public static void clickAttributeValueLink(ContentForm content, Attribute attribute)
    {
        tester.click(X_VALUE + "//a", content.getXpathId(), attribute.getCode());
    }

    /**
     * Нажать ссылку "Редактировать" на контенте типа "Параметры объекта"
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента типа "Параметры объекта"
     */
    public static void clickEditLink(ContentForm content)
    {
        tester.scrollIntoView(X_EDIT_LINK, content.getXpathId());
        tester.click(X_EDIT_LINK, content.getXpathId());
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
    }

    /**
     * Получить значение атрибута, содержащегося в контенте, по его коду
     * (Для вызова метода необходимо находится на карточке, содержащей контент "Параметры объекта")
     * @param content модель контента типа "Параметры объекта"
     * @param attrCode код атрибута
     * @return возвращает значение атрибута, отображающееся в контенте типа "Параметры объекта"
     */
    public static String getValueAttributeByCode(ContentForm content, String attrCode)
    {
        return tester.getText(X_VALUE + "/..", content.getXpathId(), attrCode).trim();
    }

    /**
     * Метод определяет используется ли на карточке списка атрибутов безопасное представление для
     * атрибута типа текст RTF
     *
     * @param content модель контента типа "Параметры объекта"
     * @param attrCode код атрибута
     * @return <code>true</code> если используется безопасное представление, иначе <code>false</code>
     */
    public static boolean isPropertyListAttributeRTFValueSafe(ContentForm content, String attrCode)
    {
        try
        {
            List<WebElement> els = tester.findElements(X_VALUE + "/iframe", content.getXpathId(), attrCode);
            return (els != null && !els.isEmpty());
        }
        catch (NoSuchElementException e)
        {
            return false;
        }
    }
}