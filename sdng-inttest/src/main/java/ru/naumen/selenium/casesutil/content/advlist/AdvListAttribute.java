package ru.naumen.selenium.casesutil.content.advlist;

import ru.naumen.selenium.casesutil.model.ModelCode;
import ru.naumen.selenium.casesutil.model.attr.Attribute;

/**
 * Модель столбца с атрибутом в представлении по умолчанию списка объектов
 *
 * <AUTHOR>
 * @since 10 марта 2016 г.
 */
public class AdvListAttribute extends ModelCode
{
    private static final String PRS = "prs";

    public AdvListAttribute(Attribute attribute)
    {
        setTitle(attribute.getTitle());
        setCode(attribute.getCode());
        setPresentation(attribute.getViewPresentation());
    }

    public String getFullTitle()
    {
        String title = getTitle();
        String code = getCode();
        return String.format("%s (%s)", title, code);
    }

    public String getPresentation()
    {
        return get(PRS);
    }

    public void setPresentation(String prsCode)
    {
        set(PRS, prsCode);
    }
}
