package ru.naumen.selenium.casesutil.content;

import ru.naumen.selenium.casesutil.model.content.AttributeToolPanel;
import ru.naumen.selenium.casesutil.model.content.ContentForm;
import ru.naumen.selenium.casesutil.model.content.ContentTab;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.Json;

/**
 * Утилитарные методы для работы с AttributeToolPanel
 * <AUTHOR>
 * @since 17.10.2016
 */
public class DSLAttributeToolPanel
{
    /**
     * Добавить тулпанель атрибута
     * @param attributeToolPanel - тулпанель атрибута
     * @param content - контент, для расположения attributeToolPanel
     */
    public static void addAttributeToolPanel(AttributeToolPanel attributeToolPanel, ContentForm content)
    {
        //@formatter:off
        ScriptModules.getModuleContent().addAttributeToolPanel(
                attributeToolPanel.getAttributeCode(),
                null,
                content.getCode(),
                content.getParentFqn(), 
                Json.GSON.toJson(attributeToolPanel.getToolPanel())
        );
        //@formatter:on
    }

    /**
     * Добавить тулпанель атрибута для контента, расположенного на определённой вкладке.
     * @param attributeToolPanel - тулпанель атрибута
     * @param tab - вкладка, на которой расположен контент
     * @param content - контент, для расположения attributeToolPanel
     */
    public static void addAttributeToolPanelOnTab(AttributeToolPanel attributeToolPanel, ContentTab tab,
            ContentForm content)
    {
        //@formatter:off
        ScriptModules.getModuleContent().addAttributeToolPanel(
                attributeToolPanel.getAttributeCode(),
                tab.getCode(),
                content.getCode(),
                content.getParentFqn(), 
                Json.GSON.toJson(attributeToolPanel.getToolPanel())
        );
        //@formatter:on
    }

    /**
     * Удалить тулпанель атрибута
     * @param attributeToolPanel - тулпанель атрибута
     * @param content - контент, где расположен attributeToolPanel
     */
    public static void deleteAttributeToolPanel(AttributeToolPanel attributeToolPanel, ContentForm content)
    {
        //@formatter:off
        ScriptModules.getModuleContent().deleteAttributeToolPanel(
                attributeToolPanel.getAttributeCode(), 
                content.getCode(),
                content.getParentFqn()
        );
        //@formatter:on
    }

    /**
     * Отредактировать тулпанель атрибута
     * @param attributeToolPanel - тулпанель атрибута
     * @param content - контент, для расположения attributeToolPanel
     */
    public static void editAttributeToolPanel(AttributeToolPanel attributeToolPanel, ContentForm content)
    {
        addAttributeToolPanel(attributeToolPanel, content);
    }
}
