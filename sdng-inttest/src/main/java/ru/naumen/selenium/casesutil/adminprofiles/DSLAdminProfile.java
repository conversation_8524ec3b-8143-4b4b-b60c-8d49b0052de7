package ru.naumen.selenium.casesutil.adminprofiles;

import java.util.Arrays;

import ru.naumen.selenium.casesutil.model.ModelCode;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfile;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.AdminProfileAccessMarker;
import ru.naumen.selenium.casesutil.model.adminprofiles.AdminProfileAccessMarkerMatrix.PermissionType;
import ru.naumen.selenium.modules.IModuleAdminProfile;
import ru.naumen.selenium.modules.ScriptModules;
import ru.naumen.selenium.util.RandomUtils;

/**
 * Методы для работы с профилями администрирования
 * <AUTHOR>
 * @since 05.05.2024
 */
public class DSLAdminProfile
{
    /**
     * Получить модуль для работы с профилями администрирования
     */
    private static IModuleAdminProfile getAdminProfileModule()
    {
        return ScriptModules.getAdminProfileModule();
    }

    /**
     * Создает на сервере новые профили администрирования, определенные их моделями, при добавлении не учитывается
     * "Матрица маркеров доступа в профиле администрирования", можно изменить только после добавления
     * @param adminProfiles модели профилей администрирования
     */
    public static void add(AdminProfile... adminProfiles)
    {
        String cleaningUuid = RandomUtils.randomUUID();
        for (AdminProfile adminProfile : adminProfiles)
        {
            getAdminProfileModule().addAdminProfile(adminProfile.getFields());
            adminProfile.setExists(true);
            adminProfile.setCleaningUuid(cleaningUuid);
        }
    }

    /**
     * Удаляет профили администрирования с сервера.
     * @param adminProfiles модели профилей администрирования
     */
    public static void delete(AdminProfile... adminProfiles)
    {
        getAdminProfileModule().deleteAdminProfile(
                Arrays.stream(adminProfiles).map(ModelCode::getCode).toArray(String[]::new));
        Arrays.stream(adminProfiles).forEach(adminProfile -> adminProfile.setExists(false));
    }

    /**
     * Изменяет существующие профили администрирования на сервере.
     * @param adminProfiles модели профилей администрирования
     */
    public static void edit(AdminProfile... adminProfiles)
    {
        for (AdminProfile adminProfile : adminProfiles)
        {
            getAdminProfileModule().editAdminProfile(adminProfile.getFields());
        }
    }

    /**
     * Задать матрицу маркеров доступа профилю администрирования
     * @param adminProfile профиль администрирования
     * @param accessMarkerMatrix матрица маркеров доступа
     */
    public static void setAccessMarkerMatrix(AdminProfile adminProfile,
            AdminProfileAccessMarkerMatrix accessMarkerMatrix)
    {
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        edit(adminProfile);
    }

    /**
     * Выдать все права профилю администрирования, в коде элемент enum ALL - это доступ ко всем операциям (CRUD +
     * доступ в интерфейсе) - но в интерфейсе по этому коду проставляется только колонка "Доступ", которая есть не у
     * всех маркеров
     * @param adminProfile - профиль администрирования
     */
    public static void addAllRightsToAdminProfile(AdminProfile adminProfile)
    {
        AdminProfileAccessMarkerMatrix accessMarkerMatrix = new AdminProfileAccessMarkerMatrix();
        accessMarkerMatrix.addAccessMarkersPermission(PermissionType.ALL, AdminProfileAccessMarker.values());
        adminProfile.setAccessMarkerMatrix(accessMarkerMatrix);
        edit(adminProfile);
    }
}