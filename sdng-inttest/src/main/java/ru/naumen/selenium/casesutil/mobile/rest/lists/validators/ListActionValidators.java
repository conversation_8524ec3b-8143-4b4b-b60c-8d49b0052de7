package ru.naumen.selenium.casesutil.mobile.rest.lists.validators;

import ru.naumen.selenium.casesutil.model.mobile.actions.MobileContentListAction;

/**
 * Валидаторы действий в списках.
 *
 * <AUTHOR>
 * @since 22.11.2024
 */
public class ListActionValidators
{
    /**
     * Возвращает валидатор для действия "Фильтрация"
     */
    public static CommonListActionValidator filtration()
    {
        return new CommonListActionValidator("filtration", "Фильтрация");
    }

    /**
     * Возвращает валидатор для действия "Сортировка"
     */
    public static SortListActionValidator sort()
    {
        return new SortListActionValidator("sort", "Сортировка");
    }

    /**
     * Возвращает валидатор для действия "Добавить объекта"
     */
    public static AddObjectListActionValidator addObject(MobileContentListAction action)
    {
        return new AddObjectListActionValidator("addObject", action.getCaption());
    }
}
