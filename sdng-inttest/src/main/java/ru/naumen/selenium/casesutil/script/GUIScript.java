package ru.naumen.selenium.casesutil.script;

import static org.junit.Assert.assertTrue;
import static ru.naumen.selenium.casesutil.GUIXpath.Other.TABLE;
import static ru.naumen.selenium.casesutil.content.advlist.GUIAdvListXpath.CONTENT_ROW;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.Assert;

import ru.naumen.selenium.casesutil.GUIForm;
import ru.naumen.selenium.casesutil.GUINavigational;
import ru.naumen.selenium.casesutil.GUITester;
import ru.naumen.selenium.casesutil.GUIXpath;
import ru.naumen.selenium.casesutil.GUIXpath.Div;
import ru.naumen.selenium.casesutil.admin.GUIScriptField;
import ru.naumen.selenium.casesutil.bo.GUISearch;
import ru.naumen.selenium.casesutil.content.advlist.GUIAdvListUtil;
import ru.naumen.selenium.casesutil.interfaceelement.GUIMultiSelect;
import ru.naumen.selenium.casesutil.messages.ErrorMessages;
import ru.naumen.selenium.casesutil.metaclass.GUIMetaClass;
import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.ModelUuid;
import ru.naumen.selenium.casesutil.model.attr.Attribute;
import ru.naumen.selenium.casesutil.model.attr.DAOAttribute;
import ru.naumen.selenium.casesutil.model.script.Constants;
import ru.naumen.selenium.casesutil.model.script.Constants.ScriptCategory;
import ru.naumen.selenium.casesutil.model.script.ScriptInfo;
import ru.naumen.selenium.casesutil.model.script.ScriptUsagePoint;
import ru.naumen.selenium.casesutil.user.StandTypeHolder;

/**
 * Методы для работы с карточками скриптов через интерфейс.
 *
 */
public class GUIScript extends GUITester
{
    /**
     * XPath заголовка номера скрипта.
     */
    public static final String X_NUMBER_CAPTION = "//*[@id='gwt-debug-numberCaption']";

    /**
     * XPath номера скрипта.
     */
    public static final String X_CODE = "//*[@id='gwt-debug-codeCaption-value']";

    /**
     * XPath названия скрипта.
     */
    public static final String X_TITLE = "//*[@id='gwt-debug-titleCaption-value']";

    /**
     * XPath описания скрипта.
     */
    public static final String X_DESCRIPTION = "//*[@id='gwt-debug-descriptionCaption-value']";

    /**
     * XPath заголовка списка используемых модулей.
     */
    public static final String X_USED_MODULES_CAPTION = "//*[@id='gwt-debug-usedModulesCaption']";

    /**
     * XPath списка используемых модулей.
     */
    public static final String X_USED_MODULES = "//*[@id='gwt-debug-usedModulesCaption-value']";

    /**
     * XPath заголовка текста скрипта.
     */
    public static final String X_TEXT_CAPTION = "//*[@id='gwt-debug-textCaption']";

    /**
     * XPath текста скрипта.
     */
    public static final String X_TEXT = "//*[@id='gwt-debug-textCaption-value']//div[@class='CodeMirror-code']//pre";
    /**
     * XPath ссылки на карточку категории скрипта. 
     */
    public static final String X_CATEGORY_LINK = "//*[@id='gwt-debug-categoriesCaption-value']";

    /**
     * XPath кнопки редактирования скрипта.
     */
    private static final String X_EDIT_BUTTON = "//*[@id='gwt-debug-edit']";

    /**
     * XPath кнопки удаления скрипта.
     */
    private static final String X_DELETE_BUTTON = GUIXpath.Div.DEL;

    /**
     * XPath списка категорий на форме редактирования скрипта
     */
    private static final String EDIT_DEFAULT_CATEGORY = GUIXpath.Div.PROPERTY_DIALOG_BOX
                                                        + "//*[@id='gwt-debug-categoriesDefault-value']//input";

    /**
     * XPath списка мест использования скрипта. 
     */
    private static final String USAGE_POINTS = "//div[@id='gwt-debug-usagePoints']";

    /**
     * XPath названия места использования в строке таблицы мест использования.
     */
    private static final String X_TABLE_ROW_TITLE = "/tbody[1]/tr[%s]/td[1]/a[text()='%s']";

    /**
     * XPath получения указанного места использования в строке таблицы мест использования.
     */
    private static final String X_TABLE_ROW = "/tbody[1]/tr[%s]/td[1]/a";

    private static final String SCRIPT_USAGE_PLACE_ERROR_MASSAGE =
            "В строке '%s' не обнаружено ожидаемое место использования в объекте: '%s'. Обнаружено: '%s'";

    private static volatile GUIAdvListUtil usagePoints;

    /**
     * Проверяет наличие ссылки на карточку категории скрипта с указанным текстом.
     *
     * @param name текст ссылки.
     */
    public static void assertCategoryName(String name)
    {
        GUITester.assertTextPresentWithMsg(X_CATEGORY_LINK, name, "Название категории не совпадает с ожидаемым.");
    }

    /**
     * Проверяет код скрипта на соответствие ожидаемому.
     *
     * @param code ожидаемый код скрипта.
     */
    public static void assertCode(String code)
    {
        GUITester.assertTextPresentWithMsg(X_CODE, code, "Значение атрибута 'Код' не совпадает с ожидаемым.");
    }

    /**
     * Проверяет наличие [Без класса] в месте использования без классов
     */
    public static void assertHolderLinksAreAbsent(ScriptUsagePoint usage)
    {
        Attribute classAttr = DAOAttribute.createPseudo("Классы", "metaClass", Constants.CLASS_SYSTEM_NOCLASS_TITLE);
        usagePoints().content().asserts().attrValue(usage, classAttr);
    }

    /**
     * Проверяет количество мест использования скрипта
     * @param expected - ожидаемое кол-во
     */
    public static void assertNumberUsagePoint(int expected)
    {
        usagePoints().content().asserts().rowsNumberOnCurrentPage(expected);
    }

    /**
     * Проверяет текст скрипта на соответствие ожидаемому.
     *
     * @param script ожидаемый текст скрипта.
     */
    public static void assertText(String script)
    {
        GUITester.assertTextPresentWithMsg(X_TEXT, script, true, "Текст скрипта не совпал с ожидаемым.");
    }

    /**
     * Позволяет убедиться, что мы находимся на карточке скрипта, представленной
     * моделью информации о скрипте
     *
     * @param info модель информации о скрипте
     */
    public static void assertThatCard(ScriptInfo info)
    {
        assertTrue("Находимся не на карточке скрипта", tester.waitAppear(X_CODE));
        assertCode(info.getCode());
    }

    /**
     * Проверяет название скрипта на соответствие ожидаемому.
     *
     * @param title ожидаемое название скрипта.
     */
    public static void assertTitle(String title)
    {
        GUITester.assertTextPresentWithMsg(X_TITLE, title, "Значение атрибута 'Название' не совпадает с ожидаемым.");
    }

    /**
     * Проверяет атрибуты скрипта "Название", "Текст" на соответствие ожидаемым.
     *
     * @param script модель информации о скрипте с ожидаемыми значениями.
     */
    public static void assertTitleText(ScriptInfo script)
    {
        assertTitle(script.getTitle());
        assertText(script.getBody());
    }

    /**
     * Проверяет значения колонки "Название" указанного места использования скрипта
     * @param usage - место использования
     */
    public static void assertUsageClasses(ScriptUsagePoint usage)
    {
        Attribute titleAttr = DAOAttribute.createPseudo("Классы", "metaClass", usage.getMetaClass());
        usagePoints().content().asserts().attrValue(usage, titleAttr);
    }

    /**
     * Проверяет значения всех колонок места использования скрипта
     * @param usage - место использования
     */
    public static void assertUsagePoint(ScriptUsagePoint usage)
    {
        Attribute titleAttr = DAOAttribute.createPseudo("Название", "title", usage.getTitle());
        Attribute categoryAttr = DAOAttribute.createPseudo("Категории", "category", usage.getCategory());
        Attribute classAttr = DAOAttribute.createPseudo("Классы", "metaClass", usage.getMetaClass());
        usagePoints().content().asserts().attrValue(usage, titleAttr, categoryAttr, classAttr);
    }

    /**
     * Проверка существования мест использования скрипта на карточке скрипта
     *
     * @param models модели объектов, в которых используется скрипт
     */
    public static void assertUsagePointsExist(ModelUuid... models)
    {
        int tableSize = GUISearch.countObjects();
        Assert.assertEquals("Количество строк в таблице не совпало с ожидаемым количеством мест использования.",
                tableSize, models.length);

        List<String> titles = Stream.of(models)
                .map(Model::getTitle)
                .sorted()
                .collect(Collectors.toList());
        for (int i = 0; i < tableSize; i++)
        {
            int rowNumber = i + 1;
            String usagePlaceTitle = titles.get(i);
            String expectedTitlePath = String.format(TABLE + X_TABLE_ROW_TITLE, rowNumber, usagePlaceTitle);
            List<String> actualTextElements = GUITester.getTextElements(String.format(TABLE + X_TABLE_ROW, rowNumber));

            GUITester.assertPresent(expectedTitlePath,
                    String.format(SCRIPT_USAGE_PLACE_ERROR_MASSAGE, rowNumber, usagePlaceTitle, actualTextElements));
        }
    }

    /**
     * Проверка отсутствия мест использования скрипта на карточке скрипта
     *
     * @param models модели объектов, в которых используется скрипт
     */
    public static void assertUsagePointNotExist(ModelUuid... models)
    {
        int tableSize = GUISearch.countObjects();
        for (Model model : models)
        {
            for (int i = 0; i < tableSize; i++)
            {
                int rowNumber = i + 1;
                List<String> actualTextElements = GUITester.getTextElements(
                        String.format(TABLE + X_TABLE_ROW, rowNumber));
                Assert.assertFalse("Среди мест использования скрипта, есть объект: " + model.getTitle(),
                        actualTextElements.contains(model.getTitle()));
            }
        }
    }

    /**
     * Проверяет значения всех колонок места использования скрипта по их названиям 
     * (без учёта пробелов и переносов)
     * @param usage - место использования
     */
    public static void assertUsagePointByTitle(ScriptUsagePoint usage)
    {
        String actual = tester.getText(usagePoints().content().getContentXpath() + CONTENT_ROW, usage.getUuid());
        actual = actual.replaceAll("\n|\r\n| ", "");
        String expected = usage.getTitle() + usage.getCategory() + usage.getMetaClass();
        expected = expected.replaceAll("\n|\r\n| ", "");
        Assert.assertEquals("В advlist'е отсутствует строка: ", expected, actual);
    }

    /**
     * Проверяет значения колонки "Название" указанного места использования скрипта
     * @param usage - место использования
     */
    public static void assertUsageTitle(ScriptUsagePoint usage)
    {
        Attribute titleAttr = DAOAttribute.createPseudo("Название", "title", usage.getTitle());
        usagePoints().content().asserts().attrValue(usage, titleAttr);
    }

    /**
     * Нажимает кнопку "редактировать" в карточке скрипта
     */
    public static void clickEdit()
    {
        tester.click(GUIXpath.Div.EDIT);
    }

    /**
     * Нажимает кнопку "редактировать" в карточке скрипта
     */
    public static void clickDelete()
    {
        tester.click(Div.DEL);
    }

    /**
     * Редактирование текста скрипта
     * @param scriptBody новый текст скрипта
     */
    public static void editScriptBody(String scriptBody)
    {
        clickEdit();

        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUIScriptField.sendKeysUsingJS(scriptBody);
        GUIForm.applyModalForm();
    }

    /**
     * Редактирование текста скрипта с последующей ошибкой
     * @param scriptBody новый текст скрипта
     * @param errorMessage ожидаемый текст ошибки
     */
    public static void editScriptBodyAssertError(String scriptBody, String errorMessage)
    {
        clickEdit();
        GUIForm.assertFormAppear(GUIXpath.Div.PROPERTY_DIALOG_BOX);
        GUIScriptField.sendKeysUsingJS(scriptBody);
        GUIForm.applyFormAssertError(errorMessage);
    }

    /**
     * Возвращает код скрипта отображаемый на карточке
     * используется для вычисления кода скрипта в GUI тестах
     * когда скрипты создаются через GUI.
     */
    public static String getCode()
    {
        return tester.getText(X_CODE);
    }

    /**
     * Переход на карточку скрипта, чья информация в модели.
     *
     * @param info информация о скрипте.
     */
    public static void goToCard(ScriptInfo info)
    {
        tester.goToPage(getCardUrl(info));
    }

    /**
     * Переход на карточку и проверка наличия сообщения о отсутствии объекта
     *
     * @param info информация о скрипте
     */
    public static void goToCardAssertMissing(ScriptInfo info)
    {
        String currentUrl = GUITester.getCurrentUrl();
        String cardUrl = getCardUrl(info);
        if (cardUrl.equals(currentUrl))
        {
            tester.refresh();
        }
        else
        {
            tester.goToPage(cardUrl);
        }
        GUIForm.assertErrorMessageOnForm(ErrorMessages.RESOURCE_CANNOT_BE_FOUND_MESSAGE);
    }

    /**
     * Переход по ссылке местоположения скрипта
     */
    public static void goToLocation(ScriptUsagePoint usage)
    {
        usagePoints().content().clickCellTitle(usage);
        if (ScriptCategory.ATTRIBUTE_CATEGORIES_DEFAULT_VALUE_TITLE.equals(usage.getCategory()))
        {
            assertTrue(tester.waitAppear(GUIMetaClass.X_TEXT_FIELD_INFO_TITLE_VALUE));
        }
    }

    /**
     * Выбирает на форме редактирования скрипта категории (предполагаемые места использования)
     * @param categoriesCodes коды категорий - предполагаемых мест использования
     */
    public static void selectDefaultCategories(String... categoriesCodes)
    {
        GUIMultiSelect.select(EDIT_DEFAULT_CATEGORY, categoriesCodes);
    }

    /**
     * Снимает галочки категории (предполагаемые места использования) на форме редактирования скрипта 
     * @param categoriesCodes коды категорий - предполагаемых мест использования
     */
    public static void unSelectDefaultCategories(String... categoriesCodes)
    {
        GUIMultiSelect.unselectNotClean(EDIT_DEFAULT_CATEGORY, categoriesCodes);
    }

    /**
     * @return утилитарный объект для работы с местами использования
     */
    public static GUIAdvListUtil usagePoints()
    {
        if (usagePoints == null)
        {
            usagePoints = new GUIAdvListUtil(USAGE_POINTS);
        }
        return usagePoints;
    }

    /**
     * Проверить, что кнопка "Редактировать" отсутствует
     */
    public static void assertEditButtonAbsent()
    {
        GUITester.assertAbsent(X_EDIT_BUTTON, "Кнопка 'Редактировать' присутствует на странице");
    }

    /**
     * Проверить, что кнопка "Удалить" отсутствует
     */
    public static void assertDeleteButtonAbsent()
    {
        GUITester.assertAbsent(X_DELETE_BUTTON, "Кнопка 'Удалить' присутствует на странице");
    }

    /**
     * Получить URL страницы - карточки скрипта 
     * @param info информация о скрипте
     * @return URL карточки
     */
    private static String getCardUrl(ScriptInfo info)
    {
        return StandTypeHolder.getCurrentStandUrl() + GUINavigational.URL_POSTFIX_ADMIN + "#script:" + info.getCode();
    }
}