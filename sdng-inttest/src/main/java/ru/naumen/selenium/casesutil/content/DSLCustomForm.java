package ru.naumen.selenium.casesutil.content;

import java.util.List;
import java.util.Map;

import org.junit.Assert;

import java.util.ArrayList;
import java.util.HashMap;

import ru.naumen.selenium.casesutil.model.Model;
import ru.naumen.selenium.casesutil.model.content.CustomForm;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.ScriptRunner;
import ru.naumen.selenium.casesutil.scripts.element.SECustomForm;
import ru.naumen.selenium.modules.IModuleCustomForm;
import ru.naumen.selenium.modules.ScriptModules;

/**
 * Утилитарные методы для работы со специальными формами
 * <AUTHOR>
 * @since 15.06.2016
 */
public class DSLCustomForm
{
    private static final String ATTR_GROUP = "attributeGroup";
    private static final String FORM_TYPE = "formType";
    private static final String TRANSITION_CLASSES = "transitionClasses";
    private static final String FORM_CODE = "formCode";
    private static final String COMMENT_ON_FORM = "commentOnForm";
    private static final String COMMENT_ATTR_GROUP = "commentAttributeGroup";
    private static final String USE_AS_DEFAULT = "useAsDefault";
    private static final String QUICK_FORM = "quickAddAndEditForm";
    private static final String MASS_EDIT_FORM = "massEditForm";
    private static final String UUID = "uuid";
    private static final String SHOW_ATTR_DESCRIPTION = "showAttrDescription";
    public static final String IMMEDIATE_OBJECT_SAVING_ENABLED = "immediateObjectSavingEnabled";
    private static final String SETTINGS_SET = "settingsSet";

    /**
     * Получить модуль для работы со специальными формами
     */
    public static IModuleCustomForm getModuleCustomForm()
    {
        return ScriptModules.getModuleCustomForm();
    }

    /**
     * Добавить специальную форму
     * @param models набор добавляемых форм
     */
    public static void add(CustomForm... models)
    {
        List<Map<String, Object>> formsData = new ArrayList<>();
        for (CustomForm model : models)
        {
            Map<String, Object> formData = new HashMap<>();
            if (QUICK_FORM.equals(model.getFormType().getCode()) || MASS_EDIT_FORM.equals(
                    model.getFormType().getCode()))
            {
                formData.put(UUID, model.getUuid());
            }
            else
            {
                formData.put(UUID, model.getFormType().getCode());
            }

            formData.put(Model.TITLE, model.getTitle());
            formData.put(FORM_TYPE, model.getFormType().name());
            formData.put(FORM_CODE, model.getFormType().getCode());
            formData.put(USE_AS_DEFAULT, model.getUseAsDefault());
            formData.put(IMMEDIATE_OBJECT_SAVING_ENABLED, model.isImmediateObjectSavingEnabled());
            formData.put(SETTINGS_SET, model.getSettingsSet());

            switch (model.getFormType()) // NOPMD
            {
                case ChangeCaseForm:
                    if (model.getShowAttrDescription() != null)
                    {
                        formData.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                    }
                    if (model.getCommentAttributeGroupCode() != null)
                    {
                        formData.put(COMMENT_ATTR_GROUP, model.getCommentAttributeGroupCode());
                    }
                case ChangeResponsibleForm:
                    if (model.getShowAttrDescription() != null)
                    {
                        formData.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                    }
                    if (model.getCommentAttributeGroupCode() != null)
                    {
                        formData.put(COMMENT_ATTR_GROUP, model.getCommentAttributeGroupCode());
                    }
                case QuickForm:
                case MassEditForm:
                    if (model.getAttributeGroupCode() != null)
                    {
                        formData.put(ATTR_GROUP, model.getAttributeGroupCode());
                    }
                    if (model.getUseAsDefault() != null)
                    {
                        formData.put(USE_AS_DEFAULT, model.getUseAsDefault());
                    }
                    if (model.getShowAttrDescription() != null)
                    {
                        formData.put(SHOW_ATTR_DESCRIPTION, model.getShowAttrDescription());
                    }
                    if (model.getCommentAttributeGroupCode() != null)
                    {
                        formData.put(COMMENT_ATTR_GROUP, model.getCommentAttributeGroupCode());
                    }
                    formData.put(TRANSITION_CLASSES, model.getTransitionClasses());
                    formData.put(COMMENT_ON_FORM, model.getCommentOnForm());
                    break;
                default:
                    throw new RuntimeException("Unknown content: " + model.getFormType());
            }

            formsData.add(formData);
        }

        getModuleCustomForm().addCustomForms(formsData);

        for (CustomForm model : models)
        {
            model.setExists(true);
            model.setXpathId("gwt-debug-" + model.getFormType().getTitle() + "." + model.getUuid());
        }
    }

    /**
     * Проверить отсутствие формы
     * @param model - форма
     */
    public static void assertAbsence(CustomForm model)
    {
        Assert.assertFalse("Специальная форма присутствует", DSLCustomForm.isPresent(model));
    }

    /**
     * Проверить наличие формы
     * @param model - форма
     */
    public static void assertPresent(CustomForm model)
    {
        Assert.assertTrue("Специальная форма отсутствует", DSLCustomForm.isPresent(model));
    }

    /**
     * Удалить специальную форму
     * @param models модель формы
     */
    public static void delete(CustomForm... models)
    {
        List<ScriptElement> elements = new ArrayList<>();
        for (CustomForm model : models)
        {
            elements.add(SECustomForm.delete(model));
        }
        ScriptRunner script = new ScriptRunner(true, elements);
        script.runScript();
        for (CustomForm model : models)
        {
            model.setExists(false);
        }
    }

    /**
     * Редактировать специальную форму                                             
     * @param models набор редактируемых форм
     */
    public static void edit(CustomForm... models)
    {
        List<Map<String, Object>> formsData = new ArrayList<>();
        for (CustomForm model : models)
        {
            Map<String, Object> formData = new HashMap<>();
            if (QUICK_FORM.equals(model.getFormType().getCode()) || MASS_EDIT_FORM.equals(
                    model.getFormType().getCode()))
            {
                formData.put(UUID, model.getUuid());
            }
            else
            {
                formData.put(UUID, model.getFormType().getCode());
            }

            formData.put(Model.TITLE, model.getTitle());
            formData.put(FORM_TYPE, model.getFormType().name());
            formData.put(FORM_CODE, model.getFormType().getCode());
            formData.put(SETTINGS_SET, model.getSettingsSet());

            switch (model.getFormType())
            {
                case QuickForm:
                    formData.put(IMMEDIATE_OBJECT_SAVING_ENABLED, model.isImmediateObjectSavingEnabled());
                    fillFormData(formData, model);
                    break;
                case ChangeCaseForm:
                case ChangeResponsibleForm:
                case MassEditForm:
                    fillFormData(formData, model);
                    break;
                default:
                    throw new RuntimeException("Unknown content: " + model.getFormType());
            }
            formsData.add(formData);
            getModuleCustomForm().editCustomForms(formsData);
        }
    }

    private static void fillFormData(Map<String, Object> formData, CustomForm model)
    {
        if (model.getAttributeGroupCode() != null)
        {
            formData.put(ATTR_GROUP, model.getAttributeGroupCode());
        }
        formData.put(TRANSITION_CLASSES, model.getTransitionClasses());
        formData.put(COMMENT_ON_FORM, model.getCommentOnForm());
        if (model.getCommentAttributeGroupCode() != null)
        {
            formData.put(COMMENT_ATTR_GROUP, model.getCommentAttributeGroupCode());
        }
        formData.put(FORM_CODE, model.getUuid());
    }

    /**
     * Присутствует ли форма
     * @param model - форма
     * @return true - да, false - нет
     */
    public static boolean isPresent(CustomForm model)
    {
        Map<String, Object> formData = new HashMap<>();
        if (QUICK_FORM.equals(model.getFormType().getCode()) || MASS_EDIT_FORM.equals(model.getFormType().getCode()))
        {
            formData.put(UUID, model.getUuid());
        }
        else
        {
            formData.put(UUID, model.getFormType().getCode());
        }
        formData.put(FORM_TYPE, model.getFormType().name());
        formData.put(FORM_CODE, model.getFormType().getCode());

        switch (model.getFormType())
        {
            case QuickForm:
            case ChangeCaseForm:
            case ChangeResponsibleForm:
            case MassEditForm:
                formData.put(FORM_CODE, model.getUuid());
                formData.put(TRANSITION_CLASSES, model.getTransitionClasses());
                break;
            default:
                throw new RuntimeException("Unknown content: " + model.getFormType());
        }

        return getModuleCustomForm().isPresentCustomForm(formData);
    }

}
