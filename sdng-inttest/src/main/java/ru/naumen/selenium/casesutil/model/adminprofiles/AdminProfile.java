package ru.naumen.selenium.casesutil.model.adminprofiles;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import ru.naumen.selenium.casesutil.content.advlist.AdvListEntry;
import ru.naumen.selenium.casesutil.model.IForRemoveModel;
import ru.naumen.selenium.casesutil.model.ModelCode;
import ru.naumen.selenium.casesutil.scripts.ScriptElement;
import ru.naumen.selenium.casesutil.scripts.element.SEAdminProfiles;

/**
 * Модель профиля администрирования
 * <AUTHOR>
 * @since 04.05.2024
 */
public class AdminProfile extends ModelCode implements IForRemoveModel, AdvListEntry
{
    /**
     * Код профиля администрирования.
     */
    public static final String CODE = "code";

    /**
     * Описание профиля администрирования.
     */
    public static final String DESCRIPTION = "description";

    /**
     * Матрица маркеров доступа в профиле администрирования
     */
    public static final String ACCESS_MARKER_MATRIX = "accessMarkerMatrix";

    public AdminProfile()
    {
        super();
    }

    public AdminProfile(Map<String, String> model)
    {
        super();
        model.forEach((key, value) -> this.model.put(key, value));
    }

    @Override
    public List<ScriptElement> addToRemoveScript()
    {
        List<ScriptElement> scripts = new ArrayList<>();
        if (isExists())
        {
            scripts.add(SEAdminProfiles.deleteAdminProfile(getCode()));
        }
        return scripts;
    }

    @Override
    public String entryId()
    {
        return getCode();
    }

    public String getDescription()
    {
        return model.get(DESCRIPTION);
    }

    public String getAccessMarkerMatrix()
    {
        return model.get(ACCESS_MARKER_MATRIX);
    }

    public void setDescription(String description)
    {
        model.put(DESCRIPTION, description);
    }

    public void setAccessMarkerMatrix(AdminProfileAccessMarkerMatrix matrix)
    {
        model.put(ACCESS_MARKER_MATRIX, matrix.asJsonString());
    }
}