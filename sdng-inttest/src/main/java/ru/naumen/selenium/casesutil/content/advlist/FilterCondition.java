package ru.naumen.selenium.casesutil.content.advlist;

/**
 * Возможные критерии фильтрации в адвлистах
 * <AUTHOR>
 * @since 25.05.2015
 */
public enum FilterCondition
{
    //@formatter:off
    CONTAINS("contains", "содержит"),
    FTS_CONTAINS("fullTextSearch_contains", "содержит"),
    NOT_CONTAINS("notContains", "не содержит"),
    NOT_CONTAINS_AND_NOT_EMPTY("notContains", "не содержит (и не пусто)"),
    NOT_CONTAINS_INCLUDE_EMPTY("notContainsIncludeEmpty", "не содержит (включая пустые)"),
    CONTAINS_IN_SET("containsInSet", "содержит любое из значений"),
    NOT_CONTAINS_IN_SET("notContainsInSet", "не содержит любое из значений"),
    EMPTY("null", "пусто"),
    NOT_EMPTY("notNull", "не пусто"),
    TITLE_CONTAINS("titleContains", "название содержит"),
    TITLE_NOT_CONTAINS("titleNotContains", "название не содержит"),
    FROM_TO("fromTo", "с ... по"),
    EQUAL("contains", "равно"),
    GREATER("greater", "больше"),
    LESS("less", "меньше"),
    TIMER_STATUS_CONTAINS("timerStatusContains", "статус содержит"),
    TIMER_STATUS_NOT_CONTAINS("timerStatusNotContains", "статус не содержит"),
    BACK_TIMER_DEADLINE_CONTAINS("backTimerDeadLineContains", "просроченность содержит"),
    BACK_TIMER_DEADLINE_FROM_TO("backTimerDeadLineFromTo", "время окончания с ... по"),
    LAST_N("lastN", "за последние \"n\" дней"),
    NEXT_N("nextN", "в ближайшие \"n\" дней"),
    LAST_N_HOURS("lastNHours", "за последние \"n\" часов"),
    NEXT_N_HOURS("nextNHours", "в ближайшие \"n\" часов"),
    STARTING_FROM("startingFrom", "начиная с"),
    FINISHING_UP_TO("finishingUpTo", "заканчивая до"),
    TODAY("today", "сегодня"),
    STRING_CONTAINS("StringContains", "содержит"),
    STRING_NOT_CONTAINS("StringNotContains", "не содержит"),
    SELECT_LIST_CONTAINS("SelectListContains", "содержит"),
    SELECT_LIST_NOT_CONTAINS("SelectListNotContains", "не содержит"),
    INTEGER_CONTAINS("IntegerContains", "содержит"),
    INTEGER_NOT_CONTAINS("IntegerNotContains", "не содержит"),
    INTEGER_GEATER("IntegerGreater", "больше"),
    INTEGER_LESS("IntegerLess", "меньше"),
    CONTAINS_YES_NO("ContainsYesNo", "содержит"),
    CONTAINS_WITH_REMOVED("containsWithRemoved", "содержит (включая архивные)"),
    NOT_CONTAINS_WITH_REMOVED("notContainsWithRemoved", "не содержит (включая архивные)"),
    CONTAINS_WITH_NESTED("containsWithNested", "содержит (включая вложенные)"),
    CONTAINS_USER("containsUser", "содержит текущего пользователя"),
    NOT_CONTAINS_USER("notContainsUser", "не содержит текущего пользователя"),
    CONTAINS_SUBJECT("containsSubject", "содержит текущий объект"),
    CONTAINS_SUBJECT_WITH_NESTED("containsSubjectWithNested", "содержит текущий объект (включая вложенные)"),
    NOT_CONTAINS_SUBJECT("notContainsSubject", "не содержит текущий объект"),
    EQUALS_SUBJECT_WITH_NESTED("containsSubjectWithNested", "равно текущему объекту (включая вложенные)"),
    EQUALS_USER("containsUser", "равно текущему пользователю"),
    NOT_EQUALS_USER("notContainsUser", "не равно текущему пользователю"),
    EQUALS_SUBJECT("containsSubject", "равно текущему объекту"),
    NOT_EQUALS_SUBJECT("notContainsSubject", "не равно текущему объекту"),
    MYSELF("containsUser", "я сам (сама)"),
    NOT_MYSELF("notContainsUser", "не я"),
    CONTAINS_USER_ATTRIBUTE("containsUserAttribute", "содержит атрибут текущего пользователя"),
    CONTAINS_USER_ATTRIBUTE_WITH_NESTED("containsUserAttributeWithNested", "содержит атрибут текущего пользователя (включая вложенные)"),
    NOT_CONTAINS_USER_ATTRIBUTE("notContainsUserAttribute", "не содержит атрибут текущего пользователя"),
    EQUALS_USER_ATTRIBUTE("containsUserAttribute", "равно атрибуту текущего пользователя"),
    EQUALS_USER_ATTRIBUTE_WITH_NESTED("containsUserAttributeWithNested", "равно атрибуту текущего пользователя (включая вложенные)"),
    NOT_EQUALS_USER_ATTRIBUTE("notContainsUserAttribute", "не равно атрибуту текущего пользователя"),
    CONTAINS_SUBJECT_ATTRIBUTE("containsSubjectAttribute", "содержит атрибут текущего объекта"),
    CONTAINS_SUBJECT_ATTRIBUTE_WITH_NESTED("containsSubjectAttributeWithNested", "содержит атрибут текущего объекта (включая вложенные)"),
    NOT_CONTAINS_SUBJECT_ATTRIBUTE("notContainsSubjectAttribute", "не содержит атрибут текущего объекта"),
    EQUALS_SUBJECT_ATTRIBUTE("containsSubjectAttribute", "равно атрибуту текущего объекта"),
    EQUALS_SUBJECT_ATTRIBUTE_WITH_NESTED("containsSubjectAttributeWithNested", "равно атрибуту текущего объекта (включая вложенные)"),
    NOT_EQUALS_SUBJECT_ATTRIBUTE("notContainsSubjectAttribute", "не равно атрибуту текущего объекта"),
    BEFORE_SUBJECT_ATTRIBUTE("beforeSubjectAttribute", "раньше атрибута текущего объекта"),
    AFTER_SUBJECT_ATTRIBUTE("afterSubjectAttribute", "позже атрибута текущего объекта"),
    BEFORE_USER_ATTRIBUTE("beforeUserAttribute", "раньше атрибута текущего пользователя"),
    AFTER_USER_ATTRIBUTE("afterUserAttribute", "позже атрибута текущего пользователя"),
    INCORRECT("incorrect", "Некорректный фильтр");
    //@formatter:on

    private final String code;
    private final String title;

    FilterCondition(String code, String title)
    {
        this.code = code;
        this.title = title;
    }

    public String getCode()
    {
        return code;
    }

    public String getTitle()
    {
        return title;
    }
}
