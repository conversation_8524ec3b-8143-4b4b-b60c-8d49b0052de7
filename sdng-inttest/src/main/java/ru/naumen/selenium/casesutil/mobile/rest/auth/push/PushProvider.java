package ru.naumen.selenium.casesutil.mobile.rest.auth.push;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

/**
 * Перечисление провайдеров push-уведомлений
 *
 * <AUTHOR>
 * @since 21.01.2025
 */
public enum PushProvider
{
    FCM("fcm"), RUSTORE("rustore"), HMS("hms");

    private static final Map<String, PushProvider> CODE_TO_PROVIDER = Arrays.stream(PushProvider.values())
            .collect(Collectors.toUnmodifiableMap(PushProvider::getCode, Function.identity()));
    static final Set<String> CODES = Collections.unmodifiableSet(CODE_TO_PROVIDER.keySet());

    private final String code;

    PushProvider(String code)
    {
        this.code = code;
    }

    public String getCode()
    {
        return code;
    }

    public static PushProvider of(@Nullable String code)
    {
        PushProvider provider = CODE_TO_PROVIDER.get(code);
        if (provider == null)
        {
            throw new IllegalArgumentException("Invalid push provider code: " + code);
        }

        return provider;
    }
}
