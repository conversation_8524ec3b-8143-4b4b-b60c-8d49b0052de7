package ru.naumen.selenium.modules;

import java.util.Map;

/**
 * Скриптовый модуль для управления профилями администрирования
 * <AUTHOR>
 * @since 04.05.2024
 */
@ModuleName(code = "ModuleAdminProfile")
public interface IModuleAdminProfile extends IModule
{
    /**
     * Создает новый профиль администрирования.
     * @param model модель профиля администрирования
     */
    void addAdminProfile(Map<String, String> model);

    /**
     * Удаляет указанный профиль администрирования.
     * @param model модель профиля администрирования
     */
    void deleteAdminProfile(Map<String, String> model);

    /**
     * Удаляет профиль администрирования по его коду.
     * @param codes коды профилей администрирования
     */
    void deleteAdminProfile(String... codes);

    /**
     * Изменяет существующий профиль администрирования.
     * @param model модель профиля администрирования
     */
    void editAdminProfile(Map<String, String> model);
}