package ru.naumen.selenium.modules;

/**
 * Модуль для работы с конфигурацией динамических полей.
 * <AUTHOR>
 * @since Apr 07, 2024
 */
@ModuleName(code = "ModuleDynamicFieldConfiguration")
public interface IModuleDynamicFieldConfiguration extends IModule
{
    /**
     * Перезагружает конфигурацию стенда.
     */
    void reload();

    /**
     * Перезагружает конфигурацию из строки XML.
     * @param configuration строка с XML-конфигурацией
     */
    void reload(String configuration);
}
