package ru.naumen.modules

import ru.naumen.core.shared.form.dropdownsettings.DropDownSettings
import ru.naumen.core.shared.form.dropdownsettings.SingleValueSubstitutionSettings
import ru.naumen.core.shared.form.dropdownsettings.dispatch.SetDropDownSettingsAction

import groovy.transform.Field

@Field ON_FOR_ALL = 'onForAll'
@Field ON_FOR_REQUIRED = 'onForRequired'

def enableSubstitution()
{
    setSubstitutionEnabled(true)
}

def disableSubstitution()
{
    setSubstitutionEnabled(false)
}

def setSubstituteAllAttrsOnForms(def formsCodes)
{
    setSubstituteOnForms(ON_FOR_ALL, formsCodes)
}

def setSubstituteRequiredAttrsOnForms(def formsCodes)
{
    setSubstituteOnForms(ON_FOR_REQUIRED, formsCodes)
}

def setSubstitutionEnabled(def isEnabled)
{
    def substitutionSettings = new SingleValueSubstitutionSettings(isEnabled)
    dispatch.execute(new SetDropDownSettingsAction(new DropDownSettings(substitutionSettings)))
}

def setSubstituteOnForms(def mode, def formsCodes)
{
    def substitutionSettings = switch (mode)
    {
        case "onForAll" -> new SingleValueSubstitutionSettings(true, formsCodes, Set.of())
        case "onForRequired" -> new SingleValueSubstitutionSettings(true, Set.of(), formsCodes);
        default -> new SingleValueSubstitutionSettings()
    }
    dispatch.execute(new SetDropDownSettingsAction(new DropDownSettings(substitutionSettings)))
}


