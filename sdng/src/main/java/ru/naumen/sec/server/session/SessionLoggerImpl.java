package ru.naumen.sec.server.session;

import static ru.naumen.sec.server.servlets.LoginServlet.USER_NAME;

import java.util.Enumeration;

import org.hibernate.Hibernate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.common.server.utils.HibernateUtil;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.AuditFacade;
import ru.naumen.sec.server.users.employee.EmployeeUser;

/**
 * Реализация сервиса логирования событий, связанных с сессиями работы пользователей.
 * <AUTHOR>
 * @since May 07, 2019
 */
@Component
public class SessionLoggerImpl implements SessionLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(SessionLoggerImpl.class);

    private final EventService eventService;
    private final MessageFacade messages;
    private final IPrefixObjectLoaderService objectLoader;
    private final UserInfoService userInfoService;
    private final AuditFacade auditFacade;

    @Inject
    public SessionLoggerImpl(EventService eventService,
            MessageFacade messages,
            IPrefixObjectLoaderService objectLoader,
            UserInfoService userInfoService,
            AuditFacade auditFacade)
    {
        this.eventService = eventService;
        this.messages = messages;
        this.objectLoader = objectLoader;
        this.userInfoService = userInfoService;
        this.auditFacade = auditFacade;
    }

    @Override
    public void login(Object principal, SessionEventType eventSource)
    {
        if (!SessionEventType.MOBILE.equals(eventSource))
        {
            authEvent(principal, null, Categories.LOGIN_SUCCESSFUL);
        }
        else
        {
            authEvent(principal, null, Categories.LOGIN_SUCCESSFUL_FROM_MOBILE);
        }
    }

    @Override
    public void loginFailure(String ip, String username, String message, HttpServletRequest request)
    {
        if (username == null)
        {
            return;
        }
//        JsonObject jsonObject = userInfoService.getUserInfo(username, ip, message);
//        String category = Categories.LOGIN_FAILURE;
//        eventService.txEvent(category, getUsernameFromRequest(request), message);
//        if (jsonObject != null)
//        {
//            LOG.info(messages.getMessage("eventService.%s".formatted(category), username, jsonObject));
//        }
    }

    private String getUsernameFromRequest(HttpServletRequest request)
    {
        boolean sessionAttrContainsUserName = false;
        Enumeration<String> attributes = request.getSession().getAttributeNames();
        while (attributes.hasMoreElements())
        {
            if (attributes.nextElement().equals(USER_NAME))
            {
                sessionAttrContainsUserName = true;
                break;
            }
        }
        return sessionAttrContainsUserName
                ? request.getSession().getAttribute(USER_NAME).toString()
                : request.getParameter("username");
    }

    @Override
    public void logout(SessionInformation session, @Nullable LogoutReason reason)
    {
        if (session instanceof SessionInfoBase sessionInfo && sessionInfo.isExpirationLoggingAllowed())
        {
            logout(sessionInfo.getPrincipal(), sessionInfo.getEventSource(), reason);
            sessionInfo.preventExpirationLogging();
        }
    }

    @Override
    public void logout(Object principal, SessionEventType eventSource, @Nullable LogoutReason reason)
    {
        switch (eventSource)
        {
        case COMMON -> authEvent(principal, reason, Categories.LOGOUT);
        case COMMON_LINKED_WITH_MOBILE ->
                authEvent(principal, reason, Categories.LOGOUT, Categories.LOGOUT_FROM_MOBILE);
        case MOBILE -> authEvent(principal, reason, Categories.LOGOUT_FROM_MOBILE);
        default -> LOG.warn("Unknown event source: {}", eventSource);
        }
    }

    private void authEvent(Object principal, @Nullable LogoutReason reason, String... categories) {
        if (!(principal instanceof UserDetails details) || details.getUsername() == null) {
            return;
        }
        String username = details.getUsername();
        Employee employee = null;
        JsonObject jsonObject = null;

        if (principal instanceof EmployeeUser employeeUser) {
            String employeeUuid = employeeUser.getUUID();
            if (employeeUuid == null) {
                LOG.warn("Employee UUID is null for user: {}", username);
                return;
            }
            Object[] result = TransactionRunner.call(TransactionType.NEW_READ_ONLY, () -> {
                Employee loaded = objectLoader.getSafe(employeeUuid);
                if (loaded == null) {
                    LOG.warn("Employee not found by UUID: {}", employeeUuid);
                    return null;
                }
                loaded = HibernateUtil.unproxy(loaded);
                Hibernate.initialize(loaded.getTeams());
                Hibernate.initialize(loaded.getLicense());
                return new Object[] { loaded, userInfoService.getUserInfo(loaded) };
            });
            if (result == null) return;
            employee = (Employee) result[0];
            jsonObject = (JsonObject) result[1];
        }

        for (String category : categories) {
            eventService.txEvent(category, employee, null, username);
            String messageKey = "eventService." + category;

            // Используем новый механизм аудита
            auditFacade.info(LOG, messages.getMessage(messageKey, username), employee, principal);

            if (reason != null) {
                LOG.info("Logout reason of user {} is {}", username, reason.getCode());
            }
        }
    }
}
