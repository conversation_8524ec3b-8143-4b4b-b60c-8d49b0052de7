package ru.naumen.sec.server.handlers;

import static ru.naumen.sec.server.servlets.LoginServlet.USER_NAME;

import java.io.IOException;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.log.LogLevel;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditFacade;
import ru.naumen.core.server.util.log.login.LoginFailureLogger;
import ru.naumen.sec.server.AuthType;

/**
 * Базовая реализация {@link AuthenticationFailureHandler обработчика неудачного входа в систему}.
 *
 * <AUTHOR>
 * @since 18.07.2025
 */
public abstract class BaseAuthenticationFailureHandler implements AuthenticationFailureHandler
{
    private static final String AUTHENTICATION_TYPE_PROPERTY_NAME = "authenticationType";

    private final MessageFacade messages;
    private final UserInfoService userInfoService;
    private final AuditFacade auditFacade;

    @Inject
    protected BaseAuthenticationFailureHandler(MessageFacade messages,
            UserInfoService userInfoService,
            AuditFacade auditFacade)
    {
        this.messages = messages;
        this.userInfoService = userInfoService;
        this.auditFacade = auditFacade;
    }

    @Override
    public final void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException exception) throws IOException, ServletException
    {
        String authenticationAttemptUsername = getUsernameFromRequest(request);
        String loginFailureMessage = messages.getMessage("eventService." + Categories.LOGIN_FAILURE,
                authenticationAttemptUsername, exception.getMessage());

        // Создаем дополнительные данные аутентификации
        ObjectNode customAuthData = JsonNodeFactory.instance.objectNode();
        customAuthData.put(AuditConstants.AuthenticationInfo.AUTHENTICATION_TYPE, getAuthenticationType().getName());

        // Используем новый механизм аудита
        auditFacade.info(LoginFailureLogger.getLogger(), loginFailureMessage,
                        request, exception, getAuthenticationType(), customAuthData);

        processAuthenticationFailure(request, response, exception);
    }

    /**
     * Обработать неудачную попытку аутентификации.
     *
     * @param request запрос, во время которого была совершена попытка аутентификации
     * @param response ответ
     * @param exception ошибка, возникшая в процессе аутентификации
     */
    public abstract void processAuthenticationFailure(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException exception)
            throws IOException, ServletException;

    @Nullable
    protected String getUsernameFromRequest(HttpServletRequest request)
    {
        HttpSession httpSession = request.getSession(false);
        if (httpSession == null)
        {
            return null;
        }

        Object userNameHttpSessionAttribute = httpSession.getAttribute(USER_NAME);
        return userNameHttpSessionAttribute == null
                ? request.getParameter(USER_NAME)
                : userNameHttpSessionAttribute.toString();
    }

    /**
     * @return используемый тип аутентификации
     */
    protected abstract AuthType getAuthenticationType();
}