package ru.naumen.metainfoadmin.client.dynadmin.content.forms;

import com.google.inject.ImplementedBy;

import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

/**
 * Фабрика форм редактирования контентов
 *
 * <AUTHOR>
 *
 */
@ImplementedBy(ContentEditorFactoryImpl.class)
public interface ContentEditorFactory
{
    <T extends FlowContent> Presenter create(T content, UIContext context);
}
