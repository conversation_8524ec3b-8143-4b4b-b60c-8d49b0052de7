package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms.toolbar;

import jakarta.inject.Inject;

import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.HasEnabled;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfoadmin.client.dynadmin.ListDtObjectContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;

import com.google.inject.assistedinject.Assisted;

/**
 * <AUTHOR>
 * @since 25.04.2016
 */
public class EditCustomFormActionHandler extends CustomFormCommandExecuteActionHandlerBase<EditCustomFormAction> implements
        HasEnabled
{
    @Inject
    public EditCustomFormActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<EditCustomFormAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    protected CustomForm getContextObject()
    {
        if (context.getParentContext() instanceof ObjectListUIContext)
        {
            ObjectListUIContext listContext = context.getParentContext();
            DtObject obj = listContext.getObjects().iterator().next();
            return obj
                    .<CustomForm> getProperty(
                            ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.ORIGINAL_CUSTOM_FORM);
        }
        else
        {
            ListDtObjectContext objectContext = context.getParentContext();
            return objectContext.getObject().<CustomForm> getProperty(
                    ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.ORIGINAL_CUSTOM_FORM);
        }
    }
}
