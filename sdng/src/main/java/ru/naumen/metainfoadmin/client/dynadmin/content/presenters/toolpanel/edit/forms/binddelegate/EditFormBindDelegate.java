package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.client.widgets.select.SelectListResources;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.ClassFqnHierarchy;
import ru.naumen.metainfo.shared.mobile.editforms.EditForm;
import ru.naumen.metainfo.shared.tags.TagsUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.events.ToolValueChangedEvent;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

import static ru.naumen.metainfo.shared.ui.Constants.EDIT_FORMS;
import static ru.naumen.metainfo.shared.ui.Constants.FQN_OF_CLASS;
import static ru.naumen.metainfo.shared.ui.Constants.CASES;
import static ru.naumen.metainfo.shared.ui.Constants.TAGS;
import static ru.naumen.metainfo.shared.ui.Constants.CLASSES_HIERARCHY;

/**
 * Компонент, заполняющий свойство "Форма редактирования" списком доступных форм, на формах добавления/редактирования
 * действия на "Карточке объекта" и в контенте "Параметры объекта" для действия "Редактировать"
 * Если карточка не ограничена типом, то выводятся все формы с подходящим классом
 * если карточка ограничена несколькими типами, то выводятся формы с подходящими типами (в списке форм доступны формы,
 * удовлетворяющие хотя бы одному из типов)
 *
 * <AUTHOR>
 * @since 05.12.2022
 */
public class EditFormBindDelegate implements PropertyDelegateBind<SelectItem, ListBoxWithEmptyOptProperty>
{
    @Inject
    private SelectListResources listResources;

    @Override
    public void bindProperty(final PropertyContainerContext context, final ListBoxWithEmptyOptProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().clear();
        fillEditForm(context, property);
        callback.onSuccess(null);
    }

    private void fillEditForm(final PropertyContainerContext context, final ListBoxWithEmptyOptProperty property)
    {
        final ToolFormContext toolFormContext = context.getContextValues().getProperty(ToolFormContext.ID);
        final UIContext parentContext = toolFormContext.getParentContext();
        if (parentContext != null && parentContext.getEventBus() != null)
        {
            ToolValueChangedEvent event = new ToolValueChangedEvent(ToolFormPropertyCodes.EDIT_FORM, context);
            parentContext.getEventBus().fireEvent(event);
        }

        final List<EditForm> forms = parentContext.getContextProperty(EDIT_FORMS);
        if (forms == null)
        {
            return;
        }

        for (final EditForm form : forms)
        {
            if (!isEditFormCorresponds(form, parentContext))
            {
                continue;
            }

            if (TagsUtils.isAnyTagEnabled(getEditFormTags(form, parentContext)))
            {
                property.getValueWidget().addItem(form.getCode(), form.getCode());
                continue;
            }

            property.getValueWidget().addItemWithStyle(form.getCode(), form.getCode(),
                    listResources.cellListStyle().attentionItem());
        }
    }

    private List<DtObject> getEditFormTags(final EditForm editForm, final UIContext context)
    {
        final List<DtObject> allEditFormsTags = context.getContextProperty(TAGS);
        final List<String> tags = editForm.getTags();
        return allEditFormsTags.stream()
                .filter(tag -> tags.contains(tag.getProperty(Constants.Tag.CODE)))
                .collect(Collectors.<DtObject> toList());

    }

    private boolean isEditFormCorresponds(final EditForm form, final UIContext context)
    {
        final ClassFqnHierarchy hierarchy = context.getContextProperty(CLASSES_HIERARCHY);
        final List<ClassFqn> cardCases = context.getContextProperty(CASES);

        final ClassFqn cardClass = context.getContextProperty(FQN_OF_CLASS);
        if (form.getClazz() != null && form.getClazz().isSameClass(cardClass))
        {
            return true;
        }

        // если есть ограничение по типам
        if (CollectionUtils.isNotEmpty(cardCases))
        {
            // хотя бы один кейс карточки есть у формы редактирования
            return getCardCasesWithDescendants(cardCases, hierarchy)
                    .anyMatch(cardCase -> form.getCases().contains(cardCase));
        }

        return form.getCases().stream()
                .anyMatch(cardClass::isClassOf);
    }

    private Stream<ClassFqn> getCardCasesWithDescendants(final List<ClassFqn> cardCases,
            final ClassFqnHierarchy hierarchy)
    {
        Stream<ClassFqn> cardCasesWithDescendants = cardCases.stream()
                .flatMap(caseFqn -> hierarchy.getDescendants(caseFqn).stream());

        return Stream.concat(cardCasesWithDescendants, cardCases.stream());
    }
}