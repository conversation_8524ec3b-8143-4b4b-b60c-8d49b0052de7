package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfo.shared.ui.EditTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, обновляющий свойство "Использовать форму быстрого редактирования" настраиваемой кнопки.
 * <AUTHOR>
 * @since Mar 02, 2019
 */
public class UseQuickEditFormRefreshDelegate implements PropertyDelegateRefresh<Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        boolean isValueHardcoded = !UI.WINDOW_KEY.equals(ctx.getParentContext().getCode());
        context.setEnabled(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, !isValueHardcoded);
        callback.onSuccess(canHaveQuickEditForm(ctx, context.getPropertyValues()));
    }

    private boolean canHaveQuickEditForm(ToolFormContext context, IProperties properties)
    {
        return ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR != context.getToolPanelKind()
               && ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR != context.getToolPanelKind()
               && context.getTool() instanceof EditTool
               || ToolPanelKind.OBJECT_ACTIONS_BAR == context.getToolPanelKind()
                  && Constants.EDIT.equals(properties.getProperty(ToolFormPropertyCodes.OBJECT_ACTION));
    }
}
