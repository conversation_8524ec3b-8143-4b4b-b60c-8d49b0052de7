package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, производящий инициализацию свойств для выбора группы атрибутов добавления файла
 * <AUTHOR>
 * @since 08.04.19
 */
public class AttributeGroupForAddFileBindDelegate implements PropertyDelegateBind<SelectItem, ListBoxProperty>
{
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property,
            AsyncCallback<Void> callback)
    {
        property.getValueWidget().clear();

        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);

        if (null == ctx.getParentContext().getMetainfo())
        {
            callback.onSuccess(null);
            return;
        }

        SingleSelectCellList<String> attributeValueWidget = property.getValueWidget();
        attributeValueWidget.clear();
        metainfoService.getMetaClass(Constants.File.FQN, new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass fileClass)
            {
                for (AttributeGroup attributeGroup : fileClass.getAttributeGroups())
                {
                    attributeValueWidget.addItem(attributeGroup.getTitle(), attributeGroup.getCode());
                }

                if (context.getPropertyValues().getProperty(ToolFormPropertyCodes.ATTRIBUTE_GROUP_FOR_ADD_FILE) == null)
                {
                    context.getPropertyValues()
                            .setProperty(ToolFormPropertyCodes.ATTRIBUTE_GROUP_FOR_ADD_FILE, AttrGroup.ADD_FORM);
                }

                callback.onSuccess(null);
            }
        });
    }
}
