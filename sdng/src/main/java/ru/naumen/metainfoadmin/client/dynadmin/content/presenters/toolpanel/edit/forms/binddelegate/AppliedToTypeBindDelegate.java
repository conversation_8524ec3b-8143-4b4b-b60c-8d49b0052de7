package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

import jakarta.inject.Inject;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.name.Named;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.client.widgets.properties.ListBoxWithEmptyOptProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.Tool.AppliedToType;
import ru.naumen.metainfo.shared.ui.UserEventTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelGinModule;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, производящий инициализацию свойства "Применяется к" настраиваемой кнопки.
 * <AUTHOR>
 * @since Mar 01, 2019
 */
public class AppliedToTypeBindDelegate implements PropertyDelegateBind<SelectItem, ListBoxWithEmptyOptProperty>
{
    private static final Map<String, Predicate<ToolFormContext>> APPLICABILITY_PREDICATES = new HashMap<>();

    static
    {
        APPLICABILITY_PREDICATES.put(AppliedToType.CURRENT_OBJECT, ctx -> ToolPanelKind.ACTION_BAR == ctx
                .getToolPanelKind() && !isListOnForm(ctx)
                                                                          || ToolPanelKind.ATTRIBUTE_ACTION_BAR
                                                                             == ctx.getToolPanelKind());

        APPLICABILITY_PREDICATES.put(AppliedToType.RELATED_OBJECT, ctx -> (ctx
                                                                                   .getToolLocationContent() instanceof RelObjPropertyList
                                                                           || isRelatedCommentList(
                ctx.getToolLocationContent())
                                                                           || isRelatedFileList(
                ctx.getToolLocationContent()))
                                                                          && (ctx.getTool() == null
                                                                              || ctx.getTool() instanceof UserEventTool));

        APPLICABILITY_PREDICATES.put(AppliedToType.LIST_OBJECTS, ctx -> (ctx.getTool() == null || ctx
                .getTool() instanceof UserEventTool
                                                                         || ToolPanelKind.MASS_OPERATIONS
                                                                            == ctx.getToolPanelKind()
                                                                         || ToolPanelKind.OBJECT_ACTIONS_BAR
                                                                            == ctx.getToolPanelKind())
                                                                        && (ctx.getToolLocationContent() instanceof ObjectListBase
                                                                            || ctx.getToolLocationContent() instanceof HierarchyGrid)
                                                                        && !(ctx.getToolLocationContent() instanceof CommentList));
    }

    private static boolean isListOnForm(ToolFormContext ctx)
    {
        return !UI.WINDOW_KEY.equals(ctx.getParentContext().getCode())
               && !UI.CONTENT_TEMPLATE.equals(ctx.getParentContext().getCode())
               && (ctx.getToolLocationContent() instanceof ObjectListBase ||
                   ctx.getToolLocationContent() instanceof HierarchyGrid)
               && !(ctx.getToolLocationContent() instanceof CommentList);
    }

    private static boolean isRelatedCommentList(Content locationContent)
    {
        return locationContent instanceof CommentList && ((CommentList)locationContent).isForRelatedObject();
    }

    private static boolean isRelatedFileList(Content locationContent)
    {
        return locationContent instanceof FileList && ((FileList)locationContent).getRelationType().equals(
                RelationType.RELATED_OBJECT_FILES);
    }

    @Inject
    @Named(EditableToolPanelGinModule.TOOL_APPLICABILITY)
    private List<Pair<String, String>> toolApplicability;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxWithEmptyOptProperty property,
            AsyncCallback<Void> callback)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        property.getValueWidget().setHasEmptyOption(false);
        for (Pair<String, String> applicability : toolApplicability)
        {
            if (APPLICABILITY_PREDICATES.get(applicability.getLeft()).test(ctx))
            {
                property.getValueWidget().addItem(applicability.getRight(), applicability.getLeft());
            }
        }

        if (property.getValueWidget().getItemCount() <= 1)
        {
            context.setDisabled(ToolFormPropertyCodes.APPLIED_TO_TYPE);
        }

        callback.onSuccess(null);
    }
}
