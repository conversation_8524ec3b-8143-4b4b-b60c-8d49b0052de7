package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import java.util.HashSet;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.select.SelectListResources;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfoadmin.client.PropertyFormDisplay;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

/**
 * <AUTHOR>
 * @since 17 марта 2016 г.
 *
 */
public class AdvlistAttributeFormPresenterAdd extends AdvlistAttributeFormPresenter
{
    @Inject
    private AdvlistDefaultPrsMessages messages;
    @Inject
    private ListBoxProperty attribute;
    @Inject
    private SelectListResources resources;

    @Inject
    public AdvlistAttributeFormPresenterAdd(PropertyFormDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    public void init(AdvlistSettingsDefault settings, ObjectListUIContext objectListContext)
    {
        this.settings = settings;
        this.objectListContext = objectListContext;
    }

    @Override
    public void onApply()
    {
        settings.getColumnList().add(
                new AdvlistColumn(SelectListPropertyValueExtractor.getValue(attribute),
                        SelectListPropertyValueExtractor.getValue(prs), 0));
        eventBus.fireEvent(new RefreshDefaultPrsFormEvent());
        unbind();
    }

    @Override
    protected void addAttributeProperty()
    {
        attribute.setCaption(messages.attrTitleColumn());

        attribute.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                SelectItem item = event.getValue();
                toggleDisabledByTagsStyle(item);
                initPrs(SelectItemValueExtractor.extract(item));
            }
        });

        Set<String> selectedAttrCodes = getSelectedAttributeCodes();
        List<String> disabledByTags = objectListContext.getMode()
                .getDisabledAttributes()
                .stream()
                .map(AttributeFqn::toString)
                .collect(Collectors.toList());
        for (String attrCode : objectListContext.getMode().getAttributeCodes())
        {
            if (selectedAttrCodes.contains(attrCode))
            {
                continue;
            }
            if (disabledByTags.contains(attrCode))
            {
                attribute.getValueWidget()
                        .addItemWithStyle(objectListContext.getMode().getAttributes().get(attrCode).getTitle(),
                                attrCode, resources.cellListStyle().disabledSelectableItem());
            }
            else
            {
                attribute.getValueWidget().addItem(objectListContext.getMode().getAttributes().get(attrCode).getTitle(),
                        attrCode);
            }
        }
        ValueChangeEvent.fire(attribute, attribute.getValue());
        getDisplay().add(attribute);
        DebugIdBuilder.ensureDebugId(attribute, "attribute");
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(messages.addAttrFormCaption());
    }

    private Set<String> getSelectedAttributeCodes()
    {
        Set<String> result = new HashSet<>();
        for (AdvlistColumn column : settings.getColumnList())
        {
            result.add(column.getAttrCode());
        }
        return result;
    }

    private void toggleDisabledByTagsStyle(SelectItem selectItem)
    {
        List<String> disabledByTags = objectListContext.getMode()
                .getDisabledAttributes()
                .stream()
                .map(AttributeFqn::toString)
                .collect(Collectors.toList());
        attribute.getValueWidget().getValueWidget().getTextBox()
                .setStyleName(resources.cellListStyle().disabledSelectableItem(),
                        disabledByTags.contains(selectItem.getCode()));
    }
}