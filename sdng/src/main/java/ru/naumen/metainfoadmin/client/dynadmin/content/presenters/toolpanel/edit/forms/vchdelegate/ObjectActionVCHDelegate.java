package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.vchdelegate;

import com.google.common.collect.Lists;

import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateVCH;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ui.Constants;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.SystemActionsIconsRegistry;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Делегат изменения значения параметра "Действие" настраиваемого пункта меню.
 * <AUTHOR>
 * @since Mar 05, 2019
 */
public class ObjectActionVCHDelegate implements PropertyDelegateVCH
{
    @Override
    public void onValueChanged(PropertyContainerContext context)
    {
        updateQuickForms(context);
        context.setProperty(ToolFormPropertyCodes.ROW_ICON, getIcon(context));
        context.getRefreshProcess().startCustomProcess(Lists.newArrayList(ToolFormPropertyCodes.IS_GEO_REQUIRED,
                ToolFormPropertyCodes.USE_QUICK_ADD_FORM, ToolFormPropertyCodes.USE_QUICK_EDIT_FORM,
                ToolFormPropertyCodes.QUICK_ADD_FORM, ToolFormPropertyCodes.QUICK_EDIT_FORM,
                ToolFormPropertyCodes.ATTRIBUTE_GROUP_FOR_ADD_FILE, ToolFormPropertyCodes.ATTRIBUTE_TO_SAVE_FILE,
                ToolFormPropertyCodes.EDIT_FORM));
        context.getPropertyControllers().get(context.getRefreshProcess().getNextOperation()).refresh();
    }

    private DtObject getIcon(PropertyContainerContext context)
    {
        String objectAction = context.getPropertyValues().getProperty(ToolFormPropertyCodes.OBJECT_ACTION);
        String iconCode = SystemActionsIconsRegistry.getIconCodeForAction(objectAction);
        if (null == iconCode)
        {
            return null;
        }
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        return ctx.getAvailableIcons().stream().filter(dto -> iconCode.equals(dto.getUUID())).findFirst().orElse(null);
    }

    private void updateQuickForms(PropertyContainerContext context)
    {
        String objectAction = context.getPropertyValues().getProperty(ToolFormPropertyCodes.OBJECT_ACTION);
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        if (!Constants.EDIT.equals(objectAction) || ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR == ctx.getToolPanelKind()
            || ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR == ctx.getToolPanelKind())
        {
            context.setProperty(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, false);
        }
        else if (!UI.WINDOW_KEY.equals(ctx.getParentContext().getCode()))
        {
            context.setProperty(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM, true);
        }
        context.setProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM, Constants.ADD.equals(objectAction));
    }
}
