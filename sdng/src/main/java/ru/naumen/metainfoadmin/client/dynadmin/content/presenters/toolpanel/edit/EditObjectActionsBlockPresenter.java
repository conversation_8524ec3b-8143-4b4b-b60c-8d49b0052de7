package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.Constants.IconsFolder;
import ru.naumen.core.shared.Constants.IconsForControlsCatalog;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.catalog.GetCatalogIconsAction;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateFormPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template.CopyFromTemplateFormPresenterFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoAction;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoResponse;

/**
 * Блок для настройки "Действия в списке объектов"
 *
 * <AUTHOR>
 * @since 01.03.17.
 */
public class EditObjectActionsBlockPresenter<C extends UIContext> extends BasicPresenter<EditObjectActionsDisplay>
        implements HasListTemplate
{
    @Inject
    private EditableToolPanelMessages messages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    protected EditObjectActionsIconBlockPresenter iconSettingsPresenter;
    @Inject
    private ObjectActionsTablePresenter<C> menuItemsPresenter;
    @Inject
    private CopyFromTemplateFormPresenterFactory copyFromTemplateFormPresenterFactory;

    private ToolPanelKind toolPanelKind;
    private ObjectActionsMenuHolder objectActionsMenuHolder;
    private C context;

    private String templateCode;
    private ListTemplateUsagePoint usagePoint;

    @Inject
    public EditObjectActionsBlockPresenter(EditObjectActionsDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void addListTemplateAndUsagePoint(String templateCode, ListTemplateUsagePoint usagePoint)
    {
        this.templateCode = templateCode;
        this.usagePoint = usagePoint;
    }

    @Override
    public String getTemplateCode()
    {
        return templateCode;
    }

    @Override
    public ListTemplateUsagePoint getUsagePoint()
    {
        return usagePoint;
    }

    public void init(ObjectActionsMenuHolder objectActionsMenuHolder, C context, ToolPanelKind toolPanelKind)
    {
        this.objectActionsMenuHolder = objectActionsMenuHolder;
        this.context = context;
        this.toolPanelKind = toolPanelKind;
    }

    public void updateActions(boolean isUseSystemSettings, ToolPanel actions, String objectActionsMenuPosition,
            String menuIconCatalogCode)
    {
        boolean previousUseSystemSettings = getDisplay().getUseSystemSettsingsCheckbox().getValue();
        getDisplay().getUseSystemSettsingsCheckbox().setValue(isUseSystemSettings, false);
        if (previousUseSystemSettings != isUseSystemSettings)
        {
            handleCheckBoxValue(isUseSystemSettings);
        }
        objectActionsMenuHolder.setUseSystemSettings(isUseSystemSettings);
        if (!isUseSystemSettings)
        {
            objectActionsMenuHolder.setObjectActionsToolPanel(actions);
            objectActionsMenuHolder.setObjectActionsMenuPosition(objectActionsMenuPosition);
            objectActionsMenuHolder.setMenuIconCatalogCode(menuIconCatalogCode);
        }
        menuItemsPresenter.refreshDisplay();
        iconSettingsPresenter.refreshDisplay();
        refreshDisplay();
    }

    protected boolean isCopyFromTemplateVisibleByParent()
    {
        Content content = Objects.requireNonNull(objectActionsMenuHolder.getObjectActionsToolPanel()).getParent();
        return content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        List<String> iconTypes = Lists.newArrayList(IconsForControlsCatalog.CODE, IconsFolder.CODE);
        dispatch.execute(new GetCatalogIconsAction(iconTypes),
                new BasicCallback<SimpleResult<List<DtObject>>>(readyState)
                {
                    @Override
                    protected void handleSuccess(SimpleResult<List<DtObject>> response)
                    {
                        iconSettingsPresenter.setIconsDataFromCatalog(response.get());
                        menuItemsPresenter.setIconsDataFromCatalog(response.get());
                    }
                });
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(messages.menuOfActionsWithObject());
        getDisplay().getUseSystemSettsingsCheckbox().setCaption(messages.useSystemObjectAction());
        getDisplay().getCopyActionsFromTemplateButton().addClickHandler(event ->
        {
            if (isResetAllowed() && isCopyFromTemplateVisibleByParent())
            {
                CopyFromTemplateFormPresenter presenter = copyFromTemplateFormPresenterFactory.create(
                        ((ObjectListUIContext)context).getObjectList(), context,
                        menuItemsPresenter.getAvailableTools());
                presenter.setFormCaller(this);
                presenter.bind();
            }
        });

        final Content parentContent = objectActionsMenuHolder.getObjectActionsToolPanel().getParent();
        ClassFqn fqn = context.getMetainfo().getFqn();
        if (parentContent instanceof RelObjPropertyList)
        {
            fqn = context.getParentContext().getMetainfo().getFqn();
        }
        GetEditToolPanelContextInfoAction action = new GetEditToolPanelContextInfoAction(fqn,
                context.getRootContentInfo().getFormId(), parentContent.getUuid(), toolPanelKind);
        action.setTemplateCode(context.getTemplateCode());
        dispatch.execute(action, new BasicCallback<GetEditToolPanelContextInfoResponse>()
        {
            @Override
            protected void handleSuccess(GetEditToolPanelContextInfoResponse response)
            {
                //Вся инициализация должна быть здесь
                menuItemsPresenter.init(objectActionsMenuHolder, context, response, toolPanelKind);
                menuItemsPresenter.bind();
                getDisplay().addWidget(menuItemsPresenter.getDisplay());

                iconSettingsPresenter.init(objectActionsMenuHolder);
                iconSettingsPresenter.bind();
                // TODO dzevako вынести наложение стиля на контент в родителя в NSDPRD-12486 Рефакторинг форм
                iconSettingsPresenter.getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
                getDisplay().addWidget(iconSettingsPresenter.getDisplay().asWidget());
                getDisplay().getUseSystemSettsingsCheckbox().addValueChangeHandler(event ->
                {
                    handleCheckBoxValue(event.getValue());
                    objectActionsMenuHolder
                            .setUseSystemSettings(getDisplay().getUseSystemSettsingsCheckbox().getValue());
                });
                getDisplay().getUseSystemSettsingsCheckbox().setValue(objectActionsMenuHolder.isUseSystemSettings());
                updateControlsVisibility();
                menuItemsPresenter.setUpdateCommand(
                        () -> handleCheckBoxValue(getDisplay().getUseSystemSettsingsCheckbox().getValue()));
            }
        });

        getDisplay().getUseSystemSettsingsCheckbox().setEnabled(
                objectActionsMenuHolder.isUseSystemSettings() || isResetAllowed());
    }

    private void updateControlsVisibility()
    {
        boolean resetAllowed = isResetAllowed();
        boolean copyAllowed = resetAllowed && isCopyFromTemplateVisibleByParent();
        getDisplay().getCopyActionsFromTemplateButton().getDisplay().asWidget().setStyleName(
                WidgetResources.INSTANCE.all().disabled(), !copyAllowed);
        iconSettingsPresenter.setEnabled(resetAllowed);
    }

    private boolean isResetAllowed()
    {
        return !Boolean.TRUE.equals(getDisplay().getUseSystemSettsingsCheckbox().getValue())
               && objectActionsMenuHolder.getObjectActionsToolPanel() != null
               && objectActionsMenuHolder.getObjectActionsToolPanel().getTools().stream().allMatch(
                tool -> hasPermission(context, PermissionType.EDIT, tool));
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        iconSettingsPresenter.unbind();
        menuItemsPresenter.unbind();
    }

    private void handleCheckBoxValue(boolean value)
    {
        getDisplay().setEnabled(!value);
        if (!value)
        {
            menuItemsPresenter.swapToUser();
            menuItemsPresenter.setEnabled(true);
            iconSettingsPresenter.swapToUser();
        }
        else
        {
            menuItemsPresenter.setEnabled(false);
            menuItemsPresenter.swapToSystem();
            iconSettingsPresenter.swapToSystem();
        }
        updateControlsVisibility();
    }
}
