package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate;

import static ru.naumen.metainfo.shared.ui.Constants.MOBILE_SYSTEM_ACTIONS_WITH_GEO_REQUIRED;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, обновляющий свойство "Передавать геопозицию устройства" настраиваемого пункта меню в МК.
 * <AUTHOR>
 * @since Mar 05, 2019
 */
public class GeoRequiredRefreshDelegate implements PropertyDelegateRefresh<Boolean, BooleanCheckBoxProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, BooleanCheckBoxProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(canHaveGeoLocation(context));
    }

    private boolean canHaveGeoLocation(PropertyContainerContext context)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        if (ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR != ctx.getToolPanelKind()
            && ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR != ctx.getToolPanelKind())
        {
            return false;
        }
        String action = context.getPropertyValues().getProperty(ToolFormPropertyCodes.OBJECT_ACTION);
        if (StringUtilities.isEmpty(action))
        {
            return false;
        }

        return isUserAction(ctx, action) || isSystemActionWithGeoRequired(action);
    }

    private boolean isSystemActionWithGeoRequired(String action)
    {
        return MOBILE_SYSTEM_ACTIONS_WITH_GEO_REQUIRED.contains(action);
    }

    private boolean isUserAction(ToolFormContext ctx, String action)
    {
        return ctx.getAvailableTools().stream().noneMatch(tool -> action.equals(((ActionTool)tool).getAction()));
    }
}
