package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings;

import ru.naumen.core.client.content.FilterAttributeSelectionMode;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.ObjectListContextDecorator;

/**
 * Декоратор для контекста списка, используемый в настройке ограничения содержимого.
 * <AUTHOR>
 * @since Oct 19, 2021
 */
public class ObjectFilterListContextDecorator extends ObjectListContextDecorator
{
    public ObjectFilterListContextDecorator(ObjectListContext decorated)
    {
        super(decorated);
    }

    @Override
    public FilterAttributeSelectionMode getAttributeSelectionMode()
    {
        return FilterAttributeSelectionMode.LIST_FILTER_TREE;
    }

    @Override
    public boolean showIgnoreIfEmpty()
    {
        return true;
    }
}
