package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import java.util.Objects;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesAction;
import ru.naumen.core.shared.dispatch.GetPermittedRelatedTypesResponse;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseHelpTextBuilderImplAbstractBO;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

/**
 * <AUTHOR>
 * @since 14 марта 2016 г.
 *
 */
public class RelObjectListHelpTextBuilderImpl extends ObjectListBaseHelpTextBuilderImplAbstractBO<RelObjectList>
{

    @Override
    public void buildHelpText()
    {
        if (context instanceof ObjectListUIContext && ((ObjectListUIContext)context).getTemplateCode() != null)
        {
            super.buildHelpText();
            return;
        }

        final List<AttrReference> attrChain = content.getAttributesChain();
        AttrReference ar = attrChain.get(attrChain.size() - 1);
        ClassFqn fqn = (attrChain.size() == 1 && ar.getClassFqn() == null) ? context.getMetainfo().getFqn()
                : ar.getClassFqn();
        dispatch.execute(new GetPermittedRelatedTypesAction(fqn, ar.getAttrCode()),
                new BasicCallback<GetPermittedRelatedTypesResponse>()
                {
                    @Override
                    protected void handleSuccess(GetPermittedRelatedTypesResponse value)
                    {
                        if (Iterables.any(value.getPermittedTypes(), MetaClassFilters.isClass()))
                        {
                            RelObjectListHelpTextBuilderImpl.super.buildHelpText();
                        }
                        else
                        {
                            final Collection<MetaClassLite> filtered = content.getCase().isEmpty()
                                    ? value.getPermittedTypes()
                                    : value.getPermittedTypes()
                                            .stream()
                                            .filter(MetaClassFilters.in(content.getCase()))
                                            .collect(Collectors.toList());
                            metainfoService.getMetaClasses(ImmutableSet.of(content.getFqnOfClass()),
                                    new BasicCallback<List<MetaClassLite>>()
                                    {
                                        @Override
                                        protected void handleSuccess(List<MetaClassLite> value)
                                        {
                                            processHelpMessageForCases(value.get(0), filtered);
                                        }
                                    });
                        }
                    }
                });
    }

    @Override
    protected void processHelpMessage(final MetaClassLite tgtMc, final String types, final String groupTitle)
    {
        if (context instanceof ObjectListUIContext && ((ObjectListUIContext)context).getTemplateCode() != null)
        {
            super.processHelpMessage(tgtMc, types, groupTitle);
            return;
        }

        final List<AttrReference> attrChain = content.getAttributesChain();
        if (attrChain.size() == 1 && (null == attrChain.get(0).getClassFqn()
                                      // для совместимости. изначально можно было указать атрибут только метакласса
                                      // настраиваемой карточки
                                      || Objects.equals(context.getMetainfo().getFqn(),
                attrChain.get(0).getClassFqn())))
        {
            // небольшая оптимизация. не запрашиваем с сервера метакласс контекста
            Attribute attr = context.getMetainfo().getAttribute(attrChain.get(0).getAttrCode());
            processHelpMessage(tgtMc, attr.getTitle(), types, groupTitle);
            return;
        }
        metainfoService.getFullMetaInfo(Lists.transform(attrChain, AttrReference.FQN_EXTRACTOR),
                new BasicCallback<List<MetaClass>>()
                {
                    @Override
                    protected void handleSuccess(List<MetaClass> value)
                    {
                        StringBuilder sb = new StringBuilder();
                        for (Iterator<AttrReference> it = attrChain.iterator(); it.hasNext(); )
                        {
                            AttrReference ar = it.next();
                            MetaClass mc = Iterables.find(value, MetaClassFilters.equal(ar.getClassFqn()));
                            Attribute attr = mc.getAttribute(ar.getAttrCode());
                            sb.append(attr.getTitle()).append(it.hasNext() ? "\\" : "");
                        }
                        processHelpMessage(tgtMc, sb.toString(), types, groupTitle);
                    }
                });
    }

    private void processHelpMessage(MetaClassLite value, String attrs, String types, String groupTitle)
    {
        // @formatter:off
        String helpText = value.isHasCases() ?
                messages.relClassAndTypes(value.getTitle(), types, attrs, groupTitle) :
                messages.relClass(value.getTitle(), attrs, groupTitle) ;
        // @formatter:on
        callback.onSuccess(buildHelpMessage(objectListMessages.contentTypeTitle(), helpText));
    }
}
