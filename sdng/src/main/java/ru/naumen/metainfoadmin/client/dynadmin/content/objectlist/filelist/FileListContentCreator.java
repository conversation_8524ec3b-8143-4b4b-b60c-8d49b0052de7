package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filelist;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.common.collect.ImmutableList;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ListContentCreator;

/**
 * {@link ContentCreator} для контента "Список файлов"
 *
 * <AUTHOR>
 */
public class FileListContentCreator extends ListContentCreator<FileList>
{
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    private SelectListProperty<String, SelectItem> attributeGroupList;

    @Inject
    private FileListContentPropertiesHolder propertiesHolder;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Inject
    private ContentCreatorMessages cmessages;

    @Override
    public void afterAddProperties(final PropertyDialogDisplay display)
    {
        super.afterAddProperties(display);
        presentationList.addValueChangeHandler(new ValueChangeHandler<SelectItem>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<SelectItem> event)
            {
                refreshAttrGroup(display, SelectItemValueExtractor.extract(event.getValue()));
            }
        });
        SingleSelectCellList<?> relationTypeWidget = propertiesHolder.getRelationType()
                .<SingleSelectCellList<?>> getValueWidget();
        relationTypeWidget.clear();
        relationTypeWidget.clearValue();
        relationTypeWidget.refreshPopupCellList();
        relationTypeWidget.addItem(cmessages.ofCurrentObject(), RelationType.OBJECT_FILES);
        relationTypeWidget.addItem(cmessages.relatedObject(), RelationType.RELATED_OBJECT_FILES);

        if (!UI.Form.NEW.equals(context.getRootContentInfo().getFormId()))
        {
            relationTypeWidget.addItem(cmessages.relatedObjectSet(),
                    RelationType.RELATED_OBJECT_SET_FILES);
            relationTypeWidget.addItem(cmessages.linkedWithObject(),
                    RelationType.LINKED_WITH_OBJECT_FILES);
            relationTypeWidget.addItem(cmessages.linkedWithRelatedObject(),
                    RelationType.LINKED_WITH_RELATED_OBJECT_FILES);
        }

        propertiesHolder.registerValueHandlers(display, validation,
                getPropertyRegistration(propertiesHolder.getRelationType()), context);

        ((ListBoxProperty)propertiesHolder.getRelationType()).trySetObjValue(RelationType.OBJECT_FILES, true);
    }

    @Override
    public void init(FileList content, UIContext context, Layout defaultParent)
    {
        super.init(content, context, defaultParent);
        propertiesHolder.init();
        metainfoService.getMetaClass(Constants.File.FQN, new BasicCallback<MetaClass>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MetaClass fileClass)
            {
                propertiesHolder.initSystemClass(fileClass);
            }
        });
    }

    @Override
    public void removeProperties()
    {
        propertiesHolder.removePropertiesInner(validation);
        super.removeProperties();
    }

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();
        // TODO нужно избавиться от захардкоженных индексов
        add(propertiesHolder.getRelationType(), 5);
        helper.asyncInitGroupList(attributeGroupList, ImmutableList.of(ru.naumen.core.shared.Constants.File.FQN),
                AttrGroup.SYSTEM);
    }

    @Override
    protected FileList getContentInner()
    {
        FileList content = super.getContentInner();
        propertiesHolder.updateContent(content, context);
        content.setAttributeGroup(SelectListPropertyValueExtractor.getValue(attributeGroupList));
        content.setClazz(ru.naumen.core.shared.Constants.File.FQN);
        content.setToolPanel(toolBarUtils.createDefault(content));
        return content;
    }

    @Override
    protected String getDefaultCaption()
    {
        return messages.files();
    }

    @Override
    protected void initPresentationList()
    {
        helper.initFileListPresentaion(presentationList);
        ((ListBoxProperty)presentationList).trySetObjValue(PresentationType.ADVLIST.getCode(), true);
    }

    protected void refreshAttrGroup(PropertyDialogDisplay display, String value)
    {
        if (PresentationType.ADVLIST.getCode().equals(value))
        {
            add(attributeGroupList);
            register(attributeGroupList,
                    display.addPropertyAfter(attributeGroupList, getPropertyRegistration(pagingPosition)));
        }
        else
        {
            remove(attributeGroupList);
        }
    }
}
