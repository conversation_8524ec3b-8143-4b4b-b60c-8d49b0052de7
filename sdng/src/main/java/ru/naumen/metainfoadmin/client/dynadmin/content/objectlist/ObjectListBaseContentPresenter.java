package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import static ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode.EDIT_ADVLIST_DEFAULT_PRS;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.display.FontIconDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.EventList;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfo.shared.ui.UserHistoryList;
import ru.naumen.metainfo.shared.ui.Window;
import ru.naumen.metainfoadmin.client.common.content.AbstractInfoContentPresenter;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayImpl.ContentCommandAction;
import ru.naumen.metainfoadmin.client.common.content.commands.EditAdvlistDefaultPrsCommandParam;
import ru.naumen.metainfoadmin.client.common.content.commands.FilterSettingsCommandParam;
import ru.naumen.metainfoadmin.client.common.content.commands.TabContentCommandCode;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.ListContainerPresenter;
import ru.naumen.objectlist.client.ListPresenter;
import ru.naumen.objectlist.client.ListPresenterImpl;
import ru.naumen.objectlist.client.ListPresenterPrsFactory;
import ru.naumen.objectlist.client.ListPresenterPrsFactoryProvider;

/**
 * базовый {@link Presenter} интерфейса администратора для {@link ObjectListBase списка объектов}
 * <AUTHOR>
 * @since 25.11.2010
 *
 */
public class ObjectListBaseContentPresenter<T extends ObjectListBase>
        extends AbstractInfoContentPresenter<ObjectListContentDisplay, T, UIContext>
        implements ListContainerPresenter<T, UIContext>
{
    @Inject
    private ListPresenterPrsFactoryProvider presenterFactoryProvider;
    @Inject
    private ObjectListBaseAdminMessages<T> objectListMessages;
    @Inject
    private ObjectListBaseHelpTextBuilder<T> helpTextBuilder;

    private ListPresenterPrsFactory<T> presenterFactory;
    private ListPresenter<T> listPresenter;
    private String lastPresentation = "";

    @Inject
    public ObjectListBaseContentPresenter(ObjectListContentDisplay display, EventBus eventBus)
    {
        super(display, eventBus, "");
    }

    @Override
    public void init(T content, UIContext context)
    {
        debugPrefix = content.getClass().getSimpleName();
        presenterFactory = presenterFactoryProvider.create(content);
        super.init(content, context);
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();

        helpTextBuilder.buildHelpText();
        //Если изменилось представление листа, нужно переопределять listPresenter
        if (!ObjectUtils.equals(lastPresentation, getContent().getPresentation()))
        {
            super.refreshToolPanel();
            listPresenter.unbind();
            bindComponents();
        }
        FontIconDisplay<FlowContent> iconDisplay = commandImages.get(TabContentCommandCode.EDIT_ADVLIST_DEFAULT_PRS);
        if (null != iconDisplay && iconDisplay.isVisible())
        {
            iconDisplay.setVisible(!(getContent() instanceof FileList)
                                   && PresentationType.ADVLIST.getCode().equals(getContent().getPresentation()));
        }
        // Изменение тулбара списка
        ((ListPresenterImpl<?, ?>)listPresenter).rebuildToolPanel();

        listPresenter.refreshDisplay();
    }

    /**
     * В списках helpText устанавливается асинхронно
     */
    @Override
    protected void setupHelpText()
    {
    }

    /**
     * В списках helpText устанавливается асинхронно
     */
    @Override
    protected String getHelpText()
    {
        return StringUtilities.EMPTY;
    }

    protected void bindComponents()
    {
        lastPresentation = getContent().getPresentation();
        listPresenter = presenterFactory.create(getContent());
        listPresenter.getListComponents().setIsActive(false);
        listPresenter.init(getContent(), getContext());
        listPresenter.bind();
        getDisplay().setPresentationDisplay(listPresenter.getDisplay());
    }

    protected ObjectListBaseAdminMessages<T> getObjectListMessages()
    {
        return objectListMessages;
    }

    @Override
    protected void initCommands()
    {
        initCommandDisplay(getContext(), TabContentCommandCode.MOVE_TAB_CONTENT, ContentCommandAction.MOVE);
        initEditAdvlistDefaultPrsCommand();
        if (content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList
            || content instanceof FileList
            || context.getRootContent() instanceof Window
               && !(content instanceof EventList || content instanceof UserHistoryList))
        {
            initCommandDisplay(listPresenter.getListComponents().<ObjectListUIContext> getContext(),
                    TabContentCommandCode.EDIT_TOOL_PANEL, ContentCommandAction.EDIT_TOOL_PANEL);
        }
        if ((content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList)
            && PresentationType.ADVLIST.getCode().equals(getContent().getPresentation()))
        {
            initFilterSettingsCommand();
        }
        if ((content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList)
            && !(content instanceof EventList || content instanceof UserHistoryList || content instanceof FileList
                 || content instanceof CommentList))
        {
            initCommandDisplay(getContext(), TabContentCommandCode.LIST_TEMPLATE_APPLY,
                    ContentCommandAction.LIST_TEMPLATE_APPLY);
        }
        if (content instanceof ObjectList || content instanceof ChildObjectList || content instanceof RelObjectList)
        {
            initCommandDisplay(getContext(), TabContentCommandCode.COPY_TO_TEMPLATE,
                    ContentCommandAction.COPY_TO_TEMPLATE);
        }
        if (isEditable() && !(getContext() instanceof ObjectListUIContext
                              && getContext().getTemplateCode() != null)) //NOPMD
        {
            initCommandDisplay(getContext(), TabContentCommandCode.EDIT_CONTENT, ContentCommandAction.EDIT);
        }
        if (!(getContext() instanceof ObjectListUIContext && getContext().getTemplateCode() != null)) //NOPMD
        {
            initCommandDisplay(getParentContentContext(), TabContentCommandCode.DELETE_CONTENT,
                    ContentCommandAction.DELETE);
        }
    }

    @Override
    protected void onBind()
    {
        bindComponents();

        helpTextBuilder.init(getContent(), getContext(), new BasicCallback<String>()
        {
            @Override
            protected void handleSuccess(String text)
            {
                getDisplay().setHelpText(text + getHelpTextForAllChilds(" | ", true));
            }
        });

        super.onBind();
    }

    @Override
    public void onRefresh(RefreshContentEvent event)
    {
        super.onRefresh(event);
        // Изменение тулбара списка
        if (event.getContent() instanceof ToolPanel && event.getContent().getParent().equals(getContent()))
        {
            init((T)event.getContent().getParent(), getContext());
            ((ListPresenterImpl<?, ?>)listPresenter).rebuildToolPanel();
            refreshDisplay();
        }
    }

    @Override
    protected void refreshContentByEvent()
    {
        // Хак, чтоб заставить список полностью обновить свои компоненты
        lastPresentation = StringUtilities.EMPTY;
        refreshDisplay();
    }

    private void initEditAdvlistDefaultPrsCommand()
    {
        if (null != context.getUITemplateContext()
            || !permissionChecker.hasPermission(content, context, EDIT_ADVLIST_DEFAULT_PRS))
        {
            return;
        }
        final ListComponents listComponents = listPresenter.getListComponents();
        EditAdvlistDefaultPrsCommandParam commandParam = new EditAdvlistDefaultPrsCommandParam(getContent(), null,
                listComponents.getContext(),
                listComponents.getDefaultPrsFormProvider());
        @SuppressWarnings("unchecked")
        BaseCommand<FlowContent, Void> command = (BaseCommand<FlowContent, Void>)commandFactory
                .create(EDIT_ADVLIST_DEFAULT_PRS, commandParam);
        FontIconDisplay<FlowContent> image = command.getFontIcon();
        getDisplay().addToolDisplay(image, ContentCommandAction.EDIT_ADVLIST_DEFAULT_PRS);
        commandImages.put(EDIT_ADVLIST_DEFAULT_PRS, image);
    }

    private void initFilterSettingsCommand()
    {
        if (null != context.getUITemplateContext()
            || !permissionChecker.hasPermission(content, context, TabContentCommandCode.FILTER_SETTINGS))
        {
            return;
        }
        FilterSettingsCommandParam commandParam = new FilterSettingsCommandParam(getContent(), null,
                listPresenter.getListComponents().getContext(), getContext());
        @SuppressWarnings("unchecked")
        BaseCommand<FlowContent, Void> command = (BaseCommand<FlowContent, Void>)commandFactory
                .create(TabContentCommandCode.FILTER_SETTINGS, commandParam);
        FontIconDisplay<FlowContent> image = command.getFontIcon();
        getDisplay().addToolDisplay(image, ContentCommandAction.FILTER_SETTINGS);
        commandImages.put(TabContentCommandCode.FILTER_SETTINGS, image);
    }
}