package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.Dialogs.DialogResult;
import ru.naumen.core.client.components.block.TitledBlockDisplay;

/**
 * <AUTHOR>
 * @since 03 марта 2016 г.
 *
 */
public class AdvListSortPresenterDefaultPrs extends AdvListSortPresenter
{
    @Inject
    private AdvlistDefaultPrsMessages messages;
    @Inject
    private Dialogs dialogs;

    @Inject
    public AdvListSortPresenterDefaultPrs(TitledBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected String getCaption()
    {
        return messages.defaultSort();
    }

    @Override
    protected void onBind()
    {
        listSortPresenter.setCustomResetClickHandler(event ->
                dialogs.question(messages.confirmSortReset(), messages.confirmSortResetQuestion(),
                        new DialogCallback()
                        {
                            @Override
                            public void handleSuccess(DialogResult result)
                            {
                                result.getWidget().hide();
                                if (!Dialogs.Buttons.YES.equals(result.getButtons()))
                                {
                                    return;
                                }
                                content.getElements().clear();
                                listSortPresenter.refreshDisplay();
                            }
                        })
        );
        super.onBind();
    }
}