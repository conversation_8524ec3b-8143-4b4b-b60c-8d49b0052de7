package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import com.google.gwt.i18n.client.LocalizableResource.DefaultLocale;
import com.google.gwt.i18n.client.Messages;

/**
 * Локализованные сообщения
 */
@DefaultLocale("ru")
public interface ObjectListContentDisplayMessages extends Messages
{
    @Description(" | Добавление в: {0}")
    String addFileIn(String fileAttr);

    @Description("Часть выводимого сообщения для контента \"Список вложенных объектов\" в интерфейсе оператора")
    String anyType();

    @Description("| Файлы из: {0}")
    String attachedFilesAttribute(String attachedFiles);

    @Description("Часть сообщения для вывода группы атрибутов")
    String attributeGroup(String groupTitle);

    @Description("Сообщения для контента \"Список вложенных объектов\" в интерфейсе оператора")
    String classAndTypes(String classTitle, String typesTitles, String group);

    @Description("Сообщения для контента \"Список вложенных объектов\" в интерфейсе оператора для классов без типов")
    String classes(String classTitle, String group);

    @Description("колонки")
    String columns();

    @Description("| Отображать: {0}")
    String commentSubjectType(String display);

    @Description("параметры контента")
    String contentParams();

    @Description(" | Настроен внешний вид по умолчанию: {0}")
    String defaultPrs(String settings);

    @Description("Внимание! В случае сохранения формы с измененной группой атрибутов, для данного контента сбросится "
                 + "настроенный Вами системный вид по умолчанию.")
    String defaultSettingsAttention();

    @Description(" | Файлы: {0}")
    String files(String files);

    @Description("фильтрация")
    String filter();

    @Description("При измененнии класса или представления, для данного контента сбросится настроенные Вами "
                 + "ограничения при фильтрации.")
    String filterSettingsClearAllAttention();

    @Description("При измененнии группы атрибутов или типов, для данного контента сбросится настроенные Вами "
                 + "ограничения при фильтрации и для новых атрибутов ограничение при фильтрации будет установлено как"
                 + " 'Без"
                 + " ограничений'.")
    String filterSettingsClearAttention();

    String groupError();

    @Description(" | Атрибут связи: {0}")
    String linkAttribute(String relationLink);

    @Description(" | Атрибут связи в классе Комментарий: {0}")
    String linkAttributeInComment(String relationLink);

    @Description(" | Атрибут связи в классе Файл: {0}")
    String linkAttributeInFile(String relationLink);

    @Description(" | Вызов меню действий с файлом доступен {0}")
    String menuOfActionsWithFileAt(String position);

    @Description(" | Вызов меню действий с объектом доступен {0}")
    String menuOfActionsWithObjectAt(String position);

    @Description("Файлы объекта")
    String objectFiles();

    @Description("Сообщения для контента \"Список связанных объектов\" в интерфейсе оператора")
    String relClass(String classTitle, String attributeTitle, String grp);

    @Description("Сообщения для контента \"Список связанных объектов\" в интерфейсе оператора")
    String relClassAndTypes(String classTitle, String typeTitles, String attributeTitle, String grp);

    @Description("сортировка")
    String sort();

    @Description("Название поля \"Переопределить SQL-запрос получения объектов списка\"")
    String overrideSqlSearchQuery();

    @Description("Название поля \"SQL-запрос\"")
    String sqlQuery();
}