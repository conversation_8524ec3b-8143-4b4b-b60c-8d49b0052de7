package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction;

import static ru.naumen.metainfo.shared.eventaction.Constants.EventAction.CAN_BREAK_QUEUE_LINK;

import java.util.function.Predicate;

import jakarta.annotation.Nullable;

import com.google.inject.Singleton;

import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfoadmin.client.jmsqueue.comands.JMSQueueCommandCode;
import ru.naumen.objectlist.client.extended.advlist.columns.AdvListActionCellContextFactory;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * Фабрика колонок действий в списке ДПС на карточке очереди
 * <AUTHOR>
 * @since 04.05.2021
 **/
@Singleton
public class EventActionListInQueueActionCellContextFactory extends AdvListActionCellContextFactory
{
    @Override
    public ExtendedListActionCellContext create(String code)
    {
        if (code.equals(JMSQueueCommandCode.BREAK_LINK))
        {
            return new ExtendedListActionCellContext(IconCodes.BREAK_LINK,
                    code, new Predicate<DtObject>()
            {
                @Override
                public boolean test(@Nullable DtObject input)
                {
                    return Boolean.TRUE.equals(input.getProperty(CAN_BREAK_QUEUE_LINK));
                }
            });
        }
        return super.create(code);
    }
}