package ru.naumen.metainfoadmin.client.dynadmin.content.creator;

import static ru.naumen.commons.shared.utils.ComparatorUtils.compose;
import static ru.naumen.core.shared.ITitled.IGNORE_CASE_COMPARATOR;

import java.util.ArrayList;
import java.util.Map.Entry;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Window;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.FunctionUtils;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.events.UpdateTabOrderEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.DisplaySynchronization;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.mvp.SafeBasicCallback;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.shared.HasReadyState.ReadyCallback;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.SaveUiAndGetSavedContentResponse;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.common.content.commands.RefreshSelectCaseContentEvent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorRegistry;
import ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications.EmbeddedApplicationContentDelegate;
import ru.naumen.metainfoadmin.client.dynadmin.content.embeddedapplications.RefreshEmbeddedApplicationEvent;

/**
 * {@link Presenter} добавления контента на {@link Layout}
 * <p>
 * Для добавления каждого конкретного типа контента используется {@link ContentCreator}, являющийся частью формы
 * добавления контента.
 *
 * <AUTHOR>
 *
 */
public class AddContentPresenter extends OkCancelPresenter<PropertyDialogDisplay>
{
    @Inject
    private NotNullValidator<SelectItem> notNullValidator;
    @Inject
    private ContentCreatorRegistry factory;
    @Inject
    private Processor validation;
    @Inject
    private AdminDialogMessages messages;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;
    @Named(PropertiesGinModule.LIST_BOX_WITH_EMPTY_OPT)
    @Inject
    private SelectListProperty<String, SelectItem> type;
    @Inject
    private EmbeddedApplicationContentDelegate embeddedApplicationContentDelegate;

    private Layout defaultParent;
    private UIContext context;
    private ContentCreator<FlowContent> creator;

    @Inject
    public AddContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    /**
     * Выставляет фокус на поле с названием добавляемого контента, если это поле есть
     */
    public void focusOnTitleField()
    {
        if (null != creator)
        {
            creator.initDisplay();
        }
    }

    public void init(UIContext context, Layout parent)
    {
        this.context = context;
        this.defaultParent = parent;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }
        final FlowContent content = creator.getCreatedContent();
        if (content == null)
        {
            return;
        }

        ClassFqn fqn = context.getMetainfo().getFqn();
        Content rootContent = context.getRootContent();
        Layout newParent = (Layout)content.getParent();
        newParent.getContent().add(content);
        embeddedApplicationContentDelegate.setAttributesProperties(content);

        metainfoModificationService.saveUIAndGetSavedContent(fqn, rootContent, content, true, context.getCode(),
                creator.getPendingTags(), new SafeBasicCallback<SaveUiAndGetSavedContentResponse>(getDisplay())
                {
                    @Override
                    protected void handleFailure(Throwable t)
                    {
                        super.handleFailure(t);
                        // откатываем изменения (удаляем последний добавленный контент)
                        newParent.getContent().remove(newParent.getContent().size() - 1);
                    }

                    @Override
                    protected void handleSuccess(SaveUiAndGetSavedContentResponse value)
                    {
                        // откатываем изменения (удаляем последний добавленный контент)
                        value.getPermissions().fillPermissionHolder(
                                ContextUtils.getRootPermissionContext(context).getPermissions());
                        content.removeFromParent();
                        newParent.getContent().add((FlowContent)value.getContent());
                        value.getContent().setParent(newParent);
                        context.getEventBus().fireEvent(new RefreshContentEvent(newParent));
                        if (content instanceof SelectCase)
                        {
                            context.getEventBus().fireEvent(new RefreshSelectCaseContentEvent());
                        }
                        unbind();
                    }
                });
    }

    @Override
    public void refreshDisplay()
    {
        initContentCreator(SelectListPropertyValueExtractor.getValue(type));
    }

    @SuppressWarnings("unchecked")
    protected void initContentCreator(String code)
    {
        FlowContent content = null;
        final ContentCreator<FlowContent> oldCreator = creator;
        if (null != oldCreator)
        {
            content = oldCreator.getContent();
        }
        if (null != code)
        {
            creator = (ContentCreator<FlowContent>)factory.getCreator(code);
            if (oldCreator != null && oldCreator.getClass().equals(creator.getClass()))
            {
                creator.init(content, context, defaultParent);
            }
            else
            {
                creator.init(null, context, defaultParent);
            }
            context.getReadyState().ready(new ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    // Для диаграммы Ганта вначале биндим свойства, потом удаляем, 
                    // иначе форма начинает скакать - перематывает вначало при refresh

                    if (oldCreator != null && !(oldCreator.getClass().getSimpleName().equals("GanttContentCreator")))
                    {
                        oldCreator.removeProperties();
                    }
                    creator.bindProperties();
                    creator.addProperties(getDisplay());
                    if (!(creator.getClass().getSimpleName().equals("GanttContentCreator") && oldCreator != null
                          && oldCreator.getClass().getSimpleName().equals("GanttContentCreator")))
                    {
                        creator.initDisplay();
                    }

                    if (oldCreator != null && oldCreator.getClass().getSimpleName().equals("GanttContentCreator"))
                    {
                        oldCreator.removeProperties();
                    }

                    eventBus.fireEvent(new UpdateTabOrderEvent(false));
                }
            });
        }
        else
        {
            if (oldCreator != null)
            {
                oldCreator.removeProperties();
            }
        }
    }

    /**
     * Инициализирует доступные типы контентов для добавления
     */
    protected void initTypes()
    {
        factory.getCreators(context, new BasicCallback<ArrayList<Entry<String, ContentCreatorFactory>>>()
        {
            @Override
            protected void handleSuccess(ArrayList<Entry<String, ContentCreatorFactory>> creatorFactories)
            {
                creatorFactories.sort(compose(IGNORE_CASE_COMPARATOR, FunctionUtils.entryValue()));
                for (Entry<String, ContentCreatorFactory> t : creatorFactories)
                {
                    type.<SingleSelectCellList<String>> getValueWidget().addItem(t.getValue().getTitle(), t.getKey());
                }
                initContentCreator(SelectListPropertyValueExtractor.getValue(type));
            }
        });
    }

    @Override
    protected void onBind()
    {
        embeddedApplicationContentDelegate.init(display, validation, null, null, null);
        register(context.getReadyState().registerSynchronization(new DisplaySynchronization(this, display)));

        getDisplay().setCaptionText(messages.addingContent());
        type.setCaption(messages.contentType());
        type.setValidationMarker(true);
        getDisplay().add(type);
        registerHandler(type.addValueChangeHandler(event ->
        {
            Scheduler.get().scheduleDeferred(() ->
            {
                initContentCreator(SelectListPropertyValueExtractor.getValue(type));
                embeddedApplicationContentDelegate.clearProperties();
            });
        }));

        initTypes();
        validation.validate(type, notNullValidator);
        ensureDebugId();
        super.onBind();
        registerHandler(context.getEventBus().addHandler(RefreshCreatorEvent.getType(), event -> refreshDisplay()));
        registerHandler(context.getEventBus().addHandler(RefreshEmbeddedApplicationEvent.getType(),
                this::onEmbeddedApplicationChange));
        registerHandler(Window.addCloseHandler(event -> embeddedApplicationContentDelegate.clearProperties()));
    }

    @Override
    protected void onUnbind()
    {
        if (null != creator)
        {
            creator.removeProperties();
        }
        embeddedApplicationContentDelegate.clearProperties();
    }

    private void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(type, "type");
    }

    private void onEmbeddedApplicationChange(RefreshEmbeddedApplicationEvent event)
    {
        embeddedApplicationContentDelegate.onEmbeddedApplicationChange(event.getApplication(), event
                .getPropertyRegistration());
    }
}
