package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import com.google.gwt.event.shared.GwtEvent;

import ru.naumen.metainfo.shared.ui.Tool;

/**
 * Иногда после редактирования тула необходимо перерисовать тул панель.
 * Например, у кнопки-скрипт не было выбрано действия и она была неактивна - 
 * после выбора действия она должна активироваться
 *
 * <AUTHOR>
 * @since 26.08.16
 *
 */
public class EventToolEditedEvent extends GwtEvent<EventToolEditedHandler>
{
    private static final Type<EventToolEditedHandler> TYPE = new Type<EventToolEditedHandler>();

    public static Type<EventToolEditedHandler> getType()
    {
        return TYPE;
    }

    private final Tool tool;

    public EventToolEditedEvent(Tool tool)
    {
        this.tool = tool;
    }

    @Override
    public GwtEvent.Type<EventToolEditedHandler> getAssociatedType()
    {
        return TYPE;
    }

    public Tool getTool()
    {
        return tool;
    }

    @Override
    protected void dispatch(EventToolEditedHandler handler)
    {
        handler.onEventToolEdited(this);
    }
}