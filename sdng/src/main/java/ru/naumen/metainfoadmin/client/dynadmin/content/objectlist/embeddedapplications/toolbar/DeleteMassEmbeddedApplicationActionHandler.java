package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.toolbar;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationMessages;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;
import ru.naumen.objectlist.client.mode.active.extended.advlist.UpdateAdvlistSelectionEvent;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
public class DeleteMassEmbeddedApplicationActionHandler extends AdminActionHandler<DeleteMassEmbeddedApplicationAction>
{
    @Inject
    private Dialogs dialogs;
    @Inject
    private CommonMessages cmessages;
    @Inject
    private EmbeddedApplicationMessages messages;
    @Inject
    private MetainfoModificationServiceAsync metainfoModificationService;

    @Inject
    public DeleteMassEmbeddedApplicationActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<DeleteMassEmbeddedApplicationAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    public void doExecute()
    {
        super.doExecute();
        final ObjectListUIContext parentContext = context.getParentContext();
        final List<String> applicationCodes = parentContext.getObjects().stream()
                .map(EmbeddedApplication.APPLICATION_EXTRACTOR)
                .map(EmbeddedApplicationAdminSettingsDto::getCode)
                .collect(Collectors.toCollection(ArrayList::new));
        dialogs.question(cmessages.confirmDelete(), cmessages.confirmDeleteQuestion2(messages.applicationsGenitive()),
                new DialogCallback()
                {
                    @Override
                    protected void onYes(Dialog widget)
                    {
                        super.onYes(widget);
                        metainfoModificationService.deleteApplications(applicationCodes, new BasicCallback<Void>()
                        {
                            @Override
                            protected void handleFailure(Throwable t)
                            {
                                super.handleFailure(t);
                                refresh();
                            }

                            @Override
                            protected void handleSuccess(Void value)
                            {
                                super.handleSuccess(value);
                                refresh();
                            }
                        });
                    }
                });
    }

    private void refresh()
    {
        ObjectListUIContext parentContext = context.getParentContext();
        parentContext.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(context.getParentContent()));
        parentContext.getParentContext().getEventBus().fireEvent(new UpdateAdvlistSelectionEvent());
    }
}
