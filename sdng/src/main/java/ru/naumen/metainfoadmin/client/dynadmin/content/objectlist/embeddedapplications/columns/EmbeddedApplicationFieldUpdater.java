package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.columns;

import jakarta.inject.Inject;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.place.shared.PlaceController;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationWithScript;
import ru.naumen.metainfo.shared.embeddedapplication.Constants;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationPlace;

/**
 * <AUTHOR>
 * @since 19.08.16
 */
public class EmbeddedApplicationFieldUpdater<C> implements FieldUpdater<DtObject, C>
{
    @Inject
    private PlaceController placeController;

    @Override
    public void update(int index, DtObject object, C value)
    {
        placeController.goTo(new EmbeddedApplicationPlace(new EmbeddedApplicationWithScript(object
                .<EmbeddedApplicationAdminSettingsDto> getProperty(Constants.Application.ORIGINAL_APPLICATION))));
    }
}
