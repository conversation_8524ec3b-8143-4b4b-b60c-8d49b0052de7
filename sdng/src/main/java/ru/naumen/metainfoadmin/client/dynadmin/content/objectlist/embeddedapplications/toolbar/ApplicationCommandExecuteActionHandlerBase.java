package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.toolbar;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.command.BaseCommand;
import ru.naumen.core.client.common.command.CommandFactory;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.OnStartBasicCallback;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;
import ru.naumen.metainfoadmin.client.toolbar.ToolBarAction;

/**
 * Базовая реализация ActionHandler'a который выполняет {@link BaseCommand}. И по завершении выполнения команды
 * обновляет список.
 * Код команды берется из {@link ActionToolContext}, и должен совпадать с кодом действия {@link ActionTool}.
 *
 * <AUTHOR>
 * @since 13.07.16
 */
public abstract class ApplicationCommandExecuteActionHandlerBase<T extends ToolBarAction> extends AdminActionHandler<T>
{
    @Inject
    private CommandFactory commandFactory;

    public ApplicationCommandExecuteActionHandlerBase(ActionToolContext context,
            AdminActionExecutorProvider<T> executorProvider)
    {
        super(context, executorProvider);
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public void doExecute()
    {
        super.doExecute();
        BasicCallback refreshCallback = new OnStartBasicCallback()
        {
            @Override
            protected void handleSuccess(Object value)
            {
                context.getParentContext().getEventBus().fireEvent(new RefreshContentEvent(context.getParentContent()));
                doRefresh();
            }
        };
        String commandCode = context.getAction();
        CommandParam commandParam = new CommandParam(getContextObject(), refreshCallback);
        BaseCommand<?, ?> command = commandFactory.create(commandCode, commandParam);

        command.execute(commandParam);
    }

    protected void doRefresh()
    {
    }

    /**
     * Получить объект из контекста, для передачи его в параметр команды {@link Command}
     */
    protected abstract EmbeddedApplicationAdminSettingsDto getContextObject();

}
