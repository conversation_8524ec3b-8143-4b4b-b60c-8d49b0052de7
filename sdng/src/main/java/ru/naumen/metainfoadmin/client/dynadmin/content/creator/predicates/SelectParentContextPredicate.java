/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 28.03.2013
 *
 */
@Singleton
public class SelectParentContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(UI.Form.NEW.equals(context.getCode())
                           && context.getMetainfo().getAttributeCodes().contains(Constants.PARENT_ATTR));
    }
}