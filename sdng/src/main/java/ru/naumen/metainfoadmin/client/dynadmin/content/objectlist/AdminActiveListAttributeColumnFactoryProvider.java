package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import ru.naumen.core.shared.Constants.AdminProfileMetainfo;
import ru.naumen.core.shared.Constants.SettingsSetMetainfo;
import ru.naumen.core.shared.Constants.TagMetainfo;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ContentTemplate;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.EventStorageRule;
import ru.naumen.metainfo.shared.ui.ApplicationList;
import ru.naumen.metainfo.shared.ui.CustomFormList;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfo.shared.ui.MailLogList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.adminprofile.columns.AdminProfileAdvlistColumnFactory;
import ru.naumen.metainfoadmin.client.dynadmin.UIContextDecorator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms.CustomFormListAttributeColumnFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.ApplicationListAttributeColumnFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.EscalationEventActionListAttributeColumnFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction.EventActionListAttributeColumnFactory;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.EventActionAdvlistUIContext;
import ru.naumen.metainfoadmin.client.eventcleaner.rule.column.EventStorageRuleColumnFactory;
import ru.naumen.metainfoadmin.client.mail.advlist.MailLogListAttributeColumnFactory;
import ru.naumen.metainfoadmin.client.sets.columns.SettingsSetAdvlistColumnFactory;
import ru.naumen.metainfoadmin.client.tags.columns.TagAdvlistColumnFactory;
import ru.naumen.metainfoadmin.client.templates.content.columns.ContentTemplateListColumnFactory;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.gin.runtime.ExtendedListAttributeColumnFactoryProvider;
import ru.naumen.objectlist.client.mode.ExtendedListAttributeColumnFactory;
import ru.naumen.objectlist.shared.CustomList;

/**
 * <AUTHOR>
 * @since 26 янв. 2017 г.
 */
@Singleton
public class AdminActiveListAttributeColumnFactoryProvider extends ExtendedListAttributeColumnFactoryProvider
{
    @Inject
    private Provider<EscalationEventActionListAttributeColumnFactory> escalationEventActionColumnFactoryProvider;
    @Inject
    private Provider<EventActionListAttributeColumnFactory> eventActionColumnFactoryProvider;
    @Inject
    private Provider<CustomFormListAttributeColumnFactory> customFormColumnFactoryProvider;
    @Inject
    private Provider<MailLogListAttributeColumnFactory> mailLogColumnFactoryProvider;
    @Inject
    private Provider<ApplicationListAttributeColumnFactory> applicationColumnFactoryProvider;
    @Inject
    private Provider<TagAdvlistColumnFactory> tagColumnFactoryProvider;
    @Inject
    private Provider<SettingsSetAdvlistColumnFactory> setColumnFactoryProvider;
    @Inject
    private Provider<ContentTemplateListColumnFactory> contentTemplateColumnFactoryProvider;
    @Inject
    private Provider<EventStorageRuleColumnFactory> eventStorageRuleColumnFactoryProvider;
    @Inject
    private Provider<AdminProfileAdvlistColumnFactory> adminProfileColumnFactoryProvider;

    @Override
    public ExtendedListAttributeColumnFactory get(ListComponents components) //NOSONAR
    {
        ObjectListBase content = components.getContent();
        if (content instanceof EventActionList)
        {
            UIContextDecorator decorator = components.getContext().getParentContext();
            EventActionAdvlistUIContext context = (EventActionAdvlistUIContext)decorator.getAdaptee();

            return context.isWithLinks()
                    ? eventActionColumnFactoryProvider.get() : escalationEventActionColumnFactoryProvider.get();
        }
        if (content instanceof CustomFormList)
        {
            return customFormColumnFactoryProvider.get();
        }
        if (content instanceof MailLogList)
        {
            return mailLogColumnFactoryProvider.get();
        }
        if (content instanceof ApplicationList)
        {
            return applicationColumnFactoryProvider.get();
        }
        if (content instanceof CustomList && TagMetainfo.FQN.equals(content.getClazz()))
        {
            return tagColumnFactoryProvider.get();
        }
        if (content instanceof CustomList && SettingsSetMetainfo.FQN.equals(content.getClazz()))
        {
            return setColumnFactoryProvider.get();
        }
        if (content instanceof CustomList && ContentTemplate.FQN.equals(content.getClazz()))
        {
            return contentTemplateColumnFactoryProvider.get();
        }
        if (content instanceof CustomList && EventStorageRule.FQN.equals(content.getClazz()))
        {
            return eventStorageRuleColumnFactoryProvider.get();
        }
        if (content instanceof CustomList && AdminProfileMetainfo.FQN.equals(content.getClazz()))
        {
            return adminProfileColumnFactoryProvider.get();
        }
        return super.get(components);
    }
}
