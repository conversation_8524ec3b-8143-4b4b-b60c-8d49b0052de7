package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.cellview.client.Header;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.cellview.client.TextHeader;
import com.google.gwt.view.client.RangeChangeEvent;
import com.google.inject.Inject;

import ru.naumen.core.client.common.Dialog;
import ru.naumen.core.client.common.DialogCallback;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonCode;
import ru.naumen.core.client.widgets.DataTable;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfoadmin.client.TableWithArrowsDisplay.WithArrowsCellTableStyle;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.objectlist.client.extended.columns.ExtendedListPresentationHelper;

/**
 * Презентер списка атрибутов на форме настройки представления по умолчанию списка объектов
 * <AUTHOR>
 * @since 19 февр. 2016 г.
 *
 */
public class AttributesContentPresenter extends
        AttributesContentPresenterBase<AdvlistColumn, AdvlistSettingsDefault, AttributesContentDisplayDefaultPrs>
{
    public interface AdvlistDefaultPrsCommandCodes
    {
        String ADD_ATTRIBUTE = "advlistDefaultPrsAddAttribute";
        String DELETE_ATTRIBUTE = "advlistDefaultPrsDeleteAttribute";
        String EDIT_ATTRIBUTE = "advlistDefaultPrsEditAttribute";
        String MOVE_ATTRIBUTE_UP = "advlistDefaultPrsMoveAttrUp";
        String MOVE_ATTRIBUTE_DOWN = "advlistDefaultPrsMoveAttrDown";
        String SAVE_SETTINGS = "saveAdvListSettingsFromListCommand";
    }

    private final AdvlistDefaultPrsMessages messages;
    private final AdvlistDefaultPrsResources resources;
    private final ExtendedListPresentationHelper listPresentationHelper;
    private final Dialogs dialogs;

    private AdvlistSettingsDefault settings;
    private ObjectListUIContext objectListContext;
    private List<String> disabledByTags;
    private MetainfoUtils metainfoUtils;

    @Inject
    public AttributesContentPresenter(AttributesContentDisplayDefaultPrs display, EventBus eventBus,
            AdvlistDefaultPrsMessages messages,
            AdvlistDefaultPrsResources resources,
            ExtendedListPresentationHelper listPresentationHelper,
            Dialogs dialogs,
            MetainfoUtils metainfoUtils)
    {
        super(display, eventBus);
        this.messages = messages;
        this.resources = resources;
        this.listPresentationHelper = listPresentationHelper;
        this.dialogs = dialogs;
        this.metainfoUtils = metainfoUtils;
    }

    public AdvlistSettingsDefault getAdvlistSettingsDefault()
    {
        return this.settings;
    }

    @Inject
    public void init(AdvlistDefaultPrsMessages messages)
    {
        presenterConfig.setCaption(messages.defaultAttrs());
        presenterConfig.setAddAttrCommandCode(AdvlistDefaultPrsCommandCodes.ADD_ATTRIBUTE);
        presenterConfig.setDeleteAttrCommandCode(AdvlistDefaultPrsCommandCodes.DELETE_ATTRIBUTE);
        presenterConfig.setEditAttrCommandCode(AdvlistDefaultPrsCommandCodes.EDIT_ATTRIBUTE);
        presenterConfig.setMoveAttrDownCode(AdvlistDefaultPrsCommandCodes.MOVE_ATTRIBUTE_DOWN);
        presenterConfig.setMoveAttrUpCode(AdvlistDefaultPrsCommandCodes.MOVE_ATTRIBUTE_UP);
        presenterConfig.setSaveAdvListSettingsFromListCode(AdvlistDefaultPrsCommandCodes.SAVE_SETTINGS);
    }

    public void init(AdvlistSettingsDefault settings, ObjectListUIContext objectListContext)
    {
        this.settings = settings;
        this.objectListContext = objectListContext;
        this.disabledByTags = objectListContext.getMode().getDisabledAttributes().stream()
                .map(AttributeFqn::toString).collect(Collectors.toList());
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        getDisplay().getInherit().setValue(settings.isInherit(), false);
        getDisplay().getTable().asWidget().setVisible(!settings.isInherit());
        getDisplay().getToolBar().asWidget().setVisible(!settings.isInherit());
        toolBar.refresh(null);
    }

    @Override
    protected void addTableColumns(DataTable<AdvlistColumn> table)
    {
        addRowStyle(table);
        addTitleColumn(table);
        addCodeColumn(table);
        addPrsColumn(table);
    }

    @Override
    protected CommandParam<AdvlistColumn, Void> createActionColumnParam()
    {
        return new DefaultPrsCommandParam(objectListContext, settings, null, refreshCallback);
    }

    @Override
    protected List<AdvlistColumn> getAttributes()
    {
        Map<String, Attribute> attributes = objectListContext.getMode().getAttributes();
        return settings.getColumnList()
                .stream()
                .filter(col -> attributes.containsKey(col.getAttrCode()))
                .collect(Collectors.toList());
    }

    @Override
    protected void moveAttribute(int oldPosition, int newPosition)
    {
        settings.getColumnList().add(newPosition, settings.getColumnList().remove(oldPosition));
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void initToolBar()
    {
        DefaultPrsCommandParam param = new DefaultPrsCommandParam(objectListContext, settings, null, refreshCallback);
        toolBar.add((ButtonPresenter<AdvlistSettingsDefault>)buttonFactory.create(ButtonCode.ADD_DEFAULT_PRS_ATTRIBUTE,
                messages.addAttrButton(), presenterConfig.getAddAttrCommandCode(), param));
        toolBar.bind();
    }

    @Override
    protected void onBind()
    {
        super.onBind();

        getDisplay().getInherit().addStyleName(resources.css().inherit());
        getDisplay().getInherit().setText(messages.inherit());
        getDisplay().getInherit().addValueChangeHandler(this::onValueChangeDialog);
        getDisplay().getTable().asWidget().setVisible(!settings.isInherit());
        getDisplay().getToolBar().asWidget().setVisible(!settings.isInherit());
    }

    private void onValueChangeDialog(ValueChangeEvent<Boolean> event)
    {
        if (Boolean.TRUE.equals(event.getValue()))
        {
            dialogs.question(messages.resetDialogCaption(), "", messages.resetAttributes(),
                    new DialogCallback()
                    {
                        @Override
                        protected void onYes(Dialog dialog)
                        {
                            onValueChange(event);
                            dialog.destroy();
                        }

                        @Override
                        protected void onNo(Dialog dialog)
                        {
                            getDisplay().getInherit().setValue(settings.isInherit(), false);
                            dialog.destroy();
                        }
                    });
        }
        else
        {
            onValueChange(event);
        }
    }

    private void onValueChange(ValueChangeEvent<Boolean> event)
    {
        ArrayList<AdvlistColumn> columnListOld = Lists.newArrayList(settings.getColumnList());
        settings.getColumnList().clear();
        for (String attrCode : objectListContext.getMode().getAttributeCodes())
        {
            AdvlistColumn column = columnListOld.stream().filter(col -> col.getAttrCode()
                    .equals(attrCode)).findFirst().orElse(null);

            int width = column != null ? column.getWidthColumn() : 0;
            Attribute attr = objectListContext.getMode().getAttributes().get(attrCode);
            String attrPrsCode = metainfoUtils.getOverriddenPrsCodeForStrAttr(attr.getViewPresentation().getCode(),
                    objectListContext.getMode().getOverriddenPrsCodes(), attr.getFqn());
            settings.getColumnList().add(new AdvlistColumn(attrCode, attrPrsCode, width));
        }
        getDisplay().getTable().asWidget().setVisible(!event.getValue());
        getDisplay().getToolBar().asWidget().setVisible(!event.getValue());
        RangeChangeEvent.fire(getDisplay().getTable(), getDisplay().getTable().getVisibleRange());
        settings.setInherit(event.getValue());
        eventBus.fireEvent(new RefreshDefaultPrsFormEvent());
    }

    private void addCodeColumn(DataTable<AdvlistColumn> table)
    {
        table.addColumn(new TextColumn<AdvlistColumn>()
        {
            @Override
            public String getValue(AdvlistColumn attribute)
            {
                return AttributeFqn.getCode(attribute.getCode());
            }
        }, messages.attrCodeColumn());
    }

    private void addPrsColumn(DataTable<AdvlistColumn> table)
    {
        table.addColumn(new TextColumn<AdvlistColumn>()
        {
            @Override
            public String getValue(AdvlistColumn object)
            {
                Attribute attribute = objectListContext.getMode().getAttributes().get(object.getAttrCode());
                AttributeFqn attributeFqn = AttributeFqn.isAttributeFqn(object.getAttrCode())
                        ? AttributeFqn.parse(object.getAttrCode())
                        : null;
                String prsCode = metainfoUtils.getOverriddenPrsCodeForStrAttr(object.getPresentationCode(),
                        objectListContext.getMode().getOverriddenPrsCodes(),
                        attributeFqn);
                return listPresentationHelper.getPresentationTitleForList(attribute, prsCode);
            }
        }, messages.attrPrsColumn());
    }

    private void addTitleColumn(DataTable<AdvlistColumn> table)
    {
        TextColumn<AdvlistColumn> column = new TextColumn<AdvlistColumn>()
        {
            @Override
            public String getValue(AdvlistColumn attribute)
            {
                return objectListContext.getMode().getAttributes().get(attribute.getAttrCode()).getTitle();
            }
        };
        WithArrowsCellTableStyle tableStyle = cellTableResources.cellTableStyle();
        column.setCellStyleNames(tableStyle.titleColumn());
        Header<?> titleHeader = new TextHeader(messages.attrTitleColumn());
        titleHeader.setHeaderStyleNames(tableStyle.titleColumn());
        table.addColumn(column, titleHeader);
    }

    private void addRowStyle(DataTable<AdvlistColumn> table)
    {
        table.setRowStyles((row, rowIndex) ->
                disabledByTags.contains(row.getAttrCode()) ? cellTableResources.cellTableStyle()
                        .disabledSelectableItem() : "");
    }
}
