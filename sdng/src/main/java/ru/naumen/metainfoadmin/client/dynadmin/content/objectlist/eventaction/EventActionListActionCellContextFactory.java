package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.eventaction;

import com.google.common.base.Predicates;
import com.google.inject.Singleton;

import jakarta.inject.Inject;
import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.client.eventaction.EventActionsPresenterSettings;
import ru.naumen.metainfo.shared.eventaction.Constants.EventAction;
import ru.naumen.metainfoadmin.shared.Constants.EventActionCommandCode;
import ru.naumen.objectlist.client.extended.advlist.columns.AdvListActionCellContextFactory;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * <AUTHOR>
 * @since Feb 26, 2015
 */
@Singleton
public class EventActionListActionCellContextFactory extends AdvListActionCellContextFactory
{
    @Inject
    private EventActionsPresenterSettings settings;

    @Override
    public ExtendedListActionCellContext create(String code)
    {
        if (EventActionCommandCode.DELETE_EVENT_ACTION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.DEL, code,
                    Predicates.and(AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE),
                            value -> settings.isWithDelete()));
        }
        else if (EventActionCommandCode.EDIT_EVENT_ACTION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.EDIT, code,
                    Predicates.and(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT),
                            value -> settings.isWithEdit()));
        }
        else if (EventActionCommandCode.TOGGLE_EVENT_ACTION.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.SWITCH, code,
                    Predicates.and(AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT), value ->
                            Boolean.TRUE.equals(value.getProperty(EventAction.CAN_BE_ENABLED))));
        }
        return super.create(code);
    }
}
