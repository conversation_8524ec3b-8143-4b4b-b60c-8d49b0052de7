package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

import com.google.gwt.dom.client.Element;
import com.google.gwt.event.shared.EventBus;

import jakarta.annotation.Nullable;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.FilterAttributeSelectionMode;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.dynadmin.BasicUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UITemplateContext;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.ObjectListMode;

/**
 * Контекст списка в админке
 * <AUTHOR>
 * @since 24.04.2012
 */
public class ObjectListUIContext extends BasicUIContext implements ObjectListContext
{
    private final ObjectListBase objectList;
    private ObjectListMode mode;
    private boolean preventOnClickTransition = false;
    private boolean inlineEditAllowed = true;
    private boolean scrollNeeded = false;
    private Element panelElement;
    private String formCode;

    public ObjectListUIContext(Context parentContext, @Nullable ContentInfo contentInfo, boolean editable,
            @Nullable ObjectListBase content)
    {
        super(parentContext, contentInfo, editable);
        this.objectList = content;
    }

    public ObjectListUIContext(Context parentContext, ContentInfo contentInfo, boolean editable, ObjectListBase content,
            EventBus eventBus)
    {
        super(parentContext, contentInfo, eventBus, editable);
        this.objectList = content;
    }

    @Override
    public String getFormCode()
    {
        return formCode;
    }

    @Override
    public FilterAttributeSelectionMode getAttributeSelectionMode()
    {
        return FilterAttributeSelectionMode.FLAT_LIST;
    }

    @Override
    public DtObject getFormObject()
    {
        return this.<UIContext> getParentContext() == null ? null : this.<UIContext> getParentContext().getObject();
    }

    @Override
    public boolean isDefaultSettingsAllowed()
    {
        return true;
    }

    @Override
    public ObjectListMode getMode()
    {
        return mode;
    }

    @Override
    public int getNextBaseTabIndex()
    {
        return 0;
    }

    @Nullable
    @Override
    public ObjectListBase getObjectList()
    {
        return objectList;
    }

    public Collection<DtObject> getObjects()
    {
        return getMode().getSelectedObjects();
    }

    @Override
    public Element getPanelElement()
    {
        return panelElement;
    }

    @Override
    public List<DtObject> getShowingObjects()
    {
        return Collections.emptyList();
    }

    @Nullable
    @Override
    public UITemplateContext getUITemplateContext()
    {
        if (getParentContext() instanceof UIContext)
        {
            return this.<UIContext> getParentContext().getUITemplateContext();
        }
        else
        {
            return super.getUITemplateContext();
        }
    }

    @Override
    public boolean hasPreventOnClickAllTransition()
    {
        return false;
    }

    @Override
    public boolean hasPreventOnClickTransition()
    {
        return preventOnClickTransition;
    }

    @Override
    public boolean hasSearchPerformed()
    {
        return false;
    }

    @Override
    public boolean isInlineEditAllowed()
    {
        return inlineEditAllowed;
    }

    @Override
    public boolean isOnAddForm()
    {
        return false;
    }

    @Override
    public boolean isOnEditRelationForm()
    {
        return false;
    }

    public boolean isPreventOnClickTransition()
    {
        return preventOnClickTransition;
    }

    @Override
    public boolean isScrollNeeded()
    {
        return scrollNeeded;
    }

    @Override
    public boolean needShowFormObject()
    {
        return false;
    }

    @Override
    public void setInlineEditAllowed(boolean inlineEditAllowed)
    {
        this.inlineEditAllowed = inlineEditAllowed;
    }

    @Override
    public void setMode(ObjectListMode mode)
    {
        this.mode = mode;
    }

    @Override
    public void setNeedShowFormObject()
    {
    }

    @Override
    public void setPanelElement(Element panelElement)
    {
        this.panelElement = panelElement;
    }

    @Override
    public void setPreventOnClickAllTransition(boolean preventOnClickAllTransition)
    {
    }

    @Override
    public void setPreventOnClickTransition()
    {
        preventOnClickTransition = true;
    }

    public void setPreventOnClickTransition(boolean preventOnClickTransition)
    {
        this.preventOnClickTransition = preventOnClickTransition;
    }

    @Override
    public void setScrollNeeded(boolean scrollNeeded)
    {
        this.scrollNeeded = scrollNeeded;
    }

    @Override
    public void setSearchPerformed(boolean isPerformed)
    {
    }

    @Override
    public void setShowingObjects(List<DtObject> list)
    {
    }

    @Override
    public void setDefaultSettingsAllowed(boolean defaultSettingsAllowed)
    {
        // Do nothing, always true.
    }

    @Override
    public void setFormCode(String formCode)
    {
        this.formCode = formCode;
    }

    @Override
    public boolean isResizableColumns()
    {
        return true;
    }
}