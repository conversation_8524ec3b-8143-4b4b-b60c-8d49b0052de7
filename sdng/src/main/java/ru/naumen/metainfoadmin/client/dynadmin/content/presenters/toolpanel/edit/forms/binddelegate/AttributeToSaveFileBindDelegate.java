package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;

import java.util.HashMap;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.admin.client.AdminMessages;
import ru.naumen.core.client.content.ContextualCallback;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.ListBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;

/**
 * Компонент, производящий инициализацию свойств для списка атрибутов типа Файл
 * <AUTHOR>
 * @since 08.04.19
 */
public class AttributeToSaveFileBindDelegate implements PropertyDelegateBind<SelectItem, ListBoxProperty>
{
    @Inject
    private MetainfoUtils metainfoUtils;

    @Inject
    private AdminMessages amessages;

    @Inject
    private MetainfoServiceAsync metainfoService;

    @Override
    public void bindProperty(PropertyContainerContext context, ListBoxProperty property, AsyncCallback<Void> callback)
    {
        property.getValueWidget().clear();

        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        if (null == ctx.getParentContext().getMetainfo() || ctx.getTool() == null)
        {
            callback.onSuccess(null);
            return;
        }

        FileList fileList = ctx.getTool().getAssociatedContent() instanceof FileList
                ? (FileList)ctx.getTool().getAssociatedContent() : null;

        SingleSelectCellList<String> attributeValueWidget = property.getValueWidget();
        attributeValueWidget.clear();

        if (fileList == null || fileList.getAttributeCodes().contains(FileList.OWN_OBJECT_FILES))
        {
            boolean isRelatedObjectFile = ctx.getTool().getAssociatedContent() instanceof RelObjPropertyList
                                          || fileList != null && fileList.getRelationType()
                    .equals(RelationType.RELATED_OBJECT_FILES);
            attributeValueWidget.addItem(
                    isRelatedObjectFile ? amessages.relatedObjectFiles() : amessages.ownObjectFiles(),
                    FileList.OWN_OBJECT_FILES);
        }

        if (fileList != null && fileList.getRelationType().equals(RelationType.RELATED_OBJECT_FILES))
        {
            Attribute attr = ctx.getParentContext().getMetainfo()
                    .getAttribute(fileList.getAttrChain().get(0).getAttrCode());
            metainfoService.getFullMetaInfo(attr.getType().getPermittedTypes(),
                    new ContextualCallback<List<MetaClass>>(ctx.getParentContext())
                    {
                        @Override
                        protected void handleSuccess(List<MetaClass> metaClasses)
                        {
                            fillAttributes(fileList, attributeValueWidget, metaClasses);
                            callback.onSuccess(null);
                        }
                    });
        }
        else if (fileList == null && ctx.getToolLocationContent() instanceof RelObjPropertyList)
        {
            Attribute attr = ctx.getParentContext().getParentContext().getMetainfo()
                    .getAttribute(((RelObjPropertyList)ctx.getToolLocationContent()).getAttrCode());
            metainfoService.getFullMetaInfo(attr.getType().getPermittedTypes(),
                    new ContextualCallback<List<MetaClass>>(ctx.getParentContext())
                    {
                        @Override
                        protected void handleSuccess(List<MetaClass> metaClasses)
                        {
                            fillAttributes(fileList, attributeValueWidget, metaClasses);
                            callback.onSuccess(null);
                        }
                    });
        }
        else if (fileList == null && ctx.getToolLocationContent() instanceof ObjectListBase)
        {
            ObjectListBase objList = (ObjectListBase)ctx.getToolLocationContent();
            //В случае если для контента не установлено ограничение включаем во множество отображаемых элементов
            //fqn'ы всех наследников того метакласса где была объявлена прямая ссылка
            if (objList.getClazz() != null)
            {
                metainfoService.getAllAttributes(objList.getClazz(), new BasicCallback<Collection<Attribute>>()
                {
                    @Override
                    public void handleSuccess(Collection<Attribute> attributes)
                    {
                        List<Attribute> linkAttrs = attributes.stream()
                                .filter(input -> FileAttributeType.CODE.equals(input.getType().getCode()))
                                .collect(Collectors.toList());
                        metainfoUtils.sort(linkAttrs);
                        linkAttrs.stream().filter(
                                        attr -> fileList == null || fileList.getAttributeCodes().contains(attr.getCode()))
                                .forEach(attribute -> attributeValueWidget
                                        .addItem(amessages.attribute(attribute.getTitle()), attribute.getCode()));
                        callback.onSuccess(null);
                    }
                });
            }
            else
            {
                metainfoService.getFullMetaInfo(Lists.newArrayList(objList.getFqns()),
                        new ContextualCallback<List<MetaClass>>(ctx.getParentContext())
                        {
                            @Override
                            protected void handleSuccess(List<MetaClass> metaClasses)
                            {
                                fillAttributes(fileList, attributeValueWidget, metaClasses);
                                callback.onSuccess(null);
                            }
                        });
            }
        }
        else
        {
            fillAttributes(fileList, attributeValueWidget, Lists.newArrayList(ctx.getParentContext().getMetainfo()));
            callback.onSuccess(null);
        }
    }

    private void fillAttributes(FileList fileList, SingleSelectCellList<String> attributeValueWidget,
            List<MetaClass> metaClasses)
    {
        Map<String, Attribute> allAttrs = new HashMap<>();
        for (MetaClass mc : metaClasses)
        {
            for (Attribute attr : mc.getAttributes())
            {
                allAttrs.put(attr.getCode() + attr.getTitle(), attr);
            }
        }
        List<Attribute> linkAttrs = allAttrs.values().stream()
                .filter(input -> !input.getType().isAttributeOfRelatedObject()
                                 && FileAttributeType.CODE.equals(input.getType().getCode()))
                .collect(Collectors.toList());
        metainfoUtils.sort(linkAttrs);
        linkAttrs.stream().filter(attr -> fileList == null || fileList.getAttributeCodes().contains(attr.getCode()))
                .forEach(attribute -> attributeValueWidget.addItem(amessages.attribute(attribute.getTitle()),
                        attribute.getCode()));
    }
}
