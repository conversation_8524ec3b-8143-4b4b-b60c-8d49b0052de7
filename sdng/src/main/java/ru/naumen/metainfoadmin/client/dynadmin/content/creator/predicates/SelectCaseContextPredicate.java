/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 26 сент. 2013 г.
 *
 */
@Singleton
public class SelectCaseContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(UI.Form.NEW.equals(context.getCode())
                           && !Constants.NO_SELECT_CASE_CONTENT.contains(context.getMetainfo().getFqn().getId()));
    }
}