package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import static ru.naumen.core.client.events.AttentionEvent.DISABLED_BY_TAGS;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.Inject;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.CoreGinjector.FormDisplayFactory;
import ru.naumen.core.client.events.AttentionEvent;
import ru.naumen.core.client.events.AttentionHandler;
import ru.naumen.core.client.forms.DialogDisplay.DialogWidth;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.AdvListFilterUtils;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.ListFilterPresenter;

/**
 * Форма редактирования фильтрации списка объектов
 * <AUTHOR>
 * @since 19 февр. 2016 г.
 *
 */
public abstract class EditAdvListFilterFormPresenter extends OkCancelPresenter<FormDisplay> implements AttentionHandler
{
    @Inject
    private AdvListFilterUtils filterUtils;
    @Inject
    protected ListFilterPresenter listFilterPresenter;
    @Inject
    protected TagsMessages tagsMessages;

    protected ListFilter content;
    protected ObjectListUIContext context;

    protected EditAdvListFilterFormPresenter(FormDisplayFactory formDisplayFactory, String caption, EventBus eventBus)
    {
        super(formDisplayFactory.create(caption), eventBus);
    }

    public void init(ListFilter content, ObjectListUIContext context)
    {
        this.content = content;
        this.context = context;
    }

    protected ObjectListContext createFilterContext()
    {
        return context;
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        registerHandler(eventBus.addHandler(AttentionEvent.getType(), this));
        getDisplay().setContent(listFilterPresenter.getDisplay());
        ListFilter listFilter = getListFilterForShow();
        listFilterPresenter.init(listFilter, createFilterContext());
        listFilterPresenter.hideTools();
        listFilterPresenter.bind();
        getDisplay().getPanel().ensureDebugId("filter");
        getDisplay().setDialogWidth(DialogWidth.W920);
        getDisplay().display();
        showAttention();
    }

    /**
     * Вернуть модель фильтра, подготовленную для отображения на форме.
     */
    protected ListFilter getListFilterForShow()
    {
        return filterUtils.cloneListFilter(content);
    }

    @Override
    public void onAttention(AttentionEvent event)
    {
        if (DISABLED_BY_TAGS.equals(event.getAttentionTypeCode()))
        {
            showAttentionMessage(true);
        }
    }

    private void showAttentionMessage(boolean show)
    {
        String message = show ? getAttentionMessage() : StringUtilities.EMPTY;
        getDisplay().addAttentionMessage(message);
    }

    protected abstract String getAttentionMessage();

    private void showAttention()
    {
        List<String> disabledByTags = context.getDisabledAttributes()
                .stream().map(AttributeFqn::toString).collect(Collectors.toList());
        List<ListFilterOrElement<?>> filterOrElements = content.getElements().stream()
                .map(ListFilterAndElement::getElements)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        boolean contains = filterOrElements.stream()
                .map(ListFilterOrElement::getAttributeFqn)
                .anyMatch(disabledByTags::contains);

        boolean disabledValue = filterOrElements.stream()
                .filter(orElement -> orElement.getValue() instanceof DtObject)
                .anyMatch(orElement ->
                        Boolean.TRUE.equals(
                                ((DtObject)orElement.getValue()).getProperty(Constants.DtoTree.DISABLED_SELECTABLE)));

        showAttentionMessage(contains || disabledValue);
    }
}