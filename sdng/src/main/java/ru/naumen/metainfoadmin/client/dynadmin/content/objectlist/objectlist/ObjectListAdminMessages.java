/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.objectlist;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminAllMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminMessages;

/**
 * <AUTHOR>
 * @since 21.12.2012
 *
 */
public class ObjectListAdminMessages implements ObjectListBaseAdminMessages<ObjectList>
{
    @Inject
    ObjectListBaseAdminAllMessages messages;

    @Override
    public String contentTypeTitle()
    {
        return messages.objectListTitle();
    }
}