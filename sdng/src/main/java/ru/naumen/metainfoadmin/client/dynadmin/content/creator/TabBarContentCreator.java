package ru.naumen.metainfoadmin.client.dynadmin.content.creator;

import java.util.Collections;
import java.util.function.Consumer;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.TabBar;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;

/**
 * {@link ContentCreator} для отображения параметров добавляемого контента типа "Панель вкладок"
 *
 * <AUTHOR>
 * @since 12.08.2010
 *
 */
public class TabBarContentCreator extends SimpleContentCreatorBase<TabBar>
{
    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> hideSingleTab;

    @Named(PropertiesGinModule.CHECK_BOX)
    @Inject
    private Property<Boolean> hideTabBorder;

    @Override
    public void fillContent(TabBar content)
    {
        // Для всей панели вкладок профили не задаются, можно только для отдельных вкладок.
        content.setProfiles(Collections.emptyList());
        content.setVersProfiles(Collections.emptyList());
        content.getTags().clear();
        content.getVisibilityCondition().getElements().clear();
        content.setPosition(Position.valueOf(SelectListPropertyValueExtractor.getValue(position)));
        setParent(content);
        if (null != settingsSet)
        {
            String settingsSetValue = SelectListPropertyValueExtractor.getValue(settingsSet);
            content.setSettingsSet(settingsSetValue);
        }
    }

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();

        ensureDebugId();
        add(hideSingleTab);
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
        prepareHideTabOptions(hideSingleTab, hideTabBorder, messages, (value) ->
        {
            if (value)
            {
                register(hideTabBorder,
                        display.addPropertyAfter(hideTabBorder, getPropertyRegistration(hideSingleTab)));
            }
            else
            {
                hideTabBorder.setValue(false, false);
                remove(hideTabBorder);
            }
        });
    }

    @Override
    protected void bindShowCaptionAndCollapsedByDefault()
    {
    }

    @Override
    protected TabBar getContentInner()
    {
        Tab tab = new Tab();
        metainfoUtils.setCaption(tab.getCaption(), caption.getValue());

        Layout layout = new Layout();
        layout.setWindowLayout(false);
        tab.setLayout(layout);
        tab.setUuid(code.getValue());
        // Ограничение по профилям и метки, указанные при создании панели вкладок, накладываются на первую вкладку. 
        tab.setProfiles(SelectListPropertyValueExtractor.getCollectionValue(profiles));
        if (null != versProfiles)
        {
            tab.setProfiles(SelectListPropertyValueExtractor.getCollectionValue(versProfiles));
        }
        tab.getTags().addAll(SelectListPropertyValueExtractor.getValue(tags));
        if (null != visibilityCondition.getValue())
        {
            tab.getVisibilityCondition().getElements().addAll(visibilityCondition.getValue().getElements());
        }

        TabBar tabBar = new TabBar();
        tab.setParent(tabBar);
        tabBar.setUuid(TabBar.PREFIX + code.getValue());
        tabBar.getTab().add(tab);
        tabBar.setHasHead(false);
        tabBar.setHideSingleTab(hideSingleTab.getValue());
        tabBar.setHideTabBorder(hideTabBorder.getValue() && tabBar.isHideSingleTab());
        return tabBar;
    }

    private void ensureDebugId()
    {
        DebugIdBuilder.ensureDebugId(hideSingleTab, "hideSingleTab");
    }

    /**
     * Подготовка опций скрытия вкладки
     * Статический метод для использования в других местах
     */
    public static void prepareHideTabOptions(Property<Boolean> hideSingleTab, Property<Boolean> hideTabBorder,
            ContentCreatorMessages messages, Consumer<Boolean> hideSingleTabChangeHandler)
    {
        hideSingleTab.setCaption(messages.hideSingleTab());
        DebugIdBuilder.ensureDebugId(hideSingleTab, "hideSingleTab");
        hideSingleTab.addValueChangeHandler(event ->
        {
            if (ClientUtils.isInlineForm())
            {
                hideSingleTabChangeHandler.accept(event.getValue());
            }
        });

        if (ClientUtils.isInlineForm())
        {
            hideTabBorder.setCaption(messages.hideTabBorder());
            DebugIdBuilder.ensureDebugId(hideTabBorder, "hideTabBorder");
        }
    }
}
