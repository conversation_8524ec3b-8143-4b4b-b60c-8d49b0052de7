package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.core.client.mvp.Display;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplay;
import ru.naumen.metainfoadmin.client.common.content.ActionContentPanelDisplayFactory;
import ru.naumen.metainfoadmin.client.common.content.DraggableContentDisplay;

/**
 * Реализация {@link ObjectListContentDisplay}
 *
 * <AUTHOR>
 *
 */
public class ObjectListContentDisplayImpl extends DraggableContentDisplay implements ObjectListContentDisplay
{
    private FlowPanel outer;
    Display presentation;
    String baseDebugId;

    @Inject
    public ObjectListContentDisplayImpl(ActionContentPanelDisplayFactory displayFactory)
    {
        super(displayFactory);
        outer = new FlowPanel();
        addWidget(outer);
    }

    public ActionContentPanelDisplay getButtonPanel()
    {
        return buttonPanel;
    }

    public FlowPanel getInfoPanel()
    {
        return infoPanel;
    }

    @Override
    public void setPresentationDisplay(Display presentation)
    {
        DebugIdBuilder.ensureDebugId(presentation, "presentation");
        if (null != this.presentation)
        {
            this.presentation.asWidget().removeFromParent();
        }
        this.presentation = presentation;
        outer.add(presentation);
    }

    @Override
    protected void onEnsureDebugId(String baseID)
    {
        baseDebugId = baseID;
        super.onEnsureDebugId(baseID);
    }
}
