package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminAllMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminMessages;

/**
 *
 * <AUTHOR>
 *
 */
public class RelObjectListAdminMessages implements ObjectListBaseAdminMessages<RelObjectList>
{
    @Inject
    ObjectListBaseAdminAllMessages messages;

    @Override
    public String contentTypeTitle()
    {
        return messages.relObjectListTitle();
    }
}