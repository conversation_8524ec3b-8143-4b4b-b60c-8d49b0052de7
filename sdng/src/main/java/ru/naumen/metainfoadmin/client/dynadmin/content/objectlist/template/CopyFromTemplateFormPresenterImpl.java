package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.template;

import static ru.naumen.metainfo.shared.Constants.UI.WINDOW_KEY;
import static ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateAction.ListSettingsParts.ObjectFilter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.google.common.collect.Lists;

import java.util.HashSet;

import com.google.gwt.event.shared.EventBus;
import com.google.inject.assistedinject.Assisted;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.content.ContextUtils;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.forms.OkCancelPresenter;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.ListTemplateTree;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithoutFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.tree.dto.impl.listtemplate.DtoTreeFactoryListTemplateContext;
import ru.naumen.core.client.tree.selection.FilteredSingleSelectionModel;
import ru.naumen.core.client.validation.NotNullValidator;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.DefaultPropertyFormDisplayImpl;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.properties.CellTreeProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;
import ru.naumen.metainfo.client.MetainfoServiceAsync;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ListTemplate;
import ru.naumen.metainfo.shared.dispatch2.SaveUiAndGetSavedContentAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateAction;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateAction.ListSettingsParts;
import ru.naumen.metainfo.shared.templates.list.dispatch.ApplyListTemplateResponse;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.HasObjectActionsMenu;
import ru.naumen.metainfo.shared.ui.ObjectActionsMenuHolder;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfo.shared.ui.ToolBar;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.AdvlistDefaultPrsFormPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings.FilterSettingsFormPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings.FilterStrategiesPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings.ObjectFilterPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditObjectActionsBlockPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.EditToolPanelBlockPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.HasListTemplate;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.ToolPanelContentPresenterEdit;
import ru.naumen.metainfoadmin.shared.list.template.ListToolsCopyUtils;

/**
 * Презентер формы применения шаблона списка
 * <AUTHOR>
 * @since 25.04.2018
 */
public class CopyFromTemplateFormPresenterImpl extends OkCancelPresenter<DefaultPropertyFormDisplayImpl>
        implements CopyFromTemplateFormPresenter
{

    private static final String DEBUG_ID = "template";

    @Inject
    private CopyFromTemplateMessages copyFromTemplateMessages;
    @Inject
    private DispatchAsync dispatch;
    @Inject
    private MetainfoServiceAsync metainfoServiceAsync;
    @Inject
    private DtoTreeFactory<DtObject, ListTemplateTree, WithoutFolders, DtoTreeFactoryListTemplateContext> treeFactory;
    @Inject
    private Dialogs dialogs;
    @Inject
    private Processor validation;
    @Inject
    private NotNullValidator<DtObject> notNullValidator;

    private CellTreeProperty selectTemplate;
    private final ObjectListBase contentOwner;
    private final UIContext context;
    private Presenter caller;
    private final List<Tool> availableTools;
    private final Set<String> availableAttrs = new HashSet<>();

    @Inject
    public CopyFromTemplateFormPresenterImpl(@Assisted ObjectListBase owner, @Assisted UIContext parentContext,
            @Assisted List<Tool> availableTools, DefaultPropertyFormDisplayImpl display, EventBus eventBus)
    {
        super(display, eventBus);
        this.contentOwner = owner;
        this.context = parentContext;
        this.availableTools = availableTools;
    }

    @Override
    public void onApply()
    {
        if (!validation.validate())
        {
            return;
        }

        super.onApply();
        ObjectListBase listFromTemplate = (ObjectListBase)selectTemplate.getValue().get(ListTemplate.TEMPLATE);
        String templateCode = (String)selectTemplate.getValue().get(FakeMetaClassesConstants.CODE);
        final String targetTemplateCode;
        if (context instanceof ObjectListUIContext)
        {
            targetTemplateCode = context.getTemplateCode();
        }
        else
        {
            targetTemplateCode = null;
        }
        if (caller != null)
        {
            ListTemplateUsagePoint usagePoint = new ListTemplateUsagePoint(context.getParentContext().getMetainfo()
                    .getFqn(), context.getCode(), contentOwner.getUuid());
            //Вызов осуществлен из блока "Действия в списке объекта"
            if (caller instanceof EditObjectActionsBlockPresenter)
            {
                EditObjectActionsBlockPresenter<?> editObjectActionsBlockPresenter =
                        (EditObjectActionsBlockPresenter<?>)caller;
                ObjectActionsMenuHolder holder = ((HasObjectActionsMenu)listFromTemplate).getObjectActionsMenu();
                ToolPanel toolPanel = holder.getObjectActionsToolPanel() != null
                        ? prepareToolPanelForCopying(holder.getObjectActionsToolPanel(), availableTools,
                        context.getCode())
                        : null;
                updateQuickForms(listFromTemplate, contentOwner, toolPanel, context.getCode());
                editObjectActionsBlockPresenter.updateActions(holder.isUseSystemSettings(), toolPanel,
                        holder.getObjectActionsMenuPosition(), holder.getMenuIconCatalogCode());
                usagePoint.getSettingsParts().add(ListSettingsParts.ObjectActions);
                ((HasListTemplate)caller).addListTemplateAndUsagePoint(templateCode, usagePoint);
                unbind();
                return;
            }
            //Вызов осуществлен из блоков "Панель действий", "Панель массовых операций"
            if (caller instanceof EditToolPanelBlockPresenter)
            {
                EditToolPanelBlockPresenter<?> toolPanelPresenter = (EditToolPanelBlockPresenter<?>)caller;
                ToolPanel valueToolPanel = null;
                if (toolPanelPresenter.getToolPanelKind().equals(ToolPanelKind.ACTION_BAR))
                {
                    valueToolPanel = prepareToolPanelForCopying(listFromTemplate.getToolPanel(), availableTools,
                            context.getCode());
                    usagePoint.getSettingsParts().add(ListSettingsParts.ActionPanel);
                }
                else if (toolPanelPresenter.getToolPanelKind().equals(ToolPanelKind.MASS_OPERATIONS))
                {
                    valueToolPanel = prepareToolPanelForCopying(listFromTemplate.getMassOperationsPanel(),
                            availableTools, context.getCode());
                    usagePoint.getSettingsParts().add(ListSettingsParts.MassActionPanel);
                }
                updateQuickForms(listFromTemplate, contentOwner, valueToolPanel, context.getCode());
                toolPanelPresenter.updateToolPanel(valueToolPanel);
                ((HasListTemplate)caller).addListTemplateAndUsagePoint(templateCode, usagePoint);
                unbind();
                return;
            }
            //Вызов осуществлен из блока "Настройка внешнего вида по умолчанию"
            if (caller instanceof AdvlistDefaultPrsFormPresenter)
            {
                AdvlistDefaultPrsFormPresenter advlistDefaultPrsForm = (AdvlistDefaultPrsFormPresenter)caller;
                AdvlistSettingsDefault advlistSettingsDefault = ListToolsCopyUtils
                        .copyAdvlistSettingsDefault(listFromTemplate.getDefaultSettings());
                advlistDefaultPrsForm.updateDefaultPrsForm(advlistSettingsDefault, availableAttrs);
                usagePoint.getSettingsParts().add(ListSettingsParts.DefaultPrs);
                ((HasListTemplate)caller).addListTemplateAndUsagePoint(templateCode, usagePoint);
                unbind();
                return;
            }
            //Вызов осуществлен из блока "Настройка ограничений при фильтрации"
            if (caller instanceof FilterStrategiesPresenter)
            {
                SaveUiAndGetSavedContentAction saveUIAction = new SaveUiAndGetSavedContentAction(
                        context.getParentContext().getMetainfo().getFqn(), context.getRootContent(), contentOwner,
                        false, context.getCode());
                dispatch.execute(
                        new ApplyListTemplateAction(targetTemplateCode, saveUIAction, contentOwner.getUuid(),
                                templateCode, Lists.newArrayList(ListSettingsParts.FiltrationSettings)),
                        new BasicCallback<ApplyListTemplateResponse>(getDisplay())
                        {
                            @Override
                            protected void handleSuccess(ApplyListTemplateResponse response)
                            {
                                unbind();
                                caller.refreshDisplay();
                                if (targetTemplateCode == null)
                                {
                                    eventBus.fireEvent(new RefreshContentEvent(contentOwner));
                                }
                            }
                        });
                return;
            }
            // Вызов осуществляется из блока настройки ограничения содержимого списка
            if (caller instanceof ObjectFilterPresenter)
            {
                SaveUiAndGetSavedContentAction saveUIAction = new SaveUiAndGetSavedContentAction(
                        context.getParentContext().getMetainfo().getFqn(), context.getRootContent(), contentOwner,
                        false, context.getCode());
                dispatch.execute(
                        new ApplyListTemplateAction(targetTemplateCode, saveUIAction, contentOwner.getUuid(),
                                templateCode, Lists.newArrayList(ObjectFilter)),
                        new BasicCallback<ApplyListTemplateResponse>(getDisplay())
                        {
                            @Override
                            protected void handleSuccess(ApplyListTemplateResponse response)
                            {
                                unbind();
                                if (response.getContent() instanceof ObjectListBase)
                                {
                                    contentOwner
                                            .setObjectFilter(((ObjectListBase)response.getContent()).getObjectFilter());
                                }
                                eventBus.fireEvent(new RefreshContentEvent(contentOwner));
                                caller.refreshDisplay();
                            }
                        });
                return;
            }
        }
        //Вызов осуществлен из контента (полное применение настроек)
        SaveUiAndGetSavedContentAction saveUIAction = new SaveUiAndGetSavedContentAction(
                context.getParentContext().getMetainfo().getFqn(), context.getRootContent(), contentOwner, false,
                context.getCode());
        dispatch.execute(new ApplyListTemplateAction(targetTemplateCode, saveUIAction, contentOwner.getUuid(),
                templateCode, Lists.newArrayList(ListSettingsParts.All)), new BasicCallback<ApplyListTemplateResponse>(
                getDisplay())
        {
            @Override
            protected void handleSuccess(ApplyListTemplateResponse response)
            {
                ObjectListBase content = (ObjectListBase)response.getContent();

                switchToolPanelContent(contentOwner.getToolPanel(), content.getToolPanel());
                switchToolPanelContent(contentOwner.getMassOperationsPanel(), content.getMassOperationsPanel());

                ((HasObjectActionsMenu)contentOwner)
                        .setObjectActionsMenu(((HasObjectActionsMenu)content).getObjectActionsMenu());
                contentOwner.setDefaultSettings(content.getDefaultSettings());
                ((HasFilterRestrictionStrategy)contentOwner).getFilterRestrictionSettings().clear();
                ((HasFilterRestrictionStrategy)contentOwner).getFilterRestrictionSettings()
                        .putAll(((HasFilterRestrictionStrategy)content).getFilterRestrictionSettings());
                contentOwner.setObjectFilter(content.getObjectFilter());

                // Обновить контент со списком
                response.getPermissions().fillPermissionHolder(
                        ContextUtils.getRootPermissionContext(context).getPermissions());
                context.getEventBus().fireEvent(new RefreshContentEvent(contentOwner));
                unbind();
                if (!response.getMessage().isEmpty())
                {
                    dialogs.info(response.getMessage());
                }
            }
        });
    }

    @Override
    public void setFormCaller(Presenter caller)
    {
        this.caller = caller;
    }

    /**
     * Заполняем возможные варианты выборы шаблона
     */
    private void fillSelectTemplates()
    {
        String attrGroupCode = null;
        if (caller != null
            && (caller instanceof AdvlistDefaultPrsFormPresenter || caller instanceof FilterSettingsFormPresenter))
        {
            attrGroupCode = contentOwner.getAttributeGroup();
        }
        availableAttrs.clear();
        metainfoServiceAsync.getAllAttributes(contentOwner.getClazz(), contentOwner.getCase(), attrGroupCode,
                new BasicCallback<Collection<Attribute>>(getDisplay())
                {
                    @Override
                    protected void handleSuccess(Collection<Attribute> attrs)
                    {
                        for (Attribute attr : attrs)
                        {
                            availableAttrs.add(attr.getFqn().toString());
                        }
                    }
                });
        DtoTreeFactoryListTemplateContext modelContext = new DtoTreeFactoryListTemplateContext(contentOwner.getClazz(),
                contentOwner.getCase(), attrGroupCode, needOthersTemplates());
        modelContext.setTreeType(Constants.DtoTree.LIST_TEMPLATES_TREE);

        treeFactory.createTree(modelContext, new BasicCallback<HasValueOrThrow<DtObject>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<DtObject> value)
            {
                PopupValueCellTree<DtObject, DtObject, FilteredSingleSelectionModel<DtObject>> tree =
                        (PopupValueCellTree<DtObject, DtObject, FilteredSingleSelectionModel<DtObject>>)value;
                selectTemplate = new CellTreeProperty(copyFromTemplateMessages.template(), tree);
                selectTemplate.ensureDebugId(DEBUG_ID);
                selectTemplate.setValidationMarker(true);
                getDisplay().add(selectTemplate);
                validation.validate(selectTemplate, notNullValidator);
            }
        });
    }

    private ToolPanel prepareToolPanelForCopying(ToolPanel toolPanel, List<Tool> availableTools, String formCode)
    {
        ToolPanel clone = (ToolPanel)toolPanel.clone();
        clone.setParent(contentOwner);
        clone.setUuid(UUIDGenerator.get().nextUUID());
        ListToolsCopyUtils.renewUUIDsToolPanel(clone, availableTools, formCode);
        return clone;
    }

    /**
     * Меняет содержимое тулпанели на содержимое другой тулпанели
     */
    private void switchToolPanelContent(ToolPanel panel, @Nullable ToolPanel sourcePanel)
    {
        if (sourcePanel == null)
        {
            return;
        }
        panel.setUseSystemSettings(sourcePanel.isUseSystemSettings());
        panel.setIgnoreInImport(sourcePanel.isIgnoreInImport());
        panel.setShowToolCaptions(sourcePanel.isShowToolCaptions());
        List<ToolBar> toolBars = new ArrayList<>();
        ObjectUtils.cloneCollection(sourcePanel.getToolBars(), toolBars);
        panel.setToolBars(toolBars);
        for (ToolBar toolBar : toolBars)
        {
            toolBar.setParent(panel);
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        getDisplay().setCaptionText(copyFromTemplateMessages.formTitle());
        fillSelectTemplates();
        getDisplay().display();
    }

    private boolean needOthersTemplates()
    {
        return caller == null || caller instanceof ToolPanelContentPresenterEdit
               || caller instanceof EditObjectActionsBlockPresenter || caller instanceof EditToolPanelBlockPresenter;
    }

    private void updateQuickForms(ObjectListBase sourceContent, ObjectListBase targetContent,
            @Nullable ToolPanel toolPanel, String formCode)
    {
        boolean resetQuickForms = !Objects.equals(sourceContent.getFqnOfClass(), targetContent.getFqnOfClass());
        boolean useQuickForm = !WINDOW_KEY.equals(formCode);
        if (null != toolPanel && (resetQuickForms || useQuickForm))
        {
            ListToolsCopyUtils.updateQuickFormsItems(toolPanel, resetQuickForms, useQuickForm);
        }
    }
}
