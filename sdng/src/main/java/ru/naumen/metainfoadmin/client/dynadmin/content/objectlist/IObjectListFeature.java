package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import jakarta.annotation.Nullable;

import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.HasObjectsRelation;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * Реализация отдельной фичи {@link ContentCreator} и презентера редактирования контента {@link Presenter}
 *
 * Экземпляр данного класса может добавить что-нибудь уникальное на форму и позже сделать необходимые изменения в
 * настройках контента
 *
 * <AUTHOR>
 *
 */
public interface IObjectListFeature
{
    /**
     * Добавление каких-либо элементов управления на форму создания контента
     * @param container
     */
    void addControlsToAddForm(MetaClass objectClass, FormPropertiesCreator container);

    /**
     * Добавление каких-либо элементов управления на форму редактировани контента
     * @param container
     */
    void addControlsToEditForm(MetaClass objectClass, PropertyDialogDisplay container);

    /**
     * Обновление настроек контента
     * @param content для обновения параметров
     * @return контент с обновленными значениями
     */
    HasObjectsRelation updateContent(HasObjectsRelation content);

    /**
     * Обновление элентов управления на форме создания/редактирования
     */
    void updateControls(MetaClass objectClass, @Nullable RelationsAttrTreeObject attrObject,
            @Nullable HasObjectsRelation content);
}
