package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.relobjectlist;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentCreator;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;

/**
 * {@link ContentCreator} для {@link RelObjectList} списка связанных объектов
 *
 * <AUTHOR>
 * @since 17.05.2011
 *
 */
public class RelObjListContentCreator extends ObjectListBaseContentCreator<RelObjectList>
{

    @Named(PropertiesGinModule.TEXT_BOX)
    @Inject
    private Property<String> formCaption;
    @Inject
    private ShowLinkedItemsFeature showRelatedItemsFeature;
    @Inject
    private ShowLinkedWithNestedItemsFeature showLinkedWithNestedItemsFeature;

    private RelationsAttrTreeObject selectedAttribute;

    @Override
    public void afterAddProperties(final PropertyDialogDisplay display)
    {
        super.afterAddProperties(display);
        // TODO нужно избавиться от захардкоженных индексов
        showLinkedWithNestedItemsFeature.init(6, context, validation, new ValueChangeHandler<RelationsAttrTreeObject>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<RelationsAttrTreeObject> event)
            {
                selectedAttribute = event.getValue();
                showClasses();
                updatePresentation();
                updateElementVisibility();
                updateShowLinkedLabel();
            }
        });
        showLinkedWithNestedItemsFeature.addControlsToEditForm(context.getMetainfo(), display);
        showLinkedWithNestedItemsFeature.getShowRelatedWithNestedProperty()
                .addValueChangeHandler(new ValueChangeHandler<Boolean>()
                {
                    @Override
                    public void onValueChange(ValueChangeEvent<Boolean> event)
                    {
                        showRelatedItemsFeature.getShowLinkedObjectsProperty().getValueWidget().asWidget()
                                .setVisible(!event.getValue());
                        if (!event.getValue())
                        {
                            showRelatedItemsFeature.updateShowLinkedLabel(context.getMetainfo(), selectedAttribute);
                        }
                    }
                });
        showRelatedItemsFeature.getShowLinkedObjectsProperty().addValueChangeHandler(new ValueChangeHandler<Boolean>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event)
            {
                showLinkedWithNestedItemsFeature.getShowRelatedWithNestedProperty().getValueWidget().asWidget()
                        .setVisible(!event.getValue());
            }
        });
    }

    @Override
    public void removeProperties()
    {
        super.removeProperties();
        showLinkedWithNestedItemsFeature.removeProperties();
    }

    @Override
    protected void bindPropertiesInner()
    {
        super.bindPropertiesInner();
        showRelatedItemsFeature.addControlsToAddForm(context.getMetainfo(), this);
        if (UI.WINDOW_KEY.equals(context.getRootContentInfo().getFormId()))
        {
            formCaption.setMaxLength(Constants.MAX_TITLE_LENGTH);
            addWithMarker(cmessages.addingLinkFormCaption(), formCaption);
            addValidation(formCaption, notEmptyValidator);
        }
        classList.setDisable();
        updateElementVisibility();
    }

    @Override
    protected RelObjectList getContentInner()
    {
        RelObjectList content = super.getContentInner();
        content.getAttributesChain().clear();
        metainfoUtils.setLocalizedValue(content.getFormCaption(), formCaption.getValue());
        showLinkedWithNestedItemsFeature.updateContent(content);
        showRelatedItemsFeature.updateContent(content);
        return content;
    }

    @Override
    protected String getDefaultCaption()
    {
        return messages.relatedObjects();
    }

    /**
     * Отображает атрибуты типа "Набор ссылок на БО" или "Обратная ссылка",
     * по которому будет формироваться список связанных объектов
     */
    protected void showAttributes()
    {
        showClasses();
        updateShowLinkedLabel();
    }

    @Override
    protected void showClasses()
    {
        classList.<SingleSelectCellList<?>> getValueWidget().clear();
        if (selectedAttribute == null)
        {
            formCaption.setValue("", true);
            classList.setValue(null);
            return;
        }
        Attribute attribute = selectedAttribute.getAttribute();
        if (attribute == null)
        {
            formCaption.setValue("", true);
            classList.setValue(null);
            return;
        }
        final ClassFqn relatedFqn = attribute.getType().<ObjectAttributeType> cast().getRelatedMetaClass();
        metainfoService.getMetaClass(relatedFqn.fqnOfClass(), new BasicCallback<MetaClass>()
        {
            @Override
            protected void handleSuccess(MetaClass relatedClass)
            {
                formCaption.setValue(cmessages.defaultAddingLinkFormCaption(relatedClass.getTitle()), true);
                showClasses(relatedClass, relatedFqn);
            }
        });
    }

    protected void showClasses(MetaClass relatedClass, ClassFqn relatedFqn)
    {
        classList.<SingleSelectCellList<?>> getValueWidget().addItem(relatedClass.getTitle(), relatedFqn.toString());
        classList.trySetObjValue(relatedFqn.toString());
        showCases(relatedFqn);
    }

    @Override
    protected void showDependentListsBlock()
    {
        showAttributes();
    }

    protected void updateElementVisibility()
    {
        Attribute attribute = null == selectedAttribute ? null : selectedAttribute.getAttribute();
        boolean visible = null != attribute && !attribute.isComputable();
        classList.setEnabled(visible);
        formCaption.setEnabled(visible);
    }

    protected void updatePresentation()
    {
        contentUtils.updateObjectListPresentation(selectedAttribute, getPropertyRegistration(presentationList),
                formCaption);
        Property<SelectItem> property = getPropertyRegistration(presentationList).getProperty();
        if (property.getValue() != null
            && PresentationType.DEFAULT.getCode().equals(SelectListPropertyValueExtractor.getValue(property)))
        {
            remove(pagingPosition);
        }
    }

    protected void updateShowLinkedLabel()
    {
        if (selectedAttribute == null)
        {
            return;
        }
        showRelatedItemsFeature.updateControls(context.getMetainfo(), selectedAttribute, null);
    }
}