package ru.naumen.metainfoadmin.client.dynadmin.content.toolbar;

import static ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES;

import jakarta.inject.Inject;
import jakarta.inject.Provider;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.client.content.ContentPresenter;
import ru.naumen.core.client.content.Context;
import ru.naumen.core.client.content.toolbar.ActionToolFactory;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.dispatch2.ValidateAddDeleteToolFormAction;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.adapters.AbstractHasComplexRelationAttrType;
import ru.naumen.metainfo.shared.ui.AddDeleteFromObjectListTool;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

/**
 * Фабрика представлений кнопки массового добавления/удаления связей.
 * <AUTHOR>
 * @since 22.06.2018
 * @param <C> тип контекста
 */
public class AddDeleteFromObjectListToolFactory<C extends Context>
        extends ActionToolFactory<AddDeleteFromObjectListTool, C>
{
    @Inject
    private Provider<AdminToolBarHelper> toolBarHelper;
    @Inject
    private DispatchAsync dispatch;

    @Override
    public ContentPresenter<AddDeleteFromObjectListTool, C> create(Content content, Context context)
    {
        AddDeleteFromObjectListTool actionTool = (AddDeleteFromObjectListTool)content;
        RelObjectList list = (RelObjectList)actionTool.getAssociatedContent();
        if (PresentationType.ADVLIST.getCode().equals(list.getPresentation()) && actionTool.getIconCode() == null)
        {
            actionTool.setIconCode("advlist_link_button");
        }
        final ContentPresenter<AddDeleteFromObjectListTool, C> presenter = create(content);
        presenter.init(actionTool, (C)context);
        presenter.bind();
        if (context instanceof ObjectListUIContext && ((ObjectListUIContext)context).getTemplateCode() == null)
        {
            MetaClass metaClass = context.getMetainfo();
            Attribute linkAttr = metaClass.getAttribute(list.getAttributesChain().get(0).getAttrCode());
            AttributeType attrType = linkAttr.getType();
            if (attrType.getPermittedTypes().contains(Constants.NOONE))
            {
                presenter.getDisplay().asWidget().setVisible(false);
            }
            if (list.getAttributesChain().size() > 1
                || list.getAttrLinkCode() != null
                   && list.getAttrLinkCode().equals(ru.naumen.metainfo.shared.Constants.CURRENT_OBJECT)
                || Presentations.QUICK_SELECTION_FIELD.equals(linkAttr.getEditPresentation().getCode()))
            {
                presenter.getDisplay().asWidget().setVisible(false);
            }
            else if (LINK_ATTRIBUTE_TYPES.contains(attrType.getCode()))
            {
                AbstractHasComplexRelationAttrType type = linkAttr.getType()
                        .<AbstractHasComplexRelationAttrType> cast();
                if (!type.isComplexRelation())
                {
                    toolBarHelper.get().disableTool(presenter);
                }
            }
            RelObjectList clonedList = (RelObjectList)list.clone();
            clonedList.setToolPanel(new ToolPanel());
            dispatch.execute(new ValidateAddDeleteToolFormAction(metaClass.getFqn(), null, clonedList),
                    new SimpleResultCallbackDecorator<>(new BasicCallback<Boolean>()
                    {
                        @Override
                        protected void handleSuccess(Boolean value)
                        {
                            if (Boolean.FALSE.equals(value))
                            {
                                content.setVisible(false);
                                presenter.getDisplay().asWidget().setVisible(false);
                            }
                        }
                    }));
        }
        return presenter;
    }
}
