package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.toolbar;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.SwitchMassEmbeddedApplicationAction;
import ru.naumen.metainfo.shared.embeddedapplication.Constants.Application;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionHandler;
import ru.naumen.objectlist.client.mode.active.extended.advlist.UpdateAdvlistSelectionEvent;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
public class DisableMassEmbeddedApplicationActionHandler extends
        AdminActionHandler<DisableMassEmbeddedApplicationAction>
{
    @Inject
    protected DispatchAsync dispatch;

    @Inject
    public DisableMassEmbeddedApplicationActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<DisableMassEmbeddedApplicationAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    public void doExecute()
    {
        super.doExecute();
        final ObjectListUIContext parentContext = context.getParentContext();

        List<String> embeddedApplicationCodes = parentContext.getObjects()
                .stream()
                .map(EmbeddedApplication.APPLICATION_EXTRACTOR)
                .filter(EmbeddedApplicationAdminSettingsDto::isOn)
                .map(EmbeddedApplicationAdminSettingsDto::getCode)
                .collect(Collectors.toList()); // NOSONAR в GWT нет метода toList()
        SwitchMassEmbeddedApplicationAction action = new SwitchMassEmbeddedApplicationAction(
                embeddedApplicationCodes, false);

        dispatch.execute(action, new BasicCallback<EmptyResult>()
        {
            @Override
            protected void handleFailure(Throwable t)
            {
                Objects.requireNonNull(parentContext.getErrorAndAttentionMsgHandler())
                        .addErrorMessage(SafeHtmlUtils.fromString(t.getMessage()));
                parentContext.getParentContext().getEventBus()
                        .fireEvent(new RefreshContentEvent(context.getParentContent()));
                parentContext.getParentContext().getEventBus().fireEvent(new UpdateAdvlistSelectionEvent());
            }

            @Override
            protected void handleSuccess(EmptyResult value)
            {
                parentContext.getParentContext().getEventBus()
                        .fireEvent(new RefreshContentEvent(context.getParentContent()));
                parentContext.getParentContext().getEventBus().fireEvent(new UpdateAdvlistSelectionEvent());
            }
        });
    }

    @Override
    public boolean isEnabled()
    {
        ObjectListUIContext parentContext = context.getParentContext();
        for (DtObject application : parentContext.getObjects())
        {
            if (application.getProperty(Application.ON, false))
            {
                return true;
            }
        }
        return false;
    }
}
