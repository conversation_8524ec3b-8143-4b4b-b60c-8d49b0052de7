package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import com.google.gwt.i18n.client.Messages;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 19 февр. 2016 г.
 *
 */
@Singleton
public interface AdvlistDefaultPrsMessages extends Messages
{
    @Description("Добавить атрибут")
    String addAttrButton();

    @Description("Добавление атрибута")
    String addAttrFormCaption();

    @Description("Код атрибута")
    String attrCodeColumn();

    @Description("Представление по умолчанию для атрибута в контенте")
    String attrPrsColumn();

    @Description("Атрибут")
    String attrTitleColumn();

    @Description("Авторасчет ширины")
    String autoWidth();

    @Description("Подтверждение сброса фильтрации")
    String confirmFilterReset();

    @Description("Вы действительно хотите сбросить фильтрацию по умолчанию?")
    String confirmFilterResetQuestion();

    @Description("Подтверждение сброса сортировки")
    String confirmSortReset();

    @Description("Вы действительно хотите сбросить сортировку по умолчанию?")
    String confirmSortResetQuestion();

    @Description("Внешний вид по умолчанию")
    String defaultAppearanceBlockTitle();

    @Description("Атрибуты, выводимые в контент по умолчанию")
    String defaultAttrs();

    @Description("Параметры контента по умолчанию")
    String defaultContentParams();

    @Description("Фильтрация по умолчанию")
    String defaultFilter();

    @Description("Сортировка по умолчанию")
    String defaultSort();

    @Description("Редактирование атрибута")
    String editAttrFormCaption();

    @Description("Настройка фильтрации списка")
    String editFiltration();

    @Description("Настройка вида по умолчанию для контента {0}")
    String formCaption(String contentTypeTitle);

    @Description("Настроенный вид по умолчанию будет отображаться в режиме оператора, если вид не выбран (в поле "
                 + "выбора указано значение \"[Выберите вид]\")")
    String formMessage();

    @Description("Да")
    String headerAppearanceHide();

    @Description("Для всех уровней")
    String headerAppearanceHideAll();

    @Description("Только для вложенных уровней")
    String headerAppearanceHideNestedOnly();

    @Description("Нет")
    String headerAppearanceNotHide();

    @Description("Скрывать заголовок уровня")
    String headerAppearancePropertyTitle();

    @Description("Наследовать настройку из связанной с контентом группы атрибутов")
    String inherit();

    @Description("Должен быть выбран хотя бы один атрибут для отображения")
    String oneAttributeShouldBeSelectedAtLeast();

    @Description("Количество объектов на странице")
    String pageCount();

    @Description("Сообщение при сбросе настроек столбцов")
    String resetAttributes();

    @Description("Подтверждение сброса настроек")
    String resetDialogCaption();

    @Description("Сбросить настройки")
    String resetSettingsButton();

    @Description("Вы действительно хотите сбросить настройки по умолчанию для элемента структуры ''{0}''")
    String resetSettingsForItemDialog(String title);

    @Description("Показывать историю вложенных объектов")
    String showNestedEventList();

    @Description("Ширина колонок по умолчанию")
    String widthColumnsDefault();

    @Description("Настройка вида по умолчанию для элемента {0}")
    String formItemCaption(String title);

    @Description("Наследовать ширину колонок из родительского уровня")
    String inheritColumnWidthsFromParentLevel();
}