/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Singleton;

/**
 * <AUTHOR>
 * @since 30.11.2012
 *
 */
@Singleton
public class WindowAndCreateContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        boolean addForm = UI.Form.NEW.equals(context.getCode());
        boolean objectCard = UI.WINDOW_KEY.equals(context.getCode());
        callback.onSuccess(addForm || objectCard);
    }
}