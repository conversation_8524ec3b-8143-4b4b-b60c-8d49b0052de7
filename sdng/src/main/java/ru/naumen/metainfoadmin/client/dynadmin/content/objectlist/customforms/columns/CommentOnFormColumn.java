package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms.columns;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.columns.ClickableTextColumn;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.client.ui.CustomFormMessages;
import ru.naumen.metainfo.shared.ui.customform.Constants;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;
import ru.naumen.metainfo.shared.ui.customform.CustomFormWithCommentBase;

/**
 * Колонка отображения свойства "Комментарий на форме"
 * <AUTHOR>
 * @since 3 окт. 2017 г.
 */
public class CommentOnFormColumn extends ClickableTextColumn<DtObject>
{
    @Inject
    protected CustomFormMessages customFormMessages;

    @Override
    public String getValue(DtObject customForm)
    {
        CustomForm form = customForm
                .getProperty(ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.ORIGINAL_CUSTOM_FORM);

        String commentOption = null;

        if (form instanceof CustomFormWithCommentBase)
        {
            commentOption = ((CustomFormWithCommentBase)form).getCommentOnFormProperty();
        }

        if (commentOption != null)
        {
            switch (commentOption)
            {
                case Constants.CustomUserForm.NOT_FILL:
                    return customFormMessages.notFill();
                case Constants.CustomUserForm.FILL:
                    return customFormMessages.fill();
                case Constants.CustomUserForm.MUST_FILL:
                    return customFormMessages.mustFill();
                default:
                    return "";
            }
        }

        return "";
    }
}