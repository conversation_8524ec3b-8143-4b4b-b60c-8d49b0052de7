/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.FocusPanel;
import com.google.inject.ImplementedBy;

import ru.naumen.core.client.mvp.Display;

/**
 * <AUTHOR>
 * @since 18 мая 2015 г.
 *
 */
@ImplementedBy(ToolPanelDisplayEditImpl.class)
public interface ToolPanelDisplayEdit extends Display
{
    FocusPanel getBottomContainer();

    Anchor getRefreshIcon();

    FlowPanel getTopContainer();

    FocusPanel getTopContainerDND();

    void setEnabled(boolean enabled);

    Anchor getCopyFromParentIcon();

    Anchor getCopyFromTemplateIcon();
}