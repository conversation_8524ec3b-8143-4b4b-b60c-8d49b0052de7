package ru.naumen.metainfoadmin.client.dynadmin.content.forms;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.ui.SelectCase;
import ru.naumen.metainfoadmin.client.dynadmin.content.propertylist.EditPropertyListContentPresenterBase;

/**
 * {@link Presenter} для редактирования контента типа {@link SelectCase}
 *
 * <AUTHOR>
 * @since 01.03.19
 *
 */
public class EditSelectCaseContentPresenter extends EditPropertyListContentPresenterBase<SelectCase>
{
    @Inject
    public EditSelectCaseContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    protected void bindProfiles()
    {
        //Не показываем поле выбора профилей для контента "Выбор типа"
    }
}
