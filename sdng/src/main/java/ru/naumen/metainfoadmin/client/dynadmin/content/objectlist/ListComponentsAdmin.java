package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.UIContextDecorator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.AdvlistUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.extended.advlist.AdvListToolBarUIContext;
import ru.naumen.objectlist.client.ListComponents;

/**
 * <AUTHOR>
 * @since 3 февр. 2017 г.
 */
public class ListComponentsAdmin extends ListComponents
{
    @Override
    public void bind()
    {
        UIContext context = (UIContext)getParentContext();
        ObjectListUIContext listContext;

        if (context instanceof UIContextDecorator
            && ((UIContextDecorator)context).getAdaptee() instanceof AdvlistUIContext)
        {
            AdvlistUIContext advlistUIContext = (AdvlistUIContext)((UIContextDecorator)context).getAdaptee();
            listContext = new AdvlistUIContext(context, context.getRootContentInfo(), context.isEditable(), content,
                    localEventBus, advlistUIContext.getComponents());
        }
        else
        {
            listContext = new ObjectListUIContext(context, context.getRootContentInfo(), context.isEditable(), content,
                    localEventBus);
            if (context instanceof UIContextDecorator
                && ((UIContextDecorator)context).getAdaptee() instanceof ObjectListUIContext)
            {
                ObjectListUIContext objectListUIContext =
                        (ObjectListUIContext)((UIContextDecorator)context).getAdaptee();
                if (objectListUIContext.hasPreventOnClickTransition())
                {
                    listContext.setPreventOnClickTransition();
                }
                listContext.setTemplateCode(objectListUIContext.getTemplateCode());
                listContext.setFormCode(objectListUIContext.getFormCode());
            }
        }
        listContext.setMode(getMode());
        setContext(listContext);

        if (isAdvList())
        {
            setListToolBarContext(new AdvListToolBarUIContext(listContext));
        }
        super.bind();
    }
}
