package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.dom.client.Document;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.Window;

import ru.naumen.core.client.common.Dialogs;
import ru.naumen.core.client.forms.FormDisplay;
import ru.naumen.core.client.forms.FormPresenter;
import ru.naumen.core.client.widgets.Constants;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

/**
 * Базовое представление формы настройки вида по умолчанию.
 * <AUTHOR>
 * @since 14.01.2021
 */
public abstract class DefaultPrsFormPresenterBase<SP extends AdvListSortPresenter> extends FormPresenter
        implements RefreshDefaultPrsFormHandler
{
    @Inject
    protected AdvlistDefaultPrsMessages messages;
    @Inject
    protected AdvlistDefaultParamsPresenter defaultParamsPresenter;
    @Inject
    protected AdvListFiltrationPresenterDefaultPrs filtrationPresenter;
    @Inject
    protected AttributesContentPresenter attributesPresenter;
    @Inject
    private AdvlistDefaultPrsResources resources;
    @Inject
    private Dialogs dialogs;

    protected final SP sortPresenter;

    protected ObjectListUIContext parentContext;
    protected ObjectListBase content;
    private AdvlistSettingsDefault settings;

    public DefaultPrsFormPresenterBase(FormDisplay display, EventBus eventBus, SP sortPresenter)
    {
        super(display, eventBus);
        this.sortPresenter = sortPresenter;
    }

    protected AdvlistSettingsDefault getSettings()
    {
        return settings;
    }

    @Override
    public void onApply()
    {
        settings.getListFilter().setWithSemanticAllowed(Boolean.TRUE);
        if (settings.isInherit())
        {
            List<AdvlistColumn> customWidthColumns = settings.getColumnList()
                    .stream()
                    .filter(column -> column
                                              .getWidthColumn() != 0)
                    .collect(Collectors.toList());
            settings.setColumnList(Lists.newArrayList(customWidthColumns));
        }
        else if (settings.getColumnList().isEmpty())
        {
            dialogs.error(messages.oneAttributeShouldBeSelectedAtLeast());
        }
    }

    @Override
    public void onRefreshDefaultPrsForm(RefreshDefaultPrsFormEvent event)
    {
        refreshDisplay();
    }

    @Override
    public void refreshDisplay()
    {
        defaultParamsPresenter.refreshDisplay();
        filtrationPresenter.refreshDisplay();
        sortPresenter.refreshDisplay();
        attributesPresenter.refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        setApplyButtonCaption(commonMessages.save());
        resources.css().ensureInjected();
        registerHandler(eventBus.addHandler(RefreshDefaultPrsFormEvent.getType(), this));

        getDisplay().addContent(defaultParamsPresenter.getDisplay());
        defaultParamsPresenter.init(settings, parentContext);
        defaultParamsPresenter.bind();
        defaultParamsPresenter.refreshDisplay();
        defaultParamsPresenter.ensureDebugId("defaultParams");
        // TODO dzevako вынести наложение стиля на контент в родителя в NSDPRD-12486 Рефакторинг форм
        defaultParamsPresenter.getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
        registerChildPresenter(defaultParamsPresenter);

        getDisplay().addContent(filtrationPresenter.getDisplay());
        filtrationPresenter.init(settings.getListFilter(), parentContext);
        filtrationPresenter.bind();
        filtrationPresenter.refreshDisplay();
        filtrationPresenter.ensureDebugId("filter");
        // TODO dzevako вынести наложение стиля на контент в родителя в NSDPRD-12486 Рефакторинг форм
        filtrationPresenter.getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
        registerChildPresenter(filtrationPresenter);

        getDisplay().addContent(sortPresenter.getDisplay());
        initSortPresenter();
        sortPresenter.bind();
        sortPresenter.refreshDisplay();
        sortPresenter.ensureDebugId("sort");
        // TODO dzevako вынести наложение стиля на контент в родителя в NSDPRD-12486 Рефакторинг форм
        sortPresenter.getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
        registerChildPresenter(sortPresenter);

        bindAttributesPresenter(parentContext);

        getDisplay().getPanel()
                .setWidth(Math.max(Window.getClientWidth() - Constants.WINDOW_WIDTH_OFFSET, 100) + "px");
        // TODO dzevako придумать другое решение NSDPRD-12486 Рефакторинг форм
        getDisplay().getContainer().setWidth("100%");
        getDisplay().asWidget().addStyleName(resources.css().form());
        getDisplay().setFixed(true);
        getDisplay().display();

        registerHandler(attributesPresenter.getDisplay().getInherit().addClickHandler(event -> getDisplay().display()));

        Document.get().getBody().addClassName(resources.css().disableScrollBar());
    }

    protected void bindAttributesPresenter(ObjectListUIContext parentContext)
    {
        getDisplay().addContent(attributesPresenter.getDisplay());
        attributesPresenter.init(settings, parentContext);
        attributesPresenter.bind();
        attributesPresenter.refreshDisplay();
        attributesPresenter.ensureDebugId("attributes");
        // TODO dzevako вынести наложение стиля на контент в родителя в NSDPRD-12486 Рефакторинг форм
        attributesPresenter.getDisplay().asWidget().addStyleName(WidgetResources.INSTANCE.form().formBlock());
        registerChildPresenter(attributesPresenter);
    }

    protected void initSortPresenter()
    {
        sortPresenter.init(settings.getListSort(), parentContext);
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        Document.get().getBody().removeClassName(resources.css().disableScrollBar());
    }

    protected void setSettings(AdvlistSettingsDefault settings)
    {
        this.settings = settings;
    }
}
