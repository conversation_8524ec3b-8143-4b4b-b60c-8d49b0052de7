package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.gwt.dom.client.Style.Overflow;
import com.google.gwt.event.shared.EventBus;

import ru.naumen.core.client.CoreGinjector.FormDisplayFactory;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.AdvListFilterUtils;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.WasChangedFilterEvent;
import ru.naumen.objectlist.client.mode.active.extended.advlist.filter.AdvListFilterPresenterChangedEvent;

/**
 * <AUTHOR>
 * @since 03 марта 2016 г.
 *
 */
public class EditAdvListFilterFormPresenterDefaultPrs extends EditAdvListFilterFormPresenter
{
    @Inject
    private AdvListFilterUtils filterUtils;

    @Inject
    public EditAdvListFilterFormPresenterDefaultPrs(FormDisplayFactory formDisplayFactory,
            AdvlistDefaultPrsMessages messages, EventBus eventBus)
    {
        super(formDisplayFactory, messages.editFiltration(), eventBus);
    }

    @Override
    public void onApply()
    {
        eventBus.fireEvent(new WasChangedFilterEvent());
        if (!listFilterPresenter.validate())
        {
            return;
        }
        List<ListFilterAndElement> removedAndElements = new ArrayList<>();
        List<ListFilterOrElement<?>> removedOrElements = new ArrayList<>();
        filterUtils.minimizeListFilter(removedAndElements, removedOrElements, listFilterPresenter.getContent());
        content.setElements(Lists.newArrayList(listFilterPresenter.getContent().getElements()));
        eventBus.fireEvent(new RefreshDefaultPrsFormEvent());
        getDisplay().destroy();
        unbind();
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        listFilterPresenter.getDisplay().asWidget().getElement().getStyle().setOverflowY(Overflow.AUTO);
        registerHandler(eventBus.addHandler(AdvListFilterPresenterChangedEvent.getType(),
                event -> getDisplay().display()));
    }

    @Override
    protected String getAttentionMessage()
    {
        return tagsMessages.disabledInDefaultFiltering();
    }
}