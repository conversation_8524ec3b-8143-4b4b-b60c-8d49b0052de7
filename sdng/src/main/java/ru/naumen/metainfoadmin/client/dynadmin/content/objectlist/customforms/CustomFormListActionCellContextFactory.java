package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms;

import com.google.inject.Singleton;

import ru.naumen.core.client.adminpermission.AdminPermissionUtils;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfoadmin.shared.Constants.CustomFormCommandCode;
import ru.naumen.objectlist.client.extended.advlist.columns.AdvListActionCellContextFactory;
import ru.naumen.objectlist.client.extended.columns.action.ExtendedListActionCellContext;

/**
 * <AUTHOR>
 * @since 21.04.2016
 */
@Singleton
public class CustomFormListActionCellContextFactory extends AdvListActionCellContextFactory
{
    @Override
    public ExtendedListActionCellContext create(String code)
    {
        if (CustomFormCommandCode.DELETE_CUSTOM_FORM.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.DEL, code,
                    AdminPermissionUtils.createPermissionPredicate(PermissionType.DELETE));
        }
        else if (CustomFormCommandCode.EDIT_CUSTOM_FORM.equals(code))
        {
            return new ExtendedListActionCellContext(IconCodes.EDIT, code,
                    AdminPermissionUtils.createPermissionPredicate(PermissionType.EDIT));
        }

        return super.create(code);
    }
}
