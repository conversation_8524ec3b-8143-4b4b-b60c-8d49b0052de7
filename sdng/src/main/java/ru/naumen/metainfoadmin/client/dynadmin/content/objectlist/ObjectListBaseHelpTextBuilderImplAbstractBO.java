package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;

import jakarta.inject.Inject;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.utils.CommonUtils.TitleExtractor;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AttributeGroupInfo;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosAction;
import ru.naumen.metainfo.shared.dispatch2.GetAttributeGroupInfosResponse;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.filters.MetaClassFilters;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

/**
 * Класс, ответственный за построение текста с описанием контента для списков основных классов (у которых могут быть
 * типы)
 * <AUTHOR>
 * @since 14 марта 2016 г.
 *
 */
public class ObjectListBaseHelpTextBuilderImplAbstractBO<T extends ObjectListBase>
        extends ObjectListBaseHelpTextBuilderImpl<T>
{
    @Inject
    protected DispatchAsync dispatch;
    @Inject
    private ObjectListBaseAdminAllMessages objectListBaseAdminAllMessages;

    @Override
    public void buildHelpText()
    {
        ClassFqn clazz = content.getClazz();
        if (null != clazz)
        {
            // отображаем объекты всех Типов или всех типов-потомков типа clazz
            metainfoService.getMetaClass(clazz, new BasicCallback<MetaClass>()
            {
                @Override
                protected void handleSuccess(MetaClass value)
                {
                    AttributeGroup group = value.getAttributeGroup(content.getAttributeGroup());
                    String groupTitle = null == group ? messages.groupError() : group.getTitle();
                    processHelpMessage(value, messages.anyType(), groupTitle);
                }
            });
        }
        else
        {
            // отображаем объекты выбранных Типов
            final ClassFqn fqnOfClass = content.getFqnOfClass();
            List<ClassFqn> fqns = Lists.newArrayList(content.getCase());
            fqns.add(fqnOfClass);
            metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
            {
                @Override
                protected void handleSuccess(List<MetaClassLite> value)
                {
                    final MetaClassLite classMetaClass = CollectionUtils.find(value,
                            MetaClassFilters.equal(fqnOfClass));
                    value.remove(classMetaClass);
                    processHelpMessageForCases(classMetaClass, value);
                }
            });
        }
    }

    protected void processHelpMessage(MetaClassLite value, String types, String groupTitle)
    {
        // @formatter:off
        String helpText = value.isHasCases() ?
                messages.classAndTypes(value.getTitle(), types, groupTitle) :
                messages.classes(value.getTitle(), groupTitle);
        // @formatter:on

        String contentTypeTitle = objectListMessages.contentTypeTitle();
        if (context instanceof ObjectListUIContext && ((ObjectListUIContext)context).getTemplateCode() != null)
        {
            contentTypeTitle = objectListBaseAdminAllMessages.objectListTitle();
        }
        callback.onSuccess(buildHelpMessage(contentTypeTitle, helpText));
    }

    protected void processHelpMessageForCases(final MetaClassLite classMetaClass, Collection<MetaClassLite> cases)
    {
        final String types = StringUtilities.join(cases, TitleExtractor.INSTANCE);

        Collection<ClassFqn> fqns = content.getCase();
        ClassFqn clazz = content.getClazz();
        if (null == clazz && fqns.isEmpty())
        {
            throw new FxException("ObjectList has no class or cases");
        }
        fqns = fqns.isEmpty() ? ImmutableSet.of(clazz) : fqns;
        GetAttributeGroupInfosAction action = new GetAttributeGroupInfosAction(fqns);
        dispatch.execute(action, new BasicCallback<GetAttributeGroupInfosResponse>()
        {
            @Override
            protected void handleSuccess(GetAttributeGroupInfosResponse response)
            {
                for (Entry<ClassFqn, ? extends List<AttributeGroupInfo>> entry : response.getGroupInfos().entrySet())
                {
                    for (AttributeGroupInfo groupInfo : entry.getValue())
                    {
                        if (content.getAttributeGroup().equals(groupInfo.getCode()))
                        {
                            processHelpMessage(classMetaClass, types, groupInfo.getTitle());
                            return;
                        }
                    }
                }
            }
        });
    }
}
