package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms;

import jakarta.inject.Inject;

import ru.naumen.admin.client.AdminMessages;
import ru.naumen.core.client.widgets.properties.container.ObjectForm;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyParametersDescriptorFactoryImpl;
import ru.naumen.metainfo.shared.ui.Tool;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;

/**
 * Описание свойств настраиваемых кнопок.
 * <AUTHOR>
 * @since Feb 28, 2019
 */
public class ToolFormPropertyParametersDescriptorFactory extends
        PropertyParametersDescriptorFactoryImpl<Tool, ObjectForm>
{
    @Inject
    private EditableToolPanelMessages messages;
    @Inject
    private AdminMessages adminMessages;
    @Inject
    protected AdminDialogMessages adminDialogMessages;

    @Override
    protected void build()
    {
        int pos = 0;
        //@formatter:off
        registerOrModifyProperty(ToolFormPropertyCodes.ACTION_AREA,                               messages.contentArea(),             true,  "actionArea",                pos++, true,  true );
        registerOrModifyProperty(ToolFormPropertyCodes.TITLE,                                     cmessages.title(),                  true,  "title",                     pos++, true,  true );
        registerOrModifyProperty(ToolFormPropertyCodes.PRESENTATION_TYPE,                         messages.presentation(),            false, "presentation",              pos++, true,  false);
        registerOrModifyProperty(ToolFormPropertyCodes.ICON,                                      messages.iconsForControls(),        true,  "icon",                      pos++, true,  true );
        registerOrModifyProperty(ToolFormPropertyCodes.APPLIED_TO_TYPE,                           messages.appliedTo(),               false, "appliedToType",             pos++, true,  false);
        registerOrModifyProperty(ToolFormPropertyCodes.SYSTEM_ACTION,                             adminMessages.action(),             false, "action",                    pos++, true,  false);
        registerOrModifyProperty(ToolFormPropertyCodes.ACTION,                                    adminMessages.action(),             false, "action",                    pos++, true,  false);

        registerOrModifyProperty(ToolFormPropertyCodes.OBJECT_ACTION,                             adminMessages.action(),             false, "objectAction",              pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.EDIT_FORM,                                 adminMessages.editForm(),           false, "editForm",                  pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.INVOCATION_METHOD,                         messages.actionInvocationMethod(),  false, "objectInvocationMethod",    pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ROW_ICON,                                  messages.iconsForControls(),        false, "icon",                      pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.RETURN_TO_LIST_AFTER_CREATION,             messages.returnToListAfterAdding(), false, "returnToListAfterCreation", pos++, false, false);

        registerOrModifyProperty(ToolFormPropertyCodes.USE_MASS_EDIT_FORM,                        messages.useMassEditForm(),         false, "useMassEditForm",           pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.MASS_EDIT_FORM,                            messages.massEditForm(),            false, "massEditForm",              pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.USE_QUICK_ADD_FORM,                        messages.useQuickAddForm(),         false, "useQuickAddForm",           pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.GO_TO_CARD_AFTER_CREATION,                 messages.goToCardAfterCreation(),   false, "goToCardAfterCreation",     pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.QUICK_ADD_FORM,                            messages.quickAddForm(),            false, "quickAddForm",              pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ATTRIBUTE_FOR_FILL_BY_CURRENT_OBJECT,      messages.attributeFillByCurrentObject(),   false, "attributeFillByCurrentObject",     pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.USE_QUICK_EDIT_FORM,                       messages.useQuickEditForm(),        false, "useQuickEditForm",          pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.QUICK_EDIT_FORM,                           messages.quickEditForm(),           false, "quickEditForm",             pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.BACKGROUND_ACTION_COLOR,                   cmessages.backgroundColor(),        false, "backgroundColor",           pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.IS_GEO_REQUIRED,                           messages.transferDeviceGeostation(),false, "isGeoRequired",             pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ATTRIBUTE_TO_SAVE_FILE,                    messages.attributeToSaveFile(),     false, "attributeToSaveFile",       pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ATTRIBUTE_GROUP_FOR_ADD_FILE,              messages.attributeGroupForAddFile(),false, "attributeGroupForAddFile",  pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.ALLOW_IN_MASS_OPERATIONS,                  messages.allowInMassOperations(),   false, "allowInMassOperations",     pos++, false, false);
        registerOrModifyProperty(ToolFormPropertyCodes.SETTINGS_SET,                              adminDialogMessages.settingsSet(),  false, "settingsSet",               pos, true, false);
        //@formatter:on
    }
}
