package ru.naumen.metainfoadmin.client.dynadmin.content.forms;

import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.base.Preconditions;
import com.google.inject.Provider;
import com.google.inject.Singleton;

import ru.naumen.core.client.mvp.Presenter;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.EditContentPresenter;

/**
 * Реализация {@link ContentEditorFactory}
 *
 * <AUTHOR>
 *
 */
@Singleton
public class ContentEditorFactoryImpl implements ContentEditorFactory
{
    @Inject
    @SuppressWarnings("rawtypes")
    private Map<Class, Provider<EditContentPresenter>> factories;

    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public <T extends FlowContent> Presenter create(T content, UIContext context)
    {
        Provider<EditContentPresenter> presenterProvider = factories.get(content.getClass());
        Preconditions.checkNotNull(presenterProvider, "Editing isn't possible for %s", content.getClass());
        EditContentPresenter presenter = presenterProvider.get();
        presenter.init(content, context);
        return presenter;
    }
}
