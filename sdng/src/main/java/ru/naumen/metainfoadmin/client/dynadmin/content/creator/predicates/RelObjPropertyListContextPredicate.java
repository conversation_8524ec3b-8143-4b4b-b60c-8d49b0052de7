package ru.naumen.metainfoadmin.client.dynadmin.content.creator.predicates;

import jakarta.inject.Singleton;

import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 * Параметры связанного объекта доступны, если есть атрибут типа Объект 
 *
 * <AUTHOR>
 * @since 07.08.2012
 */
@Singleton
public class RelObjPropertyListContextPredicate implements UIContextPredicate
{
    @Override
    public void isApplicable(UIContext context, AsyncCallback<Boolean> callback)
    {
        for (Attribute attr : context.getMetainfo().getAttributes())
        {
            if (ObjectAttributeType.CODE.equalsIgnoreCase(attr.getType().getCode()))
            {
                callback.onSuccess(true);
                return;
            }
        }
        callback.onSuccess(false);
    }
}
