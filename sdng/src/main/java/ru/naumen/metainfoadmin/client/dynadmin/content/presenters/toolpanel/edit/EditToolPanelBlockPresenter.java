package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import static ru.naumen.metainfo.shared.Constants.UI.WINDOW_KEY;
import static ru.naumen.metainfo.shared.elements.AdminPermissionHolderUtils.hasPermission;

import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.RelObjPropertyList;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.metainfo.shared.ui.ToolPanel;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelUtils;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoAction;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoResponse;

/**
 *
 * <AUTHOR>
 * @since 14 авг. 2016 г.
 */
public class EditToolPanelBlockPresenter<C extends UIContext> extends BasicPresenter<EditToolPanelBlockDisplay> implements
        HasListTemplate
{
    @Inject
    private EditableToolPanelMessages messages;
    @Inject
    DispatchAsync dispatch;
    @Inject
    private ToolPanelContentPresenterEdit<C> toolPanelPresenter;
    @Inject
    private EditableToolPanelUtils toolPanelUtils;

    private ToolPanelKind toolPanelKind;
    protected ToolPanel toolPanel;
    private C context;

    private String templateCode;
    private ListTemplateUsagePoint usagePoint;

    // Здесь хранятся настройки тулпанели, сделанные на текущей форме до того, как был отмечен чекбокс использования
    // стандартных параметров
    // При отключении галки использования стандартных параметров, возвращаются сохраненные здесь настройки тулпанели
    private ToolPanel editedToolPanel;
    // Настройки тулпанели стандартных параметров, используются при установке чекбокса "Использовать системную логику
    // формирования панели действий"
    private ToolPanel hardcodedToolPanel;

    @Inject
    public EditToolPanelBlockPresenter(EditToolPanelBlockDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void addListTemplateAndUsagePoint(String templateCode, ListTemplateUsagePoint usagePoint)
    {
        this.templateCode = templateCode;
        this.usagePoint = usagePoint;
    }

    @Override
    public String getTemplateCode()
    {
        return templateCode;
    }

    public ToolPanel getToolPanel()
    {
        ToolPanel toolPanel = toolPanelPresenter.getToolPanel();
        toolPanel.setUseSystemSettings(Boolean.TRUE.equals(getUseSystemSettings().getValue()));
        toolPanel.setPanelDisabled(Boolean.TRUE.equals(getPanelDisabled().getValue()));
        return toolPanel;
    }

    public ToolPanelKind getToolPanelKind()
    {
        return this.toolPanelKind;
    }

    @Override
    public ListTemplateUsagePoint getUsagePoint()
    {
        return usagePoint;
    }

    public void init(ToolPanel toolPanel, C context, ToolPanelKind toolPanelKind)
    {
        this.toolPanel = toolPanel;
        this.context = context;
        this.toolPanelKind = toolPanelKind;
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        toolPanelPresenter.setEnabled(!Boolean.TRUE.equals(getUseSystemSettings().getValue())
                                      && !Boolean.TRUE.equals(getPanelDisabled().getValue()));
        toolPanelPresenter.refreshDisplay();
    }

    protected BooleanCheckBoxProperty getUseSystemSettings()
    {
        return getDisplay().getUseSystemSettings();
    }

    protected BooleanCheckBoxProperty getPanelDisabled()
    {
        return getDisplay().getPanelDisabled();
    }

    public void updateToolPanel(ToolPanel toolPanel)
    {
        if (toolPanel == null)
        {
            return;
        }
        getUseSystemSettings().setValue(toolPanel.isUseSystemSettings(), true);
        if (toolPanel.isUseSystemSettings())
        {
            toolPanelPresenter.switchSettings(hardcodedToolPanel, true);
        }
        else
        {
            toolPanelPresenter.switchSettings(toolPanel, false);
            toolPanelUtils.grantToolsPermissions(toolPanel.getTools(), toolPanel, context);
        }
        refreshDisplay();
    }

    @Override
    protected void onBind()
    {
        getDisplay().setCaption(getPanelTitle());

        final Content parentContent = toolPanel.getParent();
        ClassFqn fqn = context.getMetainfo().getFqn();
        if (parentContent instanceof RelObjPropertyList)
        {
            fqn = context.getParentContext().getMetainfo().getFqn();
        }

        String formId = context.getRootContentInfo().getFormId();
        GetEditToolPanelContextInfoAction action = new GetEditToolPanelContextInfoAction(fqn, formId,
                parentContent.getUuid(), toolPanelKind);
        action.setTemplateCode(context.getTemplateCode());
        dispatch.execute(action, new BasicCallback<GetEditToolPanelContextInfoResponse>()
        {
            @Override
            protected void handleSuccess(GetEditToolPanelContextInfoResponse response)
            {
                bindToolPanel(response);
            }
        });
    }

    protected void bindToolPanel(GetEditToolPanelContextInfoResponse response)
    {
        hardcodedToolPanel = response.getHardcodedToolPanel();
        processContextInfo(response);
    }

    @Override
    protected void onUnbind()
    {
        super.onUnbind();
        toolPanelPresenter.unbind();
    }

    private void bindToolPanelPresenter(GetEditToolPanelContextInfoResponse response)
    {
        toolPanelPresenter.init(this, toolPanel, context, response, toolPanelKind);
        getDisplay().addWidget(toolPanelPresenter.getDisplay());
        toolPanelPresenter.bind();

        boolean resetAllowed = isResetAllowed();
        getUseSystemSettings().setEnabled(resetAllowed);
        toolPanelPresenter.setResetAllowed(resetAllowed);
    }

    protected final boolean isResetAllowed()
    {
        return toolPanel.isUseSystemSettings() || toolPanel.getTools().stream()
                .allMatch(tool -> hasPermission(context, PermissionType.DELETE, tool));
    }

    private void bindUseSystemSettingsProperty()
    {
        getUseSystemSettings().setCaption(getSystemSettingsTitle());
        getUseSystemSettings().setValue(toolPanel.isUseSystemSettings());
        getUseSystemSettings().ensureDebugId(getSystemSettingsDebugId());
        getUseSystemSettings().addValueChangeHandler(new ValueChangeHandler<Boolean>()
        {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event)
            {
                if (Boolean.TRUE.equals(event.getValue()))
                {
                    editedToolPanel = toolPanelPresenter.getToolPanel();
                    toolPanelPresenter.switchSettings(hardcodedToolPanel, event.getValue());
                }
                else if (editedToolPanel != null)
                {
                    toolPanelPresenter.switchSettings(editedToolPanel, event.getValue());
                }
                refreshDisplay();
            }
        });
        if (Boolean.TRUE.equals(getPanelDisabled().getValue()))
        {
            getUseSystemSettings().setEnabled(false);
        }
    }

    private void bindPanelDisabled()
    {
        if (toolPanel.getParent() instanceof HierarchyGrid && ToolPanelKind.MASS_OPERATIONS.equals(toolPanelKind))
        {
            getPanelDisabled().setCaption(messages.notUseMassPanel());
            getPanelDisabled().setValue(toolPanel.isPanelDisabled());
            getPanelDisabled().setEnabled(isResetAllowed());
            getPanelDisabled().ensureDebugId(getPanelDisable());
            getPanelDisabled().addValueChangeHandler(event ->
            {
                editedToolPanel = toolPanelPresenter.getToolPanel();
                if (Boolean.TRUE.equals(event.getValue()))
                {
                    switchMassOperationSettings(hardcodedToolPanel, event.getValue());
                }
                else if (editedToolPanel != null)
                {
                    switchMassOperationSettings(editedToolPanel, event.getValue());
                }
                refreshDisplay();
            });
        }
        else
        {
            getPanelDisabled().setDisable();
        }
    }

    private void switchMassOperationSettings(ToolPanel toolPanel, boolean doNotUseMassOperationPanel)
    {
        if (doNotUseMassOperationPanel)
        {
            getUseSystemSettings().setEnabled(false);
            toolPanel.setPanelDisabled(true);
        }
        else
        {
            getUseSystemSettings().setEnabled(true);
            toolPanel.setPanelDisabled(false);
        }
    }

    private String getPanelTitle()
    {
        return ToolPanelKind.MASS_OPERATIONS == toolPanelKind ? messages.massOperationsPanel() : messages.actionPanel();
    }

    private String getSystemSettingsDebugId()
    {
        return ToolPanelKind.MASS_OPERATIONS == toolPanelKind ? "use-system-mass-panel" : "use-system-settings";
    }

    private String getPanelDisable()
    {
        return ToolPanelKind.MASS_OPERATIONS == toolPanelKind ? "mass-operation-panel-disable" : "";
    }

    private String getSystemSettingsTitle()
    {
        if (toolPanel.getParent() instanceof Tab)
        {
            return messages.useEmptySystemSettings();
        }
        if (ToolPanelKind.MASS_OPERATIONS == toolPanelKind && (toolPanel.getParent() instanceof RelObjectList
                                                               || toolPanel.getParent() instanceof ChildObjectList
                                                               || toolPanel.getParent() instanceof ObjectList))
        {
            return context.getRootContentInfo().getFormId().equals(WINDOW_KEY) ? messages.useMassSystemSettings()
                    : messages.notUseMassPanel();
        }
        else if (ToolPanelKind.MASS_OPERATIONS == toolPanelKind)
        {
            return messages.useMassSystemSettings();
        }
        return messages.useSystemSettings();
    }

    private void processContextInfo(GetEditToolPanelContextInfoResponse response)
    {
        bindPanelDisabled();
        bindUseSystemSettingsProperty();
        bindToolPanelPresenter(response);
    }

    protected ToolPanel getHardcodedToolPanel()
    {
        return hardcodedToolPanel;
    }
}
