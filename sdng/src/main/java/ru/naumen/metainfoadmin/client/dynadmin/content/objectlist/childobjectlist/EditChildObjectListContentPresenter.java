package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist;

import static ru.naumen.metainfo.shared.filters.RelationFilters.isParent;
import static ru.naumen.metainfo.shared.filters.RelationFilters.isSelf;

import java.util.ArrayList;
import java.util.List;

import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.EditObjectListBaseContentPresenter;

/**
 * Презентер формы редактирования контента "Список вложенных объектов"
 *
 * <AUTHOR>
 * @since 08.10.2010
 *
 */
public class EditChildObjectListContentPresenter extends EditObjectListBaseContentPresenter<ChildObjectList>
{
    @Inject
    private BooleanCheckBoxProperty showNestedInNested;
    @Inject
    private ContentCreatorMessages contentCreatorMessages;

    @Inject
    public EditChildObjectListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void updateCurrentContent()
    {
        super.updateCurrentContent();
        if (context.getMetainfo().getOutgoingRelation().stream().anyMatch(Predicates.and(isSelf(), isParent()))
            || Root.FQN.equals(context.getMetainfo().getFqn()))
        {
            content.setShowNestedInNested(showNestedInNested.getValue());
        }
    }

    @Override
    protected void restoreContent(ChildObjectList oldContent)
    {
        super.restoreContent(oldContent);
        content.setShowNestedInNested(oldContent.isShowNestedInNested());
    }

    @Override
    protected boolean isContentEquals(ChildObjectList oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.isShowNestedInNested(), content.isShowNestedInNested());
    }

    @Override
    protected void bindPropertiesInner()
    {
        if (context.getMetainfo().getOutgoingRelation().stream().anyMatch(Predicates.and(isSelf(),
                isParent())) || Root.FQN.equals(context.getMetainfo().getFqn()))
        {
            showNestedInNested.setValue(content.isShowNestedInNested());
            showNestedInNested.setCaption(contentCreatorMessages.showNestedInNested());
            DebugIdBuilder.ensureDebugId(showNestedInNested, "showNestedInNested");
            getDisplay().add(showNestedInNested);
        }
        super.bindPropertiesInner();
    }

    @Override
    protected void showClasses()
    {
        List<ClassFqn> fqns = new ArrayList<ClassFqn>();
        context.getMetainfo().getIncomingRelations()
                .stream()
                .filter(isParent())
                .forEach(rel -> fqns.add(rel.getLeft()));
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> classesList)
            {
                List<MetaClassLite> classes = Lists.newArrayList(classesList);
                classes.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                for (MetaClassLite clz : metainfoUtils.getPossible(classes, true))
                {
                    String classId = clz.getFqn().getId();
                    classList.<SingleSelectCellList<?>> getValueWidget()
                            .addItem(clz.getTitle(), classId);
                }
                String classId = content.getFqnOfClass().getId();
                classList.trySetObjValue(classId);
                showClassCases(classId, content.getCase());
                showAttributeGroups(classId, content.getAttributeGroup());
            }
        });
    }
}
