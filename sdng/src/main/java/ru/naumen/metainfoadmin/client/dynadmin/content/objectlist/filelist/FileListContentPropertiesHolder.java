package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filelist;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.shared.HandlerRegistration;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.forms.TabOrderHelper;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.MultiSelectCellList;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.tree.PopupValueCellTree;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasReadyState;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.relationattrtree.RelationsAttrTreeObject;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfo.shared.ui.AddFileTool;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.AbstractFileAndCommentListPropertiesHolder;

/**
 * Представление параметров настройки контента "Список файлов"
 * на форме добавления и редактирования контента.
 * <AUTHOR>
 * @since 28.03.2019
 */
public class FileListContentPropertiesHolder extends AbstractFileAndCommentListPropertiesHolder
{
    @Inject
    @Named(PropertiesGinModule.LIST_BOX)
    private SelectListProperty<String, SelectItem> relationType;
    @Inject
    @Named(PropertiesGinModule.MULTI_SELECT_BOX)
    private SelectListProperty<Collection<String>, Collection<SelectItem>> fileAttributeCodes;

    private PropertyRegistration<Collection<SelectItem>> fileAttributeCodesPR;
    private boolean isSetRelationTypeInProgress = false;

    public SelectListProperty<Collection<String>, Collection<SelectItem>> getAttributeCodes()
    {
        return fileAttributeCodes;
    }

    public Property<SelectItem> getRelationType()
    {
        return relationType;
    }

    @Override
    public void init()
    {
        super.init();

        systemClassRelationAttribute.setCaption(messages.relationAttributeInFileClass());

        fileAttributeCodes.setCaption(messages.fromAttributes());
        fileAttributeCodes.setValidationMarker(true);
        fileAttributeCodes.ensureDebugId("fileAttributeCodes");

        relationType.setCaption(messages.relationType());
        relationType.ensureDebugId("relationType");
    }

    public void onRelationTypeChange(String relationTypeCode, final PropertyDialogDisplay display,
            final Processor validator, final PropertyRegistration<SelectItem> reationTypePR, UIContext uiContext)
    {
        removePropertiesInner(validator);
        if (RelationType.OBJECT_FILES.equals(relationTypeCode))
        {
            fillFileAttrs(Lists.newArrayList(uiContext.getMetainfo()));
            validator.validate(fileAttributeCodes, notEmptyCollectionValidator);
            fileAttributeCodesPR = display.addPropertyAfter(fileAttributeCodes, reationTypePR);
        }
        else if (RelationType.RELATED_OBJECT_FILES.equals(relationTypeCode))
        {
            fillLinkAttrs(uiContext);
            validator.validate(linkRelationAttribute, notEmptySelectItemValidator);
            linkRelationAttributePR = display.addPropertyAfter(linkRelationAttribute, reationTypePR);
            linkRelationAttributeHr = linkRelationAttribute
                    .addValueChangeHandler((evt) -> onSelectedAttrChangedInList(display, uiContext,
                            evt.getValue() == null ? null : evt.getValue().getUUID()));
            linkRelationAttributePR.setEnabled(true);
            if (!this.isSetRelationTypeInProgress)
            {
                ValueChangeEvent.fire(linkRelationAttribute, linkRelationAttribute.getValue());
            }

            validator.validate(fileAttributeCodes, notEmptyCollectionValidator);
            fileAttributeCodesPR = display.addPropertyAfter(fileAttributeCodes, linkRelationAttributePR);
        }
        else if (RelationType.RELATED_OBJECT_SET_FILES.equals(relationTypeCode))
        {
            createAttributeSelectTree(uiContext, true);
            attributeSelectHr = attributeSelect.<PopupValueCellTree<?, RelationsAttrTreeObject, ?>> getValueWidget()
                    .addValueChangeHandler((evt) -> onSelectedAttrChangedInTree(evt.getValue(), display, false));
            attributeSelect.setValidationMarker(true);
            attributeSelectPR = display.addPropertyAfter(attributeSelect, reationTypePR);
            attributeSelectVU = validator.validate(attributeSelect, notNullValidator);
            fileAttributeCodes.setValue(null, false);
            validator.validate(fileAttributeCodes, notEmptyCollectionValidator);
            fileAttributeCodesPR = display.addPropertyAfter(fileAttributeCodes, attributeSelectPR);
        }
        else if (RelationType.LINKED_WITH_OBJECT_FILES.equals(relationTypeCode))
        {
            fillRelatedSystemClassAttrs(Sets.newHashSet(uiContext.getMetainfo().getFqn()));
            validator.validate(systemClassRelationAttribute, notEmptySelectItemValidator);
            systemClassRelationAttributePR = display.addPropertyAfter(systemClassRelationAttribute, reationTypePR);
        }
        else if (RelationType.LINKED_WITH_RELATED_OBJECT_FILES.equals(relationTypeCode))
        {
            createAttributeSelectTree(uiContext, false);
            attributeSelectHr = attributeSelect.<PopupValueCellTree<?, RelationsAttrTreeObject, ?>> getValueWidget()
                    .addValueChangeHandler((evt) -> onSelectedAttrChangedInTree(evt.getValue(), display, true));
            attributeSelect.setValidationMarker(true);
            attributeSelectPR = display.addPropertyAfter(attributeSelect, reationTypePR);
            attributeSelectVU = validator.validate(attributeSelect, notNullValidator);

            validator.validate(systemClassRelationAttribute, notEmptySelectItemValidator);
            systemClassRelationAttributePR = display.addPropertyAfter(systemClassRelationAttribute,
                    attributeSelectPR);
        }

        display.ready(new HasReadyState.ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                TabOrderHelper.updateTabOrder(display, false);
            }
        });
    }

    public List<HandlerRegistration> registerValueHandlers(final PropertyDialogDisplay display,
            final Processor validator, final PropertyRegistration<SelectItem> reationTypePR, UIContext uiContext)
    {
        List<HandlerRegistration> handlers = new ArrayList<>();
        handlers.add(relationType.addValueChangeHandler((event) ->
        {
            String newRelationType = SelectItemValueExtractor.extract(event.getValue());
            onRelationTypeChange(newRelationType, display, validator, reationTypePR, uiContext);
        }));

        return handlers;
    }

    @Override
    public void removePropertiesInner(Processor validator)
    {
        super.removePropertiesInner(validator);

        if (null != fileAttributeCodesPR)
        {
            validator.unvalidate(fileAttributeCodes);
            fileAttributeCodesPR.unregister();
            fileAttributeCodesPR = null;
        }
    }

    /**
     * Перенос значений из виджета в контент     
     */
    public void updateContent(FileList content, UIContext uiContext)
    {
        String relationTypeValue = SelectListPropertyValueExtractor.getValue(relationType);
        content.setRelationType(relationTypeValue);
        if (!RelationType.LINKED_WITH_OBJECT_FILES.equals(relationTypeValue)
            && !RelationType.LINKED_WITH_RELATED_OBJECT_FILES.equals(relationTypeValue))
        {
            content.setAttributeCodes(Lists.newArrayList(SelectListPropertyValueExtractor.<String> getCollectionValue(
                    fileAttributeCodes)));
        }
        else
        {
            content.setAttributeCodes(null);
        }

        content.getAttrChain().clear();
        if (RelationType.RELATED_OBJECT_FILES.equals(relationTypeValue))
        {
            String attrCode = SelectListPropertyValueExtractor.getValue(linkRelationAttribute);
            content.getAttrChain().add(new AttrReference(uiContext.getMetainfo().getFqn(), attrCode));
        }
        else if (RelationType.RELATED_OBJECT_SET_FILES.equals(relationTypeValue))
        {
            RelationsAttrTreeObject ato = attributeSelect.getValue();
            for (; ato != null; ato = ato.getParent())
            {
                Attribute attribute = ato.getAttribute();
                if (null != attribute)
                {
                    AttrReference ar = new AttrReference(attribute.getMetaClassLite().getFqn(), attribute.getCode());
                    content.getAttrChain().add(0, ar);
                }
            }
        }
        else if (RelationType.LINKED_WITH_OBJECT_FILES.equals(relationTypeValue))
        {
            String attrCode = SelectListPropertyValueExtractor.getValue(systemClassRelationAttribute);
            content.getAttrChain().add(new AttrReference(Constants.File.FQN, attrCode));
        }
        else if (RelationType.LINKED_WITH_RELATED_OBJECT_FILES.equals(relationTypeValue))
        {
            String fileAttrCode = SelectListPropertyValueExtractor.getValue(systemClassRelationAttribute);
            content.getAttrChain().add(new AttrReference(Constants.File.FQN, fileAttrCode));

            RelationsAttrTreeObject ato = attributeSelect.getValue();
            for (; ato != null; ato = ato.getParent())
            {
                Attribute attribute = ato.getAttribute();
                if (null != attribute)
                {
                    AttrReference ar = new AttrReference(attribute.getMetaClassLite().getFqn(), attribute.getCode());
                    content.getAttrChain().add(1, ar);
                }
            }
        }
    }

    /**
     * Перенос значений свойств из контента в виджеты     
     */
    public void updateValues(FileList content, PropertyDialogDisplay display, UIContext uiContext,
            final PropertyRegistration<SelectItem> reationTypePR, final Processor validator)
    {
        this.isSetRelationTypeInProgress = true;
        SelectItem relationValue = relationType.<SingleSelectCellList<String>> getValueWidget().getItem(content
                .getRelationType());
        relationType.setValue(relationValue, true);
        onRelationTypeChange(content.getRelationType(), display, validator, reationTypePR, uiContext);
        this.isSetRelationTypeInProgress = false;

        if (RelationType.RELATED_OBJECT_FILES.equals(content.getRelationType()))
        {
            final List<AttrReference> attrChain = content.getAttrChain();
            if (!attrChain.isEmpty())
            {
                linkRelationAttribute.trySetObjValue(attrChain.get(0).getAttrCode(), true);
            }
        }

        if (RelationType.RELATED_OBJECT_SET_FILES.equals(content.getRelationType()))
        {
            updateAttributeSelectProperty(content.getAttrChain(), display, uiContext);
        }

        if (RelationType.LINKED_WITH_OBJECT_FILES.equals(content.getRelationType()))
        {
            final List<AttrReference> attrChain = content.getAttrChain();
            if (!attrChain.isEmpty())
            {
                systemClassRelationAttribute.trySetObjValue(attrChain.get(0).getAttrCode(), true);
            }
        }

        if (RelationType.LINKED_WITH_RELATED_OBJECT_FILES.equals(content.getRelationType()))
        {
            updateAttributeSelectAndRelationAttributeProperty(display, content.getAttrChain(),
                    content.getAttrChain().subList(1, content.getAttrChain().size()));
        }

        display.ready(new HasReadyState.ReadyCallback(this)
        {
            @Override
            public void onReady()
            {
                fileAttributeCodes.trySetObjValue(content.getAttributeCodes());
                fileAttributeCodes
                        .addValueChangeHandler((evt) -> onFileAttributeChanged(display, content,
                                SelectItemValueExtractor.extract(evt.getValue())));

                if (systemClassRelationAttributePR != null)
                {
                    systemClassRelationAttributePR.setEnabled(true);
                }
                if (linkRelationAttributePR != null)
                {
                    linkRelationAttributePR.setEnabled(true);
                }
                if (fileAttributeCodesPR != null)
                {
                    fileAttributeCodesPR.setEnabled(true);
                }
            }
        });
    }

    private void fillFileAttrs(List<MetaClass> fileAttrsHolders)
    {
        // Атрибуты типа "Файл", из которых необходимо доставать файлы
        MultiSelectCellList<String> attributesValueWidget = fileAttributeCodes
                .<MultiSelectCellList<String>> getValueWidget();
        attributesValueWidget.clear();
        attributesValueWidget.refreshPopupCellList();

        attributesValueWidget.addItem(messages.ownObjectFiles(), FileList.OWN_OBJECT_FILES);

        Set<Attribute> allAttrs = new HashSet<>();
        fileAttrsHolders.forEach(mc -> allAttrs.addAll(
                mc.getAttributes().stream()
                        .filter(input -> !input.getType().isAttributeOfRelatedObject() && !input.isComputable()
                                         && FileAttributeType.CODE.equals(input.getType().getCode()))
                        .collect(Collectors.toList())));
        Map<String, Attribute> fileAttrs = new HashMap<>();
        allAttrs.forEach(attr -> fileAttrs.put(attr.getCode() + attr.getTitle(), attr));

        List<Attribute> fileAttrsAsList = Lists.newArrayList(fileAttrs.values());
        metainfoUtils.sort(fileAttrsAsList);
        fileAttrsAsList.forEach(attribute -> attributesValueWidget.addItem(messages.toAttribute(attribute.getTitle()),
                attribute.getCode()));

        fileAttributeCodes.trySetObjValue(Lists.newArrayList(FileList.OWN_OBJECT_FILES));
        ValueChangeEvent.fire(fileAttributeCodes, fileAttributeCodes.getValue());
    }

    /**
     * Проверяем не настроены ли кнопки "Добавить файл" на удаленные атрибуты
     */
    private void onFileAttributeChanged(final PropertyDialogDisplay display, @Nullable FileList fileList,
            Collection<String> attrCodes)
    {
        if (fileList == null)
        {
            // для формы добавления
            return;
        }

        List<String> deletedTools = fileList.getToolPanel().getTools().stream()
                .filter(t -> t instanceof AddFileTool && !attrCodes.contains(((AddFileTool)t).getAttributeToSaveFile()))
                .map(t -> metainfoUtils.getLocalizedValue(((ActionTool)t).getTitle())).collect(Collectors.toList());

        String message = deletedTools.isEmpty() ? StringUtilities.EMPTY
                : messages.deleteToolsAttention(StringUtilities.joinQuoted(deletedTools));
        display.addAttentionMessage(message);
    }

    /**
     * Обновляем список атрибутов типа Файл при изменении атрибута связи в списке
     */
    private void onSelectedAttrChangedInList(final PropertyDialogDisplay display, UIContext uiContext,
            @Nullable String attrCode)
    {
        if (attrCode == null)
        {
            fileAttributeCodes.setValue(null);
            return;
        }

        Set<ClassFqn> permTypes = uiContext.getMetainfo().getAttribute(attrCode).getType().getPermittedTypes();
        if (permTypes.stream().noneMatch(Constants.NOONE::equals))
        {
            metainfoService.getFullMetaInfo(permTypes, new BasicCallback<List<MetaClass>>(display)
            {
                @Override
                protected void handleSuccess(List<MetaClass> metaClasses)
                {
                    fillFileAttrs(metaClasses);
                }
            });
        }
        else
        {
            display.ready(new HasReadyState.ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    fillFileAttrs(new ArrayList<>());
                }
            });
        }
    }

    /**
     * Обновляем список атрибутов типа Файл при изменении атрибута связи в дереве
     */
    private void onSelectedAttrChangedInTree(@Nullable RelationsAttrTreeObject selectedAttribute,
            PropertyDialogDisplay display,
            boolean isForLinkedFiles)
    {
        if (selectedAttribute == null)
        {
            fileAttributeCodes.setValue(new ArrayList<>());
            systemClassRelationAttribute.<SingleSelectCellList<?>> getValueWidget().setObjValue(null);
            return;
        }
        Attribute attribute = selectedAttribute.getAttribute();
        if (attribute == null)
        {
            fileAttributeCodes.setValue(new ArrayList<>());
            systemClassRelationAttribute.<SingleSelectCellList<?>> getValueWidget().setObjValue(null);
            return;
        }

        Set<ClassFqn> permTypes = attribute.getType().getPermittedTypes();
        if (isForLinkedFiles)
        {
            fillRelatedSystemClassAttrs(permTypes);
        }
        else if (permTypes.stream().noneMatch(Constants.NOONE::equals))
        {
            metainfoService.getFullMetaInfo(permTypes, new BasicCallback<List<MetaClass>>(display)
            {
                @Override
                protected void handleSuccess(List<MetaClass> metaClasses)
                {
                    fillFileAttrs(metaClasses);
                }
            });
        }
        else
        {
            display.ready(new HasReadyState.ReadyCallback(this)
            {
                @Override
                public void onReady()
                {
                    fillFileAttrs(new ArrayList<>());
                }
            });
        }
    }
}
