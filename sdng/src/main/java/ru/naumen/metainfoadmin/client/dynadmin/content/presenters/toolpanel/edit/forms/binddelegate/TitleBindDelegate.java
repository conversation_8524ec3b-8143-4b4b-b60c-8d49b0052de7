package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.binddelegate;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.TextBoxProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateBind;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfo.shared.ui.ShowRemovedTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, производящий инициализацию свойства "Название" настраиваемой кнопки.
 * <AUTHOR>
 * @since Mar 02, 2019
 */
public class TitleBindDelegate implements PropertyDelegateBind<String, TextBoxProperty>
{
    private static final int TOOL_TITLE_MAX_LENGTH = 255;
    private static final int MOBILE_TOOL_TITLE_MAX_LENGTH = 127;

    @Override
    public void bindProperty(PropertyContainerContext context, TextBoxProperty property,
            AsyncCallback<Void> callback)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        int maxLength = ToolPanelKind.MOBILE_OBJECT_ACTIONS_BAR == ctx.getToolPanelKind() ||
                        ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR == ctx.getToolPanelKind()
                ? MOBILE_TOOL_TITLE_MAX_LENGTH : TOOL_TITLE_MAX_LENGTH;
        property.setMaxLength(maxLength);

        if (ctx.getTool() instanceof ShowRemovedTool)
        {
            context.setDisabled(ToolFormPropertyCodes.TITLE);
        }
        callback.onSuccess(null);
    }
}
