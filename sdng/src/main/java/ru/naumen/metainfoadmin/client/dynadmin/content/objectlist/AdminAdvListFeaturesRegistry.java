package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist;

import jakarta.inject.Singleton;

import com.google.common.collect.ImmutableList;

import ru.naumen.metainfo.shared.ui.ApplicationList;
import ru.naumen.metainfo.shared.ui.CustomFormList;
import ru.naumen.metainfo.shared.ui.EventActionList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.objectlist.client.ListComponents;
import ru.naumen.objectlist.client.extended.advlist.FeatureCodes;
import ru.naumen.objectlist.client.gin.runtime.AdvListFeaturesRegistryImpl;

/**
 * <AUTHOR>
 * @since 18 янв. 2017 г.
 */
@Singleton
public class AdminAdvListFeaturesRegistry extends AdvListFeaturesRegistryImpl
{
    @Override
    public ImmutableList<String> getFeatures(ListComponents components)
    {
        ObjectListBase content = components.getContent();
        if (content instanceof EventActionList || content instanceof CustomFormList
            || content instanceof ApplicationList)
        {
            return ImmutableList.of(FeatureCodes.PAGING, FeatureCodes.SORT, FeatureCodes.FILTER, FeatureCodes.SELECTION,
                    FeatureCodes.MASS_OPERATION, FeatureCodes.SETTINGS);
        }
        return super.getFeatures(components);
    }
}
