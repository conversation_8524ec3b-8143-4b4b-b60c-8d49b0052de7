package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.commentlist;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;

import jakarta.inject.Inject;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.CommentList.SubjectType;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfoadmin.client.dynadmin.content.forms.SimpleEditContentPresenter;

/**
 * Представление для формы редактирования контента "Комментарии к объекту".
 * <AUTHOR>
 * @since Oct 24, 2016
 */
public class EditCommentListContentPresenter extends SimpleEditContentPresenter<CommentList>
{
    protected PropertyRegistration<SelectItem> subjectTypePR;

    @Inject
    protected CommentListContentPropertiesHolder propertiesHolder;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;

    @Inject
    public EditCommentListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void bindPropertiesInner()
    {
        propertiesHolder.init();

        propertiesHolder.getSubjectType().setDisable();
        subjectTypePR = getDisplay().add(propertiesHolder.getSubjectType());
        for (HandlerRegistration hr : propertiesHolder.registerValueHandlers(getDisplay(), validation, subjectTypePR,
                context))
        {
            registerHandler(hr);
        }
        metainfoService.getMetaClass(Constants.Comment.FQN, new BasicCallback<MetaClass>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MetaClass commentClass)
            {
                propertiesHolder.initSystemClass(commentClass);
                SingleSelectCellList<?> subjectTypeWidget = propertiesHolder.getSubjectType().getValueWidget();
                subjectTypeWidget.clear();
                subjectTypeWidget.addItem(contentMessages.ofCurrentObject(), SubjectType.CURRENT_OBJECT);
                subjectTypeWidget.addItem(contentMessages.relatedObject(), SubjectType.RELATED_OBJECT);
                subjectTypeWidget.addItem(contentMessages.relatedObjectSet(), SubjectType.RELATED_OBJECT_SET_COMMENTS);
                subjectTypeWidget.addItem(contentMessages.linkedWithObject(), SubjectType.LINKED_WITH_OBJECT_COMMENTS);
                subjectTypeWidget.addItem(contentMessages.linkedWithRelatedObject(),
                        SubjectType.LINKED_WITH_RELATED_OBJECT_COMMENTS);
                propertiesHolder.updateValues(content, display, context, subjectTypePR, validation);
                bindRestrictContent(commentClass, subjectTypePR);
            }
        });
    }

    @Override
    protected void updateCurrentContent()
    {
        super.updateCurrentContent();
        propertiesHolder.updateContent(content);

        ListFilter restrictContentValue = restrictCondition.getValue();
        if (null != restrictContentValue)
        {
            content.getRestrictContentCondition().getElements().clear();
            restrictContentValue.setWithSemanticAllowed(Boolean.FALSE);
            content.getRestrictContentCondition().getElements().addAll(restrictContentValue.getElements());
        }
    }

    @Override
    protected void restoreContent(CommentList oldContent)
    {
        super.restoreContent(oldContent);
        content.setSubjectType(oldContent.getSubjectType());
        content.setDetailedAttrGroup(oldContent.getDetailedAttrGroup());
        content.setShowAttributeAttachedFiles(oldContent.getShowAttributeAttachedFiles());
        content.setForAddFormAttrGroup(oldContent.getForAddFormAttrGroup());
        content.setForEditFormAttrGroup(oldContent.getForEditFormAttrGroup());
        content.setRelationAttrCode(oldContent.getRelationAttrCode());
        content.getAttrChain().clear();
        content.getAttrChain().addAll(oldContent.getAttrChain());
        content.getRestrictContentCondition().getElements().clear();
        content.getRestrictContentCondition().getElements()
                .addAll(oldContent.getRestrictContentCondition().getElements());
    }

    @Override
    protected boolean isContentEquals(CommentList oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getSubjectType(), content.getSubjectType())
               && eq(oldContent.getDetailedAttrGroup(), content.getDetailedAttrGroup())
               && eq(oldContent.getRelationAttrCode(), content.getRelationAttrCode())
               && eq(oldContent.getAttrChain(), content.getAttrChain())
               && eq(oldContent.getRestrictContentCondition().getElements(),
                content.getRestrictContentCondition().getElements())
               && eq(oldContent.getForEditFormAttrGroup(), content.getForEditFormAttrGroup())
               && eq(oldContent.getForAddFormAttrGroup(), content.getForAddFormAttrGroup())
               && eq(oldContent.isShowAttrDescriptionOnAddForm(), content.isShowAttrDescriptionOnAddForm())
               && eq(oldContent.isShowAttrDescriptionOnEditForm(), content.isShowAttrDescriptionOnEditForm())
               && eq(oldContent.getShowAttributeAttachedFiles(), content.getShowAttributeAttachedFiles());
    }
}
