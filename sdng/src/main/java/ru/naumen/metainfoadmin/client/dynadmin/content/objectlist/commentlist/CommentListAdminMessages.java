/**
 *
 */
package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.commentlist;

import jakarta.inject.Inject;

import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminAllMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseAdminMessages;

/**
 * <AUTHOR>
 * @since 21.12.2012
 *
 */
public class CommentListAdminMessages implements ObjectListBaseAdminMessages<CommentList>
{
    @Inject
    ObjectListBaseAdminAllMessages messages;

    @Override
    public String contentTypeTitle()
    {
        return messages.commentListTitle();
    }
}