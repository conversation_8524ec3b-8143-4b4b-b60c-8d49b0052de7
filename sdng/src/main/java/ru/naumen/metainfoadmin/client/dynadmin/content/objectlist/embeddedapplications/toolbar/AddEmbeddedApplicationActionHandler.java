package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.toolbar;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
public class AddEmbeddedApplicationActionHandler extends ApplicationCommandExecuteActionHandlerBase<AddEmbeddedApplicationAction>
{
    @Inject
    public AddEmbeddedApplicationActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<AddEmbeddedApplicationAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    protected EmbeddedApplicationAdminSettingsDto getContextObject()
    {
        return null;
    }
}
