package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit;

import java.util.Collection;
import java.util.HashMap;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.structuredobjectsviews.dispatch.GetStructuredObjectsViewWithoutUsageAction;
import ru.naumen.metainfo.shared.ui.HierarchyGrid;
import ru.naumen.metainfo.shared.ui.HierarchyGridMassOperationSettings;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.structuredobjectsviews.card.StructuredObjectsViewItemsForMassOperationsPresenter;
import ru.naumen.metainfoadmin.shared.dynadmin.GetEditToolPanelContextInfoResponse;

/**
 * Презентер формы настройки массовых операций
 * <AUTHOR>
 * @since 14.12.2020
 */
public class MassEditToolPanelBlockPresenter<C extends UIContext> extends EditToolPanelBlockPresenter<C>
{
    public static final String USE_SYSTEM_SETTINGS = "useSystemSettings";
    public static final String PANEL_DISABLED = "panelDisabled";
    public static final String CAN_DISABLE_MASS_OPERATIONS = "canDisableMassOperations";

    private final StructuredObjectsViewItemsForMassOperationsPresenter itemsPresenter;

    @Inject
    public MassEditToolPanelBlockPresenter(EditToolPanelBlockDisplay display, EventBus eventBus,
            StructuredObjectsViewItemsForMassOperationsPresenter itemsPresenter)
    {
        super(display, eventBus);
        this.itemsPresenter = itemsPresenter;
    }

    @Override
    protected void bindToolPanel(GetEditToolPanelContextInfoResponse response)
    {
        super.bindToolPanel(response);
        prepareStructure();
        setEnabled();
    }

    private void prepareStructure()
    {
        if (isHierarchyGrid())
        {
            dispatch.execute(new GetStructuredObjectsViewWithoutUsageAction(
                            ((HierarchyGrid)toolPanel.getParent()).getStructuredObjectsViewCode()),
                    new BasicCallback<SimpleResult<Collection<DtObject>>>()
                    {
                        @Override
                        protected void handleSuccess(SimpleResult<Collection<DtObject>> value)
                        {
                            value.get().stream().findFirst().ifPresent(structure -> initStructure(structure)); // NOPMD
                        }
                    });
        }
    }

    private boolean isHierarchyGrid()
    {
        return toolPanel.getParent() instanceof HierarchyGrid;
    }

    private void initStructure(DtObject structure)
    {
        HashMap<String, Boolean> notUseMassOperationMap =
                ((HierarchyGrid)getHardcodedToolPanel().getParent()).getHierarchyGridMassOperationSetting()
                        .getNotUseMassOperationMap();
        itemsPresenter.init(structure, notUseMassOperationMap);
        setPanelDisabledToStructureDTO();
        getDisplay().addWidget(itemsPresenter.getDisplay());
        itemsPresenter.bind();
        setUseSystemSettingToStructureDTO();
        structure.setProperty(CAN_DISABLE_MASS_OPERATIONS, isResetAllowed());
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        if (isHierarchyGrid())
        {
            setEnabled();
            setUseSystemSettingToStructureDTO();
            setPanelDisabledToStructureDTO();
            itemsPresenter.refreshDisplay();
        }
    }

    private void setUseSystemSettingToStructureDTO()
    {
        itemsPresenter.getStructuredObjectsView()
                .setProperty(USE_SYSTEM_SETTINGS, isUseSystemSettings());
    }

    private void setPanelDisabledToStructureDTO()
    {
        itemsPresenter.getStructuredObjectsView()
                .setProperty(PANEL_DISABLED, isPanelDisabled());
    }

    private void setEnabled()
    {
        itemsPresenter.getDisplay().setEnabled(!isUseSystemSettings() && !isPanelDisabled());
    }

    private boolean isUseSystemSettings()
    {
        return Boolean.TRUE.equals(getUseSystemSettings().getValue());
    }

    private boolean isPanelDisabled()
    {
        return Boolean.TRUE.equals(getPanelDisabled().getValue());
    }

    public void prepareSaveHierarchyGrid()
    {
        HashMap<String, Boolean> notUseMassOperationMap = itemsPresenter.getNotUseMassOperationMap(
                isUseSystemSettings(), isPanelDisabled());
        HierarchyGridMassOperationSettings hierarchyGridMassOperationSettings =
                new HierarchyGridMassOperationSettings(notUseMassOperationMap);
        ((HierarchyGrid)toolPanel.getParent()).setHierarchyGridMassOperationSetting(hierarchyGridMassOperationSettings);
    }
}
