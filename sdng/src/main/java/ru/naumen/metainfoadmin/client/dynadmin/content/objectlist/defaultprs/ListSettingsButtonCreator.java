package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import jakarta.inject.Inject;

import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.content.toolbar.display.buttons.ButtonPresenter;
import ru.naumen.core.client.content.toolbar.display.factories.ButtonFactory;
import ru.naumen.core.client.widgets.WidgetResources;

/**
 * Создает кнопки, применяет соответсвующие стили
 * <AUTHOR>
 * @since 08.05.2018
 */
public class ListSettingsButtonCreator
{
    @Inject
    private ButtonFactory buttonFactory;

    public ButtonPresenter<?> createButton(String code, String title)
    {
        ButtonPresenter<?> button = buttonFactory.create(code, title);
        Widget asWidget = button.getDisplay().asWidget();
        asWidget.addStyleName(WidgetResources.INSTANCE.all().pullRight());
        return button;
    }
}
