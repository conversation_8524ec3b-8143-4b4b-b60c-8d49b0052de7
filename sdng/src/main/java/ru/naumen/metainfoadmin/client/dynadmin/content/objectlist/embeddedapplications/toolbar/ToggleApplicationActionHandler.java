package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.toolbar;

import com.google.inject.assistedinject.Assisted;

import jakarta.inject.Inject;
import ru.naumen.core.client.content.toolbar.ActionToolContext;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.embeddedapplication.Constants;
import ru.naumen.metainfoadmin.client.dynadmin.ListDtObjectContext;
import ru.naumen.metainfoadmin.client.toolbar.AdminActionExecutorProvider;

/**
 * <AUTHOR>
 * @since 07.07.2016
 */
public class ToggleApplicationActionHandler extends ApplicationCommandExecuteActionHandlerBase<ToggleEmbeddedApplicationAction>
{
    @Inject
    public ToggleApplicationActionHandler(@Assisted ActionToolContext context,
            AdminActionExecutorProvider<ToggleEmbeddedApplicationAction> executorProvider)
    {
        super(context, executorProvider);
    }

    @Override
    protected EmbeddedApplicationAdminSettingsDto getContextObject()
    {
        ListDtObjectContext objectContext = context.getParentContext();
        return objectContext.getObject()
                .<EmbeddedApplicationAdminSettingsDto> getProperty(Constants.Application.ORIGINAL_APPLICATION);
    }
}
