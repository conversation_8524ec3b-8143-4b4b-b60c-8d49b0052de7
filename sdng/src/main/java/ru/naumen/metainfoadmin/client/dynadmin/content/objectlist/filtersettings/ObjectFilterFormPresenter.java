package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filtersettings;

import jakarta.inject.Inject;

import com.google.gwt.event.shared.EventBus;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.CallbackDecorator;
import ru.naumen.core.client.CoreGinjector.FormDisplayFactory;
import ru.naumen.core.client.content.RefreshContentEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.client.MetainfoModificationServiceAsync;
import ru.naumen.metainfo.shared.templates.list.dispatch.EditListListTemplateAction;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs.EditAdvListFilterFormPresenter;
import ru.naumen.objectlist.client.ObjectListContext;
import ru.naumen.objectlist.client.mode.active.extended.advlist.features.filter.WasChangedFilterEvent;

/**
 * Представление формы настройки фильтрации для ограничения списка объектов.
 * <AUTHOR>
 * @since Aug 29, 2018
 */
public class ObjectFilterFormPresenter extends EditAdvListFilterFormPresenter
{
    @Inject
    private MetainfoModificationServiceAsync metainfoServiceAsync;
    @Inject
    private DispatchAsync dispatch;

    @Inject
    protected ObjectFilterFormPresenter(FormDisplayFactory formDisplayFactory, EventBus eventBus,
            FilterSettingsMessages messages)
    {
        super(formDisplayFactory, messages.editObjectFilter(), eventBus);
    }

    @Override
    public void onApply()
    {
        eventBus.fireEvent(new WasChangedFilterEvent());
        if (!listFilterPresenter.validate())
        {
            return;
        }
        ListFilter listFilter = listFilterPresenter.getContent();
        listFilter.setWithSemanticAllowed(Boolean.FALSE);
        listFilter.removeEmptyElements();
        ListFilter oldFilter = ObjectUtils.clone(context.getObjectList().getObjectFilter());
        context.getObjectList().setObjectFilter(listFilter);

        BasicCallback<Void> callback = new BasicCallback<Void>(getDisplay())
        {
            @Override
            protected void handleFailure(Throwable t)
            {
                context.getObjectList().setObjectFilter(oldFilter);
                super.handleFailure(t);
            }

            @Override
            protected void handleSuccess(Void value)
            {
                context.getEventBus().fireEvent(new RefreshContentEvent(context.getObjectList()));
                unbind();
            }
        };
        if (null != context.getTemplateCode())
        {
            dispatch.execute(new EditListListTemplateAction(context.getTemplateCode(), context.getObjectList()),
                    new CallbackDecorator<SimpleResult<DtObject>, Void>(callback)
                    {
                        @Override
                        protected Void apply(SimpleResult<DtObject> from)
                        {
                            return null;
                        }
                    });
        }
        else
        {
            metainfoServiceAsync.saveUI(context.getParentContext().getMetainfo().getFqn(),
                    context.<UIContext> getParentContext().getRootContent(), null,
                    context.getRootContentInfo().getFormId(), context.getObjectList(), false, callback);
        }
    }

    @Override
    protected ObjectListContext createFilterContext()
    {
        return new ObjectFilterListContextDecorator(context);
    }

    @Override
    protected String getAttentionMessage()
    {
        return tagsMessages.disabledInListContentRestriction();
    }
}
