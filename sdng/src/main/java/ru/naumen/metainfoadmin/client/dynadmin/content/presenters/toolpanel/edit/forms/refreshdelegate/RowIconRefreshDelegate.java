package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.refreshdelegate;

import com.google.gwt.user.client.rpc.AsyncCallback;

import ru.naumen.core.client.widgets.properties.DtObjectSelectProperty;
import ru.naumen.core.client.widgets.properties.container.PropertyContainerContext;
import ru.naumen.core.client.widgets.properties.container.elements.PropertyDelegateRefresh;
import ru.naumen.core.shared.Constants.ObjectActions;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.core.shared.ui.toolbar.ToolPanelKind;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms.ToolFormPropertyCodes;

/**
 * Компонент, обновляющий свойство "Иконка" настраиваемого пункта меню.
 * <AUTHOR>
 * @since Mar 01, 2019
 */
public class RowIconRefreshDelegate implements PropertyDelegateRefresh<SelectItem, DtObjectSelectProperty>
{
    @Override
    public void refreshProperty(PropertyContainerContext context, DtObjectSelectProperty property,
            AsyncCallback<Boolean> callback)
    {
        callback.onSuccess(isRowWithIcon(context));
    }

    private boolean isRowWithIcon(PropertyContainerContext context)
    {
        ToolFormContext ctx = context.getContextValues().getProperty(ToolFormContext.ID);
        return (ToolPanelKind.OBJECT_ACTIONS_BAR == ctx.getToolPanelKind()
                || ToolPanelKind.MOBILE_CONTENT_ACTIONS_BAR == ctx.getToolPanelKind())
               && ObjectActions.INVOKE_ICON_CLICK.equals(context.getPropertyValues().getProperty(
                ToolFormPropertyCodes.INVOCATION_METHOD));
    }
}
