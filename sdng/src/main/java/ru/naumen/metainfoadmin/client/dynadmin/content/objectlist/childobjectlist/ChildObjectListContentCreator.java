package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.childobjectlist;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import com.google.common.base.Predicates;
import com.google.common.collect.Lists;

import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.utils.CommonUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.Relation;
import ru.naumen.metainfo.shared.filters.RelationFilters;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseContentCreator;

/**
 * {@link ContentCreator} для {@link ChildObjectList списка объектов}
 *
 * <AUTHOR>
 *
 */
public class ChildObjectListContentCreator extends ObjectListBaseContentCreator<ChildObjectList>
{
    @Inject
    private BooleanCheckBoxProperty showNestedInNested;

    @Override
    protected void bindAfterCaption()
    {
        if (context.getMetainfo().getOutgoingRelation().stream().anyMatch(Predicates.and(RelationFilters.isSelf(),
                RelationFilters.isParent()))
            || Root.FQN.equals(context.getMetainfo().getFqn()))
        {
            add(messages.showNestedInNested(), showNestedInNested);
            showNestedInNested.ensureDebugId("showNestedInNested");
        }
        super.bindAfterCaption();
    }

    @Override
    protected ChildObjectList getContentInner()
    {
        ChildObjectList result = super.getContentInner();
        if (context.getMetainfo().getOutgoingRelation().stream().anyMatch(
                Predicates.and(RelationFilters.isSelf(), RelationFilters.isParent()))
            || Root.FQN.equals(context.getMetainfo().getFqn()))
        {
            result.setShowNestedInNested(showNestedInNested.getValue());
        }
        return result;
    }

    @Override
    protected String getDefaultCaption()
    {
        return messages.nestedObjects();
    }

    @Override
    protected void showClasses()
    {
        Collection<Relation> relations = metainfoUtils.getIncomingParentRelations(
                context.getMetainfo().getIncomingRelations(), context.getMetainfo().getFqn());
        List<ClassFqn> fqns = new ArrayList<ClassFqn>();
        for (Relation rel : relations)
        {
            fqns.add(rel.getLeft());
        }
        metainfoService.getMetaClasses(fqns, new BasicCallback<List<MetaClassLite>>()
        {
            @Override
            protected void handleSuccess(List<MetaClassLite> classesList)
            {
                List<MetaClassLite> classes = Lists.newArrayList(classesList);
                classes.sort(CommonUtils.METACLASSLITE_COMPARATOR);
                for (MetaClassLite clz : metainfoUtils.getPossible(classes, true))
                {
                    String classId = clz.getFqn().getId();
                    classList.<SingleSelectCellList<?>> getValueWidget().addItem(clz.getTitle(), classId);
                }
                if (!classes.isEmpty())
                {
                    showCases(ClassFqn.parseNullSafe(SelectListPropertyValueExtractor.getValue(classList)));
                    showAttributeGroups(SelectListPropertyValueExtractor.getValue(classList));
                }
            }
        });
    }
}
