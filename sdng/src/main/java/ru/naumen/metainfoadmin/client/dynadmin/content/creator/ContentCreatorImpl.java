package ru.naumen.metainfoadmin.client.dynadmin.content.creator;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.google.inject.Provider;

import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.FormPropertiesCreator;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.tree.dto.DtoTreeGinModule.WithFolders;
import ru.naumen.core.client.tree.dto.factory.DtoTreeFactory;
import ru.naumen.core.client.validation.Processor;
import ru.naumen.core.client.validation.Processor.ValidationUnit;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.AbstractMessageWidget;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.SelectListProperty;
import ru.naumen.core.client.widgets.properties.condition.ConditionProperty;
import ru.naumen.core.client.widgets.properties.condition.ConditionPropertyFactory;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.ui.FlowContent;
import ru.naumen.metainfo.shared.ui.Layout;
import ru.naumen.metainfo.shared.ui.Position;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.UIContext;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreator;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormHelper;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentFormType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentPropertiesOwner;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ContentParentTreeFactoryContext;
import ru.naumen.metainfoadmin.client.dynadmin.parenttree.ParentTreeGinModule.ContentParentTree;
import ru.naumen.metainfoadmin.client.sets.forms.SettingsSetOnFormCreator;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsPropertyFactory;

/**
 * <AUTHOR>
 * @since 27.02.2013
 *
 */
public abstract class ContentCreatorImpl<T extends FlowContent> extends FormPropertiesCreator
        implements ContentCreator<T>, ContentPropertiesOwner
{
    @Inject
    protected Processor validation;
    @Inject
    protected ContentUtils contentUtils;
    @Inject
    protected ContentCreatorMessages messages;
    @Inject
    protected CommonMessages cmessages;
    @Inject
    protected ConditionPropertyFactory conditionPropertyFactory;
    @Inject
    protected TagsPropertyFactory tagsPropertyFactory;
    @Inject
    protected Provider<T> contentProvider;
    @Inject
    protected ContentFormHelper helper;
    @Inject
    private SettingsSetOnFormCreator settingsSetOnFormCreator;
    @Inject
    private SharedSettingsClientService sharedSettingsClientService;
    @Named(PropertiesGinModule.LIST_BOX)
    @Inject
    protected SelectListProperty<String, SelectItem> position;

    protected Property<Collection<SelectItem>> profiles;
    protected Property<Collection<SelectItem>> versProfiles;

    @Inject
    private DtoTreeFactory<DtObject, ContentParentTree, WithFolders, ContentParentTreeFactoryContext> treeFactory;
    protected Property<DtObject> parent;
    private ContentParentTreeFactoryContext treeContext;

    protected ConditionProperty visibilityCondition;

    protected TagsProperty tags;
    protected Property<SelectItem> settingsSet;

    protected UIContext context;

    protected T content;
    protected Layout defaultParent;

    @Override
    public <P> PropertyRegistration<P> addPropertyAfter(Property<P> property, Property<?> propertyBefore)
    {
        add(property, indexOf(propertyBefore) + 1);
        return getPropertyRegistration(property);
    }

    public <R> ValidationUnit<R> addValidation(HasValueOrThrow<R> value, Validator<R> validator)
    {
        ValidationUnit<R> unit = validation.validate(value, validator);
        add(unit);
        return unit;
    }

    @Override
    public void afterAddProperties(PropertyDialogDisplay display)
    {
    }

    @Override
    public final void bindProperties()
    {
        bindPropertiesInner();
        bindProfiles();
        bindVisibilityCondition();
        bindTags();
        bindPositions();
        bindSettingsSet();
    }

    public void fillContent(T content)
    {
        if (null != profiles)
        {
            content.getProfiles().clear();
            content.getProfiles().addAll(SelectListPropertyValueExtractor.getCollectionValue(profiles));
        }
        if (null != versProfiles)
        {
            content.getVersProfiles().clear();
            content.getVersProfiles().addAll(SelectListPropertyValueExtractor.getCollectionValue(versProfiles));
        }
        content.getVisibilityCondition().getElements().clear();
        if (null != visibilityCondition.getValue())
        {
            content.getVisibilityCondition().getElements().addAll(visibilityCondition.getValue().getElements());
        }
        if (null != tags)
        {
            content.getTags().clear();
            content.getTags().addAll(SelectListPropertyValueExtractor.getCollectionValue(tags));
        }
        if (null != settingsSet)
        {
            String settingsSetValue = SelectListPropertyValueExtractor.getValue(settingsSet);
            content.setSettingsSet(settingsSetValue);
        }

        content.setPosition(Position.valueOf(SelectListPropertyValueExtractor.getValue(position)));
    }

    protected void setParent(T content)
    {
        content.setParent(treeContext.getContentByDtObject(parent.getValue()));
    }

    @Nullable
    @Override
    public AbstractMessageWidget getAttention()
    {
        return null;
    }

    @Override
    public ContentFormType getContentFormType()
    {
        return ContentFormType.ADD_CONTENT;
    }

    @Override
    public Processor getValidationProcessor()
    {
        return validation;
    }

    @Override
    public T getContent()
    {
        T result = getContentInner();
        fillContent(result);
        return result;
    }

    @Override
    public final T getCreatedContent()
    {
        if (validate())
        {
            return getContent();
        }
        return null;
    }

    @Override
    public List<DtObject> getPendingTags()
    {
        return null == tags ? null : tags.getPendingTags();
    }

    @Override
    public void init(T content, UIContext context, Layout defaultParent)
    {
        this.context = context;
        this.content = content;
        this.defaultParent = defaultParent;
    }

    @Override
    public void initDisplay()
    {
    }

    protected void bindPositions()
    {
        helper.initPositionProperty(position);
        add(position);
    }

    protected void bindProfiles()
    {
        profiles = contentUtils.createProfilesProperty(this.context.getMetainfo().getFqn(), false);
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        add(profiles);
        if (sharedSettingsClientService.isModuleInstalled(Modules.PLANNED_VERSION))
        {
            versProfiles = contentUtils.createProfilesProperty(this.context.getMetainfo().getFqn(), true);
            DebugIdBuilder.ensureDebugId(versProfiles, "versProfiles");
            add(versProfiles);
        }
    }

    protected void bindParent()
    {
        treeContext = new ContentParentTreeFactoryContext(context);
        treeFactory.createTree(treeContext, new BasicCallback<HasValueOrThrow<DtObject>>()
        {
            @Override
            protected void handleSuccess(HasValueOrThrow<DtObject> value)
            {
                parent = ContentFormHelper.createTreeProperty(treeContext, value, defaultParent, cmessages.placeCard());
                addWithMarker(parent);
            }
        });
    }

    abstract protected void bindPropertiesInner();

    protected void bindTags()
    {
        tags = tagsPropertyFactory.createProperty();
        DebugIdBuilder.ensureDebugId(tags, "tags");
        add(tags);
    }

    protected void bindSettingsSet()
    {
        settingsSet = settingsSetOnFormCreator.createSettingSetFormElement();
        add(settingsSet);
    }

    protected void bindVisibilityCondition()
    {
        visibilityCondition = conditionPropertyFactory.create(context, messages.visibilityConditionForm(),
                "visibilityCondition");
        visibilityCondition.setCaption(messages.visibilityCondition());
        DebugIdBuilder.ensureDebugId(visibilityCondition, "visibilityCondition");
        add(visibilityCondition);
    }

    protected T getContentInner()
    {
        T content = contentProvider.get();
        return content;
    }

    protected boolean validate()
    {
        return validation.validate();
    }
}