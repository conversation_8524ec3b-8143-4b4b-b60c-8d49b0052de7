package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.commentlist;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Sets;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.CommentList.SubjectType;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.ObjectListBaseHelpTextBuilderImplSystemBO;

/**
 * <AUTHOR>
 * @since 15 марта 2016 г.
 *
 */
public class CommentListHelpTextBuilderImpl extends ObjectListBaseHelpTextBuilderImplSystemBO<CommentList>
{
    @Override
    public void buildHelpText()
    {
        Set<ClassFqn> requiredClasses = Sets.newHashSet(content.getClazz());
        if (!SubjectType.RELATED_OBJECT.equals(content.getSubjectType()))
        {
            requiredClasses.addAll(
                    content.getAttrChain().stream().map(AttrReference::getClassFqn).collect(Collectors.toSet()));
        }
        metainfoService.getFullMetaInfo(requiredClasses, new BasicCallback<List<MetaClass>>()
        {
            @Override
            protected void handleSuccess(List<MetaClass> requiredMetaClasses)
            {
                StringBuilder commentListText = new StringBuilder();
                commentListText.append(' ');

                MetaClass commentMetaClass = requiredMetaClasses.stream()
                        .filter(mc -> mc.getFqn().equals(content.getClazz())).findFirst().get();

                if (SubjectType.RELATED_OBJECT.equals(content.getSubjectType()))
                {
                    Attribute attr = context.getMetainfo().getAttribute(content.getRelationAttrCode());
                    if (null != attr)
                    {
                        commentListText.append(messages.linkAttribute(attr.getTitle()));
                        commentListText.append(' ');
                    }
                }
                if (SubjectType.RELATED_OBJECT_SET_COMMENTS.equals(content.getSubjectType())
                    || SubjectType.LINKED_WITH_RELATED_OBJECT_COMMENTS.equals(content.getSubjectType()))
                {
                    String chainAsString = content.getAttrChain().stream()
                            .skip(SubjectType.LINKED_WITH_RELATED_OBJECT_COMMENTS.equals(content.getSubjectType()) ? 1
                                    : 0)
                            .map(ar -> requiredMetaClasses.stream().filter(mc -> mc.getFqn().equals(ar.getClassFqn()))
                                    .findFirst().get().getAttribute(ar.getAttrCode()).getTitle())
                            .collect(Collectors.joining("\\"));
                    commentListText.append(messages.linkAttribute(chainAsString));
                    commentListText.append(' ');
                }

                if (SubjectType.LINKED_WITH_OBJECT_COMMENTS.equals(content.getSubjectType())
                    || SubjectType.LINKED_WITH_RELATED_OBJECT_COMMENTS.equals(content.getSubjectType()))
                {
                    commentListText.append(messages.linkAttributeInComment(
                                    commentMetaClass.getAttribute(content.getAttrChain().get(0).getAttrCode()).getTitle()))
                            .append(' ');
                }

                if (content.getShowAttributeAttachedFiles() != null)
                {
                    Attribute attr = commentMetaClass.getAttribute(content.getShowAttributeAttachedFiles());
                    if (null != attr)
                    {
                        commentListText.append(messages.attachedFilesAttribute(attr.getTitle()));
                        commentListText.append(StringUtilities.SPACE);
                    }
                }

                callback.onSuccess(buildHelpMessage(objectListMessages.contentTypeTitle(), commentListText.toString()));
            }
        });
    }
}
