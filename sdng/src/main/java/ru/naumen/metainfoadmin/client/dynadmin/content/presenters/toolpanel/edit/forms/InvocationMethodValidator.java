package ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.edit.forms;

import java.util.Optional;

import jakarta.inject.Inject;

import com.google.inject.assistedinject.Assisted;

import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.validation.HasValidation;
import ru.naumen.core.client.validation.ValidateEvent;
import ru.naumen.core.client.validation.Validator;
import ru.naumen.core.client.widgets.HasValueOrThrow;
import ru.naumen.core.shared.Constants.ObjectActions;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.ActionTool;
import ru.naumen.metainfoadmin.client.dynadmin.content.presenters.toolpanel.EditableToolPanelMessages;

/**
 * Валидатор для поля "Способ вызова действия".
 * <AUTHOR>
 * @since Mar 10, 2019
 */
public class InvocationMethodValidator implements Validator<SelectItem>
{
    private final ToolFormContext context;
    private final EditableToolPanelMessages messages;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public InvocationMethodValidator(@Assisted ToolFormContext context, EditableToolPanelMessages messages,
            MetainfoUtils metainfoUtils)
    {
        this.context = context;
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
    }

    @Override
    public boolean validate(HasValueOrThrow<SelectItem> hasValue)
    {
        String value = SelectItemValueExtractor.<String> extract(hasValue.getValue());
        if (value.equals(ObjectActions.INVOKE_ROW_CLICK))
        {
            Optional<ActionTool> findAny = context.getObjectActionsMenuHolder().getObjectActionsToolPanel().getTools()
                    .stream().map(it -> (ActionTool)it).filter(it -> it.getInvocationMethodCode().equals(value))
                    .findAny();
            if (findAny.isPresent() && !findAny.get().getUuid().equals(context.getTool().getUuid())
                && hasValue instanceof HasValidation)
            {
                ActionTool actionTool = findAny.get();
                ((HasValidation)hasValue).addValidationMessage(messages
                        .rowClickAlreadyConfigured(metainfoUtils.getLocalizedValue(actionTool.getCaption())));
                return false;
            }
        }
        return true;
    }

    @Override
    public void validateAsync(HasValueOrThrow<SelectItem> hasValue, ValidateEvent event)
    {
        validate(hasValue);
    }
}
