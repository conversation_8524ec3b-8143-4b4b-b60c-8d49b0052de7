package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.tags;

import java.util.HashSet;
import java.util.Map;

import com.google.common.collect.Lists;

import java.util.HashMap;

import jakarta.inject.Inject;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.shared.Constants.TagMetainfo;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BooleanAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.Constants.TextAttributeType;
import ru.naumen.metainfo.shared.dispatch2.GetMetainfoForObjectListResponse;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType_SnapshotObject;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.elements.MetaClassStubFactory;
import ru.naumen.metainfo.shared.elements.Presentation_SnapshotObject;
import ru.naumen.metainfo.shared.elements.StringAttributeType_SnapshotObject;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.objectlist.client.metainfo.ObjectListMetainfoProviderBase;
import ru.naumen.objectlist.client.metainfo.ResetAdvlistAfterProcessingMetainfoEvent;
import ru.naumen.objectlist.client.mode.active.ObjectListActive;

/**
 * Провайдер метаинформации для меток.
 * <AUTHOR>
 * @since Oct 05, 2017
 */
public class TagAdvlistMetainfoProvider extends ObjectListMetainfoProviderBase
{
    @Inject
    private CommonMessages cmessages;
    @Inject
    private TagsMessages messages;

    @Override
    public void getMetainfo()
    {
        GetMetainfoForObjectListResponse response = new GetMetainfoForObjectListResponse();
        response.setAttributeCodes(Lists.newArrayList(TagMetainfo.Attributes.TITLE_ATTR.toString(),
                TagMetainfo.Attributes.DESCRIPTION_ATTR.toString(),
                TagMetainfo.Attributes.ENABLED_ATTR.toString()));
        response.setAttributes(createAttributes());
        ((ObjectListActive)mode).setCheckObjectPermissions(false);
        mode.receiveMetainfoResponse(response);
        localEventBus.fireEvent(new ResetAdvlistAfterProcessingMetainfoEvent());
    }

    @Override
    public void updateAttributes(MetaClass newMetaClass)
    {
    }

    private Map<String, Attribute> createAttributes()
    {
        Map<String, Attribute> attrs = new HashMap<>();
        attrs.put(TagMetainfo.Attributes.TITLE_ATTR.toString(), createTitleAttribute());
        attrs.put(TagMetainfo.Attributes.DESCRIPTION_ATTR.toString(), createDescriptionAttribute());
        attrs.put(TagMetainfo.Attributes.ENABLED_ATTR.toString(), createEnabledAttribute());
        return attrs;
    }

    private Attribute createDescriptionAttribute()
    {
        Attribute_SnapshotObject titleAttr = new Attribute_SnapshotObject();
        titleAttr.__init__fqn(TagMetainfo.Attributes.DESCRIPTION_ATTR);
        titleAttr.__init__code(TagMetainfo.Attributes.DESCRIPTION_ATTR.getCode());
        StringAttributeType_SnapshotObject stringType = new StringAttributeType_SnapshotObject();
        stringType.__init__attribute(titleAttr);
        stringType.__init__permittedTypes(new HashSet<>());
        stringType.__init__code(TextAttributeType.CODE);
        titleAttr.__init__type(stringType);
        titleAttr.__init__computable(false);
        titleAttr.__init__title(cmessages.description());
        titleAttr.__init__metaClass(getMetaClass());
        titleAttr.__init__metaClassLite(getMetaClassLite());
        titleAttr.__init__filteredByScript(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.TEXT_EDIT);
        titleAttr.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.TEXT_VIEW);
        titleAttr.__init__viewPresentation(viewPresentation);
        return titleAttr;
    }

    private Attribute createEnabledAttribute()
    {
        Attribute_SnapshotObject enabledAttr = new Attribute_SnapshotObject();
        enabledAttr.__init__fqn(TagMetainfo.Attributes.ENABLED_ATTR);
        enabledAttr.__init__code(TagMetainfo.Attributes.ENABLED_ATTR.getCode());
        AttributeType_SnapshotObject booleanType = new AttributeType_SnapshotObject();
        booleanType.__init__attribute(enabledAttr);
        booleanType.__init__code(BooleanAttributeType.CODE);
        booleanType.__init__permittedTypes(new HashSet<>());
        enabledAttr.__init__type(booleanType);
        enabledAttr.__init__computable(false);
        enabledAttr.__init__title(messages.enabled());
        enabledAttr.__init__metaClass(getMetaClass());
        enabledAttr.__init__metaClassLite(getMetaClassLite());
        enabledAttr.__init__filteredByScript(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.BOOL_CHECKBOX);
        enabledAttr.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.BOOL_YES_NO);
        enabledAttr.__init__viewPresentation(viewPresentation);
        return enabledAttr;
    }

    private Attribute createTitleAttribute()
    {
        Attribute_SnapshotObject titleAttr = new Attribute_SnapshotObject();
        titleAttr.__init__fqn(TagMetainfo.Attributes.TITLE_ATTR);
        titleAttr.__init__code(TagMetainfo.Attributes.TITLE_ATTR.getCode());
        StringAttributeType_SnapshotObject stringType = new StringAttributeType_SnapshotObject();
        stringType.__init__attribute(titleAttr);
        stringType.__init__permittedTypes(new HashSet<>());
        stringType.__init__code(StringAttributeType.CODE);
        titleAttr.__init__type(stringType);
        titleAttr.__init__computable(false);
        titleAttr.__init__title(cmessages.title());
        titleAttr.__init__metaClass(getMetaClass());
        titleAttr.__init__metaClassLite(getMetaClassLite());
        titleAttr.__init__filteredByScript(false);

        Presentation_SnapshotObject editPresentation = new Presentation_SnapshotObject();
        editPresentation.__init__code(Presentations.STRING_EDIT);
        titleAttr.__init__editPresentation(editPresentation);

        Presentation_SnapshotObject viewPresentation = new Presentation_SnapshotObject();
        viewPresentation.__init__code(Presentations.STRING_VIEW);
        titleAttr.__init__viewPresentation(viewPresentation);
        return titleAttr;
    }

    private MetaClass getMetaClass()
    {
        return MetaClassStubFactory.createMetaClass(getClassFqn());
    }

    private MetaClassLite getMetaClassLite()
    {
        return MetaClassStubFactory.createMetaClassLite(getClassFqn());
    }

    private ClassFqn getClassFqn()
    {
        return TagMetainfo.FQN;
    }
}
