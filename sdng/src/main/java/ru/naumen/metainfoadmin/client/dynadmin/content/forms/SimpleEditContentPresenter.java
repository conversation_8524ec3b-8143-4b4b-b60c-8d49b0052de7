package ru.naumen.metainfoadmin.client.dynadmin.content.forms;

import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.MANAGE_PROFILES_ON_FORMS;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.Collection;
import java.util.List;

import com.google.gwt.event.shared.EventBus;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.client.settings.SharedSettingsClientService;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.validation.NotEmptyValidator;
import ru.naumen.core.client.validation.StringLengthValidator;
import ru.naumen.core.client.widgets.HasProperties.Property;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;
import ru.naumen.core.client.widgets.properties.BooleanCheckBoxProperty;
import ru.naumen.core.client.widgets.properties.PropertiesGinModule;
import ru.naumen.core.client.widgets.properties.condition.ConditionProperty;
import ru.naumen.core.client.widgets.properties.condition.ConditionPropertyFactory;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.CommentList;
import ru.naumen.metainfo.shared.ui.ListFilter;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterOrElement;
import ru.naumen.metainfo.shared.ui.ShowCaptionContent;
import ru.naumen.metainfoadmin.client.AdminDialogMessages;
import ru.naumen.metainfoadmin.client.dynadmin.ContentUtils;
import ru.naumen.metainfoadmin.client.dynadmin.content.EditContentPresenter;
import ru.naumen.metainfoadmin.client.dynadmin.content.creator.SimpleContentCreatorBase;
import ru.naumen.metainfoadmin.client.tags.TagsMessages;
import ru.naumen.metainfoadmin.client.tags.property.TagsProperty;
import ru.naumen.metainfoadmin.client.tags.property.TagsPropertyFactory;

/**
 * Презентер для контентов у которых доступно изменение названия, профиля и код не доступен для изменения
 *
 * <AUTHOR>
 *
 */
public abstract class SimpleEditContentPresenter<C extends ShowCaptionContent> extends EditContentPresenter<C>
{
    public static final String RESTRICT_CONDITION_ID = "restrictCondition";

    @Inject
    protected AdminDialogMessages messages;
    @Inject
    protected ContentUtils contentUtils;
    @Inject
    protected ConditionPropertyFactory conditionPropertyFactory;
    @Inject
    protected TagsPropertyFactory tagsPropertyFactory;
    @Inject
    protected MetainfoUtils metainfoUtils;
    @Inject
    protected NotEmptyValidator notEmptyValidator;
    @Inject
    private StringLengthValidator stringLengthValidator;
    @Inject
    protected SharedSettingsClientService sharedSettingsClientService;
    @Inject
    private TagsMessages tagsMessages;
    @Inject
    @Named(PropertiesGinModule.TEXT_BOX)
    protected Property<String> caption;
    @Inject
    protected BooleanCheckBoxProperty showCaption;

    protected Property<Collection<SelectItem>> profiles;
    protected Property<Collection<SelectItem>> versProfiles;
    protected ConditionProperty visibilityCondition;
    protected ConditionProperty restrictCondition;
    protected TagsProperty tags;
    protected boolean changeParent = false;

    @Inject
    protected SimpleEditContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void bindProperties()
    {
        bindPropertiesBefore();

        bindCaption();
        bindShowCaptionAndCollapsedByDefault();
        bindCode();
        bindParent();

        bindPropertiesInner();

        bindProfiles();
        bindVisibilityCondition();
        bindTags();
        bindSettingsSet();

        bindPropertiesAfter();
    }

    /**
     * Добавить свойства до основных свойств
     */
    protected void bindPropertiesBefore()
    {
    }

    /**
     * Добавить свойства после основных свойств
     */
    protected void bindPropertiesAfter()
    {
    }

    protected void bindCaption()
    {
        caption.setCaption(commonMessages.title());
        caption.setValue(metainfoUtils.getLocalizedValue(content.getCaption()));
        caption.setValidationMarker(true);
        caption.setMaxLength(ContentUtils.MAX_TITLE_LENGTH);

        validation.validate(caption, notEmptyValidator);
        stringLengthValidator.setMaxLength(ContentUtils.MAX_TITLE_LENGTH);
        validation.validate(caption, stringLengthValidator);
        DebugIdBuilder.ensureDebugId(caption, "caption");

        getDisplay().add(caption);
    }

    protected void bindProfiles()
    {
        profiles = createProfilesProperty();
        DebugIdBuilder.ensureDebugId(profiles, "profiles");
        boolean hasManageProfilesOnFormsEditPermission =
                adminPermissionCheckServiceSync.hasPermission(MANAGE_PROFILES_ON_FORMS, EDIT);

        profiles.setEnabled(hasManageProfilesOnFormsEditPermission);

        getDisplay().add(profiles);
        if (sharedSettingsClientService.isModuleInstalled(Modules.PLANNED_VERSION))
        {
            versProfiles = contentUtils.createProfilesProperty(getUiFqn(), content, true);
            DebugIdBuilder.ensureDebugId(versProfiles, "versProfiles");
            versProfiles.setEnabled(hasManageProfilesOnFormsEditPermission);
            getDisplay().add(versProfiles);
        }
    }

    protected Property<Collection<SelectItem>> createProfilesProperty()
    {
        return contentUtils.createProfilesProperty(getUiFqn(), content, false);
    }

    /**
     * Построение кастомных свойств контентов
     */
    protected abstract void bindPropertiesInner();

    /**
     * Обработать галочки {@link BooleanCheckBoxProperty Отображать название},
     * {@link BooleanCheckBoxProperty Свёрнут по умолчанию}. Галочки связаны между собой и второй не может быть
     * отдельно без первой. Поэтому они обрабатываются вместе.
     */
    protected void bindShowCaptionAndCollapsedByDefault()
    {
        showCaption.setCaption(commonMessages.showCaption());
        boolean showCaptionValue = content.isShowCaption();
        showCaption.setValue(showCaptionValue);
        DebugIdBuilder.ensureDebugId(showCaption, "showCaption");
        getDisplay().add(showCaption);

        // при наличии галочки "Отображать название"
        // может быть галочка "Свёрнут по умолчанию" у контентов,
        // которые можно свернуть
        if (content.isCanBeCollapsed())
        {
            collapseByDefault.setCaption(contentMessages.collapsedByDefault());
            collapseByDefault.setValue(content.isCollapsedByDefault());
            collapseByDefault.getValueWidget().setVisible(showCaptionValue);
            DebugIdBuilder.ensureDebugId(collapseByDefault, "collapseByDefault");
            getDisplay().add(collapseByDefault);

            //добавляем обработку включения/отключения галочки "Отображать название"
            SimpleContentCreatorBase.setShowCaptionChangeListener(showCaption, collapseByDefault);
        }
    }

    protected void bindTags()
    {
        tags = tagsPropertyFactory.createProperty(new BasicCallback<TagsProperty>()
        {
            @Override
            protected void handleSuccess(TagsProperty value)
            {
                value.trySetObjValue(content.getTags());
            }
        });
        DebugIdBuilder.ensureDebugId(tags, "tags");

        getDisplay().add(tags);
    }

    /**
     * Добавление виджета "Условия отображения контента"
     */
    protected void bindVisibilityCondition()
    {
        visibilityCondition = conditionPropertyFactory.create(context, contentMessages.visibilityConditionForm(),
                "visibilityCondition");
        visibilityCondition.getValueWidget().setAttentionMessage(tagsMessages.disabledInContentDisplayConditions());
        visibilityCondition.setValue(getListFilter(content.getVisibilityCondition()));
        visibilityCondition.setCaption(contentMessages.visibilityCondition());
        DebugIdBuilder.ensureDebugId(visibilityCondition, "visibilityCondition");
        getDisplay().add(visibilityCondition);
    }

    /**
     * Добавление виджета "Ограничить содержимое контента"
     */
    protected void bindRestrictContent(MetaClass commentClass, PropertyRegistration<SelectItem> after)
    {
        restrictCondition = conditionPropertyFactory.create(commentClass, contentMessages.restrictContentForm(),
                RESTRICT_CONDITION_ID, context.getRootContentInfo());
        restrictCondition.getValueWidget().setAttentionMessage(tagsMessages.disabledInContentDisplayConditions());
        restrictCondition.setValue(getListFilter(((CommentList)content).getRestrictContentCondition()));
        restrictCondition.setCaption(contentMessages.restrictContent());
        DebugIdBuilder.ensureDebugId(restrictCondition, RESTRICT_CONDITION_ID);
        display.addPropertyAfter(restrictCondition, after);
    }

    private static ListFilter getListFilter(ListFilter listFilter)
    {
        listFilter.getElements().stream()
                .map(ListFilterAndElement::getElements)
                .flatMap(Collection::stream)
                .forEach(SimpleEditContentPresenter::setPropertyDisabledByTags);
        return listFilter;
    }

    @Override
    protected void updateAfterSave(C savedContent)
    {
        content.setEnabled(savedContent.isEnabled());
    }

    protected void updateCurrentContent()
    {
        super.updateCurrentContent();

        metainfoUtils.setCaption(content.getCaption(), caption.getValue());
        content.setShowCaption(showCaption.getValue());
        content.setCollapsedByDefault(collapseByDefault.getValue());

        ListFilter visibilityConditionValue = visibilityCondition.getValue();
        if (null != visibilityConditionValue)
        {
            content.getVisibilityCondition().getElements().clear();
            visibilityConditionValue.setWithSemanticAllowed(Boolean.FALSE);
            content.getVisibilityCondition().getElements().addAll(
                    visibilityConditionValue.getElements());
        }

        if (profiles != null)
        {
            content.getProfiles().clear();
            content.getProfiles().addAll(SelectListPropertyValueExtractor.getCollectionValue(profiles));
        }

        if (versProfiles != null)
        {
            content.getVersProfiles().clear();
            content.getVersProfiles().addAll(SelectListPropertyValueExtractor.getCollectionValue(versProfiles));
        }

        if (tags != null)
        {
            content.getTags().clear();
            content.getTags().addAll(SelectListPropertyValueExtractor.getCollectionValue(tags));
        }
    }

    @Override
    protected void restoreContent(C oldContent)
    {
        super.restoreContent(oldContent);
        content.setCaption(oldContent.getCaption());
        content.setShowCaption(oldContent.isShowCaption());
        content.setCollapsedByDefault(oldContent.isCollapsedByDefault());
        content.getVisibilityCondition().getElements().clear();
        content.getVisibilityCondition().getElements().addAll(oldContent.getVisibilityCondition().getElements());
        content.getProfiles().clear();
        content.getProfiles().addAll(oldContent.getProfiles());
        content.getVersProfiles().clear();
        content.getVersProfiles().addAll(oldContent.getVersProfiles());
        content.getTags().clear();
        content.getTags().addAll(oldContent.getTags());
    }

    @Override
    protected boolean isContentEquals(C oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getCaption(), content.getCaption())
               && eq(oldContent.isShowCaption(), content.isShowCaption())
               && eq(oldContent.isCollapsedByDefault(), content.isCollapsedByDefault());
    }

    protected List<DtObject> getPendingTags()
    {
        return null == tags ? null : tags.getPendingTags();
    }

    private static void setPropertyDisabledByTags(ListFilterOrElement<?> orElement)
    {
        if (orElement.getAttributeDtObject() == null)
        {
            return;
        }
        orElement.getAttributeDtObject().setProperty(Constants.DtoTree.DISABLED_SELECTABLE, !orElement.isEnabled());
    }
}