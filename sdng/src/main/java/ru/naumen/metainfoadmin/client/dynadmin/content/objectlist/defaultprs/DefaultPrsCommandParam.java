package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.defaultprs;

import ru.naumen.core.client.common.command.CommandParam;
import ru.naumen.metainfo.shared.ui.AdvlistColumn;
import ru.naumen.metainfo.shared.ui.AdvlistSettingsDefault;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.presenter.ObjectListUIContext;

import com.google.gwt.user.client.rpc.AsyncCallback;

/**
 *
 * <AUTHOR>
 * @since 20 апр. 2015 г.
 */
public class DefaultPrsCommandParam extends CommandParam<AdvlistColumn, Void>
{
    private final ObjectListUIContext objectListContext;
    private final AdvlistSettingsDefault settings;

    public DefaultPrsCommandParam(ObjectListUIContext objectListContext, AdvlistSettingsDefault settings,
            AdvlistColumn column, AsyncCallback<Void> callback)
    {
        super(column, callback);
        this.objectListContext = objectListContext;
        this.settings = settings;
    }

    public ObjectListUIContext getObjectListContext()
    {
        return objectListContext;
    }

    public AdvlistSettingsDefault getSettings()
    {
        return settings;
    }
}