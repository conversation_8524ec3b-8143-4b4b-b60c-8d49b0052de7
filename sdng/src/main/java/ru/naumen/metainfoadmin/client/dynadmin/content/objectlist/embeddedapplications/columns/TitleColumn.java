package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.embeddedapplications.columns;

import jakarta.inject.Inject;

import com.google.gwt.place.shared.Place;

import ru.naumen.core.client.widgets.columns.LinkToPlaceColumn;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationAdminSettingsDto;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.EmbeddedApplicationWithScript;
import ru.naumen.metainfo.shared.embeddedapplication.Constants;
import ru.naumen.metainfoadmin.client.embeddedapplications.EmbeddedApplicationPlace;

/**
 * <AUTHOR>
 * @since 07.07.16
 */
public class TitleColumn extends LinkToPlaceColumn<DtObject>
{
    @Inject
    public TitleColumn()
    {
        super(null);
    }

    @Override
    public Place getPlace(DtObject object)
    {
        return new EmbeddedApplicationPlace(new EmbeddedApplicationWithScript(
                object.<EmbeddedApplicationAdminSettingsDto> getProperty(Constants.Application.ORIGINAL_APPLICATION)));
    }
}
