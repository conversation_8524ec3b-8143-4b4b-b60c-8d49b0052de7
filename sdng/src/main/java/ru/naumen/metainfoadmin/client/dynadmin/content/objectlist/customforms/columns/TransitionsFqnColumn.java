package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.customforms.columns;

import java.util.ArrayList;

import jakarta.inject.Inject;

import ru.naumen.core.client.widgets.columns.ClickableSafeHtmlTextCell;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.elements.MetaClassLite;
import ru.naumen.metainfo.shared.ui.customform.CustomForm;

import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.user.cellview.client.Column;

/**
 * Колонка отображения Классов объектов для переходы в типы
 *
 * <AUTHOR>
 * @since 25.04.2016
 */
public class TransitionsFqnColumn extends Column<DtObject, SafeHtml>
{
    @Inject
    private Formatters formatters;

    @Inject
    public TransitionsFqnColumn(ClickableSafeHtmlTextCell cell)
    {
        super(cell);
        cell.ensureDebugId("transitionsfqn");
    }

    @Override
    public SafeHtml getValue(DtObject dto)
    {
        CustomForm form = dto
                .getProperty(ru.naumen.metainfo.shared.ui.customform.Constants.CustomUserForm.ORIGINAL_CUSTOM_FORM);

        ArrayList<MetaClassLite> metaClasses = form.getTargetCases().getTargets();
        return SafeHtmlUtils.fromTrustedString(formatters.title(metaClasses));
    }
}