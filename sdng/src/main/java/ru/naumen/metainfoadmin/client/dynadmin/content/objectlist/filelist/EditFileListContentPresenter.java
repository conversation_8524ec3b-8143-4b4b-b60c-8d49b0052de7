package ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.filelist;

import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.SelectItemValueExtractor;
import ru.naumen.core.client.forms.PropertyDialogDisplay;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.widgets.HasProperties.PropertyRegistration;
import ru.naumen.core.client.widgets.SelectListPropertyValueExtractor;
import ru.naumen.core.client.widgets.clselect.SingleSelectCellList;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.dto.SelectItem;
import ru.naumen.metainfo.client.AdminMetainfoServiceAsync;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.ui.FileList;
import ru.naumen.metainfo.shared.ui.FileList.RelationType;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;
import ru.naumen.metainfoadmin.client.dynadmin.content.ContentCreatorMessages;
import ru.naumen.metainfoadmin.client.dynadmin.content.objectlist.EditListContentPresenter;

/**
 * Презентер редактирования контента "Список файлов"
 *
 * <AUTHOR>
 * @since 28.06.2013
 *
 */
public class EditFileListContentPresenter extends EditListContentPresenter<FileList>
{
    @Inject
    private FileListContentPropertiesHolder propertiesHolder;
    @Inject
    protected AdminMetainfoServiceAsync metainfoService;
    @Inject
    private ContentCreatorMessages contentCreatorMessages;

    @Inject
    public EditFileListContentPresenter(PropertyDialogDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    protected void initPresentationList()
    {
        contentHelper.initFileListPresentaion(presentationList);
    }

    @Override
    protected void onPresentationValueChanged(SelectItem value)
    {
        super.onPresentationValueChanged(value);
        refreshAttrGroupList(SelectItemValueExtractor.extract(value));
    }

    @Override
    protected ClassFqn getContentClassFqn()
    {
        return File.FQN;
    }

    @Override
    protected void bindPropertiesInner()
    {
        propertiesHolder.init();
        super.bindPropertiesInner();

        refreshAttrGroupList(SelectListPropertyValueExtractor.getValue(presentationList));

        propertiesHolder.getRelationType().setDisable();
        // TODO нужно избавиться от захардкоженных индексов
        PropertyRegistration<SelectItem> relationTypePR = getDisplay().addProperty(propertiesHolder.getRelationType(),
                5);
        for (HandlerRegistration hr : propertiesHolder.registerValueHandlers(getDisplay(), validation, relationTypePR,
                context))
        {
            registerHandler(hr);
        }

        metainfoService.getMetaClass(Constants.File.FQN, new BasicCallback<MetaClass>(context.getReadyState())
        {
            @Override
            protected void handleSuccess(MetaClass fileClass)
            {
                propertiesHolder.initSystemClass(fileClass);
                SingleSelectCellList<?> relationTypeWidget = propertiesHolder.getRelationType()
                        .<SingleSelectCellList<?>> getValueWidget();
                relationTypeWidget.clear();
                relationTypeWidget.addItem(contentCreatorMessages.ofCurrentObject(), RelationType.OBJECT_FILES);
                relationTypeWidget.addItem(contentCreatorMessages.relatedObject(), RelationType.RELATED_OBJECT_FILES);
                relationTypeWidget.addItem(contentCreatorMessages.relatedObjectSet(),
                        RelationType.RELATED_OBJECT_SET_FILES);
                relationTypeWidget.addItem(contentCreatorMessages.linkedWithObject(),
                        RelationType.LINKED_WITH_OBJECT_FILES);
                relationTypeWidget.addItem(contentCreatorMessages.linkedWithRelatedObject(),
                        RelationType.LINKED_WITH_RELATED_OBJECT_FILES);
                propertiesHolder.updateValues(content, getDisplay(), context, relationTypePR, validation);
            }
        });
    }

    @Override
    protected void updateCurrentContent()
    {
        super.updateCurrentContent();
        propertiesHolder.updateContent(content, context);
    }

    @Override
    protected String getAttributeGroupValue()
    {
        String presentation = SelectListPropertyValueExtractor.getValue(presentationList);
        return eq(PresentationType.DEFAULT.getCode(), presentation)
                ? AttrGroup.SYSTEM
                : super.getAttributeGroupValue();
    }

    @Override
    protected void restoreContent(FileList oldContent)
    {
        super.restoreContent(oldContent);
        content.setRelationType(oldContent.getRelationType());
        content.setAttributeCodes(oldContent.getAttributeCodes());
    }

    @Override
    protected boolean isContentEquals(FileList oldContent)
    {
        return super.isContentEquals(oldContent)
               && eq(oldContent.getRelationType(), content.getRelationType())
               && eq(oldContent.getAttributeCodes(), content.getAttributeCodes());
    }

    /**
     * Обновление свойства "Группа атрибутов", зависящих от списка представлений.
     * В представлении "Иконки" это свойство неактуально (с формы нужно убрать)
     * @param presentation представление
     */
    private void refreshAttrGroupList(String presentation)
    {
        if (PresentationType.ADVLIST.getCode().equals(presentation))
        {
            if (attrGroupReg == null)
            {
                if (pagingLocationReg == null)
                {
                    attrGroupReg = display.addPropertyAfter(attributeGroup, presentationListReg);
                }
                else
                {
                    attrGroupReg = display.addPropertyAfter(attributeGroup, pagingLocationReg);
                }
            }
        }
        else
        {
            if (attrGroupReg != null)
            {
                attrGroupReg.unregister();
                attrGroupReg = null;
            }
        }
    }
}