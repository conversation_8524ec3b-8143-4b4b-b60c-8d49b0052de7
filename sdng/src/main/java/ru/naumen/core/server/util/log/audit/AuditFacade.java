package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import java.util.Comparator;
import java.util.List;

/**
 * Фасад для централизованного управления логированием аудита.
 * Координирует работу провайдеров данных аудита и формирует итоговые сообщения.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class AuditFacade
{
    private final List<AuditDataProvider> providers;
    private final AuditConfiguration configuration;
    private final ObjectMapper objectMapper;

    @Inject
    public AuditFacade(List<AuditDataProvider> providers,
                      AuditConfiguration configuration,
                      ObjectMapper objectMapper)
    {
        // Сортируем провайдеры по порядку выполнения
        this.providers = providers.stream()
                .sorted(Comparator.comparingInt(AuditDataProvider::getOrder))
                .toList();
        this.configuration = configuration;
        this.objectMapper = objectMapper;
    }

    /**
     * Логирует сообщение с данными аудита на уровне INFO.
     *
     * @param logger логгер для записи
     * @param message сообщение для логирования
     * @param context объекты контекста (HttpServletRequest, Employee, AuthType и т.д.)
     */
    public void info(Logger logger, String message, Object... context)
    {
        log(logger, AuditLogLevel.INFO, message, context);
    }

    /**
     * Логирует сообщение с данными аудита на уровне WARN.
     *
     * @param logger логгер для записи
     * @param message сообщение для логирования
     * @param context объекты контекста
     */
    public void warn(Logger logger, String message, Object... context)
    {
        log(logger, AuditLogLevel.WARN, message, context);
    }

    /**
     * Логирует сообщение с данными аудита на уровне ERROR.
     *
     * @param logger логгер для записи
     * @param message сообщение для логирования
     * @param context объекты контекста
     */
    public void error(Logger logger, String message, Object... context)
    {
        log(logger, AuditLogLevel.ERROR, message, context);
    }

    /**
     * Логирует сообщение с данными аудита на указанном уровне.
     *
     * @param logger логгер для записи
     * @param level уровень логирования
     * @param message сообщение для логирования
     * @param context объекты контекста
     */
    public void log(Logger logger, AuditLogLevel level, String message, Object... context)
    {
        try
        {
            ObjectNode auditData = buildAuditJson(logger, context);
            String finalMessage = formatMessage(message, auditData);

            logAtLevel(logger, level, finalMessage);
        }
        catch (Exception e)
        {
            // В случае ошибки логируем исходное сообщение без аудита
            logger.error("Failed to build audit data for message: " + message, e);
            logAtLevel(logger, level, message);
        }
    }

    /**
     * Строит JSON объект с данными аудита для указанного логгера.
     *
     * @param logger логгер
     * @param context объекты контекста
     * @return JSON объект с данными аудита
     */
    public ObjectNode buildAuditJson(Logger logger, Object... context)
    {
        DefaultAuditContext auditContext = new DefaultAuditContext();
        auditContext.initialize(context);

        ObjectNode result = auditContext.getOrCreateAuditData();

        // Выполняем провайдеры в порядке приоритета
        for (AuditDataProvider provider : providers)
        {
            try
            {
                if (provider.canProvide(logger))
                {
                    ObjectNode providerData = provider.provide(logger, auditContext);
                    if (providerData != null && providerData.size() > 0)
                    {
                        // Объединяем данные от провайдера с общими данными
                        mergeJsonObjects(result, providerData);
                    }
                }
            }
            catch (Exception e)
            {
                // Логируем ошибку провайдера, но продолжаем работу
                logger.debug("Provider {} failed to provide audit data", provider.getName(), e);
            }
        }

        return result;
    }

    /**
     * Форматирует сообщение, добавляя к нему JSON данные аудита.
     *
     * @param message исходное сообщение
     * @param auditData JSON данные аудита
     * @return отформатированное сообщение
     */
    private String formatMessage(String message, ObjectNode auditData)
    {
        if (auditData == null || auditData.size() == 0)
        {
            return message;
        }

        try
        {
            String auditJson = objectMapper.writeValueAsString(auditData);
            return message + " " + auditJson;
        }
        catch (Exception e)
        {
            // В случае ошибки сериализации возвращаем исходное сообщение
            return message + " {\"auditDataError\":\"" + e.getMessage() + "\"}";
        }
    }

    /**
     * Логирует сообщение на указанном уровне.
     *
     * @param logger логгер
     * @param level уровень логирования
     * @param message сообщение
     */
    private void logAtLevel(Logger logger, AuditLogLevel level, String message)
    {
        switch (level)
        {
            case TRACE -> logger.trace(message);
            case DEBUG -> logger.debug(message);
            case INFO -> logger.info(message);
            case WARN -> logger.warn(message);
            case ERROR -> logger.error(message);
        }
    }

    /**
     * Объединяет два JSON объекта.
     *
     * @param target целевой объект
     * @param source исходный объект
     */
    private void mergeJsonObjects(ObjectNode target, ObjectNode source)
    {
        source.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            if (!target.has(key))
            {
                target.set(key, entry.getValue());
            }
            else
            {
                // Если ключ уже существует, добавляем префикс с именем провайдера
                String newKey = key + "_duplicate";
                target.set(newKey, entry.getValue());
            }
        });
    }
}