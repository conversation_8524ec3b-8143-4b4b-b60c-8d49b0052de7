package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;

/**
 * Интерфейс для провайдеров данных аудита.
 * Каждый провайдер отвечает за предоставление определенного типа информации
 * для логирования аудита (информация о пользователе, объектах и т.д.).
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
public interface AuditDataProvider
{
    /**
     * Проверяет, может ли данный провайдер предоставить данные для указанного логгера.
     *
     * @param logger логгер, для которого проверяется возможность предоставления данных
     * @return true, если провайдер может предоставить данные для данного логгера
     */
    boolean canProvide(Logger logger);

    /**
     * Предоставляет данные аудита в формате JSON для указанного логгера.
     *
     * @param logger логгер, для которого предоставляются данные
     * @param auditContext контекст аудита для обмена данными между провайдерами
     * @return JSON объект с данными аудита
     */
    ObjectNode provide(Logger logger, AuditContext auditContext);

    /**
     * Возвращает порядок выполнения провайдера.
     * Провайдеры с меньшим значением выполняются первыми.
     *
     * @return порядок выполнения (по умолчанию 0)
     */
    default int getOrder()
    {
        return 0;
    }

    /**
     * Возвращает имя провайдера для логирования и отладки.
     *
     * @return имя провайдера
     */
    default String getName()
    {
        return this.getClass().getSimpleName();
    }
}