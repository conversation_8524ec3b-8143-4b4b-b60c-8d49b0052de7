package ru.naumen.core.server.flex.spi;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.config.PackagesToScanCustomizer;

/**
 * Удаляет пакет ru.naumen.core.server.events из списка пакетов, в которых будут искаться
 * {@link jakarta.persistence.Entity сущности} , если db.events.datasource.enabled = true
 *
 * <AUTHOR>
 * @since 05.10.2022
 */
@Component
public class RemoveEventPackageFromPackagesToScanCustomizer implements PackagesToScanCustomizer
{
    protected final boolean isDbEventsDataSourceEnable;

    public RemoveEventPackageFromPackagesToScanCustomizer(
            @Value("${db.events.datasource.enabled}") boolean isDbEventsDataSourceEnable)
    {
        this.isDbEventsDataSourceEnable = isDbEventsDataSourceEnable;
    }

    @Override
    public void customize(List<String> packagesToScan)
    {
        if (isDbEventsDataSourceEnable)
        {
            packagesToScan.removeIf("ru.naumen.core.server.events"::equals);
        }
    }
}