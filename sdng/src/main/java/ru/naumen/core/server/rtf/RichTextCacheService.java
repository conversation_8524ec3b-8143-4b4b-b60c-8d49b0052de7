package ru.naumen.core.server.rtf;

import static ru.naumen.metainfo.shared.Constants.Accessors.ATTRIBUTE_OF_RELATED_OBJECT;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.metainfo.shared.Constants.RichTextAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Сервис, позволяет взаимодействовать с кешем, который используется для атрибутов RichText
 *
 * <AUTHOR>
 * @since 8/4/22
 */
public interface RichTextCacheService
{
    @Nullable
    Object getValueFromCache(String objUuid, String attrCode);

    void putValueToCache(String objUuid, String attrCode, @Nullable Object value, List<String> objectsLink);

    void deleteFromCache(String objUuid);

    void deleteFromCacheByAttributeCode(String fqn, String attrCode);

    void deleteFromCache(String objUuid, String attrCode);

    void deleteFromCache(String uuid, Collection<String> attributes);

    boolean isRtfWorkWithCacheEnable();

    void setRtfWorkWithCacheEnable(boolean rtfWorkWithCacheEnable);

    static boolean rtfAttributeForCache(Attribute attribute)
    {
        return RichTextAttributeType.CODE.equals(attribute.getType().getCode())
               && Boolean.FALSE.equals(attribute.isComputable())
               && Boolean.FALSE.equals(attribute.isComputableOnForm())
               && Boolean.FALSE.equals(attribute.isComputableByAnyCatalogElementsScript())
               && Boolean.FALSE.equals(attribute.isSystemComputable())
               && (attribute.isUseGenerationRule() == null || Boolean.FALSE.equals(attribute.isUseGenerationRule()))
               && Boolean.FALSE.equals(attribute.isDeterminable())
               && Boolean.FALSE.equals(attribute.getAccessor().equals(ATTRIBUTE_OF_RELATED_OBJECT));
    }
}