package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;

import jakarta.inject.Inject;

/**
 * Базовый абстрактный класс для провайдеров данных аудита.
 * Предоставляет общую функциональность для всех провайдеров.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
public abstract class AbstractAuditDataProvider implements AuditDataProvider
{
    protected final AuditConfiguration auditConfiguration;

    @Inject
    protected AbstractAuditDataProvider(AuditConfiguration auditConfiguration)
    {
        this.auditConfiguration = auditConfiguration;
    }

    /**
     * Создает новый пустой JSON объект.
     *
     * @return новый ObjectNode
     */
    protected ObjectNode createObjectNode()
    {
        return JsonNodeFactory.instance.objectNode();
    }

    /**
     * Безопасно добавляет свойство в JSON объект.
     * Если значение null, свойство не добавляется.
     *
     * @param node JSON объект
     * @param propertyName имя свойства
     * @param value значение
     */
    protected void putIfNotNull(ObjectNode node, String propertyName, String value)
    {
        if (value != null)
        {
            node.put(propertyName, value);
        }
    }

    /**
     * Безопасно добавляет свойство в JSON объект.
     * Если значение null, свойство не добавляется.
     *
     * @param node JSON объект
     * @param propertyName имя свойства
     * @param value значение
     */
    protected void putIfNotNull(ObjectNode node, String propertyName, JsonNode value)
    {
        if (value != null && !value.isNull())
        {
            node.set(propertyName, value);
        }
    }

    /**
     * Безопасно добавляет числовое свойство в JSON объект.
     * Если значение null, свойство не добавляется.
     *
     * @param node JSON объект
     * @param propertyName имя свойства
     * @param value значение
     */
    protected void putIfNotNull(ObjectNode node, String propertyName, Number value)
    {
        if (value != null)
        {
            if (value instanceof Integer)
            {
                node.put(propertyName, value.intValue());
            }
            else if (value instanceof Long)
            {
                node.put(propertyName, value.longValue());
            }
            else if (value instanceof Double)
            {
                node.put(propertyName, value.doubleValue());
            }
            else
            {
                node.put(propertyName, value.toString());
            }
        }
    }

    /**
     * Безопасно добавляет булево свойство в JSON объект.
     * Если значение null, свойство не добавляется.
     *
     * @param node JSON объект
     * @param propertyName имя свойства
     * @param value значение
     */
    protected void putIfNotNull(ObjectNode node, String propertyName, Boolean value)
    {
        if (value != null)
        {
            node.put(propertyName, value);
        }
    }

    /**
     * Находит объект определенного типа в контексте аудита.
     *
     * @param auditContext контекст аудита
     * @param clazz класс искомого объекта
     * @param <T> тип объекта
     * @return найденный объект или null
     */
    protected <T> T findInContext(AuditContext auditContext, Class<T> clazz)
    {
        return auditContext.get(clazz).orElse(null);
    }

    /**
     * Находит объект по ключу в контексте аудита.
     *
     * @param auditContext контекст аудита
     * @param key ключ объекта
     * @param clazz класс искомого объекта
     * @param <T> тип объекта
     * @return найденный объект или null
     */
    protected <T> T findInContext(AuditContext auditContext, String key, Class<T> clazz)
    {
        return auditContext.get(key, clazz).orElse(null);
    }
}
