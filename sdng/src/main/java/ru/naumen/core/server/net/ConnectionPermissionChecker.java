package ru.naumen.core.server.net;

import jakarta.annotation.Nullable;

/**
 * Сервис, предназначенный для проверки разрешения на взаимодействие с
 * внешней системой в соответствии с настройками silent mode
 *
 * <AUTHOR>
 * @since 5 апр. 2016 г.
 */
public interface ConnectionPermissionChecker
{
    String NAME = "connectionPermissionChecker";

    /**
     * Проверить разрешения на соединение с удаленной системой
     *
     * @param hostname - адрес удаленной системы
     * @throws SilentModeViolationException - в случае если соединение запрещено
     */
    void checkPermission(String hostname);

    /**
     * Проверить, является ли адрес разрешенным для подключения
     *
     * @param hostname - проверяемый адрес
     * @return true, в случае если адрес разрешен
     */
    boolean isPermitted(@Nullable String hostname);

    /**
     * Зарегестрировать адрес как специальный адрес по которому возможно подключение
     * при включенном Silent Mode
     *
     * @param hostname - адрес
     */
    void registerHostname(@Nullable String hostname);

    /**
     * Зарегестрировать временно резрешенный для подключения адрес
     * После того как зарегестрированный адрес будет использован его
     * нужно убрать из коллекции разрешенных
     *
     * @param hostname - регистрируемый адрес
     */
    void registerTemporarilySuitableHost(String hostname);

    /**
     * Убрать адрес из коллекции разрешенных
     *
     * @param hostname - удаляемый адрес
     */
    void unregisterTemporarilyHostname(String hostname);
}
