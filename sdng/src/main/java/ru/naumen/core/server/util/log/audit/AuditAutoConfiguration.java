package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Автоматическая конфигурация для системы аудита.
 * Обеспечивает регистрацию всех необходимых компонентов.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Configuration
@ComponentScan(basePackages = "ru.naumen.core.server.util.log.audit")
public class AuditAutoConfiguration
{
    /**
     * Создает ObjectMapper для сериализации JSON, если он не определен.
     *
     * @return ObjectMapper
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper auditObjectMapper()
    {
        return new ObjectMapper();
    }
}
