package ru.naumen.core.server.net;

import static ru.naumen.core.server.util.JsonUtils.toJson;
import static ru.naumen.metainfo.shared.ui.Constants.CHANGE_STATE;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.FastAction;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.activity.AbstractTokenizer;
import ru.naumen.core.shared.dispatch.AddFormParametersHolder;
import ru.naumen.core.shared.dispatch.EditFormParametersHolder;
import ru.naumen.core.shared.utils.FormPlaceParameter;
import ru.naumen.metainfo.server.spi.TextEncodingService;

/**
 * Утилитарный класс для генерации ссылок на клиентские формы.
 * Первоначально все методы данного класса планировалось реализовать в ApiUtils,
 * однако это вызвало проблемы с mock-объектами в тестах.
 * Чтобы не переписывать тесты, принято решение выделить их в отдельный класс. 
 *
 * <AUTHOR>
 * @since 12 февр. 2019 г.
 *
 */
@Component
public class BuildUrlHelper
{
    public static final Function<FormPlaceParameter, String> FORM_PARAMETERS_TO_STRING_CONVERTER = fpp -> (fpp
                                                                                                                   .getAttribute()
                                                                                                           + StringUtilities.EQUAL
                                                                                                           + fpp.getValue());

    private final Joiner joiner = Joiner.on(StringUtilities.COMMA).skipNulls();

    private static final int URL_MAX_LENGTH_DEFAULT = 2000;

    public static final String MOBILE_FORM = "mobileForm";

    private static final String FAST = "fast";

    private static int URL_MAX_LENGTH = URL_MAX_LENGTH_DEFAULT;

    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private TextEncodingService textEncodingService;

    @VisibleForTesting
    public void initFromTest(ConfigurationProperties configurationProperties, TextEncodingService textEncodingService)
    {
        this.configurationProperties = configurationProperties;
        this.textEncodingService = textEncodingService;
    }

    /**
     * Добавляет "дополнительные параметры" к url, при необходимости сжимая (кодируя) их.
     * Дополнительные параметры всегда передаем после конструкции "!...!", так как иначе
     * могут быть проблемы с токенизацией (см.: {@link AbstractTokenizer#parse(String)})
     *
     * @param url строка адреса, к которой нужно добавить дополнительные параметры
     * @param extraOptions список дополнительных параметров
     * @param needShortify true явно указывает на необходимость сжатия. В противном случае ориентируемся на
     * максимальную длину url
     * @param linksLifetime время жизни сгенерированных ссылок, в днях (необязательный параметр)
     * @param isMenuItem ссылка используется для элемента навигационного меню
     * @return строка url с дополнительными параметрами, сжатыми (закодированными) если требуется.
     */
    public String addExtraOptionsToUrl(String url, @Nullable Collection<FormPlaceParameter> extraOptions,
            boolean needShortify, @Nullable Integer linksLifetime, boolean isMenuItem)
    {
        StringBuilder result = new StringBuilder(url);
        if (!CollectionUtils.isEmpty(extraOptions))
        {
            //Дополнительные параметры всегда передаем после конструкции "!...!", так как иначе
            //могут быть проблемы с токенизацией (см.: {@link AbstractTokenizer#parse(String)})
            int separatorsCount = url.length() - url.replace(AbstractTokenizer.PARAMETERS_SEPARATOR, "").length();
            for (int i = 0; i < (2 - separatorsCount); i++)
            {
                result.append(AbstractTokenizer.PARAMETERS_SEPARATOR);
            }
            String extraOptionsString = UrlUtils.encodeUrlComponent(getFormParametersAsString(extraOptions));
            if (textEncodingService.isEnabled() && (needShortify ||
                                                    (extraOptionsString.length() + url.length()) >= URL_MAX_LENGTH))
            {
                Date removalDate = null;
                if (!isMenuItem)
                {
                    removalDate = DateUtils.addDays(new Date(), linksLifetime == null ? textEncodingService
                            .getObjectListLinksLifetime() : linksLifetime);
                }

                String encodedParameters = textEncodingService.encodeParameters(extraOptions, removalDate, true);
                FormPlaceParameter fpp = new FormPlaceParameter(Constants.ENCODED_PARAMETERS, encodedParameters);
                extraOptionsString = UrlUtils.encodeUrlComponent(getFormParametersAsString(Arrays.asList(fpp)));
            }
            result.append(extraOptionsString);
        }
        return result.toString();
    }

    /**
     * Возвращает базовый URL интерфейса оператора
     * @return базовый URL интерфейса оператора. Оканчивается на '/'
     */
    public String getBaseUrl()
    {
        String baseUrl = configurationProperties.getBaseUrl();
        // @formatter:off
        return baseUrl.endsWith("/")
                ? baseUrl + ru.naumen.core.shared.Constants.OPERATOR_ALIAS + "/"
                : baseUrl + "/" + ru.naumen.core.shared.Constants.OPERATOR_ALIAS + "/";
        // @formatter:on
    }

    /**
     * Формирует ссылку на форму редактирования объекта. В случае, если длина ссылки больше максимально допустимой
     * (urlMaxLength), часть параметров ссылки "кодируется"
     *
     * @param parametersHolder объект, содержащий параметры для создания ссылки
     * @return строка со ссылкой на форму редактирования объекта
     */
    public String buildEditFormLink(EditFormParametersHolder parametersHolder)
    {
        return buildEditFormLinkUnsafe(shortifyEditFormParametersIfNeed(parametersHolder));
    }

    private EditFormParametersHolder shortifyEditFormParametersIfNeed(EditFormParametersHolder parametersHolder)
    {
        if (textEncodingService.isEnabled() && buildEditFormLinkUnsafe(parametersHolder).length() >= URL_MAX_LENGTH)
        {
            Collection<FormPlaceParameter> parameters = parametersHolder.getParameters() == null
                    ? Collections.emptyList()
                    : parametersHolder.getParameters();

            parametersHolder.setParameters(Collections.singletonList(shortifyFormParameters(parameters)));
        }
        return parametersHolder;
    }

    /**
     * Формирует ссылку на открытие формы редактирования объекта
     *
     * @param parametersHolder объект, содержащий параметры для создания ссылки
     * @return строка со ссылкой на форму редактирования объекта
     */
    private String buildEditFormLinkUnsafe(EditFormParametersHolder parametersHolder)
    {
        String objUUID = parametersHolder.getObjectUuid();
        String accessKey = parametersHolder.getAccessKey();
        String mobileCode = parametersHolder.getMobileCode();
        Collection<FormPlaceParameter> parameters = parametersHolder.getParameters();

        final StringBuilder url = new StringBuilder(getBaseUrl()).append('?');
        if (accessKey != null)
        {
            url.append(accessKey).append('&');
        }
        url.append(toAnchor("edit:" + objUUID));
        if (mobileCode != null)
        {
            url.append(UrlUtils.encodeUrlComponent("!{\"" + BuildUrlHelper.MOBILE_FORM + "\":\"" + mobileCode + "\"}"));
        }
        if (parameters != null && !parameters.isEmpty())
        {
            if (mobileCode == null)
            {
                url.append(UrlUtils.encodeUrlComponent("!"));
            }
            String attributesStr = getFormParametersAsString(parameters);
            url.append(UrlUtils.encodeUrlComponent("!" + attributesStr));
        }
        return url.toString();
    }

    /**
     * Формирует ссылку на форму добавления объекта. В случае, если длина ссылки больше максимально допустимой
     * (urlMaxLength), часть параметров ссылки "кодируется"
     *
     * @param parametersHolder объект, содержащий параметры для создания ссылки
     * @return строка со ссылкой на форму добавления объекта
     */
    public String buildAddFormLink(AddFormParametersHolder parametersHolder)
    {
        return buildAddFormLinkUnsafe(shortifyAddFormParametersIfNeed(parametersHolder));
    }

    /**
     * Формирует ссылку на список объектов на отдельной странице
     *
     * @param linksLifetime - продолжительность жизни ссылки в днях (необязательный параметр)
     * @param extraOptions - дополнительные параметры (фильтрация, сортировка и т.д., то есть все, что может
     * использваться как на карточке объекта, так и на отдельной странице отображения списка) (необязательный параметр)
     * @param needShortify - true явно указывает на необходимость сжатия дополнительных параметров. В противном
     *                     случае ориентируемся на
     * максимальную длину url
     * @param isMenuItem ссылка используется для элемента навигационного меню
     * @return ссылка на список объектов на отдельной странице
     */
    public String buildObjectListLink(@Nullable Integer linksLifetime,
            @Nullable Collection<FormPlaceParameter> extraOptions, boolean needShortify, boolean isMenuItem)
    {
        String url;
        /* В случае с ссылкой на контент в элементе левого меню нас интересует относительная ссылка */
        if (isMenuItem)
        {
            url = "#list:";
        }
        else
        {
            url = getBaseUrl() + '?' + toAnchor("list:");
        }
        return addExtraOptionsToUrl(url, extraOptions, needShortify, linksLifetime, isMenuItem);
    }

    /**
     * При необходимости, сокращает (на сколько это возможно) исходные параметры для построения ссылки на форму 
     * добавления объекта.
     * @param parametersHolder - объект, содержащий параметры для создания ссылки.
     * @return переданный объект, у которого коллекции параметров и доступных типов сокращены на столько,
     * чтобы результирующая длина ссылки была менее, чем urlMaxLength.
     */
    public AddFormParametersHolder shortifyAddFormParametersIfNeed(AddFormParametersHolder parametersHolder)
    {
        int retryCounter = 0;
        //Для формы добавления есть только 2 попытки сократить url:
        //1) сократить типы и 2) сократить параметры 
        while (buildAddFormLinkUnsafe(parametersHolder).length() >= URL_MAX_LENGTH
               && retryCounter < 2)
        {
            parametersHolder = shortifyLongestPartOfAddFormLink(parametersHolder);
            retryCounter++;
        }
        return parametersHolder;
    }

    /**
     * Если "кодирование" текста разрешено, cокращает самую длинную часть ссылки на форму добавления объекта
     * (перечень типов или параметров)
     * @param parametersHolder - объект, содержащий параметры для создания ссылки.
     * @return переданный объект, у которого коллекция, образующая самую длинную строку - "закодирована". 
     */
    private AddFormParametersHolder shortifyLongestPartOfAddFormLink(AddFormParametersHolder parametersHolder)
    {
        if (textEncodingService.isEnabled())
        {
            String parameters = getFormParametersAsString(parametersHolder.getParameters());
            String cases = getAddFormPosibleCasesAsString(parametersHolder.getCases());
            boolean isParametersEncoded = parametersHolder.getParameters().stream().anyMatch(
                    fp -> Constants.ENCODED_PARAMETERS.equals(fp.getAttribute()));

            if (parameters.length() > cases.length() && !isParametersEncoded)
            {
                parametersHolder.setParameters(Collections.singletonList(
                        shortifyFormParameters(parametersHolder.getParameters())));
            }
            else
            {
                String encodedCases = textEncodingService.encodePossibleCases(parametersHolder.getCases());
                parametersHolder.setCases(Collections.singletonList(encodedCases));
            }
        }
        return parametersHolder;
    }

    private FormPlaceParameter shortifyFormParameters(Collection<FormPlaceParameter> parameters)
    {
        String encodedParameters = textEncodingService.encodeParameters(parameters);
        return new FormPlaceParameter(Constants.ENCODED_PARAMETERS, encodedParameters);
    }

    /**
     * Формирует ссылку на форму добавления объекта. В случае, если длина ссылки будет больше максимально 
     * допустимой (URL_MAX_LENGTH), часть параметров ссылки будет "закодировано"
     * @param parametersHolder объект, содержащий параметры для создания ссылки.
     * @return строка со ссылкой на форму добавления объекта
     */
    public String buildAddFormLinkUnsafe(AddFormParametersHolder parametersHolder)
    {
        Map<String, Object> extendedParametersMap = new HashMap<>();
        if (parametersHolder.getMobileCode() != null)
        {
            extendedParametersMap.put(MOBILE_FORM, parametersHolder.getMobileCode());
        }
        //Параметры в "новом" формате (в случае их наличия) добавляются после {"fast":"true"}
        if (parametersHolder.isFastCreate())
        {
            extendedParametersMap.put(FAST, Boolean.TRUE.toString());
        }

        StringBuilder url = new StringBuilder(getBaseUrl()).append('?').append(toAnchor("add:"));
        url.append(parametersHolder.getClassFqn().toString()).append(StringUtilities.COLON);
        if (parametersHolder.getParentUuid() != null)
        {
            url.append(parametersHolder.getParentUuid());
        }
        url.append(StringUtilities.COLON).append(getAddFormPosibleCasesAsString(parametersHolder.getCases()));

        // Параметры в "новом" формате добавляются в ссылку через "!" 
        if (!MapUtils.isEmpty(extendedParametersMap))
        {
            String newParams = UrlUtils.encodeUrlComponent(toJson(extendedParametersMap));
            url.append('!').append(newParams);
        }

        if (!CollectionUtils.isEmpty(parametersHolder.getParameters()))
        {
            //Параметры добавляются в ссылку в "новом" формате через "!", в "старом" через ":" 
            url.append(MapUtils.isEmpty(extendedParametersMap) ? StringUtilities.COLON : '!');
            String oldParams = getFormParametersAsString(parametersHolder.getParameters());

            url.append(UrlUtils.encodeUrlComponent(oldParams));
        }
        return url.toString();
    }

    /**
     * Формирует ссылку для перехода на форму смены статуса объекта
     * @param values - коллекция значений атрибутов на форме смены статуса
     * @param generatedUrl - url объекта, форма смены статуса которого будет открыта при переходе по ссылке
     * @return строка со ссылкой на форму смены статуса
     */
    public String buildChangeStateLink(Collection<FormPlaceParameter> values, String generatedUrl)
    {
        StringBuilder url = new StringBuilder(generatedUrl);
        url.append('!');
        JsonObject action = new JsonObject();
        action.add(FastAction.CODE, new JsonPrimitive(CHANGE_STATE));

        JsonObject properties = new JsonObject();
        for (FormPlaceParameter v : values)
        {
            if (HasState.STATE.equals(v.getAttribute()))
            {
                properties.add(HasState.TARGET_STATE, toJsonElement(v.getValue()));
                continue;
            }
            if (v.getValue() != null)
            {
                properties.add(v.getAttribute(), toJsonElement(v.getValue()));
            }
        }
        action.add(FastAction.VALUES, properties);

        JsonObject params = new JsonObject();
        params.add(FastAction.ACTION, action);

        if (textEncodingService.isEnabled())
        {
            String encodedParameters = textEncodingService.encodedText(params.toString());
            Map<String, Object> extendedParametersMap = new HashMap<>();
            extendedParametersMap.put(FAST, Boolean.TRUE.toString());

            url.append(UrlUtils.encodeUrlComponent(toJson(extendedParametersMap)))
                    .append('!')
                    .append(Constants.ENCODED_PARAMETERS)
                    .append(StringUtilities.EQUAL)
                    .append(encodedParameters);
        }
        else
        {
            url.append(encodeUrl(params.toString()));
        }
        return url.toString();
    }

    private static JsonElement toJsonElement(Object value)
    {
        return switch (value)
        {
            case String strValue -> new JsonPrimitive(strValue);
            case Boolean bolValue -> new JsonPrimitive(bolValue);
            case Number numValue -> new JsonPrimitive(numValue);
            default -> throw new IllegalArgumentException("Property values must be primitive");
        };
    }

    /**
     * Возвращает параметры для установки на различных формах (например, добавления объекта) в виде строки 
     * @param parameters коллекция параметров
     * @return параметры для установки на форме в виде строки
     */
    public String getFormParametersAsString(Collection<FormPlaceParameter> parameters)
    {
        return parameters.stream().map(FORM_PARAMETERS_TO_STRING_CONVERTER).collect(Collectors.joining(
                StringUtilities.COMMA));
    }

    /**
     * Возвращает перечень допустимых типов для установки на форме добавления объекта в виде строки
     * @param posibleCases коллекция типов
     * @return перечень допустимых типов в виде строки
     */
    private String getAddFormPosibleCasesAsString(Collection<String> posibleCases)
    {
        return joiner.join(posibleCases);
    }

    /**
     * Формирование якоря
     *
     * @param str строка
     *
     * @return якорь
     */
    public String toAnchor(String str)
    {
        return ru.naumen.core.shared.Constants.ANCHOR + StringUtilities.EQUAL + encodeUrl(str);
    }

    /**
     * Заменяет спец. символы в url на символы юникода.
     * @param url декодируемый url
     * @return декодированный url
     */
    public static String encodeUrl(@NotNull String url)
    {
        return url.replaceAll("\\\"", "%22")
                .replaceAll("\\{", "%7B").replaceAll("\\}", "%7D")
                .replaceAll("\\[", "%5B").replaceAll("\\]", "%5D")
                .replaceAll("\\\\", "%5C");

    }

    public static int getUrlMaxLength()
    {
        return URL_MAX_LENGTH;
    }

    @VisibleForTesting
    public static void setUrlMaxLength(int value)
    {
        URL_MAX_LENGTH = value;
    }

    @VisibleForTesting
    public static int getUrlMaxLengthDefault()
    {
        return URL_MAX_LENGTH_DEFAULT;
    }
}
