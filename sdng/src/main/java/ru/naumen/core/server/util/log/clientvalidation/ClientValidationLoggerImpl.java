package ru.naumen.core.server.util.log.clientvalidation;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import jakarta.inject.Inject;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.shared.dispatch.clientvalidation.AttributeInfo;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.SuperUser;

/**
 * Реализация {@link ClientValidationLogger}
 *
 * <AUTHOR>
 * @since 10.07.2025
 */
@Lazy
@Component
public class ClientValidationLoggerImpl implements ClientValidationLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(ClientValidationLoggerImpl.class);

    private final ConfigurationProperties configurationProperties;
    private final CurrentUserProvider currentUserProvider;
    private final UserInfoService userInfoService;

    @Inject
    public ClientValidationLoggerImpl(ConfigurationProperties configurationProperties,
            CurrentUserProvider currentUserProvider,
            UserInfoService userInfoService)
    {
        this.currentUserProvider = currentUserProvider;
        this.configurationProperties = configurationProperties;
        this.userInfoService = userInfoService;
    }

    @Override
    public void log(String formCode, List<String> objUuids, String metaClass, Map<String, Set<AttributeInfo>> validationErrors)
    {
        if (configurationProperties.isLogClientValidationEnabled())
        {
            for (Entry<String, Set<AttributeInfo>> entry : validationErrors.entrySet())
            {
                LOG.warn("{} {}",
                        entry.getKey(),
                        convertInfoToJson(formCode, objUuids, metaClass, entry.getValue()));
            }
        }
    }

    private ObjectNode convertInfoToJson(
            String formCode, List<String> objUuids, String metaClass, Set<AttributeInfo> attributes)
    {
        ObjectNode infoJson = JsonNodeFactory.instance.objectNode();
        infoJson.put("form", formCode);
        infoJson.put("user", userInfoService.getUserInfo(LOG, ((CurrentEmployeeContext)currentUserProvider).getCurrentEmployee()));
        infoJson.put("objects", createObjectInfo(objUuids, metaClass, attributes));

        return infoJson;
    }

    private static ObjectNode createObjectInfo(List<String> objUuids, String metaClass, Set<AttributeInfo> attrCodes)
    {
        ObjectNode objectInfo = JsonNodeFactory.instance.objectNode();

        objectInfo.put("metaClass", metaClass);

        ArrayNode uuids = JsonNodeFactory.instance.arrayNode();
        objUuids.forEach(uuids::add);
        objectInfo.set("UUID", uuids);

        ArrayNode attributesInfo = JsonNodeFactory.instance.arrayNode();
        attrCodes.forEach(attributeInfo ->
                {
                    ObjectNode info = JsonNodeFactory.instance.objectNode();
                    info.put("code", attributeInfo.getCode());
                    info.put("title", attributeInfo.getTitle());
                    info.put("value", attributeInfo.getValue());
                    attributesInfo.add(info);
                });
        objectInfo.set("attributes", attributesInfo);

        return objectInfo;
    }
}