package ru.naumen.core.server.flex.spi;

import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.google.common.collect.Lists;

import ru.naumen.config.PackagesToScanCustomizer;

/**
 * Конфигурация пакетов для поиска {@link jakarta.persistence.Entity сущностей}
 *
 * <AUTHOR>
 * @since 05.10.2022
 */
@Configuration("packagesToScanConfiguration")
public class PackagesToScanConfiguration
{
    private final List<String> initialPackagesToScan = Lists.newArrayList(
            "ru.naumen.core.server.bo",
            "ru.naumen.core.server.catalog",
            "ru.naumen.core.server.comment",
            "ru.naumen.core.server.events",
            "ru.naumen.core.server.filestorage",
            "ru.naumen.core.server.flex",
            "ru.naumen.core.server.keyvaluestorage",
            "ru.naumen.core.server.mail",
            "ru.naumen.mailsender.server.service.sender",
            "ru.naumen.sec.server.log",
            "ru.naumen.core.server.naming.spi",
            "ru.naumen.core.server.plannedevent",
            "ru.naumen.core.server.script.api.accesskeys",
            "ru.naumen.sec.server.jwt.mobile",
            "ru.naumen.core.server.upload.spi",
            "ru.naumen.ndap.server.bo",
            "ru.naumen.ndap.server.catalog",
            "ru.naumen.mailreader.server.queue",
            "ru.naumen.mailreader.server.task.log",
            "ru.naumen.reports.server.objects",
            "ru.naumen.core.server.metastorage.impl.objectgraph",
            "ru.naumen.core.server.push",
            "ru.naumen.mobile.push.mobileregistration",
            "ru.naumen.mobile.metainfoadmin.server.geo.history",
            "ru.naumen.sec.server.security",
            "ru.naumen.sec.server.session",
            "ru.naumen.core.server.encodedfqns",
            "ru.naumen.core.server.encodedtext",
            "ru.naumen.core.server.eventaction",
            "ru.naumen.core.server.embeddedapplication.accesskey",
            "ru.naumen.core.server.superuser",
            "ru.naumen.core.server.idstorage",
            "ru.naumen.core.server.maintenance.domain",
            "ru.naumen.smia.server.model",
            "ru.naumen.smia.server.learningProcess.bo.task",
            "ru.naumen.core.server.hierarchyresult",
            "ru.naumen.omnichannel.server.bo",
            "ru.naumen.omnichannel.server.catalog",
            "ru.naumen.dynamicfield.core.server.bo",
            "ru.naumen.dynamicfield.core.server.catalog",
            "ru.naumen.core.server.wf.defferededitendstate.model",
            "ru.naumen.core.server.cluster.readonlysettings",
            "ru.naumen.core.server.monitoring.stacktrace.record",
            "ru.naumen.core.server.partition");

    @Bean("packagesToScan")
    public List<String> getPackagesToScan(List<PackagesToScanCustomizer> packagesToScanCustomizers)
    {
        packagesToScanCustomizers.forEach(customizer -> customizer.customize(initialPackagesToScan));
        return initialPackagesToScan;
    }
}
