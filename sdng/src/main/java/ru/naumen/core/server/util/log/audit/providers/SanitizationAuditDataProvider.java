package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.common.server.utils.html.SanitizationEventLoggerImpl;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * Провайдер данных аудита для событий санитизации XSS.
 * Предоставляет специфичную информацию о санитизации контента.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class SanitizationAuditDataProvider extends AbstractAuditDataProvider
{
    @Inject
    public SanitizationAuditDataProvider(AuditConfiguration auditConfiguration)
    {
        super(auditConfiguration);
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        // Предоставляем данные только для логгера санитизации
        return SanitizationEventLoggerImpl.class.getName().equals(logger.getName());
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode sanitizationInfo = createObjectNode();

        // Ищем данные санитизации в контексте
        ObjectNode sanitizationData = findInContext(auditContext, ObjectNode.class);
        if (sanitizationData != null)
        {
            // Копируем данные санитизации
            sanitizationData.fields().forEachRemaining(entry -> 
                sanitizationInfo.set(entry.getKey(), entry.getValue()));
        }

        // Ищем санитизированный объект
        IUUIDIdentifiable sanitizedObject = findInContext(auditContext, IUUIDIdentifiable.class);
        if (sanitizedObject != null)
        {
            addSanitizedObjectInfo(sanitizationInfo, sanitizedObject);
        }

        // Добавляем информацию о типе события
        sanitizationInfo.put("securityEventType", "XSS_PREVENTION");
        sanitizationInfo.put("threatLevel", "MEDIUM");
        
        return sanitizationInfo;
    }

    @Override
    public int getOrder()
    {
        // Выполняется после основных провайдеров
        return AuditConstants.ProviderOrder.CUSTOM + 200;
    }

    @Override
    public String getName()
    {
        return "SanitizationAuditDataProvider";
    }

    /**
     * Добавляет информацию о санитизированном объекте.
     *
     * @param sanitizationInfo JSON объект для заполнения
     * @param sanitizedObject санитизированный объект
     */
    private void addSanitizedObjectInfo(ObjectNode sanitizationInfo, IUUIDIdentifiable sanitizedObject)
    {
        putIfNotNull(sanitizationInfo, AuditConstants.ObjectInfo.OBJECT_UUID, sanitizedObject.getUUID());
        putIfNotNull(sanitizationInfo, AuditConstants.ObjectInfo.OBJECT_CLASS, 
                    sanitizedObject.getClass().getSimpleName());
        
        if (sanitizedObject instanceof ITitled)
        {
            putIfNotNull(sanitizationInfo, AuditConstants.ObjectInfo.OBJECT_TITLE, 
                        ((ITitled) sanitizedObject).getTitle());
        }
    }
}
