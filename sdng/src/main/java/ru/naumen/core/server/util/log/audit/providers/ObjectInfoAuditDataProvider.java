package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.dynaform.server.permissions.PermissionsCheckModuleService;

import java.util.Collection;

/**
 * Провайдер данных аудита для информации об объектах.
 * Предоставляет информацию о правах доступа к объектам.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class ObjectInfoAuditDataProvider extends AbstractAuditDataProvider
{
    private final PermissionsCheckModuleService permissionsCheckModuleService;

    @Inject
    public ObjectInfoAuditDataProvider(AuditConfiguration auditConfiguration,
                                     PermissionsCheckModuleService permissionsCheckModuleService)
    {
        super(auditConfiguration);
        this.permissionsCheckModuleService = permissionsCheckModuleService;
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        return auditConfiguration.isNeedLogObjectsPermissionDetails(logger);
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode objectInfo = createObjectNode();

        // Ищем объекты в контексте
        collectObjectsInfo(objectInfo, auditContext);

        return objectInfo;
    }

    @Override
    public int getOrder()
    {
        return AuditConstants.ProviderOrder.OBJECT_INFO;
    }

    /**
     * Собирает информацию об объектах из контекста.
     *
     * @param objectInfo JSON объект для заполнения
     * @param auditContext контекст аудита
     */
    private void collectObjectsInfo(ObjectNode objectInfo, AuditContext auditContext)
    {
        // Ищем различные типы объектов в контексте
        IUUIDIdentifiable uuidObject = findInContext(auditContext, IUUIDIdentifiable.class);
        if (uuidObject != null)
        {
            addObjectInfo(objectInfo, uuidObject, auditContext);
        }

        // Ищем коллекции объектов
        Collection<?> objects = findInContext(auditContext, Collection.class);
        if (objects != null && !objects.isEmpty())
        {
            addObjectsCollectionInfo(objectInfo, objects, auditContext);
        }
    }

    /**
     * Добавляет информацию об одном объекте.
     *
     * @param objectInfo JSON объект для заполнения
     * @param object объект
     * @param auditContext контекст аудита
     */
    private void addObjectInfo(ObjectNode objectInfo, IUUIDIdentifiable object, AuditContext auditContext)
    {
        putIfNotNull(objectInfo, AuditConstants.ObjectInfo.OBJECT_UUID, object.getUUID());
        
        if (object instanceof ITitled)
        {
            putIfNotNull(objectInfo, AuditConstants.ObjectInfo.OBJECT_TITLE, 
                        ((ITitled) object).getTitle());
        }

        putIfNotNull(objectInfo, AuditConstants.ObjectInfo.OBJECT_CLASS, 
                    object.getClass().getSimpleName());

        // Добавляем информацию о правах доступа
        addPermissionsInfo(objectInfo, object, auditContext);
    }

    /**
     * Добавляет информацию о коллекции объектов.
     *
     * @param objectInfo JSON объект для заполнения
     * @param objects коллекция объектов
     * @param auditContext контекст аудита
     */
    private void addObjectsCollectionInfo(ObjectNode objectInfo, Collection<?> objects, AuditContext auditContext)
    {
        ArrayNode objectsArray = JsonNodeFactory.instance.arrayNode();
        
        for (Object obj : objects)
        {
            if (obj instanceof IUUIDIdentifiable)
            {
                ObjectNode objInfo = createObjectNode();
                addObjectInfo(objInfo, (IUUIDIdentifiable) obj, auditContext);
                objectsArray.add(objInfo);
            }
        }

        if (objectsArray.size() > 0)
        {
            objectInfo.set("objects", objectsArray);
        }
    }

    /**
     * Добавляет информацию о правах доступа к объекту.
     *
     * @param objectInfo JSON объект для заполнения
     * @param object объект
     * @param auditContext контекст аудита
     */
    private void addPermissionsInfo(ObjectNode objectInfo, IUUIDIdentifiable object, AuditContext auditContext)
    {
        try
        {
            Employee employee = findInContext(auditContext, Employee.class);
            if (employee != null && permissionsCheckModuleService != null)
            {
                // Получаем информацию о группах и лицензиях пользователя
                String groups = permissionsCheckModuleService.getGroups(employee).toString();
                String licenses = permissionsCheckModuleService.getLicenses(employee).toString();
                
                putIfNotNull(objectInfo, AuditConstants.UserInfo.GROUPS, groups);
                putIfNotNull(objectInfo, AuditConstants.UserInfo.LICENSES, licenses);
            }
        }
        catch (Exception e)
        {
            // В случае ошибки добавляем информацию об ошибке
            objectInfo.put("permissionsError", "Failed to get permissions: " + e.getMessage());
        }
    }
}
