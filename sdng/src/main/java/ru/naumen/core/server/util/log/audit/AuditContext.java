package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Optional;

/**
 * Контекст для передачи данных между провайдерами аудита.
 * Позволяет провайдерам обмениваться информацией и избегать дублирования работы.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
public interface AuditContext
{
    /**
     * Получает объект из контекста по типу.
     *
     * @param clazz класс объекта
     * @param <T> тип объекта
     * @return Optional с объектом или пустой Optional
     */
    <T> Optional<T> get(Class<T> clazz);

    /**
     * Получает объект из контекста по ключу.
     *
     * @param key ключ
     * @param clazz класс объекта
     * @param <T> тип объекта
     * @return Optional с объектом или пустой Optional
     */
    <T> Optional<T> get(String key, Class<T> clazz);

    /**
     * Сохраняет объект в контексте по типу.
     *
     * @param object объект для сохранения
     * @param <T> тип объекта
     */
    <T> void put(T object);

    /**
     * Сохраняет объект в контексте по ключу.
     *
     * @param key ключ
     * @param object объект для сохранения
     * @param <T> тип объекта
     */
    <T> void put(String key, T object);

    /**
     * Проверяет, содержит ли контекст объект указанного типа.
     *
     * @param clazz класс объекта
     * @return true, если объект найден
     */
    boolean contains(Class<?> clazz);

    /**
     * Проверяет, содержит ли контекст объект по ключу.
     *
     * @param key ключ
     * @return true, если объект найден
     */
    boolean contains(String key);

    /**
     * Получает или создает JSON объект для накопления данных аудита.
     *
     * @return ObjectNode для данных аудита
     */
    ObjectNode getOrCreateAuditData();

    /**
     * Очищает контекст.
     */
    void clear();
}
