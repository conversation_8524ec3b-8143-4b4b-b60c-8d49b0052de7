package ru.naumen.core.server.flex.spi;

import java.sql.Connection;
import java.util.Map;

import org.hibernate.boot.Metadata;
import org.hibernate.boot.model.relational.SqlStringGenerationContext;
import org.hibernate.dialect.Dialect;
import org.hibernate.tool.schema.extract.spi.DatabaseInformation;

import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Интерфейс стратегии обновления схемы БД при перестроении session factory
 *
 * <AUTHOR>
 * @since 11.03.2020
 */
@FunctionalInterface
public interface SchemaUpdaterStrategy
{
    /**
     * Возвращает генератор запросов для обновления схемы БД
     *
     * @param connection       подключение к БД
     * @param dialect          текущий диалект SQL
     * @param databaseMetadata метаданные БД
     * @param metadata         маппинг для session factory
     * @param context          контекст генерации sql скриптов
     * @param configuration    конфигурация session factory
     */
    SessionFactorySchemaUpdater getSchemaUpdater(DataBaseInfo dataBaseInfo, Connection connection, Dialect dialect,
            DatabaseInformation databaseMetadata, Metadata metadata, SqlStringGenerationContext context,
            Map<?, ?> configuration);
}
