package ru.naumen.core.server.flex;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.googlecode.functionalcollections.FunctionalIterables.make;
import static ru.naumen.commons.server.utils.StringUtilities.stringBegin;
import static ru.naumen.core.server.hibernate.DDLTool.MAX_IDENTIFIER_LENGTH;
import static ru.naumen.core.server.schemaoptimization.SchemaOptimizationFeatureConfiguration.DELETE_SIGN;
import static ru.naumen.core.server.schemaoptimization.SchemaOptimizationFeatureConfiguration.DELETE_SIGN_MIN;
import static ru.naumen.core.shared.Constants.Agreement.RESOLUTION_TIME_RULE;

import java.lang.annotation.Annotation;
import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Field;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;

import org.apache.commons.lang3.RandomStringUtils;
import org.hibernate.internal.SessionFactoryImpl;
import org.hibernate.metamodel.MappingMetamodel;
import org.hibernate.persister.collection.CollectionPersister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.base.Predicates;
import com.google.common.collect.ImmutableSet;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.InjectorUtils;
import ru.naumen.commons.server.utils.PredicateUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.FunctionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.metainfo.server.spi.naming.ClassNameGenerator;
import ru.naumen.core.server.flex.spi.ReloadableSessionFactory;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasHardcoded;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Вспомогательные методы
 *
 * <AUTHOR>
 */
@Component
public class FlexHelper
{
    // чтобы проверяемые sql исключения нормально работали в лямбдах
    @FunctionalInterface
    public interface CheckedFunction<T, R>
    {
        R apply(T t) throws Exception;
    }

    public enum AttributePredicates implements Predicate<Attribute>
    {
        CatalogItems
                {
                    @SuppressFBWarnings(
                            "NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return CatalogItemsAttributeType.CODE.equals(checkNotNull(attribute).getType().getCode());
                    }
                },
        ManyToMany
                {
                    //@formatter:off
            private final Set<String> CODES = ImmutableSet.of(
                    CatalogItemsAttributeType.CODE,
                    BOLinksAttributeType.CODE,
                    CaseListAttributeType.CODE);
            //@formatter:on

                    @SuppressFBWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return CODES.contains(checkNotNull(attribute).getType().getCode());
                    }
                },
        Hyperlink
                {
                    @SuppressFBWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return HyperlinkAttributeType.CODE.equals(checkNotNull(attribute).getType().getCode());
                    }
                },
        BOLinks
                {
                    @SuppressFBWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return BOLinksAttributeType.CODE.equals(checkNotNull(attribute).getType().getCode());
                    }
                },
        BackLinks
                {
                    @SuppressFBWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return BackLinkAttributeType.CODE.equals(checkNotNull(attribute).getType().getCode());
                    }
                },
        AttributeOfRelatedObject
                {
                    @SuppressFBWarnings("NP_PARAMETER_MUST_BE_NONNULL_BUT_MARKED_AS_NULLABLE")
                    @Override
                    public boolean apply(Attribute attribute)
                    {
                        return checkNotNull(attribute).getType().isAttributeOfRelatedObject();
                    }
                }

    }

    public static class ManyToManyToTableNameFunction implements Function<Attribute, String>
    {
        @Override
        public String apply(Attribute attribute)
        {
            return getTableName(attribute.getMetaClass(), attribute).toLowerCase();
        }
    }

    private final static Logger LOG = LoggerFactory.getLogger(FlexHelper.class);

    public static final String UUID_STATIC_METHOD = "getUUIDPrefix";

    /**
     * Разделитель используемый в {@link Entity#name()} для разделения названия Класса от кода Типа
     */
    public static final String ENTITY_NAME_DELIMITER = "$";

    /**
     * Разделитель используемый в название колнки таблица базы данных для разделения кода Типа от кода
     * пользовательского атрибута
     */
    private static final String COLUMN_NAME_DELIMITER = "$";

    public static final String NULL_DISCRIMINATOR = ".";

    public static final String NULL_DISCRIMINATOR_FORMULA = "'" + NULL_DISCRIMINATOR + "'";

    private static final List<String> LONG_CODE_NAMES = Arrays.asList("internalphonenumber", "mobilephonenumber",
            RESOLUTION_TIME_RULE.toLowerCase(), "availablemetrics", "descriptioninrtf", "commentauthoralias");

    private static final java.util.function.Predicate<Attribute> isFlex =
            PredicateUtils.isPropertyEquals(Attribute.class, HasHardcoded.HARDCODED, false)::test;

    public static final int PREFIX_LENGTH_FOR_MANY_TO_MANY = 8;

    public static final String TABLE_PREFIX = "tbl_";
    public static final String INDEX_PREFIX = "idx_";

    /**
     * Префикс для формирования имени подзапроса в SQL, реализованного как Common Table Expression
     */
    public static final String HCTE_SQLNAME_PREFIX = "sub_objects_";

    private static final String CODE = "code";
    private static final String NAME = "name";

    public static String getColumnName(Attribute attr)
    {
        return getColumnName(attr.getDeclaredMetaClass(), attr.getCode());
    }

    public static String getColumnName(ClassFqn fqn, String attr)
    {
        boolean isCase = StringUtilities.isEmptyTrim(fqn.getCase());
        return isCase ? attr : fqn.getCase() + COLUMN_NAME_DELIMITER + attr;
    }

    @Inject
    private MetainfoService metainfoService;

    @Inject
    @Lazy
    private ReloadableSessionFactory sessionFactoryBean;

    @Inject
    private MessageFacade messages;
    private final ManyToManyToTableNameFunction toTableNameFunction = new ManyToManyToTableNameFunction();

    /**
     * Динамически создает java-класс для пользовательских метаклассов.
     * <p>
     * Переопределяет методы:
     * <ol>
     * <li>getPrefixLoaderUUIDPrefix()</li>
     * <li>getMetaClassId()</li>
     * <li>getEntityName()</li>
     * </ol>
     * <p>
     * Добавляет аннотации к классу:
     * <ol>
     * <li>jakarta.persistence.Entity</li>
     * <li>jakarta.persistence.Table</li>
     * <li>org.hibernate.annotations.DiscriminatorFormula (для типизированных Классов)</li>
     * <li>UUIDPrefix</li>
     * </ol>
     * Для Классов вложенных в другой класс добавляются:
     * <ol>
     * <li>геттер getParent() возвращающий значение пользовательского атрибута parent</li>
     * </ol>
     */

    @Inject
    ClassNameGenerator nameGenerator;

    public void checkManyToManyTableUnique(Attribute attribute)
    {
        if (!AttributePredicates.ManyToMany.apply(attribute) || attribute.isHardcoded())
        {
            return;
        }
        String tableName = toTableNameFunction.apply(attribute);
        Set<Attribute> equalTableAttributes = make(getManyToManyFlexAttributes())
                .filter(Predicates.not(Predicates.equalTo(attribute)))
                .filter(Predicates.compose(Predicates.equalTo(tableName), toTableNameFunction))
                .toSet();
        if (equalTableAttributes.isEmpty())
        {
            return;
        }
        String attrCode = attribute.getType().getCode();
        String alreadyExistErrorMessage = switch (attrCode)
        {
            case BOLinksAttributeType.CODE -> "MetaClassImpl.attribute.manyToManyEqualsTable";
            case CatalogItemsAttributeType.CODE -> "MetaClassImpl.attribute.manyToManyEqualsTableCatalogs";
            default -> "MetaClassImpl.attribute.manyToManyEqualsTableCaseList";
        };
        throw new ClassMetainfoServiceException(
                messages.getMessage(alreadyExistErrorMessage, equalTableAttributes.iterator().next().getCode()));
    }

    public Attribute getBackLinkRelatedAttribute(Attribute attribute)
    {
        ru.naumen.metainfo.shared.elements.ObjectAttributeType casted = attribute.getType().cast();
        String attributeCode = casted.getProperty(BackLinkAttributeType.BACK_ATTR_CODE);
        ClassFqn relatedFqn = casted.getRelatedMetaClass();
        MetaClass relatedMetaClass = metainfoService.getMetaClass(relatedFqn);
        return relatedMetaClass.getAttribute(attributeCode);
    }

    public Set<Attribute> getBackLinksFlexAttributes()
    {
        return getFlexAtributesByType(AttributePredicates.BackLinks);
    }

    public Set<Attribute> getBOLinksFlexAttributes()
    {
        return getFlexAtributesByType(AttributePredicates.BOLinks);
    }

    public Set<Attribute> getCatalogItemsFlexAttributes()
    {
        return getFlexAtributesByType(AttributePredicates.CatalogItems);
    }

    public CollectionPersister getCollectionPersister(Attribute attribute)
    {
        SessionFactoryImpl sf = sessionFactoryBean.getCurrentSessionFactory();
        MappingMetamodel metamodel = sf.getMetamodel();

        ClassFqn fqn = attribute.getDeclaredConcretMetaClass();
        String role = metainfoService.getEntityJavaClassName(fqn) + "." + attribute.getCode();

        CollectionPersister persister = metamodel.findCollectionDescriptor(role);
        if (persister != null)
        {
            return persister;
        }

        role = metainfoService.getFullEntityName(fqn) + "." + attribute.getCode();
        return metamodel.findCollectionDescriptor(role);
    }

    /**
     * Возвращает название колонки таблицы базы данных для атрибута
     *
     * @param cls  метакласса атрибута
     * @param attr атрибут
     * @return название колонки таблицы базы данных для атрибута
     */
    public static String getColumnName(MetaClass cls, Attribute attr)
    {
        return getColumnName(cls, attr.getCode());
    }

    /**
     * Возвращает название колонки таблицы базы данных для атрибута
     *
     * @param cls  метакласса атрибута
     * @param attr атрибут
     * @return название колонки таблицы базы данных для атрибута
     */
    public static String getColumnName(MetaClass cls, String attr)
    {
        return getColumnName(cls.getFqn(), attr);
    }

    public static Set<Attribute> getDeclaredManyToManyFlexAttrs(MetaClassImpl metaclass)
    {
        Collection<Attribute> declaredAttributes = MetaClassImpl.DECLARED_ATTRIBUTES_EXTRACTOR.apply(metaclass);
        //@formatter:off
        return new HashSet<>(make(declaredAttributes)
                .filter(AttributePredicates.ManyToMany)
                .filter(isFlex::test)
                .toSet());
        //@formatter:on
    }

    /**
     * Получение имени {@link JoinColumn} из аннотации {@link JoinTable} в атрибуте системного класса или типа
     *
     * @param fqn      fqn системного класса или типа
     * @param attrCode код атрибута
     * @return имя колонки или "" если аннотация отсутствует
     */
    public String getJoinColumnName(ClassFqn fqn, String attrCode)
    {
        for (Field field : metainfoService.getJavaClass(fqn).getDeclaredFields())
        {
            String code = (String)getAnnotationAttribute(field, ru.naumen.metainfo.server.annotations.Attribute.class,
                    CODE);
            if (null != code && code.equals(attrCode))
            {
                JoinColumn[] columns = (JoinColumn[])getAnnotationAttribute(field, JoinTable.class, "joinColumns");
                if (columns != null && columns.length > 0)
                {
                    return columns[0].name();
                }
            }
        }
        return "";
    }

    /**
     * Получение имени таблицы из аннотации {@link JoinTable} в атрибуте системного класса или типа
     *
     * @param fqn      fqn системного класса или типа
     * @param attrCode код атрибута
     * @return имя таблицы или "" если аннотация отсутствует
     */
    public String getJoinTableName(ClassFqn fqn, String attrCode)
    {
        for (Field field : metainfoService.getJavaClass(fqn).getDeclaredFields())
        {
            String code = (String)getAnnotationAttribute(field, ru.naumen.metainfo.server.annotations.Attribute.class,
                    CODE);
            if (null != code && code.equals(attrCode))
            {
                String result = (String)getAnnotationAttribute(field, JoinTable.class, NAME);
                return null != result ? result : "";
            }

        }
        return "";
    }

    public Set<Attribute> getFlexAttributesOfRelatedObject()
    {
        return getFlexAtributesByType(AttributePredicates.AttributeOfRelatedObject);
    }

    public Set<Attribute> getHyperlinkFlexAttributes()
    {
        return getFlexAtributesByType(AttributePredicates.Hyperlink);
    }

    /**
     * Формирует имя индекса для атрибута. Так как максимальная длина имен ограничена
     * {@link DDLTool#MAX_IDENTIFIER_LENGTH},
     * то полная информация о классе/типе/коде не входит в имя, оставим код атрибута и хэш fqnа класса
     * (если будем просто обрезать имя, то очень вероятны коллизии - например, коды класса/типа длинные, тогда на код
     * атрибута не хватит места -
     * у всех атрибутов типа будет один индекс)
     *
     * @param suffix - 1 символьный суффикс индекса, нужен для разграничения индексов (уникальный/не уникальный)
     *               так как Hibernate генерирует только неуникальные индексы, то могут быть созданы два индекса
     *               уникальный/не
     *               уникальный на одно поле
     */
    public static String getIndexName(Attribute attribute, @Nullable Character suffix)
    {
        final String suffixStr = null == suffix ? "" : suffix + "_";
        // индекс надо отнести к метаклассу в котором объявлен атрибут (но не к абстрактному классу)
        ClassFqn fqnOfAttributeIndex = CommonUtils.getFqnOfDeclaredClass(attribute);

        /*
         * ivodopyanov 17.10.2013
         * Раньше metaClassHash генерился как ClassFqn.hashCode, из-за чего наименование индекса зависело от
         * реализации ClassFqn.hashCode
         * Это вызывало false negative при поиске существующих индексов на заданный атрибут (т.к. у старого индекса
         * было наименование, сгенеренное по старому алгоритму)
         * Реализация была изменена на копию реализации ClassFqn.hashCode на данный момент, чтобы устранить
         * зависимость от ClassFqn.hashCode
         * (теперь его можно менять, но алгоритм формирования наименований индексов останется прежним)
         */
        final int metaClassHash = ObjectUtils
                .hashCode((Object)ObjectUtils.hashCode(fqnOfAttributeIndex.getCase(), fqnOfAttributeIndex.asString()));

        /*
         * imartemjanov 10.02.2014
         * Необходима проверка кода атрибута на совпадение со списком имен.
         * В случае положительного результата значение кода арибута, участвующее в создании индекса,
         * урезается на количество символов, превышающее максимально допустимое значение.
         * DDLTool.MAX_IDENTIFIER_LENGHT
         */
        String validAttributeCodeName = trimLongAttributeCode(attribute.getCode());
        String suffixIndexName = "_" + suffixStr + Integer.toHexString(metaClassHash).toLowerCase();
        return INDEX_PREFIX + validAttributeCodeName + suffixIndexName;
    }

    public static String trimLongAttributeCode(String longCode)
    {
        return LONG_CODE_NAMES.contains(longCode.toLowerCase()) ? longCode.substring(0, Constants.MAX_ID_LENGTH) :
                longCode;
    }

    public Set<Attribute> getManyToManyFlexAttributes()
    {
        return getFlexAtributesByType(AttributePredicates.ManyToMany);
    }

    public Set<String> getManyToManyFlexAttributesTables()
    {
        return new HashSet<>(CollectionUtils.transform(getManyToManyFlexAttributes(), toTableNameFunction));
    }

    /**
     * Возвращает название таблицы, которая создается при создании связи многие-ко многим(например, атрибут типа
     * "Набор элементов справочника")
     * Название формируется по следующему шаблону: tbl_ + classId + $ + caseId + _ + attrCode (все коды обрезаются до
     * 8 символов из-за ограничения
     * на длину имени в 30 символов в Oracle)
     */
    public static String getTableName(ClassFqn fqn, Attribute attr)
    {
        boolean notCase = fqn.isClass();
        String truncatedClassId = stringBegin(fqn.getId(), PREFIX_LENGTH_FOR_MANY_TO_MANY);
        String truncatedCaseId = stringBegin(fqn.getCase(), PREFIX_LENGTH_FOR_MANY_TO_MANY);
        String truncatedAttrCode = stringBegin(attr.getCode(), PREFIX_LENGTH_FOR_MANY_TO_MANY);

        return TABLE_PREFIX
               + (notCase
                ? truncatedClassId
                : truncatedClassId + ENTITY_NAME_DELIMITER + truncatedCaseId)
               + '_'
               + truncatedAttrCode;
    }

    /**
     * Возвращает имя ограничения по типу. Заменяет имя таблицы на хэш, в случае превышения
     * максимальной допустипой длины идентификатора
     * {@link DDLTool#MAX_IDENTIFIER_LENGTH}
     */
    public static String getNormalizedConstraintName(String table)
    {
        String constraintName = "CHK_" + table + "_case_id";
        return constraintName.length() > MAX_IDENTIFIER_LENGTH
                ? "CHK_" + Integer.toHexString(ObjectUtils.hashCode(table)).toLowerCase() + "_case_id"
                : constraintName;
    }

    public static String getTableName(MetaClass cls, Attribute attr)
    {
        return getTableName(cls.getFqn(), attr);
    }

    private static Object getAnnotationAttribute(AnnotatedElement element, Class<?> type, String attribute)
    {
        for (Annotation anno : AnnotationUtils.getAnnotations(element))
        {
            if (type.isAssignableFrom(anno.annotationType()))
            {
                return AnnotationUtils.getValue(anno, attribute);
            }
        }
        return null;
    }

    private Set<Attribute> getFlexAtributesByType(AttributePredicates byFlexType)
    {
        //@formatter:off
        return make(metainfoService.getMetaClasses())
            .transform(FunctionUtils.newType(MetaClassImpl.class))
            .transform(MetaClassImpl.DECLARED_ATTRIBUTES_EXTRACTOR::apply)
            .inject(make(new ArrayList<>()), InjectorUtils.concat())
            .filter(isFlex::test)
            .filter(byFlexType)
            .toSet();
        //@formatter:on
    }

    /**
     * Переименовать колонки/индексы/констрейнты
     */
    public static int renameColumns(DDLTool tool, String tableName, String defaultSchema,
            Collection<String> columnsForDelete) throws SQLException
    {
        int counForDelete = 0;
        Set<String> alreadyRemovedConstraints = new HashSet<>();
        for (String columnName : columnsForDelete)
        {
            if (!columnName.endsWith(DELETE_SIGN))
            {
                counForDelete++;
                final Set<String> existingConstraints = tool.getExistingConstraints(tableName, columnName);
                for (String index : tool.getExistingIndexNames(tableName, columnName))
                {
                    // отфильтруем PRIMARY KEY, чтобы переименовать их в обработке ограничений ниже
                    if (existingConstraints.contains(index))
                    {
                        continue;
                    }
                    tool.renameIndex(index, getUniqueNameForRemove(tool::indexExists, index, DELETE_SIGN_MIN));
                }
                for (String constraint : existingConstraints)
                {
                    // Не будем дважды переименовывать составные ограничения (к примеру в НБО первичный ключ составной)
                    if (!alreadyRemovedConstraints.contains(constraint))
                    {
                        final String newNameConst = getUniqueNameForRemove(
                                name -> tool.constraintExists(null, name), constraint, DELETE_SIGN_MIN);
                        tool.renameConstraint(tableName, defaultSchema, constraint, newNameConst);
                        alreadyRemovedConstraints.add(newNameConst);
                    }
                }

                tool.renameColumn(tableName, columnName,
                        getUniqueNameForRemove(name -> tool.columnExists(tableName, name), columnName, DELETE_SIGN));
            }
        }
        return counForDelete;
    }

    /**
     * Получить уникальное имя для переименования таблицы/индекса/констрейнта/колонки для удаления
     */
    public static String getUniqueNameForRemove(CheckedFunction<String, Boolean> checkedFunction, String oldName,
            String postfix)
    {
        try
        {
            String newName = oldName + postfix;
            if (newName.length() > MAX_IDENTIFIER_LENGTH)
            {
                newName = newName.substring(0, MAX_IDENTIFIER_LENGTH - postfix.length() - 3);
                newName = newName + RandomStringUtils.randomAlphabetic(3) + postfix;
            }
            if (checkedFunction.apply(newName))
            {
                newName = oldName + RandomStringUtils.randomAlphabetic(3);
                return getUniqueNameForRemove(checkedFunction, newName, postfix);
            }
            return newName;
        }
        catch (Exception e)
        {
            throw new FxException(e.getMessage(), e);
        }
    }

    /**
     * Возвращает название столбца связи при построении таблицы многие-ко многим
     *
     * @param attribute атрибут связи
     * @return название столбца
     */
    public static String getManyToManyJoinColumnName(Attribute attribute)
    {
        return attribute.getMetaClass().getFqn().getId() + "_id";
    }

    /**
     * Возвращает название таблицы, которая создается при создании связи многие-ко многим(например, атрибут типа
     * "Набор элементов справочника")
     * Название формируется по следующему шаблону: tbl_ + classId + $ + caseId + _ + attrCode (все коды обрезаются до
     * 8 символов из-за ограничения
     * на длину имени в 30 символов в Oracle)
     *
     * @param attr атрибут, для которого будет получено название таблицы связи.
     * @return название таблицы связи.
     */
    public static String getManyToManyTableName(Attribute attr)
    {
        final ClassFqn declaredMetaClassFqn = attr.getDeclaredMetaClass();
        return getTableName(declaredMetaClassFqn, attr);
    }
}
