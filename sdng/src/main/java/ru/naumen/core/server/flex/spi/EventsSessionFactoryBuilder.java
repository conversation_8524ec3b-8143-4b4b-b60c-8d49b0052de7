package ru.naumen.core.server.flex.spi;

import java.sql.Connection;
import java.sql.SQLException;

import org.hibernate.SessionFactory;
import org.springframework.core.io.ResourceLoader;

import ru.naumen.core.server.flex.spi.classloader.ClassLoaderDetails;
import ru.naumen.core.server.hibernate.ComplexInterceptor;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;

/**
 * Конструирует {@link SessionFactory} для работы с событиями с поддержкой флекс атрибутов
 * <p>
 * Используется в системе для обращения к событиям
 *
 * <AUTHOR>
 * @since 16.12.2019
 */
public class EventsSessionFactoryBuilder extends FlexSessionFactoryBuilder
{
    EventsSessionFactoryBuilder(TransactionalDataSource dataSource,
            ResourceLoader resourceLoader, ComplexInterceptor interceptor, DataBaseInfo dataBaseInfo,
            ClassLoaderDetails details)
    {
        super(dataSource, resourceLoader, interceptor, SessionFactoryType.FLEX_EVENTS, dataBaseInfo, details);
    }

    @Override
    protected void initStateResponsibleEvents()
    {
        // DO NOTHING
    }

    @Override
    protected void initSidObjectClasses()
    {
        // DO NOTHING
    }

    @Override
    protected void dropUnnecessary(DDLTool ddlTool)
    {
        // DO NOTHING
    }

    @Override
    protected Connection getConnectionForSchemaUpdate() throws SQLException
    {
        return dataSource.getNonTransactionalConnection();
    }
}
