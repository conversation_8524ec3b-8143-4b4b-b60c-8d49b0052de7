# Система аудита логирования

Новая система аудита предоставляет единообразный механизм для добавления контекстной информации в логи приложения.

## Архитектура

Система построена на основе паттернов Strategy и Factory и состоит из следующих компонентов:

- **AuditDataProvider** - интерфейс для провайдеров данных аудита
- **AuditFacade** - центральный фасад для логирования с аудитом
- **AuditContext** - контекст для передачи данных между провайдерами
- **AuditConfiguration** - конфигурация логгеров

## Провайдеры данных

### UserInfoAuditDataProvider
Предоставляет информацию о пользователе:
- Логин, UUID, название
- IP адрес, ID сессии (для расширенного режима)
- Группы и лицензии (для расширенного режима)

### ObjectInfoAuditDataProvider
Предоставляет информацию об объектах:
- UUID и название объекта
- Класс объекта
- Права доступа

### RequestInfoAuditDataProvider
Предоставляет информацию о HTTP запросе:
- URI и метод запроса
- IP адрес, User-Agent, Referer

### AuthenticationInfoAuditDataProvider
Предоставляет информацию об аутентификации:
- Тип аутентификации
- Причина неудачи
- Дополнительные данные

## Использование

### Базовое использование

```java
@Inject
private AuditFacade auditFacade;

// Простое логирование с аудитом
auditFacade.info(logger, "User action performed", employee, request);
```

### Использование с дополнительными данными

```java
// Создание дополнительных данных
ObjectNode customData = JsonNodeFactory.instance.objectNode();
customData.put("operation", "CREATE");
customData.put("objectType", "Document");

auditFacade.info(logger, "Document created", employee, document, customData);
```

### Использование AuditLoggerHelper

```java
@Inject
private AuditLoggerHelper auditHelper;

// Логирование события аутентификации
ObjectNode authData = auditHelper.createAdditionalData();
auditHelper.addField(authData, "loginMethod", "LDAP");
auditHelper.logAuthenticationEvent(logger, "Login successful", request, authType, authData);

// Логирование доступа к объекту
auditHelper.logObjectAccessEvent(logger, "Document accessed", employee, document, "READ");
```

## Конфигурация

Настройка логгеров осуществляется через properties:

```properties
# Логгеры с расширенной информацией о пользователе
ru.naumen.logger.details.user.loggers=ru.naumen.core.server.util.log.login.LoginFailureLogger,ru.naumen.sec.server.session.SessionLoggerImpl

# Логгеры с информацией о правах на объекты
ru.naumen.logger.details.objects.permissions.loggers=ru.naumen.security.ObjectAccessLogger
```

## Интеграция с существующими логгерами

### Пример интеграции LoginFailureLogger

Было:
```java
JsonObject jsonObject = new JsonObject();
jsonObject.addProperty("authenticationType", getAuthenticationType().getName());
loginFailureMessage += " " + jsonObject;
LoginFailureLogger.getLogger().info(loginFailureMessage);
```

Стало:
```java
ObjectNode customAuthData = JsonNodeFactory.instance.objectNode();
customAuthData.put("authenticationType", getAuthenticationType().getName());

auditFacade.info(LoginFailureLogger.getLogger(), loginFailureMessage,
                request, exception, getAuthenticationType(), customAuthData);
```

## Создание собственных провайдеров

```java
@Component
public class CustomAuditDataProvider extends AbstractAuditDataProvider {
    
    @Override
    public boolean canProvide(Logger logger) {
        // Логика определения, может ли провайдер предоставить данные
        return logger.getName().contains("custom");
    }
    
    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext) {
        ObjectNode data = createObjectNode();
        
        // Сбор данных
        CustomObject obj = findInContext(auditContext, CustomObject.class);
        if (obj != null) {
            putIfNotNull(data, "customField", obj.getValue());
        }
        
        return data;
    }
    
    @Override
    public int getOrder() {
        return AuditConstants.ProviderOrder.CUSTOM;
    }
}
```

## Преимущества

1. **Единообразие** - все логгеры используют одинаковый формат аудита
2. **Расширяемость** - легко добавлять новые типы данных
3. **Гибкость** - каждый логгер может иметь свой набор провайдеров
4. **Производительность** - провайдеры вызываются только при необходимости
5. **Совместимость** - интегрируется с существующей архитектурой

## Формат вывода

Пример итогового лога:
```
2025-08-18 10:30:15 INFO LoginFailureLogger - Authentication failed for user: john.doe {"title":"John Doe","login":"john.doe","UUID":"12345","ip":"*************","sessionId":"SESS123","authenticationType":"LDAP","success":false,"failureReason":"Invalid credentials"}
```
