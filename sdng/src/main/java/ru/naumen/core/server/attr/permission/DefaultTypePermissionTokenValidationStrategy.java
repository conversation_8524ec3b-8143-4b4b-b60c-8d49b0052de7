package ru.naumen.core.server.attr.permission;

import java.util.Collection;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.datatoken.DataAccessTokenService;
import ru.naumen.core.server.datatoken.DataAccessTokenService.TokenType;
import ru.naumen.core.server.factory.AbstractStrategy;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.DtoTree;
import ru.naumen.core.shared.attr.TypePermissionToken;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.FakeMetaClassesConstants.ReportInstance.Attributes;

/**
 * Стратегия валидации списка возможных значений ссылочного атрибута по умолчанию.
 * <AUTHOR>
 * @since Jan 23, 2023
 */
@Component
public class DefaultTypePermissionTokenValidationStrategy extends AbstractStrategy<String>
        implements TypePermissionTokenValidationStrategy
{
    private final DataAccessTokenService dataAccessTokenService;

    @Inject
    public DefaultTypePermissionTokenValidationStrategy(
            TypePermissionTokenValidationStrategyFactory strategyFactory,
            DataAccessTokenService dataAccessTokenService)
    {
        super(strategyFactory);
        this.dataAccessTokenService = dataAccessTokenService;
    }

    @Override
    public boolean validate(@Nullable TypePermissionToken token, @Nullable Collection<ClassFqn> cases,
            @Nullable AttributeFqn attributeFqn, @Nullable String sourceType)
    {
        if (Attributes.AUTHOR_UUID.equals(attributeFqn) && CollectionUtils.isEmpty(cases))
        {
            return true;
        }

        if (!dataAccessTokenService.validateToken(TokenType.PossibleValues, token))
        {
            return false;
        }

        if (null == token || null == token.getDataToken())
        {
            return true;
        }

        if (null != attributeFqn && Constants.PARENT_ATTR.equals(attributeFqn.getCode())
            && null != token.getAllowedSourceTypes()
            && token.getAllowedSourceTypes().contains(DtoTree.SELECT_PARENT_TREE))
        {
            return true;
        }
        if (null != token.getAllowedSourceTypes() && token.getAllowedSourceTypes().contains(sourceType)
            && !DtoTree.SELECT_PARENT_TREE.equals(sourceType))
        {
            return true;
        }
        if (null != cases)
        {
            return cases.stream().allMatch(fqn -> validateCase(token, fqn));
        }
        return Objects.equals(token.getAttributeFqn(), attributeFqn);
    }

    private static boolean validateCase(TypePermissionToken token, ClassFqn fqn)
    {
        return token.getPermittedClasses().contains(fqn.fqnOfClass()) || token.getPermittedCases().contains(fqn);
    }
}
