package ru.naumen.core.server.util.log.audit;

/**
 * Константы для системы аудита.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
public final class AuditConstants
{
    private AuditConstants()
    {
        // Утилитарный класс
    }

    /**
     * Константы для полей информации о пользователе
     */
    public static final class UserInfo
    {
        public static final String TITLE = "title";
        public static final String LOGIN = "login";
        public static final String UUID = "UUID";
        public static final String IP = "ip";
        public static final String SESSION_ID = "sessionId";
        public static final String GROUPS = "groups";
        public static final String LICENSES = "licenses";
        public static final String PROFILES = "profiles";

        private UserInfo()
        {
            // Утилитарный класс
        }
    }

    /**
     * Константы для полей информации об объектах
     */
    public static final class ObjectInfo
    {
        public static final String OBJECT_UUID = "objectUUID";
        public static final String OBJECT_TITLE = "objectTitle";
        public static final String OBJECT_CLASS = "objectClass";
        public static final String PERMISSIONS = "permissions";
        public static final String ACCESS_LEVEL = "accessLevel";

        private ObjectInfo()
        {
            // Утилитарный класс
        }
    }

    /**
     * Константы для полей контекста запроса
     */
    public static final class RequestInfo
    {
        public static final String REQUEST_URI = "requestURI";
        public static final String REQUEST_METHOD = "requestMethod";
        public static final String USER_AGENT = "userAgent";
        public static final String REFERER = "referer";
        public static final String REMOTE_ADDR = "remoteAddr";

        private RequestInfo()
        {
            // Утилитарный класс
        }
    }

    /**
     * Константы для полей аутентификации
     */
    public static final class AuthenticationInfo
    {
        public static final String AUTHENTICATION_TYPE = "authenticationType";
        public static final String AUTHENTICATION_METHOD = "authenticationMethod";
        public static final String FAILURE_REASON = "failureReason";
        public static final String SUCCESS = "success";

        private AuthenticationInfo()
        {
            // Утилитарный класс
        }
    }

    /**
     * Порядок выполнения провайдеров по умолчанию
     */
    public static final class ProviderOrder
    {
        public static final int USER_INFO = 100;
        public static final int OBJECT_INFO = 200;
        public static final int REQUEST_INFO = 300;
        public static final int AUTHENTICATION_INFO = 400;
        public static final int CUSTOM = 1000;

        private ProviderOrder()
        {
            // Утилитарный класс
        }
    }
}
