package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.sec.server.AuthType;

/**
 * Утилитарный класс для упрощения интеграции существующих логгеров с новым механизмом аудита.
 * Предоставляет удобные методы для логирования с аудитом.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class AuditLoggerHelper
{
    private final AuditFacade auditFacade;

    @Inject
    public AuditLoggerHelper(AuditFacade auditFacade)
    {
        this.auditFacade = auditFacade;
    }

    /**
     * Логирует событие аутентификации с дополнительными данными.
     *
     * @param logger логгер
     * @param message сообщение
     * @param request HTTP запрос
     * @param authType тип аутентификации
     * @param additionalData дополнительные данные
     */
    public void logAuthenticationEvent(Logger logger, String message, HttpServletRequest request, 
                                     AuthType authType, ObjectNode additionalData)
    {
        auditFacade.info(logger, message, request, authType, additionalData);
    }

    /**
     * Логирует событие сессии пользователя.
     *
     * @param logger логгер
     * @param message сообщение
     * @param employee пользователь
     * @param sessionData дополнительные данные сессии
     */
    public void logSessionEvent(Logger logger, String message, Employee employee, Object... sessionData)
    {
        auditFacade.info(logger, message, employee, sessionData);
    }

    /**
     * Логирует событие доступа к объекту.
     *
     * @param logger логгер
     * @param message сообщение
     * @param employee пользователь
     * @param accessedObject объект, к которому осуществляется доступ
     * @param operation операция
     */
    public void logObjectAccessEvent(Logger logger, String message, Employee employee, 
                                   Object accessedObject, String operation)
    {
        ObjectNode operationData = JsonNodeFactory.instance.objectNode();
        operationData.put("operation", operation);
        
        auditFacade.info(logger, message, employee, accessedObject, operationData);
    }

    /**
     * Логирует ошибку с контекстом аудита.
     *
     * @param logger логгер
     * @param message сообщение об ошибке
     * @param exception исключение
     * @param context дополнительный контекст
     */
    public void logErrorWithAudit(Logger logger, String message, Exception exception, Object... context)
    {
        auditFacade.error(logger, message, exception, context);
    }

    /**
     * Создает объект с дополнительными данными для аудита.
     *
     * @return новый ObjectNode
     */
    public ObjectNode createAdditionalData()
    {
        return JsonNodeFactory.instance.objectNode();
    }

    /**
     * Добавляет стандартные поля в дополнительные данные.
     *
     * @param data объект для заполнения
     * @param key ключ
     * @param value значение
     * @return тот же объект для цепочки вызовов
     */
    public ObjectNode addField(ObjectNode data, String key, String value)
    {
        if (value != null)
        {
            data.put(key, value);
        }
        return data;
    }

    /**
     * Добавляет числовое поле в дополнительные данные.
     *
     * @param data объект для заполнения
     * @param key ключ
     * @param value значение
     * @return тот же объект для цепочки вызовов
     */
    public ObjectNode addField(ObjectNode data, String key, Number value)
    {
        if (value != null)
        {
            data.put(key, value.toString());
        }
        return data;
    }

    /**
     * Добавляет булево поле в дополнительные данные.
     *
     * @param data объект для заполнения
     * @param key ключ
     * @param value значение
     * @return тот же объект для цепочки вызовов
     */
    public ObjectNode addField(ObjectNode data, String key, Boolean value)
    {
        if (value != null)
        {
            data.put(key, value);
        }
        return data;
    }
}
