package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Провайдер данных аудита для информации о HTTP запросе.
 * Предоставляет информацию о запросе, IP адресе, User-Agent и т.д.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class RequestInfoAuditDataProvider extends AbstractAuditDataProvider
{
    @Inject
    public RequestInfoAuditDataProvider(AuditConfiguration auditConfiguration)
    {
        super(auditConfiguration);
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        // Предоставляем информацию о запросе для логгеров с расширенной информацией о пользователе
        return auditConfiguration.isNeedLogUserDetails(logger);
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode requestInfo = createObjectNode();

        // Ищем HttpServletRequest в контексте
        HttpServletRequest request = findInContext(auditContext, HttpServletRequest.class);
        if (request != null)
        {
            collectRequestInfo(requestInfo, request);
        }

        // Добавляем информацию из контекста безопасности
        collectSecurityContextInfo(requestInfo);

        return requestInfo;
    }

    @Override
    public int getOrder()
    {
        return AuditConstants.ProviderOrder.REQUEST_INFO;
    }

    /**
     * Собирает информацию о HTTP запросе.
     *
     * @param requestInfo JSON объект для заполнения
     * @param request HTTP запрос
     */
    private void collectRequestInfo(ObjectNode requestInfo, HttpServletRequest request)
    {
        try
        {
            putIfNotNull(requestInfo, AuditConstants.RequestInfo.REQUEST_URI, request.getRequestURI());
            putIfNotNull(requestInfo, AuditConstants.RequestInfo.REQUEST_METHOD, request.getMethod());
            putIfNotNull(requestInfo, AuditConstants.RequestInfo.REMOTE_ADDR, request.getRemoteAddr());
            putIfNotNull(requestInfo, AuditConstants.RequestInfo.USER_AGENT, 
                        request.getHeader("User-Agent"));
            putIfNotNull(requestInfo, AuditConstants.RequestInfo.REFERER, 
                        request.getHeader("Referer"));
        }
        catch (Exception e)
        {
            requestInfo.put("requestInfoError", "Failed to collect request info: " + e.getMessage());
        }
    }

    /**
     * Собирает информацию из контекста безопасности.
     *
     * @param requestInfo JSON объект для заполнения
     */
    private void collectSecurityContextInfo(ObjectNode requestInfo)
    {
        try
        {
            // IP адрес из контекста безопасности
            String userIP = CurrentEmployeeContext.getUserIP();
            if (userIP != null)
            {
                putIfNotNull(requestInfo, AuditConstants.UserInfo.IP, userIP);
            }

            // ID сессии
            String sessionId = CurrentEmployeeContext.getSessionId();
            if (sessionId != null)
            {
                putIfNotNull(requestInfo, AuditConstants.UserInfo.SESSION_ID, sessionId);
            }
        }
        catch (Exception e)
        {
            requestInfo.put("securityContextError", 
                          "Failed to collect security context info: " + e.getMessage());
        }
    }
}
