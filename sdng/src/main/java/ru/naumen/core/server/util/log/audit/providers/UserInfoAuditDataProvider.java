package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Провайдер данных аудита для информации о пользователе.
 * Предоставляет базовую и расширенную информацию о текущем пользователе.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class UserInfoAuditDataProvider extends AbstractAuditDataProvider
{
    private final UserInfoService userInfoService;
    private final CurrentUserProvider currentUserProvider;

    @Inject
    public UserInfoAuditDataProvider(AuditConfiguration auditConfiguration,
                                   UserInfoService userInfoService,
                                   CurrentUserProvider currentUserProvider)
    {
        super(auditConfiguration);
        this.userInfoService = userInfoService;
        this.currentUserProvider = currentUserProvider;
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        // Этот провайдер всегда может предоставить базовую информацию о пользователе
        return true;
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode userInfo = createObjectNode();

        // Проверяем, есть ли уже информация о пользователе в контексте
        Employee employee = findInContext(auditContext, Employee.class);
        
        if (employee == null)
        {
            // Пытаемся получить текущего пользователя
            employee = getCurrentEmployee();
            if (employee != null)
            {
                auditContext.put(employee);
            }
        }

        if (employee != null)
        {
            // Используем существующий UserInfoService для получения информации
            ObjectNode existingUserInfo = userInfoService.getUserInfo(logger, employee);
            if (existingUserInfo != null)
            {
                return existingUserInfo;
            }
        }

        // Если не удалось получить информацию через UserInfoService, 
        // собираем базовую информацию самостоятельно
        collectBasicUserInfo(userInfo, auditContext);

        return userInfo;
    }

    @Override
    public int getOrder()
    {
        return AuditConstants.ProviderOrder.USER_INFO;
    }

    /**
     * Получает текущего пользователя.
     *
     * @return текущий пользователь или null
     */
    private Employee getCurrentEmployee()
    {
        try
        {
            if (CurrentEmployeeContext.getCurrentUserPrincipal() != null)
            {
                return ((CurrentEmployeeContext) currentUserProvider).getCurrentEmployee();
            }
        }
        catch (Exception e)
        {
            // Логируем ошибку, но не прерываем процесс аудита
            // LOG.debug("Failed to get current employee", e);
        }
        return null;
    }

    /**
     * Собирает базовую информацию о пользователе.
     *
     * @param userInfo JSON объект для заполнения
     * @param auditContext контекст аудита
     */
    private void collectBasicUserInfo(ObjectNode userInfo, AuditContext auditContext)
    {
        try
        {
            // Базовая информация, доступная всегда
            putIfNotNull(userInfo, AuditConstants.UserInfo.LOGIN, 
                        CurrentEmployeeContext.getCurrentUserLogin());
            
            String userUuid = currentUserProvider.getCurrentUserUUID();
            putIfNotNull(userInfo, AuditConstants.UserInfo.UUID, userUuid);

            // Проверяем, есть ли Employee в контексте
            Employee employee = findInContext(auditContext, Employee.class);
            if (employee != null)
            {
                putIfNotNull(userInfo, AuditConstants.UserInfo.TITLE, employee.getTitle());
            }
            else if (CurrentEmployeeContext.getCurrentUserPrincipal() != null)
            {
                // Пытаемся получить title из контекста безопасности
                String title = CurrentEmployeeContext.isActuallySuperUser() ?
                    ((ru.naumen.sec.server.users.superuser.SuperUser)
                        CurrentEmployeeContext.getCurrentUserPrincipal()).getTitle() :
                    getCurrentEmployee() != null ? getCurrentEmployee().getTitle() : null;
                putIfNotNull(userInfo, AuditConstants.UserInfo.TITLE, title);
            }
        }
        catch (Exception e)
        {
            // В случае ошибки добавляем минимальную информацию
            userInfo.put("error", "Failed to collect user info: " + e.getMessage());
        }
    }
}
