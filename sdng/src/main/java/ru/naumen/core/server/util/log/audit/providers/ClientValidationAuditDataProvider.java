package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.core.server.util.log.clientvalidation.ClientValidationLoggerImpl;

import java.util.List;

/**
 * Провайдер данных аудита для клиентской валидации.
 * Предоставляет специфичную информацию о валидации форм и объектов.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class ClientValidationAuditDataProvider extends AbstractAuditDataProvider
{
    @Inject
    public ClientValidationAuditDataProvider(AuditConfiguration auditConfiguration)
    {
        super(auditConfiguration);
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        // Предоставляем данные только для логгера клиентской валидации
        return ClientValidationLoggerImpl.class.getName().equals(logger.getName());
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode validationInfo = createObjectNode();

        // Ищем данные валидации в контексте
        ObjectNode validationData = findInContext(auditContext, ObjectNode.class);
        if (validationData != null)
        {
            // Копируем данные валидации
            validationData.fields().forEachRemaining(entry -> 
                validationInfo.set(entry.getKey(), entry.getValue()));
        }

        // Ищем список UUID объектов
        @SuppressWarnings("unchecked")
        List<String> objUuids = findInContext(auditContext, List.class);
        if (objUuids != null && !objUuids.isEmpty())
        {
            ArrayNode uuidsArray = JsonNodeFactory.instance.arrayNode();
            objUuids.forEach(uuidsArray::add);
            validationInfo.set("validatedObjectUUIDs", uuidsArray);
        }

        // Ищем метакласс
        String metaClass = findInContext(auditContext, "metaClass", String.class);
        if (metaClass != null)
        {
            putIfNotNull(validationInfo, "validatedMetaClass", metaClass);
        }

        // Добавляем информацию о типе события
        validationInfo.put("eventType", "CLIENT_VALIDATION_ERROR");
        
        return validationInfo;
    }

    @Override
    public int getOrder()
    {
        // Выполняется после основных провайдеров
        return AuditConstants.ProviderOrder.CUSTOM + 100;
    }

    @Override
    public String getName()
    {
        return "ClientValidationAuditDataProvider";
    }
}
