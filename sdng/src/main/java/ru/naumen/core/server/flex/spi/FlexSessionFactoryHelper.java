package ru.naumen.core.server.flex.spi;

import static com.google.common.base.Predicates.not;
import static com.google.common.base.Predicates.or;
import static ru.naumen.core.shared.Constants.HasState.STATE;
import static ru.naumen.core.shared.Constants.HasState.STATE_START_TIME;
import static ru.naumen.metainfo.shared.Constants.BackLinkAttributeType.CODE;
import static ru.naumen.metainfo.shared.elements.MetaClassLite.MetaClassLitePredicates.abstractClass;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import com.google.common.base.Predicate;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.util.HierarchyUtils;
import ru.naumen.core.server.wf.HasState;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.TimerAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.HasHardcoded;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite;

/**
 * <AUTHOR>
 * @since 20.09.2012
 */
@Component
public class FlexSessionFactoryHelper
{
    private static final Set<String> WORKFLOW_ATTRIBUTES = Sets.newHashSet(STATE, STATE_START_TIME);
    private static final Predicate<Attribute> IS_WORKFLOW_ATTRIBUTE = new Predicate<Attribute>()
    {
        @Override
        public boolean apply(Attribute input)
        {
            return WORKFLOW_ATTRIBUTES.contains(input.getCode());
        }
    };

    private final MetainfoService metainfoService;
    private final FlexHelper flexHelper;
    protected ConfigurationProperties configProperties;

    @Inject
    public FlexSessionFactoryHelper(MetainfoService metainfoService, FlexHelper flexHelper,
            ConfigurationProperties configProperties)
    {
        this.metainfoService = metainfoService;
        this.flexHelper = flexHelper;
        this.configProperties = configProperties;
    }

    /**
     * @param logger если null, то логирования не будет.
     */
    public java.util.function.Predicate<MetaClassLite> isUUIDIdentifiableClass(@Nullable final Logger logger)
    {
        return cls ->
        {
            ClassFqn fqn = cls.getFqn();
            Class<?> javaClass = metainfoService.getJavaClass(fqn);

            // пропускаем классы не являющиеся наследниками UUIDIdentifiableBase
            // вероятнее всего это тестовые mock-классы
            boolean result = UUIDIdentifiableBase.class.isAssignableFrom(javaClass)
                             || HierarchyUtils.isSystemCase(javaClass);
            if (!result && logger != null)
            {
                logger.warn("Skipping init cases for " + fqn);
            }
            return result;
        };
    }

    /**
     * У типов достаем только определенные в них атрибуты, у классов все атрибуты (вверх по иерархии)
     * @param cls
     * @return
     */
    public Collection<Attribute> listHbmAttributes(MetaClass cls)
    {
        ClassFqn fqn = cls.getFqn();
        Class<?> javaClass = metainfoService.getJavaClass(fqn);
        boolean isAbstractBoClass = AbstractBO.class.isAssignableFrom(javaClass);
        boolean isHardcodedWF = HasState.class.isAssignableFrom(javaClass);

        Collection<Attribute> attributes = fqn.isCase()
                ? MetaClassImpl.DECLARED_ATTRIBUTES_EXTRACTOR.apply((MetaClassImpl)cls)
                : cls.getAttributes();

        Stream<Attribute> result = attributes.stream()
                .filter(java.util.function.Predicate.not(attr -> attr.isComputable() || attr.isSystemComputable() ||
                                                                 excludeAttributeOfRelatedObject(attr)));

        if (!cls.isSystemCase())
        {
            result = isAbstractBoClass && !isHardcodedWF && cls.isHasWorkflow() ?
                    result.filter(or(IS_WORKFLOW_ATTRIBUTE, not(HasHardcoded.IS_HARDCODED)))
                    : result.filter(not(HasHardcoded.IS_HARDCODED));
        }

        Set<Attribute> resultAttributes = Sets.newHashSet(result.collect(Collectors.toList()));
        resultAttributes.addAll(getSystemCaseAttrs(attributes));

        return resultAttributes;
    }

    /**
     * В методе две проверки:
     * <li>Проверяет что атрибут является "Атрибутом связанного объекта" который ссылаются на счетчик</li>
     * <li>Для всех остальных проверяем что что атрибут является "Атрибутом связанного объекта" и включен параметр
     * ru.naumen.core.server.excludeAllRelatedAttrs</li>
     * @param attr атрибут
     */
    private boolean excludeAttributeOfRelatedObject(Attribute attr)
    {
        AttributeType type = attr.getType();
        if (type.isAttributeOfRelatedObject() && (type.getCode().equals(BackTimerAttributeType.CODE) || type.getCode()
                .equals(TimerAttributeType.CODE)))
        {
            return true;
        }
        return configProperties.isExcludeAllRelatedAttrs() && type.isAttributeOfRelatedObject();
    }

    /**
     * @return классы, для которых нужно генерировать hbm - 
     *
     */
    public Collection<MetaClass> listHbmMetaClasses()
    {
        //@formatter:off
        return metainfoService.getMetaClasses().stream()
                .filter(abstractClass().negate())
                .filter(isUUIDIdentifiableClass(null))
                .collect(Collectors.toList());
        //@formatter:on
    }

    private List<Attribute> getSystemCaseAttrs(Collection<Attribute> attributes)
    {
        List<Attribute> systemCaseAttrs = new ArrayList<>();
        for (Attribute attr : attributes)
        {
            boolean isBackBOLink = CODE.equals(attr.getType().getCode());
            boolean isHardCoded = attr.isHardcoded();

            if (isBackBOLink && isHardCoded)
            {
                Attribute relatedAttribute = flexHelper.getBackLinkRelatedAttribute(attr);
                if (relatedAttribute.isHardcoded() && relatedAttribute.getMetaClass().isSystemCase())
                {
                    systemCaseAttrs.add(attr);
                }
            }
        }

        return systemCaseAttrs;
    }
}
