package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * Реализация контекста аудита по умолчанию.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
public class DefaultAuditContext implements AuditContext
{
    private final Map<String, Object> contextData = new HashMap<>();
    private ObjectNode auditData;

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(Class<T> clazz)
    {
        String key = clazz.getName();
        Object value = contextData.get(key);
        if (value != null && clazz.isAssignableFrom(value.getClass()))
        {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> get(String key, Class<T> clazz)
    {
        Object value = contextData.get(key);
        if (value != null && clazz.isAssignableFrom(value.getClass()))
        {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }

    @Override
    public <T> void put(T object)
    {
        if (object != null)
        {
            String key = object.getClass().getName();
            contextData.put(key, object);
        }
    }

    @Override
    public <T> void put(String key, T object)
    {
        if (key != null && object != null)
        {
            contextData.put(key, object);
        }
    }

    @Override
    public boolean contains(Class<?> clazz)
    {
        String key = clazz.getName();
        return contextData.containsKey(key);
    }

    @Override
    public boolean contains(String key)
    {
        return contextData.containsKey(key);
    }

    @Override
    public ObjectNode getOrCreateAuditData()
    {
        if (auditData == null)
        {
            auditData = JsonNodeFactory.instance.objectNode();
        }
        return auditData;
    }

    @Override
    public void clear()
    {
        contextData.clear();
        auditData = null;
    }

    /**
     * Инициализирует контекст с массивом объектов.
     *
     * @param objects объекты для добавления в контекст
     */
    public void initialize(Object... objects)
    {
        if (objects != null)
        {
            for (Object obj : objects)
            {
                if (obj != null)
                {
                    put(obj);
                }
            }
        }
    }
}
