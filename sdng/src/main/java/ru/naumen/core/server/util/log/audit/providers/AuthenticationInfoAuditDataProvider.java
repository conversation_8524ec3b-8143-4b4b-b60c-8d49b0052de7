package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.util.log.audit.AbstractAuditDataProvider;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.AuditContext;
import ru.naumen.sec.server.AuthType;

/**
 * Провайдер данных аудита для информации об аутентификации.
 * Предоставляет информацию о типе аутентификации, причинах неудач и т.д.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@Component
public class AuthenticationInfoAuditDataProvider extends AbstractAuditDataProvider
{
    @Inject
    public AuthenticationInfoAuditDataProvider(AuditConfiguration auditConfiguration)
    {
        super(auditConfiguration);
    }

    @Override
    public boolean canProvide(Logger logger)
    {
        // Предоставляем информацию об аутентификации для всех логгеров
        return true;
    }

    @Override
    public ObjectNode provide(Logger logger, AuditContext auditContext)
    {
        ObjectNode authInfo = createObjectNode();

        // Ищем информацию об аутентификации в контексте
        collectAuthenticationInfo(authInfo, auditContext);

        return authInfo;
    }

    @Override
    public int getOrder()
    {
        return AuditConstants.ProviderOrder.AUTHENTICATION_INFO;
    }

    /**
     * Собирает информацию об аутентификации.
     *
     * @param authInfo JSON объект для заполнения
     * @param auditContext контекст аудита
     */
    private void collectAuthenticationInfo(ObjectNode authInfo, AuditContext auditContext)
    {
        // Ищем тип аутентификации
        AuthType authType = findInContext(auditContext, AuthType.class);
        if (authType != null)
        {
            putIfNotNull(authInfo, AuditConstants.AuthenticationInfo.AUTHENTICATION_TYPE, 
                        authType.getName());
        }

        // Ищем исключение аутентификации
        AuthenticationException authException = findInContext(auditContext, AuthenticationException.class);
        if (authException != null)
        {
            putIfNotNull(authInfo, AuditConstants.AuthenticationInfo.FAILURE_REASON, 
                        authException.getMessage());
            authInfo.put(AuditConstants.AuthenticationInfo.SUCCESS, false);
        }
        else
        {
            // Если нет исключения, считаем аутентификацию успешной
            authInfo.put(AuditConstants.AuthenticationInfo.SUCCESS, true);
        }

        // Ищем дополнительную информацию об аутентификации по ключам
        String authMethod = findInContext(auditContext, "authenticationMethod", String.class);
        if (authMethod != null)
        {
            putIfNotNull(authInfo, AuditConstants.AuthenticationInfo.AUTHENTICATION_METHOD, authMethod);
        }

        // Ищем пользовательские данные аутентификации
        ObjectNode customAuthData = findInContext(auditContext, ObjectNode.class);
        if (customAuthData != null)
        {
            // Объединяем пользовательские данные с основными
            customAuthData.fields().forEachRemaining(entry ->
                authInfo.set(entry.getKey(), entry.getValue()));
        }
    }
}
