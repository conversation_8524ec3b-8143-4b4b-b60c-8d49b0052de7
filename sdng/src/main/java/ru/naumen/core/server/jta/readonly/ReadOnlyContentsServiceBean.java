package ru.naumen.core.server.jta.readonly;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.GroovyUsage;

/**
 * Реализация, есть доп методы для манипуляций из консоли
 * <AUTHOR>
 * @since 08.04.19
 */
@Component
public class ReadOnlyContentsServiceBean implements ReadOnlyContentsService
{
    private final Set<String> readOnlyContents = ConcurrentHashMap.newKeySet();
    private final Set<String> readOnlyExclusions = ConcurrentHashMap.newKeySet();
    private volatile boolean allReadOnlyContentsWithExclusions;

    @Inject
    public ReadOnlyContentsServiceBean(@Value("${ru.naumen.readonly.contents}") String initialReadOnlyContents,
            @Value("${ru.naumen.readonly.allWithExclusions}") boolean allWithExclusions,
            @Value("${ru.naumen.readonly.exclusions}") String initialReadOnlyExclusions)
    {
        for (String contentUuid : initialReadOnlyContents.split(","))
        {
            if (StringUtils.isNotEmpty(contentUuid))
            {
                readOnlyContents.add(contentUuid.trim());
            }
        }

        for (String contentUuid : initialReadOnlyExclusions.split(","))
        {
            if (StringUtils.isNotEmpty(contentUuid))
            {
                readOnlyExclusions.add(contentUuid.trim());
            }
        }
        allReadOnlyContentsWithExclusions = allWithExclusions;
    }

    /**
     * Добавить контент по  UUID к списку контентов для работы на read only datasource
     * @param contentUUID UUID контента
     */
    @GroovyUsage
    public void addReadOnlyContent(String contentUUID)
    {
        readOnlyContents.add(contentUUID);
    }

    @GroovyUsage
    public void addReadOnlyContents(Collection<String> contentUUIDs)
    {
        contentUUIDs.forEach(this::addReadOnlyContent);
    }

    /**
     * Использовать ли все контенты на read only datasource кроме списка-исключений
     * @param allReadOnlyContentsWithExclusions испольузется ли список контентов-исключений
     */
    @GroovyUsage
    public void setAllReadOnlyContentsWithExclusions(boolean allReadOnlyContentsWithExclusions)
    {
        this.allReadOnlyContentsWithExclusions = allReadOnlyContentsWithExclusions;
    }

    /**
     * Добавить контент по  UUID к списку контентов-исключений для работы не на read only datasource
     * @param contentUUID UUID контента
     */
    @GroovyUsage
    public void addReadOnlyExclusions(String contentUUID)
    {
        readOnlyExclusions.add(contentUUID);
    }

    @GroovyUsage
    public void addReadOnlyExclusions(Collection<String> contentUUIDs)
    {
        contentUUIDs.forEach(this::addReadOnlyExclusions);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isReadOnlyContent(@Nullable String contentUUID)
    {
        if (StringUtils.isEmpty(contentUUID))
        {
            return false;
        }

        if (!allReadOnlyContentsWithExclusions)
        {
            return readOnlyContents.contains(contentUUID);
        }
        else
        {
            return !readOnlyExclusions.contains(contentUUID);
        }
    }

    @GroovyUsage
    public void removeReadOnlyContent(String contentUUID)
    {
        readOnlyContents.remove(contentUUID);
    }

    @GroovyUsage
    public void removeReadOnlyExclusions(String contentUUID)
    {
        readOnlyExclusions.remove(contentUUID);
    }
}
