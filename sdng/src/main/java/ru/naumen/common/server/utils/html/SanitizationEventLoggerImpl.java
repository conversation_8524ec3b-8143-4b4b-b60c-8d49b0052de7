package ru.naumen.common.server.utils.html;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import jakarta.inject.Inject;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderServiceImpl;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.AuditFacade;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Реализация {@link SanitizationEventLogger}
 *
 * <AUTHOR>
 * @since 16.07.2025
 */
@Component
public class SanitizationEventLoggerImpl implements SanitizationEventLogger
{
    private static final Logger LOG = LoggerFactory.getLogger(SanitizationEventLoggerImpl.class);
    private final CurrentUserProvider currentUserProvider;
    private final MetainfoService metainfoService;
    private final PrefixObjectLoaderServiceImpl loaderService;
    private final UserInfoService userInfoService;
    private final AuditFacade auditFacade;

    public enum XssEvent
    {
        SUBMISSION("Potential XSS removed during form submission"),
        RENDERING("Potential XSS removed during page rendering"),
        UNKNOWN("Potential XSS removed");

        private final String message;

        XssEvent(String message)
        {
            this.message = message;
        }

        public String getMessage()
        {
            return message;
        }

        public static XssEvent fromString(String value)
        {
            try
            {
                return XssEvent.valueOf(value);
            }
            catch (Exception ex)
            {
                return XssEvent.UNKNOWN;
            }
        }
    }

    @Inject
    public SanitizationEventLoggerImpl(CurrentUserProvider currentUserProvider,
            MetainfoService metainfoService,
            PrefixObjectLoaderServiceImpl loaderService,
            UserInfoService userInfoService,
            AuditFacade auditFacade)
    {
        this.currentUserProvider = currentUserProvider;
        this.metainfoService = metainfoService;
        this.loaderService = loaderService;
        this.userInfoService = userInfoService;
        this.auditFacade = auditFacade;
    }

    @Override
    public void log(SanitizedContentStorage unsafeContent)
    {
        if (unsafeContent.hasUnsafeElements())
        {
            try
            {
                // Получаем текущего пользователя для контекста аудита
                var currentEmployee = ((CurrentEmployeeContext) currentUserProvider).getCurrentEmployee();

                // Создаем дополнительные данные санитизации
                ObjectNode sanitizationData = createSanitizationData(unsafeContent);

                // Получаем объект, если он есть
                IUUIDIdentifiable sanitizedObject = getSanitizedObject();

                // Используем новую систему аудита
                auditFacade.warn(LOG, SanitizationScope.getSanitizeEvent().getMessage(),
                               currentEmployee, sanitizationData, sanitizedObject);
            }
            catch (Exception e)
            {
                // Fallback к старому методу в случае ошибки
                LOG.warn("Failed to use audit system for sanitization logging, falling back to old method", e);
                logWithOldMethod(unsafeContent);
            }
        }
    }

    /**
     * Создает данные санитизации для новой системы аудита.
     */
    private ObjectNode createSanitizationData(SanitizedContentStorage unsafeContent)
    {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode sanitizationData = mapper.createObjectNode();

        sanitizationData.put("eventType", "XSS_SANITIZATION");

        // Добавляем информацию о форме
        if (SanitizationScope.getSanitizeForm() != null)
        {
            sanitizationData.put("form", SanitizationScope.getSanitizeForm());
        }

        // Добавляем информацию об атрибуте
        if (SanitizationScope.getSanitizeAttribute() != null)
        {
            sanitizationData.put("attribute", SanitizationScope.getSanitizeAttribute());
        }

        // Добавляем payload
        sanitizationData.set("payload", mapper.valueToTree(unsafeContent));

        return sanitizationData;
    }

    /**
     * Получает санитизированный объект.
     */
    private IUUIDIdentifiable getSanitizedObject()
    {
        String uuid = SanitizationScope.getSanitizedObject();
        if (uuid != null)
        {
            return loaderService.load(uuid);
        }
        return null;
    }

    /**
     * Fallback метод для логирования старым способом.
     */
    private void logWithOldMethod(SanitizedContentStorage unsafeContent)
    {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode logRecord = mapper.createObjectNode();

        addFormInfo(logRecord);
        addUserInfo(LOG, logRecord);
        addObjectInfo(logRecord);
        addPayload(logRecord, unsafeContent);

        LOG.warn("{} {}", SanitizationScope.getSanitizeEvent().getMessage(), logRecord);
    }

    private static void addFormInfo(ObjectNode node)
    {
        if (SanitizationScope.getSanitizeForm() != null)
        {
            node.put("form", SanitizationScope.getSanitizeForm());
        }
    }

    private static void addPayload(ObjectNode node, SanitizedContentStorage unsafeContent)
    {
        ObjectMapper mapper = new ObjectMapper();
        node.set("payload", mapper.valueToTree(unsafeContent));
    }

    private void addUserInfo(Logger logger, ObjectNode node)
    {
        if (CurrentEmployeeContext.getCurrentUserPrincipal() != null)
        {
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode user = mapper.createObjectNode();
            ObjectNode userInfo = userInfoService.getUserInfo(logger,
                    ((CurrentEmployeeContext)currentUserProvider).getCurrentEmployee());
            //            user.put("login", CurrentEmployeeContext.getCurrentUserLogin());
//            user.put("title", CurrentEmployeeContext.isActuallySuperUser() ?
//                    ((SuperUser)CurrentEmployeeContext.getCurrentUserPrincipal()).getTitle() :
//                    ((CurrentEmployeeContext)currentUserProvider).getCurrentEmployee().getTitle());
//            user.put("UUID", CurrentEmployeeContext.getCurrentUserLogin());
            node.set("user", userInfo);
        }
    }

    private void addObjectInfo(ObjectNode node)
    {
        String uuid = SanitizationScope.getSanitizedObject();
        if (uuid != null)
        {
            IUUIDIdentifiable iuuidIdentifiable = loaderService.load(uuid);
            ClassFqn classFqn = metainfoService.getClassFqn(iuuidIdentifiable);
            MetaClass metaClass = metainfoService.getMetaClass(classFqn);

            ObjectMapper mapper = new ObjectMapper();
            ObjectNode object = mapper.createObjectNode();
            object.put("metaClass", metaClass.getFqn().asString());
            object.put("metaClassTitle", metaClass.getTitle());
            if (iuuidIdentifiable instanceof ITitled titledObject)
            {
                object.put("title", titledObject.getTitle());
            }
            object.put("uuid", uuid);
            object.put("attribute", SanitizationScope.getSanitizeAttribute());

            node.set("objects", mapper.createArrayNode().add(object));
        }
    }
}