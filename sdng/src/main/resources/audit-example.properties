# Пример конфигурации системы аудита

# Логгеры с расширенной информацией о пользователе
# Для этих логгеров будет добавляться IP адрес, ID сессии, группы и лицензии
ru.naumen.logger.details.user.loggers=\
  ru.naumen.core.server.util.log.login.LoginFailureLogger,\
  ru.naumen.sec.server.session.SessionLoggerImpl,\
  ru.naumen.security.AccessControlLogger,\
  ru.naumen.admin.server.AdminActionLogger

# Логгеры с информацией о правах на объекты
# Для этих логгеров будет добавляться информация о правах доступа к объектам
ru.naumen.logger.details.objects.permissions.loggers=\
  ru.naumen.security.ObjectAccessLogger,\
  ru.naumen.documents.DocumentAccessLogger,\
  ru.naumen.workflow.WorkflowActionLogger

# Дополнительные настройки логирования (опционально)
# Включить отладочную информацию для системы аудита
logging.level.ru.naumen.core.server.util.log.audit=DEBUG

# Настройки Jackson для сериализации JSON (если нужны специфичные настройки)
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-dates-as-timestamps=false
