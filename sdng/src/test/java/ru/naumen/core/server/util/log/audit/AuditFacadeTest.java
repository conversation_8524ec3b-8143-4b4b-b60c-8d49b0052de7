package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Тесты для {@link AuditFacade}.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@ExtendWith(MockitoExtension.class)
class AuditFacadeTest
{
    @Mock
    private AuditDataProvider provider1;
    
    @Mock
    private AuditDataProvider provider2;
    
    @Mock
    private AuditConfiguration configuration;
    
    @Mock
    private Logger logger;
    
    private ObjectMapper objectMapper;
    private AuditFacade auditFacade;

    @BeforeEach
    void setUp()
    {
        objectMapper = new ObjectMapper();
        List<AuditDataProvider> providers = Arrays.asList(provider1, provider2);
        auditFacade = new AuditFacade(providers, configuration, objectMapper);
    }

    @Test
    void testBuildAuditJson_WithMultipleProviders()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(true);
        when(provider2.canProvide(logger)).thenReturn(true);
        when(provider1.getOrder()).thenReturn(1);
        when(provider2.getOrder()).thenReturn(2);

        ObjectNode data1 = JsonNodeFactory.instance.objectNode();
        data1.put("field1", "value1");
        when(provider1.provide(eq(logger), any(AuditContext.class))).thenReturn(data1);

        ObjectNode data2 = JsonNodeFactory.instance.objectNode();
        data2.put("field2", "value2");
        when(provider2.provide(eq(logger), any(AuditContext.class))).thenReturn(data2);

        // Act
        ObjectNode result = auditFacade.buildAuditJson(logger, "testContext");

        // Assert
        assertNotNull(result);
        assertEquals("value1", result.get("field1").asText());
        assertEquals("value2", result.get("field2").asText());
        
        verify(provider1).canProvide(logger);
        verify(provider2).canProvide(logger);
        verify(provider1).provide(eq(logger), any(AuditContext.class));
        verify(provider2).provide(eq(logger), any(AuditContext.class));
    }

    @Test
    void testBuildAuditJson_WithProviderThatCannotProvide()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(true);
        when(provider2.canProvide(logger)).thenReturn(false);
        when(provider1.getOrder()).thenReturn(1);
        when(provider2.getOrder()).thenReturn(2);

        ObjectNode data1 = JsonNodeFactory.instance.objectNode();
        data1.put("field1", "value1");
        when(provider1.provide(eq(logger), any(AuditContext.class))).thenReturn(data1);

        // Act
        ObjectNode result = auditFacade.buildAuditJson(logger, "testContext");

        // Assert
        assertNotNull(result);
        assertEquals("value1", result.get("field1").asText());
        assertNull(result.get("field2"));
        
        verify(provider1).canProvide(logger);
        verify(provider2).canProvide(logger);
        verify(provider1).provide(eq(logger), any(AuditContext.class));
        verify(provider2, never()).provide(any(), any());
    }

    @Test
    void testBuildAuditJson_WithProviderException()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(true);
        when(provider2.canProvide(logger)).thenReturn(true);
        when(provider1.getOrder()).thenReturn(1);
        when(provider2.getOrder()).thenReturn(2);

        ObjectNode data1 = JsonNodeFactory.instance.objectNode();
        data1.put("field1", "value1");
        when(provider1.provide(eq(logger), any(AuditContext.class))).thenReturn(data1);
        
        when(provider2.provide(eq(logger), any(AuditContext.class)))
            .thenThrow(new RuntimeException("Provider error"));

        // Act
        ObjectNode result = auditFacade.buildAuditJson(logger, "testContext");

        // Assert
        assertNotNull(result);
        assertEquals("value1", result.get("field1").asText());
        // Провайдер с ошибкой не должен влиять на результат
        
        verify(provider1).provide(eq(logger), any(AuditContext.class));
        verify(provider2).provide(eq(logger), any(AuditContext.class));
    }

    @Test
    void testInfo_LogsWithCorrectLevel()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(false);
        when(provider2.canProvide(logger)).thenReturn(false);

        // Act
        auditFacade.info(logger, "Test message", "context");

        // Assert
        verify(logger).info(contains("Test message"));
    }

    @Test
    void testWarn_LogsWithCorrectLevel()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(false);
        when(provider2.canProvide(logger)).thenReturn(false);

        // Act
        auditFacade.warn(logger, "Test warning", "context");

        // Assert
        verify(logger).warn(contains("Test warning"));
    }

    @Test
    void testError_LogsWithCorrectLevel()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(false);
        when(provider2.canProvide(logger)).thenReturn(false);

        // Act
        auditFacade.error(logger, "Test error", "context");

        // Assert
        verify(logger).error(contains("Test error"));
    }

    @Test
    void testLog_WithAuditData()
    {
        // Arrange
        when(provider1.canProvide(logger)).thenReturn(true);
        when(provider1.getOrder()).thenReturn(1);

        ObjectNode data = JsonNodeFactory.instance.objectNode();
        data.put("user", "testUser");
        when(provider1.provide(eq(logger), any(AuditContext.class))).thenReturn(data);

        // Act
        auditFacade.info(logger, "Test message", "context");

        // Assert
        verify(logger).info(argThat(message -> 
            message.contains("Test message") && 
            message.contains("testUser")));
    }
}
