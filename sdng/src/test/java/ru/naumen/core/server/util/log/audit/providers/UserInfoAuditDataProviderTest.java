package ru.naumen.core.server.util.log.audit.providers;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.AuditConfiguration;
import ru.naumen.core.server.util.log.audit.AuditConstants;
import ru.naumen.core.server.util.log.audit.DefaultAuditContext;
import ru.naumen.core.shared.utils.CurrentUserProvider;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Тесты для {@link UserInfoAuditDataProvider}.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@ExtendWith(MockitoExtension.class)
class UserInfoAuditDataProviderTest
{
    @Mock
    private AuditConfiguration auditConfiguration;
    
    @Mock
    private UserInfoService userInfoService;
    
    @Mock
    private CurrentUserProvider currentUserProvider;
    
    @Mock
    private Logger logger;
    
    @Mock
    private Employee employee;
    
    private UserInfoAuditDataProvider provider;
    private DefaultAuditContext auditContext;

    @BeforeEach
    void setUp()
    {
        provider = new UserInfoAuditDataProvider(auditConfiguration, userInfoService, currentUserProvider);
        auditContext = new DefaultAuditContext();
    }

    @Test
    void testCanProvide_AlwaysReturnsTrue()
    {
        // Act & Assert
        assertTrue(provider.canProvide(logger));
    }

    @Test
    void testProvide_WithEmployeeInContext()
    {
        // Arrange
        auditContext.put(employee);
        
        ObjectNode expectedUserInfo = auditContext.getOrCreateAuditData();
        expectedUserInfo.put(AuditConstants.UserInfo.LOGIN, "testUser");
        expectedUserInfo.put(AuditConstants.UserInfo.TITLE, "Test User");
        
        when(userInfoService.getUserInfo(logger, employee)).thenReturn(expectedUserInfo);

        // Act
        ObjectNode result = provider.provide(logger, auditContext);

        // Assert
        assertNotNull(result);
        assertEquals("testUser", result.get(AuditConstants.UserInfo.LOGIN).asText());
        assertEquals("Test User", result.get(AuditConstants.UserInfo.TITLE).asText());
        
        verify(userInfoService).getUserInfo(logger, employee);
    }

    @Test
    void testProvide_WithoutEmployeeInContext()
    {
        // Arrange
        when(userInfoService.getUserInfo(eq(logger), any(Employee.class))).thenReturn(null);
        when(currentUserProvider.getCurrentUserUUID()).thenReturn("user-uuid-123");

        // Act
        ObjectNode result = provider.provide(logger, auditContext);

        // Assert
        assertNotNull(result);
        // Должна быть собрана базовая информация
        assertTrue(result.size() >= 0); // Может быть пустым, если нет доступа к контексту безопасности
    }

    @Test
    void testGetOrder()
    {
        // Act & Assert
        assertEquals(AuditConstants.ProviderOrder.USER_INFO, provider.getOrder());
    }

    @Test
    void testProvide_HandlesExceptions()
    {
        // Arrange
        auditContext.put(employee);
        when(userInfoService.getUserInfo(logger, employee))
            .thenThrow(new RuntimeException("Service error"));

        // Act
        ObjectNode result = provider.provide(logger, auditContext);

        // Assert
        assertNotNull(result);
        // Провайдер должен обработать ошибку и вернуть базовую информацию
    }
}
