package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Тесты для {@link DefaultAuditContext}.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
class DefaultAuditContextTest
{
    private DefaultAuditContext context;

    @BeforeEach
    void setUp()
    {
        context = new DefaultAuditContext();
    }

    @Test
    void testPutAndGetByClass()
    {
        // Arrange
        String testString = "test";
        Integer testInteger = 42;

        // Act
        context.put(testString);
        context.put(testInteger);

        // Assert
        Optional<String> retrievedString = context.get(String.class);
        Optional<Integer> retrievedInteger = context.get(Integer.class);

        assertTrue(retrievedString.isPresent());
        assertEquals("test", retrievedString.get());
        
        assertTrue(retrievedInteger.isPresent());
        assertEquals(42, retrievedInteger.get());
    }

    @Test
    void testPutAndGetByKey()
    {
        // Arrange
        String testValue = "testValue";
        String key = "testKey";

        // Act
        context.put(key, testValue);

        // Assert
        Optional<String> retrieved = context.get(key, String.class);
        assertTrue(retrieved.isPresent());
        assertEquals("testValue", retrieved.get());
    }

    @Test
    void testGetNonExistentByClass()
    {
        // Act
        Optional<Double> result = context.get(Double.class);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void testGetNonExistentByKey()
    {
        // Act
        Optional<String> result = context.get("nonExistentKey", String.class);

        // Assert
        assertFalse(result.isPresent());
    }

    @Test
    void testContainsByClass()
    {
        // Arrange
        String testString = "test";
        context.put(testString);

        // Act & Assert
        assertTrue(context.contains(String.class));
        assertFalse(context.contains(Integer.class));
    }

    @Test
    void testContainsByKey()
    {
        // Arrange
        context.put("testKey", "testValue");

        // Act & Assert
        assertTrue(context.contains("testKey"));
        assertFalse(context.contains("nonExistentKey"));
    }

    @Test
    void testGetOrCreateAuditData()
    {
        // Act
        ObjectNode auditData1 = context.getOrCreateAuditData();
        ObjectNode auditData2 = context.getOrCreateAuditData();

        // Assert
        assertNotNull(auditData1);
        assertNotNull(auditData2);
        assertSame(auditData1, auditData2); // Должен возвращать тот же объект
    }

    @Test
    void testClear()
    {
        // Arrange
        context.put("testKey", "testValue");
        context.put("test");
        context.getOrCreateAuditData();

        // Act
        context.clear();

        // Assert
        assertFalse(context.contains("testKey"));
        assertFalse(context.contains(String.class));
        
        // После clear должен создаваться новый объект аудита
        ObjectNode newAuditData = context.getOrCreateAuditData();
        assertNotNull(newAuditData);
        assertEquals(0, newAuditData.size());
    }

    @Test
    void testInitialize()
    {
        // Arrange
        String testString = "test";
        Integer testInteger = 42;

        // Act
        context.initialize(testString, testInteger, null); // null должен игнорироваться

        // Assert
        assertTrue(context.contains(String.class));
        assertTrue(context.contains(Integer.class));
        
        assertEquals("test", context.get(String.class).get());
        assertEquals(42, context.get(Integer.class).get());
    }

    @Test
    void testPutNull()
    {
        // Act
        context.put((String) null);
        context.put("key", null);

        // Assert
        assertFalse(context.contains(String.class));
        assertFalse(context.contains("key"));
    }

    @Test
    void testGetWithWrongType()
    {
        // Arrange
        context.put("testString");

        // Act
        Optional<Integer> result = context.get(String.class.getName(), Integer.class);

        // Assert
        assertFalse(result.isPresent());
    }
}
