package ru.naumen.core.server.util.log.audit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.util.log.UserInfoService;
import ru.naumen.core.server.util.log.audit.providers.AuthenticationInfoAuditDataProvider;
import ru.naumen.core.server.util.log.audit.providers.UserInfoAuditDataProvider;
import ru.naumen.core.shared.utils.CurrentUserProvider;
import ru.naumen.sec.server.AuthType;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Интеграционные тесты для системы аудита.
 *
 * <AUTHOR>
 * @since 05.08.2025
 */
@ExtendWith(MockitoExtension.class)
class AuditIntegrationTest
{
    @Mock
    private AuditConfiguration auditConfiguration;
    
    @Mock
    private UserInfoService userInfoService;
    
    @Mock
    private CurrentUserProvider currentUserProvider;
    
    @Mock
    private Logger logger;
    
    @Mock
    private Employee employee;
    
    @Mock
    private HttpServletRequest request;
    
    @Mock
    private AuthType authType;
    
    private AuditFacade auditFacade;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp()
    {
        objectMapper = new ObjectMapper();
        
        // Создаем провайдеры
        UserInfoAuditDataProvider userInfoProvider = 
            new UserInfoAuditDataProvider(auditConfiguration, userInfoService, currentUserProvider);
        AuthenticationInfoAuditDataProvider authInfoProvider = 
            new AuthenticationInfoAuditDataProvider(auditConfiguration);
        
        // Создаем фасад с провайдерами
        auditFacade = new AuditFacade(
            Arrays.asList(userInfoProvider, authInfoProvider), 
            auditConfiguration, 
            objectMapper
        );
    }

    @Test
    void testFullAuditFlow_AuthenticationFailure()
    {
        // Arrange
        when(employee.getTitle()).thenReturn("John Doe");
        when(employee.getUUID()).thenReturn("employee-uuid-123");
        when(authType.getName()).thenReturn("LDAP");
        
        ObjectNode userInfo = JsonNodeFactory.instance.objectNode();
        userInfo.put(AuditConstants.UserInfo.LOGIN, "john.doe");
        userInfo.put(AuditConstants.UserInfo.TITLE, "John Doe");
        userInfo.put(AuditConstants.UserInfo.UUID, "employee-uuid-123");
        
        when(userInfoService.getUserInfo(eq(logger), eq(employee))).thenReturn(userInfo);
        
        // Создаем дополнительные данные аутентификации
        ObjectNode customAuthData = JsonNodeFactory.instance.objectNode();
        customAuthData.put(AuditConstants.AuthenticationInfo.AUTHENTICATION_TYPE, "LDAP");
        
        // Act
        auditFacade.info(logger, "Authentication failed for user: john.doe", 
                        employee, request, authType, customAuthData);

        // Assert
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(logger).info(messageCaptor.capture());
        
        String loggedMessage = messageCaptor.getValue();
        assertNotNull(loggedMessage);
        assertTrue(loggedMessage.contains("Authentication failed for user: john.doe"));
        assertTrue(loggedMessage.contains("john.doe"));
        assertTrue(loggedMessage.contains("John Doe"));
        assertTrue(loggedMessage.contains("LDAP"));
        assertTrue(loggedMessage.contains("employee-uuid-123"));
    }

    @Test
    void testAuditFlow_WithMultipleProviders()
    {
        // Arrange
        when(auditConfiguration.isNeedLogUserDetails(logger)).thenReturn(true);
        
        ObjectNode userInfo = JsonNodeFactory.instance.objectNode();
        userInfo.put(AuditConstants.UserInfo.LOGIN, "test.user");
        when(userInfoService.getUserInfo(eq(logger), any(Employee.class))).thenReturn(userInfo);
        
        when(authType.getName()).thenReturn("FORM");

        // Act
        auditFacade.warn(logger, "Security warning", employee, authType);

        // Assert
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(logger).warn(messageCaptor.capture());
        
        String loggedMessage = messageCaptor.getValue();
        assertNotNull(loggedMessage);
        assertTrue(loggedMessage.contains("Security warning"));
        assertTrue(loggedMessage.contains("test.user"));
        assertTrue(loggedMessage.contains("FORM"));
    }

    @Test
    void testAuditFlow_WithProviderError()
    {
        // Arrange
        when(userInfoService.getUserInfo(eq(logger), any(Employee.class)))
            .thenThrow(new RuntimeException("Service unavailable"));
        
        when(authType.getName()).thenReturn("OAUTH");

        // Act & Assert - не должно выбрасывать исключение
        assertDoesNotThrow(() -> {
            auditFacade.error(logger, "System error occurred", employee, authType);
        });

        // Проверяем, что сообщение все равно залогировано
        verify(logger).error(contains("System error occurred"));
    }

    @Test
    void testBuildAuditJson_ReturnsValidJson()
    {
        // Arrange
        ObjectNode userInfo = JsonNodeFactory.instance.objectNode();
        userInfo.put(AuditConstants.UserInfo.LOGIN, "test.user");
        userInfo.put(AuditConstants.UserInfo.UUID, "user-123");
        
        when(userInfoService.getUserInfo(eq(logger), any(Employee.class))).thenReturn(userInfo);
        when(authType.getName()).thenReturn("BASIC");

        // Act
        ObjectNode result = auditFacade.buildAuditJson(logger, employee, authType);

        // Assert
        assertNotNull(result);
        assertTrue(result.has(AuditConstants.UserInfo.LOGIN));
        assertTrue(result.has(AuditConstants.AuthenticationInfo.AUTHENTICATION_TYPE));
        
        assertEquals("test.user", result.get(AuditConstants.UserInfo.LOGIN).asText());
        assertEquals("BASIC", result.get(AuditConstants.AuthenticationInfo.AUTHENTICATION_TYPE).asText());
    }

    @Test
    void testAuditFlow_WithEmptyContext()
    {
        // Act
        auditFacade.info(logger, "Simple message");

        // Assert
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(logger).info(messageCaptor.capture());
        
        String loggedMessage = messageCaptor.getValue();
        assertNotNull(loggedMessage);
        assertTrue(loggedMessage.contains("Simple message"));
        // Может содержать пустой JSON объект или только базовую информацию
    }
}
